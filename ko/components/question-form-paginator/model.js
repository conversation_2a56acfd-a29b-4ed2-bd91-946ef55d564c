import { INTER_BLOCK } from "@/data/question-types";
// TODO TASK-4484
//import { ContactPointsCollection } from "@/models/data-collection/contact-points";
import { Question } from "@/pages/poll/logic/models/question";
import { Poll } from "@/pages/poll/logic/models/poll";
import { clearLogic } from "@/pages/poll/logic/api";
import { LogicPage } from "Models/logic/page";
import { DialogsModule } from "Utils/dialogs-module";
import { Translator } from "@/utils/translate";
const QuestionsTranslator = Translator("questions");

// TODO TASK-4484
// const contactPointsCollection = new ContactPointsCollection();
// contactPointsCollection.load();

function getQuestionConfig(q) {
  let type = "default";
  let isInter = false;
  if (
    q?.main_question_type == INTER_BLOCK ||
    q?.mainQuestionType == INTER_BLOCK
  ) {
    isInter = true;
    type = "inter";
   
    if (q.intermediateBlock) {
      if (q.intermediateBlock.screen_type == 3) type = "end";
      else if (q.intermediateBlock.screen_type == 2) type = "start";
      else if (q.intermediateBlock.show_question_number) type = "default";
    }
  }

  return { type, isInter };
}

function getActiveQuestionConfig(question) {
  let type = "default";
  let isInter = false;

  if (question.type == INTER_BLOCK) {
    type = "inter";
    isInter = true;
    let screenType = question.blockType();
    if (screenType == "end") type = "end";
    else if (screenType == "start") type = "start";
    else {
      if (question.showNumber()) type = "default";
    }
  }

  return {
    type,
    isInter,
  };
}

function formatQuestionsList(l, activeId, activeQuestion, ctx) {
  return l.map((q, index) => {
    let name = q?.service_name || q?.serviceName;
    let config = { type: "default" };

    if (q?.id == activeId) config = getActiveQuestionConfig(activeQuestion);
    else config = getQuestionConfig(q);

    let pointId = q?.point_id || q?.pointId;
    let pointName = q?.pointName;
    let tooltip = q?.tooltip || q?.data?.tooltip || null;

    return new Question(
        Object.assign({}, q, {
          name: name,
          id: q?.id,
          type: ko.observable(config.type),
          isInter: config.isInter,
          isSystem: q?.is_system,
          pointId,
          pointName,
          tooltip: tooltip,
        }),
        index + 1,
        ctx,
    );
  });
}

export class ViewModel {
  constructor(params, element) {
    DialogsModule(this);
    this.poll = new Poll(params.poll);
    this.subscriptions = [];
    this.translator = QuestionsTranslator;

    this.startQuestions = ko.observableArray([]);
    this.defaultQuestions = ko.observableArray([]);
    this.endQuestions = ko.observableArray([]);

    this.activeQuestionId =
      "activeQuestionId" in params
        ? params.activeQuestionId
        : ko.observable(null);
    this.activeQuestion = params.activeQuestion;
    this.activateQuestion = params.activateQuestion;

    this.isAuto = params.isAuto;
    if (!this.isAuto) element.classList.add("dense");

    this.hasLogic = params.hasLogic;
    this.hasEndLogic = params.hasEndLogic || ko.observable(false);

    this.randomOrder = params.randomOrder || ko.observable(false);

    this.blinked = params.blinked || ko.observableArray([]);
    this.blinked.subscribe((v) => {
      if (v.length) {
        setTimeout(() => {
          this.blinked([]);
        }, 12000);
      }
    });
    
    // Flag to track whether we've already shown the modal for the current drag operation
    this.isModalShown = false;

    this.questionsList = ko.computed(() => {
      const list = this.defaultQuestions();
      const indexes = {};

      const ordered = list.filter((q) => {
        if (!q.isIntermediate) return true;
        return q.intermediateBlock.show_question_number;
      });

      ordered.forEach((q, i) => {
        indexes[q.id] = {
          index: i + 1,
          question: q,
        };
      });

      return indexes;
    });

    // Store the isSaved observable from params if provided
    this.isSaved = params.isSaved || ko.observable(true);
    this.isChanged = params.isChanged || ko.observable(false); 
    
    // Store the openUnsavedChangesModal function from params if provided
    this.openUnsavedChangesModal = params.openUnsavedChangesModal;

    this.isSortableEnabled = ko.observable(typeof params.isSortableEnabled === "function" ? params.isSortableEnabled() : params.isSortableEnabled);

    this.pagesMode = ko.observable(false);
    if ("pagesMode" in params) {
      if (ko.isObservable(params.pagesMode)) this.pagesMode = params.pagesMode;
      else this.pagesMode(params.pagesMode);
    }
    this.pages = ko.observableArray([]);
    this.parentPages = params.pages || [];
    if (window.location.pathname.includes('logic')) { // ToDo refactor
      this.pages = params.pages;
    }

    this.groups = ko.computed(() => {
      if (this.pagesMode()) {
        const order = this.defaultQuestions().map((q) => q.id);
        let questionsSet = this.defaultQuestions();

        let counter = 1;
        let groups = this.pages().map((page, index) => {
          let questions = [...page.questions()];

          // отсортировать по порядку
          questions.sort((a, b) => {
            return order.indexOf(a.id) - order.indexOf(b.id);
          });
          // соотнести id вопросов с вопросами
          questions = questions
            .map(({ id }) => {
              let question = questionsSet.find((q) => q.id == id);
              if (question)
                question.groupIndex =
                  question.type() == "default" ? counter++ : "";
              return question;
            })
            .filter(Boolean);
          return {
            ...page,
            tooltip: page.name()
              ? page.name()
              : this.translator.t(`Название страницы {number}`, {
                  number: index + 1,
                })(),
            questions,
          };
        });

        return groups;
      }
      return null;
    });

    this.questions = ko.computed(() => {
      return [
        ...this.startQuestions(),
        ...this.defaultQuestions(),
        ...this.endQuestions(),
      ];
    }, this);

    this.donorOrderError = ko.observable(false);

    this.oldQuestionsOrder = ko.observable(
      this.defaultQuestions().map((q) => q.id)
    );

    this.resorting = ko.observable(false);

    this.formatQuestions(params.questions());
    this.initPages(this.poll.displayPages);

    if (ko.isObservable(params.questions)) {
      params.questions.subscribe((v) => {
        const sp = new URLSearchParams(location.search.slice(1));
        if (sp.has("stop")) {
          return;
        } 
        this.formatQuestions(v);

        setTimeout(() => {
          this.onQuestionsUpdate(params.pages); // ToDo refactor
        }, 100);
      });
    }

    this.subscribe(ko.utils.unwrapObservable(this.activeQuestion));

    if (ko.isObservable(this.activeQuestion)) {
      this.activeQuestion.subscribe((v) => {
        const sp = new URLSearchParams(location.search.slice(1));
        if (sp.has("stop")) return;
        this.formatQuestions(params.questions());
        this.subscribe(v);
      });
    }

    this.clearLogicPenging = ko.observable(false);
    
    this.rootResortQuestions = params.rootResortQuestions ? params.rootResortQuestions.bind(window.vm) : null; // ToDo refactor
  }

  isBlinked(questionId) {
    return ko.computed(() => this.blinked().some((id) => id == questionId));
  }

  formatQuestions(questionsData) {
    let questions = formatQuestionsList(
      questionsData,
      ko.utils.unwrapObservable(this.activeQuestionId),
      ko.utils.unwrapObservable(this.activeQuestion),
      this,
    );

    this.groupQuestions(questions);

    if (questions.length) {
      questions[questions.length - 1].isLast(true);
    }
  }

  groupQuestions(questions) {
    this.startQuestions(questions.filter((q) => q.type() == "start"));
    let defaultQuestions = questions.filter(
      (q) => q.type() !== "start" && q.type() !== "end"
    );
    // defaultQuestions.forEach((q, i) => {
    //   q.index = ko.pureComputed(() => {
    //     if (q.type() !== "default") return 0;
    //     return (
    //       defaultQuestions.slice(0, i).filter((q) => q.type() == "default")
    //         .length + 1
    //     );
    //   });
    // });
    this.defaultQuestions(defaultQuestions);

    this.endQuestions(questions.filter((q) => q.type() == "end"));

    if (this.pagesMode()) {
      [
        ...this.startQuestions(),
        ...this.endQuestions(),
      ].forEach(el => {
        const page = this.getQuestionPage(el.id);
        if (!page || !page.questions || !page.questions().length) {
          return;
        }
        page.questions(page.questions().filter(question => question.id !== el.id));
      });
    }
  }

  subscribe(question) {
    if (!question) return;
  }

  handleInterBlockChange(event) {
    if (event.field == "screenType") {
      let type = "inter";

      let screenType = event.data.type;
      if (screenType == "start") type = "start";
      else if (screenType == "end") type = "end";
      else if (event.data.showNumber) type = "default";

      let questions = this.questions();

      let activeQuestionId = ko.utils.unwrapObservable(this.activeQuestionId);

      let question = questions.find((q) => q.id == activeQuestionId);
      question.type(type);

      this.groupQuestions(questions);
    }
  }

  checkDonors(event, ui) {
    let questionId = ui.item.data("id");
    let question = this.getQuestion(questionId);

    const children = [...event.target.children];
    const newPosition = children.findIndex(
      (el) => el === ui.placeholder.get(0)
    );

    const recipients = this.defaultQuestions().filter(
      (q) => q.donorId === question.id
    );

    if (recipients.length) {
      const recipientPositions = recipients.map((q) => {
        return children.findIndex((el) => el.dataset.id === q.id.toString());
      });

      if (recipientPositions.some((p) => p < newPosition)) {
        this.donorOrderError(true);
        return;
      }
    }

    const donorId = question.donorId;
    if (!donorId) return;

    const donorPosition = children.findIndex(
      (el) => el.dataset.id === donorId.toString()
    );

    if (newPosition < donorPosition) {
      this.donorOrderError(true);
    } else {
      this.donorOrderError(false);
    }
  }

  onStartSorting(event, ui) {
    console.log('debug: event', event)
    console.log('debug: ui', ui)
    if (this.isSaved()) {
      return ui;
    }

    let questionId = ui.item.data("id");

    setTimeout(() => {
      this.activateQuestion(questionId);
    }, 10);
  }

  onStopSorting(event, ui) {
    let questionId = ui.item.data("id");

    // Reset the modal shown flag when the drag operation ends
    this.isModalShown = false;
    
    this.donorOrderError(false);

    if (this.isDonorBeforeRecipients(questionId)) {
      const newOrder = this.defaultQuestions().map((v) => v.id);
      this.oldQuestionsOrder(newOrder);
      this.resort();
    } else {
      const oldOrder = this.oldQuestionsOrder();
      this.defaultQuestions.sort((a, b) => {
        const aIndex = oldOrder.indexOf(a.id);
        const bIndex = oldOrder.indexOf(b.id);
        return aIndex - bIndex;
      });
    }
  }

  getQuestion(qId) {
    let q = this.questions().find((q) => q.id == qId);
    return q;
  }

  isDonorBeforeRecipients(questionId) {
    const newOrder = this.defaultQuestions().map((v) => v.id);
    const questionIndex = newOrder.indexOf(questionId);

    const recipients = this.defaultQuestions().filter(
      (q) => q.donorId === questionId
    );

    if (recipients.length) {
      const recipientIndexes = recipients.map((q) => newOrder.indexOf(q.id));
      if (recipientIndexes.some((i) => i < questionIndex)) return false;
    }

    const question = this.getQuestion(questionId);
    const donorId = question.donorId;

    const donorIndex = newOrder.indexOf(donorId);
    return !donorId || questionIndex > donorIndex;
  }

  /** Сортировка списка */
  resort() {
    const ids = this.questions().map((q) => q.id);
    if (this.resorting()) return;
    this.resortQuestions(ids);
    this.rootResortQuestions(ids, true);

  }

  resortQuestions(ids) {
    this.resorting(true);

    this.startQuestions.sort((a, b) => {
      return ids.indexOf(a.id) - ids.indexOf(b.id);
    });
    this.endQuestions.sort((a, b) => {
      return ids.indexOf(a.id) - ids.indexOf(b.id);
    });

    this.defaultQuestions.sort((a, b) => {
      return ids.indexOf(a.id) - ids.indexOf(b.id);
    });
    this.reorderQuestions(this.defaultQuestions());

    this.questions().map((q, i) => q.index(i + 1));

    this.resorting(false);

    this.questions()[this.questions().length - 1].isLast(true);

    this.updateQuestionsPositions(ids);
  }

  getTooltipText(question) {
    if (!question) return 'Нет описания';
    const tooltip = question.tooltip ? ko.unwrap(question.tooltip) : null;

    if (!tooltip) return 'Нет описания';

    return `
    <div class="tooltip-question">
      ${tooltip.text ? `<div class="tooltip-question-text">${tooltip.text}</div>` : ''}
      <div class="tooltip-question-type">${tooltip.type}</div>
    </div>
  `;
  };

  /** Расчет нумерации вопросов (с учетом промежуточных экранов) */
  reorderQuestions(questions) {
    let counter = 1;

    questions.forEach((q, i) => {
      let order = q.isIntermediate && !q.showNumber ? null : counter++;

      q.order(order);
    });
  }

  /** Обновление общего порядка вопросов */
  updateQuestionsPositions(ids) {
    $.ajax({
      url: `/foquz/foquz-poll/update-questions-positions?pollId=${this.poll.id}`,
      method: "POST",
      data: {
        ids,
      },
    }).then(() => {
      // Send PREVIEW_REFRESH message to iframe
      let arIFrames = document.querySelectorAll(".active iframe");
      arIFrames.forEach(($iframe) => {
        if ($iframe.contentWindow) {
          $iframe.contentWindow.postMessage({ type: "PREVIEW_REFRESH" }, "*");
        }
      });
    });
  }

  moveQuestionToPage(q, pageId) {
    let fromPage = q.page;
    let question = this.questions().find((question) => question.id == q.id);

    if (fromPage !== pageId && question.hasConditions()) {
      question.conditions.removeAll();
    }

    let correctOrder = [];
    this.pages().forEach((page) => {
      page.questions().forEach((q) => {
        correctOrder.push(q.id);
      });
    });

    if (fromPage != pageId) {
      $.ajax({
        method: "POST",
        url: ApiUrl("poll-display-page/move-question", {
          questionId: q.id,
          pageId,
        }),
        success: (response) => {
          q.page = pageId;
          this.updatePages(response.pages, correctOrder);
          
          const ids = [
            ...this.startQuestions().map((q) => q.id),
            ...correctOrder,
            ...this.endQuestions().map((q) => q.id),
          ]
          this.resortQuestions(ids);
          this.rootResortQuestions(ids, true);
        },
      });
    } else {
      const ids = [
        ...this.startQuestions().map((q) => q.id),
        ...correctOrder,
        ...this.endQuestions().map((q) => q.id),
      ]
      this.resortQuestions(ids);
      this.rootResortQuestions(ids, true);
    }
  }

  onSortOver(targetPage, questionElement, mirror) {
    let questionId = questionElement.data("id");
    let currentPage = this.getQuestionPage(questionId);

    if (currentPage.id == targetPage.id) {
      mirror.removeClass("warning");
    } else {
      mirror.addClass("warning");
    }
  }

  getQuestionPage(qId) {
    return this.pages().find((p) => {
      return p.questions().some(({ id }) => id == qId);
    });
  }

  getQuestionPageIndex(qId) {
    return this.pages().findIndex((p) => {
      return p.questions().some(({ id }) => id == qId);
    });
  }

  updatePages(data, questionsOrder) {
    let pages = this.pages();
    let newPages = [];
    
    // Get the order of questions from defaultQuestions
    let order = questionsOrder;
    
    // Get the list of all question IDs
    let questionIds = this.questions().map((q) => q.id);

    let ids = pages.map((p) => p.id);
    let newIds = data.map((p) => p.id);
    let rIds = _.difference(ids, newIds); // removed

    this.pages.remove((p) => rIds.includes(p.id));

    data.forEach((pData) => {
      let page = pages.find((p) => p.id == pData.id);

      let pQuestions = pData.questions || [];

      // Filter questions to only include those that exist in the questionIds list
      pQuestions = pQuestions.filter((q) => {
        return questionIds.includes(parseInt(q.id));
      });
      
      // Sort questions based on their order in the defaultQuestions list
      pQuestions.sort((a, b) => {
        return order.indexOf(parseInt(a.id)) - order.indexOf(parseInt(b.id));
      });
      
      // Ensure each question has the page property set
      pQuestions = pQuestions.map(q => ({
        ...q,
        page: pData.id
      }));
      
      pData.questions = pQuestions;

      if (page) {
        const parentPage = (this.parentPages || []).find(p => p.id == page.id);
        if (parentPage) {
          parentPage.update(pData);
          parentPage.questions(pQuestions);
        }
        page.update(pData);
        page.questions(pQuestions);
        newPages.push(page);
      } else {
        newPages.push(new LogicPage(pData));
      }
    });

    this.pages(newPages);
  }

  initPages(data) {
    let order = this.defaultQuestions().map((q) => q.id);

    let questions = this.questions().map((q) => q.id);

    this.pages(
      data.map((p) => {
        p.questions = p.questions.filter((q) => {
          return questions.find((qId) => qId == q.id);
        });
        p.questions.sort((a, b) => {
          return order.indexOf(parseInt(a.id)) - order.indexOf(parseInt(b.id));
        });
        return new LogicPage(p);
      })
    );
  }

  onQuestionsUpdate(data) {
    ko.unwrap(data).forEach(p => {
      p.questions().forEach(q => {
        const question = this.questions().find(qs => qs.id === q.id);
        if (!question) {
          return;
        }
        const page = this.pages().find(pg => pg.id === q.page);
        if (!page) {
          return;
        }
        if (!page.questions().find(qs => qs.id === q.id)) {
          page.questions.push(q);
        }
      });
    })
  }

  sortableBeforeMove(arg) {
    if (this.hasLogic()) {
      arg.cancelDrop = true;
      this.confirm({
        title: "",
        text: 'Для опроса настроены логические условия/условия отображения вопросов. Для продолжения изменения порядка вопросов с помощью перетаскивания необходимо удалить логику.',
        cancel: "Отменить",
        confirm: "Удалить логику и продолжить",
        mode: "danger",
      }).then(() => {
        this.clearLogicPenging(true);
        clearLogic(this.poll.id);
        this.questions().forEach((q) => q.clearLogic());
        this.hasLogic(false); // ToDo refactor
        this.clearLogicPenging(false);
      });
    }
  }

  onClick(event, questionId) {
    // If the question is saved, just activate it
    if (this.isSaved()) {
      this.activateQuestion(questionId);
      return;
    }
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }

  hideAllTooltips() {
    setTimeout(()=>{
      document.querySelectorAll('.tooltip').forEach(el => {
        if (el) {
          el.remove();
        }
      });
    },1);
  }
}
