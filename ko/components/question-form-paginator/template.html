<div
  class="question-form-paginator__container"
  data-bind="
    css: {
      'question-form-paginator__container--groups': pagesMode
    },
    attr: {
      'data-active': activeQuestionId
    }
  "
>
  <!-- ko foreach: startQuestions -->
  <div
    class="question-form-paginator__item"
    data-bind="
      attr: {
        'data-id': id,
        'data-point': !!pointId,
        'data-toggle': 'tooltip',
        'data-placement': 'bottom',
        'data-html': 'true',
        'data-original-title': $component.getTooltipText($component.getQuestion(id)),
      },
      css: {
        'active': id == $component.activeQuestionId(),
        'blinked': $component.isBlinked(id)
      },
      click: function(e) {
        $component.hideAllTooltips();
        $component.onClick(e, id)
      },
      event: {
         mouseleave: function(){
             $component.hideAllTooltips();
          },
        mousedown: function(data, event) {
          console.log('DRAG START');
          if (!$component.isSaved()) {
            $component.onStartSorting(event, { item: { data: function() { return id; } } });
          }
          return true;
        }
      },
    "
  >
    <!-- ko if: pointId -->
    <svg-icon params="name: 'aim', " class="svg-icon--sm mr-1"></svg-icon>
    <!-- /ko -->
    start
  </div>
  <!-- /ko -->

  <!-- ko if: pagesMode -->
  <!-- ko foreach: {data: pages, as: 'page', noChildContext: true} -->
  <div
    class="question-form-paginator__group"
    data-bind="
      css: {
        'question-form-paginator__group--empty': page.questions().length == 0
      },
      tooltip,
      tooltipText: page.tooltip
    "
  >
    <div class="question-form-paginator__group-meta">
      <div data-bind="text: $index()  + 1" class="font-weight-700"></div>
      <!-- ko if: randomOrder -->
      <svg-icon
        params="name: 'shuffle', width: 12, height: 10"
        data-bind="tooltip, tooltipText: $component.translator.t('На странице включен случайный порядок вопросов')"
        title=""
      ></svg-icon>
      <!-- /ko -->
    </div>
    <div class="question-form-paginator__group-items">
      <!-- ko template: {
        name: 'question-form-paginator-group-paged',
        data: {
          page: page,
          isSortableEnabled,
        }
      } -->
      <!-- /ko -->
    </div>
  </div>
  <!-- /ko -->
  <!-- /ko -->

  <!-- ko ifnot: pagesMode -->
  <!-- ko template: {
    name: 'question-form-paginator-group',
    data: {
      questions: $component.defaultQuestions,
      isSortableEnabled,
    }
  } -->
  <!-- /ko -->
  <!-- /ko -->

  <div
    class="d-inline"
    data-bind="
      foquzSortable: {
        data: endQuestions,
        as: 'question',
        connectClass: false,
        template: 'question-form-paginator-end-item',
        beforeMove: function(a) {
          $component.sortableBeforeMove(a);
        },
        afterMove: function() {
          $component.resort();
        },
        isEnabled: $component.isSaved,
        options: {
          'start': function(event, ui) {
            $component.onStartSorting(event, ui);
          },
          forceHelperSize: true,
          forcePlaceholderSize: true,
          scrollSensitivity: 10,
          tolerance: 'pointer',
        },
      },
    "
  >
  </div>

  <!-- ko using: $parent -->
  <!-- ko template: { nodes: $componentTemplateNodes } -->
  <!-- /ko -->
  <!-- /ko -->

  <!-- ko if: randomOrder -->
  <div
    class="question-form-paginator__random"
    data-bind="tooltip, tooltipText: $component.translator.t('Включен случайный порядок страниц')"
  >
    <svg-icon
      params="name: 'shuffle', width: 12, height: 10"
      class="f-color-light"
    ></svg-icon>
  </div>
  <!-- /ko -->
</div>

<template id="question-form-paginator-group">
  <div
    class="d-inline"
    data-bind="
      foquzSortable: {
        data: questions,
        as: 'question',
        connectClass: false,
        template: 'question-form-paginator-item',
        beforeMove: function(a) {
          $component.sortableBeforeMove(a);
        },
        options: {
          'start': function(event, ui) {
            $component.onStartSorting(event, ui);
          },
          'change': function(event, ui) {
            $component.checkDonors(event, ui);
          },
          'stop': function(event, ui) {
            $component.onStopSorting(event, ui);
          },
          forceHelperSize: true,
          forcePlaceholderSize: true,
          scrollSensitivity: 10,
          tolerance: 'pointer',
        },
        isEnabled: $component.isSaved,
      },
    "
  ></div>
</template>

<template id="question-form-paginator-group-paged">
  <div
    class="d-inline question-page"
    data-bind="
      foquzSortable: {
        data: page.questions,
        as: 'question',
        connectClass: 'question-page',
        beforeMove: function(a) {
          $component.sortableBeforeMove(a);
        },
        afterMove: function(q, e) {
          $component.moveQuestionToPage(q.item, page.id);
        },
        options: {
          'start': function(event, ui) {
            $component.onStartSorting(event, ui);
          },
          forceHelperSize: true,
          forcePlaceholderSize: true,
          scrollSensitivity: 10,
          tolerance: 'pointer',
        },
        isEnabled: $component.isSaved,
      },
      event: {
        'sortover': function(page, event, ui) {
          $component.onSortOver(page, ui.item, ui.helper)
          return true;
        },
      },
    "
  >
    <div
      class="question-form-paginator__item item-default position-relative"
      data-bind="
        visible: $component.getQuestion(question.id),
        css: {
          'active': question.id == $component.activeQuestionId(),
          'blinked': $component.isBlinked(question.id),
          'item-inter': $component.getQuestion(question.id) && $component.getQuestion(question.id).type() === 'inter',
        },
        click: function(e) {
          $component.onClick(e, question.id)
          $component.hideAllTooltips();
        },
        event: {
           mouseleave: function(){
            $component.hideAllTooltips();
          },
          mousedown: function(data, event) {
            console.log('DRAG START');
            if (!$component.isSaved()) {
              $component.onStartSorting(event, { item: { data: function() { return question.id; } } });
            }
            return true;
          }
        },
        attr: {
          'data-type': $component.getQuestion(question.id) && $component.getQuestion(question.id).type(),
          'data-id': question.id,
          'data-point': $component.getQuestion(question.id) && !!$component.getQuestion(question.id).pointId,
          'data-toggle': 'tooltip',
          'data-placement': 'bottom',
          'data-html': 'true',
          'data-original-title': $component.getTooltipText($component.getQuestion(question.id)),
        },
      "
    >
      <!-- ko with: $component.getQuestion(question.id) -->
        <!-- ko if: type() === 'inter' -->
            <!-- ko let: { interBlock: typeof intermediateBlock === 'function' ? intermediateBlock() : intermediateBlock } -->
                <!-- ko if: interBlock && !interBlock.show_question_number -->
                    <span class="asterisk">*</span>
                <!-- /ko -->
                <!-- ko ifnot: interBlock && !interBlock.show_question_number -->
                    <!-- ko if: $parent.isAuto -->
                        <!-- ko if: isSystem -->
                        <svg-icon params="name: 'star'" class="svg-icon--xs mr-1" style="color: #9bb0fb"></svg-icon>
                        <!-- /ko -->
                        <!-- ko text: name --><!-- /ko -->
                    <!-- /ko -->
                    <!-- ko ifnot: $parent.isAuto -->
                        <span data-index></span>
                    <!-- /ko -->
                <!-- /ko -->
            <!-- /ko -->
        <!-- /ko -->
        
        <!-- ko ifnot: type() === 'inter' -->
            <!-- ko if: $parent.isAuto -->
                <!-- ko if: isSystem -->
                <svg-icon params="name: 'star'" class="svg-icon--xs mr-1" style="color: #9bb0fb"></svg-icon>
                <!-- /ko -->
                <!-- ko text: name --><!-- /ko -->
            <!-- /ko -->
            <!-- ko ifnot: $parent.isAuto -->
                <span data-index></span>
            <!-- /ko -->
        <!-- /ko -->
      <!-- /ko -->
    </div>
  </div>
</template>

<template id="question-form-paginator-item">
  <div
    class="question-form-paginator__item item-default position-relative"
    data-bind="
      css: {
        'active': question.id == $component.activeQuestionId(),
        'blinked': $component.isBlinked(question.id),
        'item-inter': question.type() === 'inter',
      },
      click: function(e) {
        $component.onClick(e, question.id)
        $component.hideAllTooltips();
      },
      event: {
          mouseleave: function(){
             $component.hideAllTooltips();
          },
        mousedown: function(data, event) {
          console.log('DRAG START');
          if (!$component.isSaved()) {
            $component.onStartSorting(event, { item: { data: function() { return question.id; } } });
          }
          return true;
        }
      },
      attr: {
        'data-type': question.type(),
        'data-id': question.id,
        'data-point': !!question.pointId,
        'data-toggle': 'tooltip',
        'data-placement': 'bottom',
        'data-html': 'true',
         'data-original-title': $component.getTooltipText($component.getQuestion(question.id)),
      },
    "
  >
    <!-- ko if: question.pointId -->
    <svg-icon params="name: 'aim', " class="svg-icon--sm mr-1"></svg-icon>
    <!-- /ko -->

    <!-- ko if: question.type() === 'inter' --><span class="asterisk">*</span>
    <!-- /ko -->

    <!-- ko ifnot: question.type() === 'inter' -->

    <!-- ko if: $component.isAuto -->
    <!-- ko if: question.isSystem -->
    <svg-icon
      params="name: 'star'"
      class="svg-icon--xs mr-1"
      style="color: #9bb0fb"
    ></svg-icon>
    <!-- /ko -->
    <!-- ko text: question.name -->
    <!-- /ko -->
    <!-- /ko -->

    <!-- ko ifnot: $component.isAuto -->
    <span data-index></span>
    <!-- /ko -->

    <!-- /ko -->
  </div>
</template>

<template id="question-form-paginator-end-item">
  <div
    class="question-form-paginator__item item-end"
    data-bind="
      attr: {
        'data-id': id,
        'data-point': !!pointId,
        'data-toggle': 'tooltip',
        'data-placement': 'bottom',
        'data-html': 'true',
        'data-original-title': $component.getTooltipText($component.getQuestion(id)),
      },
      css: {
        'active': id == $component.activeQuestionId(),
        'blinked': $component.isBlinked(id)
      },
      click: function(e) {
        $component.hideAllTooltips();
        $component.onClick(e, id)
      },
      event: {
          mouseleave: function(){
             $component.hideAllTooltips();
          },
        mousedown: function(data, event) {
          console.log('DRAG START');
          if (!$component.isSaved()) {
            $component.onStartSorting(event, { item: { data: function() { return id; } } });
          }
          return true;
        }
      },
    "
  >
    <!-- ko if: pointId -->
    <svg-icon params="name: 'aim', " class="svg-icon--sm mr-1"></svg-icon>
    <!-- /ko -->
    end
    <!-- ko if: !$parent.hasEndLogic() && $index() == 0 && $parent.endQuestions().length > 1 -->
    <span class="f-exclamation-mark"></span>
    <!-- /ko -->
  </div>
</template>
