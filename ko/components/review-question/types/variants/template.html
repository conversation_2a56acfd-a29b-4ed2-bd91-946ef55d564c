<!-- ko using: question -->
<div
  class="review-question-view"
  data-bind="log, css: 'review-questions-view--type_' + type + ' '"
>
  <div class="review-question-view__name">
    <!-- ko if: typeof position !== "undefined" -->
    <span class="position" data-bind="text: position + '.'"></span>
    <!-- /ko -->
    <span>
      <!-- ko text: displayName -->
      <!-- /ko -->
      <!-- ko if: isRequired -->
      <span class="question-name__required-mark">*</span>
      <!-- /ko -->
    </span>
  </div>

  <!-- ko if: subDescription -->
  <div
    class="review-question-view__subdescription service-text"
    data-bind="text: subDescription"
  ></div>
  <!-- /ko -->

  <!-- ko if: ($component.review.withPoints() || $component.question.without_points) && correctVariantsText -->
  <div class="review-question-view__meta">
    <!-- ko if: hasAnswer && !$component.question.without_points -->
    <div class="mr-4 mb-2">
      <span
        class="f-color-service"
        data-bind="text: _t('Баллов за ответ') + ':'"
      ></span>
      <span
        data-bind="html: _t('main', '{num1} из {num2}', {
        num1: '<span class=\'bold\'>' + (answerPoints || 0) + '</span>',
        num2: '<span class=\'bold\'>' + (maxPoints || 0) + '</span>',
      }), css: {'empty': answerPoints == 0 || !answerPoints}"
      ></span>
      <!-- ko if: answerPoints == 0 || !answerPoints -->
      <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M0 7C0 8.933 0.783502 10.683 2.05025 11.9497C3.317 13.2165 5.067 14 7 14C10.866 14 14 10.866 14 7C14 5.067 13.2165 3.317 11.9497 2.05025C10.683 0.783502 8.933 0 7 0C3.13401 0 0 3.13401 0 7Z" fill="#FF0200"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M3 7C3 6.44772 3.44772 6 4 6H10C10.5523 6 11 6.44772 11 7C11 7.55228 10.5523 8 10 8H4C3.44772 8 3 7.55228 3 7Z" fill="white"/>
      </svg>
      <!-- /ko -->
    </div>
    <!-- /ko -->

    <!-- ko if: hasAnswer && $component.question.without_points -->
    <div class="mr-4 mb-2">
      <span
        class="f-color-service"
        data-bind="text: _t('Баллов за ответ') + ':'"
      ></span>
      <span
        data-bind="html: _t('main', '<span>' + 'выбранные варианты не участвуют в подсчёте' + '</span>')"
      ></span>
    </div>
    <!-- /ko -->

    <div class="mb-2">
      <span
        class="f-color-service"
        data-bind="text: _t('Правильный ответ') + ':'"
      ></span>
      <span data-bind="text: correctVariantsText"></span>
    </div>
  </div>
  <!-- /ko -->

  <!-- ko if: text -->
  <div class="review-question-view__text" data-bind="text: text"></div>
  <!-- /ko -->

  <div class="review-question-view__body">
    <div class="review-question-view__answer-section">
      <!-- ko if: hasAnswer -->
      <!-- ko if: gallery.length -->
      <div
        class="f-color-text mb-4 d-flex align-items-center cursor-pointer"
        data-bind="fancyboxGalleryItem: {
        gallery: fancyGallery,
        index: 0,
        noCursor: true,
        }"
      >
        <span class="f-icon f-icon--picture mr-2">
          <svg>
            <use href="#picture-icon"></use>
          </svg>
        </span>
        <span
          class="f-color-primary f-fs-3 bold"
          data-bind="text: _t('answers', 'Галерея')"
        ></span>
      </div>
      <!-- /ko -->

      <!-- ko if: skipped -->
      <div
        class="review-question-stars__skipped"
        data-bind="text: _t('answers', 'Респондент отказался от ответа')"
      ></div>
      <!-- /ko -->

      <!-- ko ifnot: skipped -->

      <div class="review-question-view-variants">
        <!-- ko foreach: { data: variants, as: 'variant' } -->
        <div
          class="review-question-view-variant align-items-center"
          data-bind="
            css: {
              'review-question-view-variant--selected': $parent.answer.includes($data.id),
              'review-question-view-variant--deleted': variant.deleted,
            },
            visible: !window.IS_EXTERNAL || $parent.answer.includes($data.id),
          "
        >
          <!-- ko if: variant.file_id -->
          <a data-bind="attr: {href: window.location.origin + variant.file_url}" target="_blank" class="file-loader-preview_review-print-link">
            <img
              data-bind="attr: {
                          src: variant.preview_url
                      }"
              alt=""
            />
          </a>
          <file-loader-preview class="file-loader-preview file-loader-preview_review mr-15p" data-bind="click: function (_, event) {
            event.stopPropagation();
          }," params="loading: false, disabled: true, file: variant.file_url, preview: variant.preview_url,
          onRemove: function() { 
              variant.file(null)
              variant.value('')
          }">

        </file-loader-preview>
          <!-- /ko -->
          <!-- ko text: variant.text -->
          <!-- /ko -->

          <!-- ko if: variant.deleted -->
          <span class="removed">(Удален)</span>
          <!-- /ko -->

          <!-- ko if: $parent.correctVariantsIds.includes(variant.id) -->
          <svg-icon
            params="name: 'check'"
            class="ml-2 svg-icon--sm f-color-text"
          ></svg-icon>
          <!-- /ko -->
        </div>
        <!-- /ko -->

        <!-- ko if: isSelfAnswer -->
        <div
          class="review-question-view-variant review-question-view-variant--self-variant d-block"
          data-bind="css: {'review-question-view-variant--selected': selfVariant}"
        >
          <!-- ko text: selfVariantLabel || 'Свой вариант' -->
          <!-- /ko -->
          <!-- ko if: selfVariant -->
          <div
            class="review-question-view-variant__custom-variant"
            data-bind="text: selfVariant"
          ></div>
          <!-- /ko -->
        </div>
        <!-- /ko -->
        

      </div>
      <!-- /ko -->
      <!-- /ko -->
      <!-- ko ifnot: hasAnswer -->
      <div class="review-question-view-block skip-block">
        <div
          class="review-question-view-block__text review-question-view-block__text--skipped"
          data-bind="
            text: $component.isHidden ?
              'Вопрос для респондента не отображался' :
              'Вопрос пропущен',
          "
        ></div>
      </div>
      <!-- /ko -->
    </div>
    <!-- .review-question-view__answer-section -->
  </div>
  <!-- .review-question-view__body -->
  <!-- Комментарий -->
  <!-- ko template: { name: 'review-question-view-comment-template', data: { text: comment } } -->
  <!-- /ko -->
  <!-- /Комментарий -->
</div>
<!-- .review-question-view -->
<!-- /ko -->
