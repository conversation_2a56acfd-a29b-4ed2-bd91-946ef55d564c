<!-- ko descendantsComplete: $component.onInit.bind($component )-->
<div class="client-fields__line client-fields__line--clone"
     data-bind="element: line">

     <!-- ko if: values.gender -->
     <div class="client-fields-item"
          data-item="gender">
          <!-- ko if: values.gender == 1 -->
          <svg-icon class="client-fields-item__icon"
                    params="name: 'gender-male'"></svg-icon>
          <!-- /ko -->
          <!-- ko if: values.gender == 2 -->
          <svg-icon class="client-fields-item__icon"
                    params="name: 'gender-female'"></svg-icon>
          <!-- /ko -->
     </div>
     <!-- /ko -->

     <!-- ko if: values.birthday -->
     <div class="client-fields-item"
          data-item="birthday">
          <svg-icon class="client-fields-item__icon"
                    params="name: 'client-birthday'"></svg-icon>
          <span class="client-fields-item__data"
                data-bind="text: values.birthday"></span>
     </div>
     <!-- /ko -->

     <!-- ko if: values.filials -->
     <div class="client-fields-item"
          data-item="filials">
          <svg-icon class="client-fields-item__icon"
                    params="name: 'client-filials'"></svg-icon>
          <span class="client-fields-item__data"
                data-bind="html: values.filials.join(', ')"></span>
     </div>
     <!-- /ko -->

     <!-- ko if: values.lastOrderDate -->
     <div class="client-fields-item"
          data-item="lastOrderDate">
          <svg-icon class="client-fields-item__icon"
                    params="name: 'client-last-order'"></svg-icon>
          <span class="client-fields-item__data"
                data-bind="text: values.lastOrderDate"></span>
     </div>
     <!-- /ko -->

     <!-- ko if: values.ltv -->
     <div class="client-fields-item"
          data-item="ltv">
          <svg-icon class="client-fields-item__icon"
                    params="name: 'client-ltv'"></svg-icon>
          <span class="client-fields-item__data"
                data-bind="text: values.ltv"></span>
     </div>
     <!-- /ko -->

     <!-- ko if: values.addedAt -->
     <div class="client-fields-item"
          data-item="addedAt">
          <svg-icon class="client-fields-item__icon"
                    params="name: 'client-added'"></svg-icon>
          <span class="client-fields-item__data"
                data-bind="text: values.addedAt"></span>
     </div>
     <!-- /ko -->

     <!-- ko if: values.updatedAt -->
     <div class="client-fields-item"
          data-item="updatedAt">
          <svg-icon class="client-fields-item__icon"
                    params="name: 'client-updated'"></svg-icon>
          <span class="client-fields-item__data"
                data-bind="text: values.updatedAt"></span>
     </div>
     <!-- /ko -->

     <!-- ko if: values.tags -->
     <div class="client-fields-item"
          data-item="tags">
          <svg-icon class="client-fields-item__icon"
                    params="name: 'client-tags'"></svg-icon>
          <span class="client-fields-item__data"
                data-bind="text: values.tags.join(', ')"></span>
     </div>
     <!-- /ko -->

     <!-- ko if: values.additional -->
     <div class="client-fields-item"
          data-item="additional">
          <svg-icon class="client-fields-item__icon"
                    params="name: 'client-fields'"></svg-icon>
          <span class="client-fields-item__data">
               <!-- ko foreach: values.additional -->
               <span class="client-field">
                    <span class="client-field__name"
                          data-bind="text: $parent.getFieldName($data.id) + ':'"></span>
                    <span class="client-field__value"
                          data-bind="text: text"></span>
               </span>
               <!-- /ko -->
          </span>
     </div>
     <!-- /ko -->


</div>

<div class="client-fields__line">

     <!-- ko if: values.gender -->
     <!-- ko if: fields.gender -->
     <div class="client-fields-item">
          <!-- ko if: values.gender == 1 -->
          <svg-icon class="client-fields-item__icon"
                    params="name: 'gender-male'"
                    data-bind="tooltip, tooltipText: _t('Мужской пол')"></svg-icon>
          <!-- /ko -->
          <!-- ko if: values.gender == 2 -->
          <svg-icon class="client-fields-item__icon"
                    params="name: 'gender-female'"
                    data-bind="tooltip, tooltipText: _t('Женский пол')"></svg-icon>
          <!-- /ko -->
     </div>
     <!-- /ko -->
     <!-- /ko -->

     <!-- ko if: values.birthday -->
     <!-- ko if: fields.birthday -->
     <div class="client-fields-item"
          data-bind="tooltip, tooltipText: _t('Дата рождения') + ': ' + values.birthday">
          <svg-icon class="client-fields-item__icon"
                    params="name: 'client-birthday'"></svg-icon>
          <span class="client-fields-item__data"
                data-bind="text: values.birthday"></span>
     </div>
     <!-- /ko -->
     <!-- /ko -->

     <!-- ko if: values.filials -->
     <!-- ko if: fields.filials -->
     <div class="client-fields-item"
          data-bind="tooltip, tooltipText: _t('Филиалы контакта') + ': ' + values.filials.join(', ')">
          <svg-icon class="client-fields-item__icon"
                    params="name: 'client-filials'"></svg-icon>
          <span class="client-fields-item__data"
                data-bind="html: values.filials.join(', ')"></span>
     </div>
     <!-- /ko -->
     <!-- /ko -->

     <!-- ko if: values.lastOrderDate -->
     <!-- ko if: fields.lastOrderDate -->
     <div class="client-fields-item"
          data-bind="tooltip, tooltipText: _t('Дата последнего заказа') + ': ' + values.lastOrderDate">
          <svg-icon class="client-fields-item__icon"
                    params="name: 'client-last-order'"></svg-icon>
          <span class="client-fields-item__data"
                data-bind="text: values.lastOrderDate"></span>
     </div>
     <!-- /ko -->
     <!-- /ko -->

     <!-- ko if: values.ltv -->
     <!-- ko if: fields.ltv -->
     <div class="client-fields-item"
          data-bind="tooltip, tooltipText: _t('LTV') + ': ' + values.ltv + ' ₽'">
          <svg-icon class="client-fields-item__icon"
                    params="name: 'client-ltv'"></svg-icon>
          <span class="client-fields-item__data"
                data-bind="text: values.ltv + ' ₽'"></span>
     </div>
     <!-- /ko -->
     <!-- /ko -->

     <!-- ko if: values.addedAt -->
     <!-- ko if: fields.addedAt -->
     <div class="client-fields-item"
          data-bind="tooltip, tooltipText: _t('Дата добавления контакта') + ': ' + values.addedAt">
          <svg-icon class="client-fields-item__icon"
                    params="name: 'client-added'"></svg-icon>
          <span class="client-fields-item__data"
                data-bind="text: values.addedAt"></span>
     </div>
     <!-- /ko -->
     <!-- /ko -->

     <!-- ko if: values.updatedAt -->
     <!-- ko if: fields.updatedAt -->
     <div class="client-fields-item"
          data-bind="tooltip, tooltipText: _t('Дата обновления контакта') + ': ' + values.updatedAt">
          <svg-icon class="client-fields-item__icon"
                    params="name: 'client-updated'"></svg-icon>
          <span class="client-fields-item__data"
                data-bind="text: values.updatedAt"></span>
     </div>
     <!-- /ko -->
     <!-- /ko -->

     <!-- ko if: values.tags -->
     <!-- ko if: fields.tags -->
     <div class="client-fields-item"
          data-bind="tooltip, tooltipText: _t('Теги контакта')">
          <svg-icon class="client-fields-item__icon"
                    params="name: 'client-tags'"></svg-icon>
          <span class="client-fields-item__data"
                data-bind="text: values.tags.join(', ')"></span>
     </div>
     <!-- /ko -->
     <!-- /ko -->

     <!-- ko if: values.additional -->
     <!-- ko if: fields.additional -->
     <div class="client-fields-item">
          <svg-icon class="client-fields-item__icon"
                    params="name: 'client-fields'"></svg-icon>
          <span class="client-fields-item__data">
               <!-- ko foreach: values.additional -->
               <span class="client-field">
                    <span class="client-field__name"
                          data-bind="text: $parent.getFieldName($data.id) + ':'"></span>
                    <span class="client-field__value"
                          data-bind="text: text"></span>
               </span>
               <!-- /ko -->
          </span>
     </div>
     <!-- /ko -->
     <!-- /ko -->

     <!-- ko if: hasHidden -->
     <div data-bind="dropdown, dropdownClass: 'client-fields-dropdown', dropdownMode: 'modal'">
          <button class="button-ghost"
                  data-dropdown-target>
               <svg-icon params="name: 'ellipsis'"
                         class="f-color-service"></svg-icon>
          </button>

          <template>
               <div class="client-fields-dropdown__wrapper"
                    data-tooltip-container>
                    <div class="client-fields-dropdown__scroll"
                         data-bind="nativeScrollbar">
                         <!-- ko if: values.gender -->
                         <!-- ko ifnot: fields.gender -->
                         <div class="client-fields-item">
                              <!-- ko if: values.gender == 1 -->
                              <svg-icon class="client-fields-item__icon"
                                        params="name: 'gender-male'"
                                        data-bind="tooltip, tooltipText: _t('Мужской пол')"></svg-icon>
                              <!-- /ko -->
                              <!-- ko ifnot: values.gender == 2 -->
                              <svg-icon class="client-fields-item__icon"
                                        params="name: 'gender-female'"
                                        data-bind="tooltip, tooltipText: _t('Женский пол')"></svg-icon>
                              <!-- /ko -->
                         </div>
                         <!-- /ko -->
                         <!-- /ko -->
                         <!-- ko if: values.birthday -->
                         <!-- ko ifnot: fields.birthday -->
                         <div class="client-fields-item">
                              <div class="client-fields-item__icon"
                                   data-bind="tooltip, tooltipText: 'Дата рождения: ' + values.birthday"
                                   data-placemen>
                                   <svg-icon params="name: 'client-birthday'"></svg-icon>
                              </div>
                              <div class="client-fields-item__data"
                                   data-bind="text: values.birthday"></div>
                         </div>
                         <!-- /ko -->
                         <!-- /ko -->
                         <!-- ko if: values.filials -->
                         <!-- ko ifnot: fields.filials -->
                         <div class="client-fields-item">
                              <div class="client-fields-item__icon"
                                   data-bind="tooltip, tooltipText: _t('Филиалы контакта') + ': ' + values.filials.join(', ')">
                                   <svg-icon params="name: 'client-filials'"></svg-icon>
                              </div>
                              <div class="client-fields-item__data">
                                   <!-- ko foreach: values.filials -->
                                   <div class="client-filial"
                                        data-bind="html: $data"></div>
                                   <!-- /ko -->
                              </div>
                         </div>
                         <!-- /ko -->
                         <!-- /ko -->
                         <!-- ko if: values.lastOrderDate -->
                         <!-- ko ifnot: fields.lastOrderDate -->
                         <div class="client-fields-item">
                              <div class="client-fields-item__icon"
                                   data-bind="tooltip, tooltipText: _t('Дата последнего заказа') + ': ' + values.lastOrderDate">
                                   <svg-icon params="name: 'client-last-order'"></svg-icon>
                              </div>
                              <div class="client-fields-item__data"
                                   data-bind="text: values.lastOrderDate"></div>
                         </div>
                         <!-- /ko -->
                         <!-- /ko -->
                         <!-- ko if: values.ltv -->
                         <!-- ko ifnot: fields.ltv -->
                         <div class="client-fields-item">
                              <div class="client-fields-item__icon"
                                   data-bind="tooltip, tooltipText: _t('LTV') + ': ' + values.ltv + ' ₽'">
                                   <svg-icon params="name: 'client-ltv'"></svg-icon>
                              </div>
                              <div class="client-fields-item__data"
                                   data-bind="text: values.ltv + ' ₽'"></div>
                         </div>
                         <!-- /ko -->
                         <!-- /ko -->
                         <!-- ko if: values.addedAt -->
                         <!-- ko ifnot: fields.addedAt -->
                         <div class="client-fields-item">
                              <div class="client-fields-item__icon"
                                   data-bind="tooltip, tooltipText: _t('Дата добавления контакта') + ': ' + values.addedAt">
                                   <svg-icon params="name: 'client-added'"></svg-icon>
                              </div>
                              <div class="client-fields-item__data"
                                   data-bind="text: values.addedAt"></div>
                         </div>
                         <!-- /ko -->
                         <!-- /ko -->
                         <!-- ko if: values.updatedAt -->
                         <!-- ko ifnot: fields.updatedAt -->
                         <div class="client-fields-item">
                              <div class="client-fields-item__icon"
                                   data-bind="tooltip, tooltipText: _t('Дата обновления контакта') + ': ' + values.updatedAt">
                                   <svg-icon params="name: 'client-updated'"></svg-icon>
                              </div>
                              <div class="client-fields-item__data"
                                   data-bind="text: values.updatedAt"></div>
                         </div>
                         <!-- /ko -->
                         <!-- /ko -->
                         <!-- ko if: values.tags -->
                         <!-- ko ifnot: fields.tags -->
                         <div class="client-fields-item">
                              <div class="client-fields-item__icon "
                                   data-bind="tooltip, tooltipText: _t('Теги контакта')">
                                   <svg-icon params="name: 'client-tags'"></svg-icon>

                              </div>
                              <div class="client-fields-item__data">
                                   <!-- ko foreach: values.tags -->
                                   <div class="client-tag"
                                        data-bind="text: $data"></div>
                                   <!-- /ko -->
                              </div>
                         </div>
                         <!-- /ko -->
                         <!-- /ko -->
                         <!-- ko if: values.additional -->
                         <!-- ko ifnot: fields.additional -->
                         <div class="client-fields-item">
                              <div class="client-fields-item__icon">
                                   <svg-icon params="name: 'client-fields'"></svg-icon>
                              </div>
                              <div class="client-fields-item__data">
                                   <!-- ko foreach: review.clientFields -->
                                   <div class="client-field">
                                        <div class="client-field__name"
                                             data-bind="text: $parent.getFieldName($data.id)"></div>
                                        <div class="client-field__value"
                                             data-bind="text: text"></div>
                                   </div>
                                   <!-- /ko -->
                              </div>
                         </div>
                         <!-- /ko -->
                         <!-- /ko -->
                         <!-- ko if: values.api -->
                         <!-- ko ifnot: fields.api -->
                         <div class="client-fields-item">
                              <svg-icon class="client-fields-item__icon"
                                        params="name: 'signs'"></svg-icon>
                              <div class="client-fields-item__data">
                                   <!-- ko foreach: values.api -->
                                   <div class="client-field">
                                        <div class="client-field__name"
                                             data-bind="text: id"></div>
                                        <div class="client-field__value"
                                             data-bind="text: text"></div>
                                   </div>
                                   <!-- /ko -->
                              </div>
                         </div>
                         <!-- /ko -->
                         <!-- /ko -->
                    </div>

                    <div class="text-center flex-shrink-0"><button data-dropdown-close
                                 class="f-btn f-btn-link"
                                 data-bind="text: _t('Закрыть')"></button></div>
               </div>
          </template>
     </div>
     <!-- /ko -->


</div>
<!-- /ko -->
