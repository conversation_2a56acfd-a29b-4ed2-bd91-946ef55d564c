import * as questionTypes from "Data/question-types";

import rateFormatter from "./rate";
import variantsFormatter from "./variants";
import textFormatter from "./text";
import dateFormatter from "./date";
import addressFormatter from "./address";
import fileFormatter from "./file";
import quizFormatter from "./quiz";
import priorityFormatter from "./priority";
import mediaVariantsFormatter from "./media-variants";
import galleryFormatter from "./gallery";
import smileFormatter from "./smile";
import npsFormatter from "./nps";
import matrixFormatter from "./matrix";
import matrix3DFormatter from "./matrix-3d";
import diffFormatter from "./diff";
import starsFormatter from "./stars";
import starVariantsFormatter from "./star-variants";
import ratingFormatter from "./rating";
import filialsFormatter from "./filials";
import classifierFormatter from "./classifier";
import interFormatter from "./inter";
import scaleFormatter from "./scale";
import distributionScaleFormatter from "./distribution-scale";
import cardSortingFormatter from "./card-sorting";
import firstClickTestFormatter from "./first-click-test";

export const formatters = {
  [questionTypes.RATE_QUESTION]: rateFormatter,
  [questionTypes.VARIANTS_QUESTION]: variantsFormatter,
  [questionTypes.TEXT_QUESTION]: textFormatter,
  [questionTypes.DATE_QUESTION]: dateFormatter,
  [questionTypes.ADDRESS_QUESTION]: addressFormatter,
  [questionTypes.FILE_QUESTION]: fileFormatter,
  [questionTypes.QUIZ_QUESTION]: quizFormatter,
  [questionTypes.PRIORITY_QUESTION]: priorityFormatter,
  [questionTypes.MEDIA_VARIANTS_QUESTION]: mediaVariantsFormatter,
  [questionTypes.GALLERY_QUESTION]: galleryFormatter,
  [questionTypes.SMILE_QUESTION]: smileFormatter,
  [questionTypes.NPS_QUESTION]: npsFormatter,
  [questionTypes.MATRIX_QUESTION]: matrixFormatter,
  [questionTypes.MATRIX_3D_QUESTION]: matrix3DFormatter,
  [questionTypes.DIFF_QUESTION]: diffFormatter,
  [questionTypes.STARS_QUESTION]: starsFormatter,
  [questionTypes.INTER_BLOCK]: interFormatter,
  [questionTypes.STAR_VARIANTS_QUESTION]: starVariantsFormatter,
  [questionTypes.FILIALS_QUESTION]: filialsFormatter,
  [questionTypes.CLASSIFIER_QUESTION]: classifierFormatter,
  [questionTypes.RATING_QUESTION]: ratingFormatter,
  [questionTypes.SCALE_QUESTION]: scaleFormatter,
  [questionTypes.DISTRIBUTION_SCALE_QUESTION]: distributionScaleFormatter,
  [questionTypes.CARD_SORTING_QUESTION]: cardSortingFormatter,
  [questionTypes.FIRST_CLICK_TEST]: firstClickTestFormatter,
};
