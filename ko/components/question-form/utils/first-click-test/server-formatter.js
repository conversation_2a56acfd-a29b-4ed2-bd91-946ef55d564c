export default function (data) {
  let question = {};

  // Gallery data (inherited from GalleryQuestion)
  question.enableGallery = data.gallery && data.gallery.length > 0 ? 1 : 0;
  question.gallery = data.gallery
    .filter((v) => v.mediaId)
    .map((v) => {
      return {
        id: v.mediaId,
        description: v.description || "",
      };
    });
  question.files = data.gallery.filter((v) => v.mediaId).map((v) => v.mediaId);

  // Click areas as JSON string
  question.click_areas = JSON.stringify(data.clickAreas || []);

  question.firstClick = {};

  // First Click Test settings
  question.firstClick.mobile_view = data.mobileDisplay === "height" ? "1" : "0";
  question.firstClick.min_click = parseInt(data.minClicks) || 1;
  question.firstClick.max_click = data.maxClicks ? parseInt(data.maxClicks) : '';
  question.firstClick.show_time = data.displayTime ? parseInt(data.displayTime) : '';
  question.firstClick.button_text = data.buttonText || "";
  question.firstClick.allow_cancel_click = data.allowClickCancel ? "1" : "0";

  // Standard options
  question.skip_option = data.skipOption ? "1" : "0";
  question.skip_text = data.skipText || "";
  question.comment_option = data.commentOption ? "1" : "0";

  // Clean up empty values
  Object.keys(question).forEach(key => {
    if (question[key] === "" && key !== "button_text" && key !== "skip_text") {
      question[key] = null;
    }
  });

  console.log("1-click-test: server-formatter", question);

  return question;
}
