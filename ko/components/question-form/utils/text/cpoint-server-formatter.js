import { serverMaskFormatter } from '../mask-formatter';
import { cPointServerLinkFormatter } from '../link-formatter';

export default function (data) {
	let question = {};

	question.maskType = data.maskType;
	question.maskConfig = serverMaskFormatter(data.maskConfig);
	question.commentLengthRange = data.lengthRange;
	question.placeholderText = data.placeholder;
	question.skip = data.skip ? 1 : 0;
	question.skip_text = data.skipText || '';

	question = {
		...question,
		...cPointServerLinkFormatter(data)
	}

	question.variantsType = data.isMultiline ? 1 : 0;

	return question;
}
