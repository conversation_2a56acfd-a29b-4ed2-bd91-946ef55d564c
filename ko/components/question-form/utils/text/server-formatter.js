import {
  serverMaskFormatter,
} from '../mask-formatter';

export default function (data) {
  let question = {};

  question.mask = data.maskType;
  question.mask_config = serverMaskFormatter(data.maskConfig);
  question.comment_minlength = data.lengthRange[0];
  question.comment_maxlength = data.lengthRange[1];
  question.placeholder_text = data.placeholder;

  question.variants_element_type = data.isMultiline ? 1 : 0;

  question.link_with_client_field = data.linkWithClientField ? 1 : 0;
  question.linked_client_field = data.linkedField;
  question.rewrite_linked_field = data.rewriteExistLink ? 1 : 0;

  question.skip = data.skip ? 1 : 0;
  question.skip_text = data.skipText || '';

  return question;
}
