import { previewMaskFormatter } from '../mask-formatter';
import { previewLinkFormatter } from '../link-formatter';

export default function (data) {

	let previewData = {};

	previewData.text = data.text;

	previewData.textFieldParam = {
		min: data.lengthRange[0],
		max: data.lengthRange[1],
		skip: data.skip ? 1 : 0,
		skip_text: data.skipText || "",
	};

	previewData.maskType = data.maskType;
	previewData.maskConfig = previewMaskFormatter(data.maskConfig);
	previewData.placeholderText = data.placeholder;
	previewData.variantsType = data.isMultiline ? 1 : 0;

	previewData = {
		...previewData,
		...previewLinkFormatter(data)
	}

	return previewData;
}
