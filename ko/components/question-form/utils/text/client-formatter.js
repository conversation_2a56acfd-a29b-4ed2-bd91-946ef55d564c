export default function (data, mode) {
	let commentLengthRange = mode === "cpoint" ? data.commentLengthRange : [data.comment_minlength, data.comment_maxlength];
	let placeholder = data.placeholderText;

	let isMultiline = mode === "cpoint" ? data.variantsType == 1 : data.variants_element_type == 1;

	return {
		skip: data.skip ? 1 : 0,
		skipText: data.skip_text || "",
		placeholder: placeholder,
		lengthRange: commentLengthRange,
		isMultiline,
	}
}
