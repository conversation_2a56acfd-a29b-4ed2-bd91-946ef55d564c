import RateQuestion from "./types/rate";
import VariantsQuestion from "./types/variants";
import TextQuestion from "./types/text";
import DateQuestion from "./types/date";
import AddressQuestion from "./types/address";
import FileQuestion from "./types/file";
import QuizQuestion from "./types/quiz";
import PriorityQuestion from "./types/priority";
import ItemsQuestion from "./types/items";
import MediaVariantsQuestion from "./types/media-variants";
import GalleryQuestion from "./types/gallery";
import SmileQuestion from "./types/smile";
import NPSQuestion from "./types/nps";
import MatrixQuestion from "./types/matrix";
import DiffQuestion from "./types/diff";
import StarsQuestion from "./types/stars";
import StarVariantsQuestion from "./types/star-variants";
import RatingQuestion from "./types/rating";
import { ClassifierQuestion, FilialQuestion} from "./types/classifier";
import ScaleQuestion from "./types/scale";
import DistributionScaleQuestion from "./types/distribution-scale";
import Matrix3DQuestion from "./types/matrix-3d";
import CardSortingQuestion from "./types/card-sorting";
import FirstClickTestQuestion from "./types/first-click-test";

import InterBlock from "./types/inter";

import * as VariantsList from "@/components/question-form/components/variants/variants-list";
import * as VariantsMaxCount from "@/components/question-form/components/variants/variants-max-count";
import * as VariantsMinCount from "@/components/question-form/components/variants/variants-min-count";
import * as VariantsType from "@/components/question-form/components/variants/variants-type";

import * as DonorVariantsList from "@/components/question-form/components/variants/donor-variants-list";
import * as DonorVariantsType from "@/components/question-form/components/variants/donor-variants-type";

import { registerComponent } from "@/utils/engine/register-component";

registerComponent("fc-variants-type", VariantsType);
registerComponent("fc-variants-list", VariantsList);
registerComponent("fc-variants-max-count", VariantsMaxCount);
registerComponent("fc-variants-min-count", VariantsMinCount);
registerComponent("fc-donor-variants-type", DonorVariantsType);
registerComponent("fc-donor-variants-list", DonorVariantsList);

export {
  RateQuestion,
  VariantsQuestion,
  TextQuestion,
  DateQuestion,
  AddressQuestion,
  FileQuestion,
  QuizQuestion,
  PriorityQuestion,
  ItemsQuestion,
  MediaVariantsQuestion,
  GalleryQuestion,
  SmileQuestion,
  NPSQuestion,
  MatrixQuestion,
  DiffQuestion,
  StarsQuestion,
  StarVariantsQuestion,
  RatingQuestion,
  ClassifierQuestion,
  FilialQuestion,
  InterBlock,
  ScaleQuestion,
  DistributionScaleQuestion,
  Matrix3DQuestion,
  CardSortingQuestion,
  FirstClickTestQuestion,
};
