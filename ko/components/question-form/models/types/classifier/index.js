import GalleryQuestion from "../gallery";

import { CLASSIFIER_QUESTION, FILIALS_QUESTION } from "@/data/question-types";
import { getCompanyCollections } from "@/api/collections/get-collections";
import { getCollection } from "@/api/collections/collection";
import { FvmCheckedTree } from "@/presentation/viewModels/fvm-checked-tree";
import { formatCollection } from "@/utils/collections/format-collection";
import * as CheckedTree from "@/presentation/views/fc-checked-tree";
import * as CollectionItem from "./components/collection-item";
import { registerComponent } from "@/utils/engine/register-component";
import { toCollectionsPage } from "@/router/settings";
import { HidePoppersEvent } from "@/utils/events/modal";

import * as FilialsSelect from "./components/filials-select";

registerComponent("fc-filials-select", FilialsSelect);
registerComponent("fc-checked-tree", CheckedTree);
registerComponent("fc-collection-item-view", CollectionItem);

class ClassifierQuestion extends GalleryQuestion {
  constructor(controller, config = {}) {
    config.galleryConfig = {
      freeRemove: true,
    };

    super(controller, config);

    this.filials = this.controller.filials;
    this.filialsLoaded = this.controller.filialsLoaded;
    this.filialsLoadedPromise = this.controller.filialsLoadedPromise;
    this.filialsList = ko.computed(() => {
      const filials = this.filials();
      let list = [];
      filials.forEach((item) => {
        list = [...list, ...item.items];
      });
      return list;
    });
    this.hasFilialsCategories = ko.computed(() => {
      const filials = this.filials();
      const hasCategories = filials.some((el) => el.category.id && el.items.length);
      return hasCategories;
    })
    this.selectedFilials = ko.observableArray([]).extend({
      required: {
        message: "Нужно выбрать хотя бы один филиал",
      },
    });

    this.updating = ko.observable(false);

    // collections
    this.dictionaries = ko.observableArray([]);
    getCompanyCollections().then((list) => {
      const dictionaries = list
        .filter((collection) => collection.active && (!collection.system || collection.id === 'filials'))
        .map((collection) => {
          return {
            id: collection.id,
            count: collection.count,
            text: collection.name,
            disabled: collection.count == 0,
            filial: collection.id === 'filials',
          };
        });
      dictionaries.sort((a, b) => {
        if (a.filial) return -1;
        if (b.filial) return 1;
        return a.text.toLowerCase() < b.text.toLowerCase() ? -1 : 1;
      });
      this.dictionaries(dictionaries);
    });
    this.listTypes = [
      { id: "tree", label: "Древовидный список" },
      { id: "list", label: "Простой список" },
    ];
    this.variantTypes = [
      { id: 0, text: "Выбор одного варианта", icon: "input-radio" },
      { id: 1, text: "Выбор нескольких вариантов", icon: "input-checkbox" },
    ];
    this.sortTypes = [
      { id: "default", text: "По умолчанию" },
      { id: "alphabetter", text: "По алфавиту" },
      { id: "random", text: "В случайном порядке" },
    ];

    this.hasCategories = ko.observable(false);
    this.hasVariantTypes = ko.observable(false);

    // question type
    this.type = CLASSIFIER_QUESTION;
    this.isFilials = ko.observable(false);
    this.isFilials.subscribe((isFilials) => {
      this.type = isFilials ? FILIALS_QUESTION : CLASSIFIER_QUESTION;
    })

    // dictionaries

    this.tmpItems = null;
    this.dictionaryId = ko.observable(null).extend({
      required: {
        message: "Обязательное поле",
      },
    });
    this.dictionaryItems = ko.observableArray([]).extend({
      required: {
        message: "Нужно выбрать хотя бы один вариант",
      },
    });
    this.itemsLoading = ko.observable(false);
    this.dictionaryTree = null;

    let _treeSb = null;

    HidePoppersEvent.on(() => {
      this._emptyModalShown = false;
    })


    this.dictionaryList = ko.observableArray([]);

    this.partiallyChecked = ko.computed(() => this.dictionaryItems().length < this.dictionaryList().length);

    this.dictionaryId.subscribe(async (v) => {
      this.updating(true);
      this.dictionaryItems([]);

      if (!v) {
        this.dictionaryTree = null;
        this.tmpItems = null;
        return;
      } 
      
      this.itemsLoading(true);
      let hasCategories = true;

      if (v === 'filials') {
        this.isFilials(true);
        this.hasVariantTypes(false);
        await this.filialsLoadedPromise();
        hasCategories = this.hasFilialsCategories()
      } else {
        this.isFilials(false);
        this.hasVariantTypes(true);
        const { elements } = await getCollection(v);
        let items = formatCollection(elements);
        if (_treeSb) _treeSb.dispose();

        const tree = new FvmCheckedTree(items);
        _treeSb = tree.value.subscribe((v) => {
          this.dictionaryItems(v);
        });

        hasCategories = tree.tree.some((el) => el.category);
        this.dictionaryList(Object.keys(tree.childrenTree).filter(key => !tree.childrenTree[key].length))
        this.dictionaryTree = tree;
        if (this.tmpItems) {
          tree.setValue(this.tmpItems);
          this.tmpItems = null;
        }
      }
      this.hasCategories(hasCategories);
      this.listType(hasCategories ? this.listType() : 'list');

      this.variantType(this.hasVariantTypes() ? this.variantType() : 0);
      this.itemsLoading(false);
      setTimeout(() => this.updating(false));
    });

    // options
    this.dropdown = ko.observable(false);
    this.listType = ko.observable("tree");
    this.variantType = ko.observable(1);
    this.sort = ko.observable("default");
    this.maxVariantsCount = ko.observable(null);

    // gallery
    this.galleryEnabled = ko.observable(false);
    this.commentIsRequired = ko.observable(null);

    // skip
    this.skip = ko.observable(false);
    this.skipText = ko.observable("");
    this.showTooltips = ko.observable(false);
    this.disableSelectCategory = ko.observable(false);

    // subscriptions
    [
      this.disableSelectCategory,
      this.showTooltips,
      this.dictionaryId,
      this.dictionaryItems,
      this.dropdown,
      this.listType,
      this.variantType,
      this.sort,
      this.maxVariantsCount,
      this.skip,
      this.skipText,
      this.commentIsRequired,
      this.selectedFilials,
    ].forEach((f, i) => {
      this.subscriptions.push(
        f.subscribe(() => {
          if (this.updating()) {
            const onUpdating = this.updating.subscribe(() => {
              this.onChange(true);
              onUpdating.dispose();
            })
          } else {
            this.onChange(true);
          }
        })
      );
    });

    this.variantType.subscribe((v) => {
      if (v !== 1) {
        this.disableSelectCategory(false)
      }
    })
    this.listType.subscribe((v) => {
      if (v !== 'tree') {
        this.disableSelectCategory(false)
      }
    })
    this.dropdown.subscribe((v) => {
      if (v) {
        this.disableSelectCategory(false)
      }
    })
  }

  toggleAll() {
    if (this.isFilials()) {
      const filials = this.filialsList().map(item => item.id);
      if (this.selectedFilials().length !== this.filialsList().length) {
        this.selectedFilials(filials);
      } else {
        this.selectedFilials([])
      }
    } else {
      this.partiallyChecked = ko.computed(() => this.dictionaryItems().length < this.dictionaryList().length);
      if (this.dictionaryItems().length !== this.dictionaryList().length) {
        this.dictionaryTree.setValue(this.dictionaryList());
      } else {
        this.dictionaryTree.setValue([]);
      }
    }
  }

  removeVariant(v, cb) {
    const hasAnswers = parseInt(this.countAnswers) > 0;

    if (!hasAnswers) {
      cb();
      return;
    }

    const isMultiple = Array.isArray(v) && v.length > 1;

    this.confirm({
      title: isMultiple
        ? "Удаление вариантов ответа"
        : "Удаление варианта ответа",
      text: isMultiple
        ? "По этому вопросу уже собирается статистика. Для удаления вариантов ответа из анкеты прохождения опроса нажмите кнопку «Снять выделение»."
        : "По этому вопросу уже собирается статистика. Для удаления варианта ответа из анкеты прохождения опроса нажмите кнопку «Снять выделение».",
      mode: "danger",
      confirm: "Снять выделение",
    }).then(() => {
      setTimeout(() => {
        cb()
      }, 500)
    });
  }

  infoDisabledVariantTypes() {
    if (this.hasVariantTypes()) return;
    this.info({
      text: 'В справочнике <b class="bold">«Филиалы»</b> можно выбрать только один вариант ответа.',
    });
  }

  showEmptyCollectionModal(collection) {
    if (this._emptyModalShown) return;
    this._emptyModalShown = true;
    this.controller
      .info({
        text: `Чтобы использовать справочник «${
          collection.text
        }», нужно <a class="font-weight-500" target="_blank" href="${toCollectionsPage()}">добавить в него хотя бы один элемент</a>`,
        onHide: () => {
          this._emptyModalShown = false;
        }
      })
  }

  getLogicVariants(ids) {
    if (!this.dictionaryTree) return [];
    return ids.map((id) => {
      return id;
    });
  }

  updateData(data) {
    super.updateData(data);

    if (String(data.type) === String(FILIALS_QUESTION)) {
      this.isFilials(true);

      this.dictionaryId('filials');

      const {
        filials,
        dropdown,
        galleryEnabled,
        listType,
        sortType,
        skip,
        skipText
      } = data;

      this.listType(listType || "tree");
  
      if (this.commentIsRequired() === null) {
        this.commentIsRequired(data.galleryCommentRequired)
      }
  
      this.galleryEnabled(galleryEnabled);
  
      this.dropdown(dropdown);
  
      this.selectedFilials(filials);
      
      this.sort(sortType || "default");
      this.skip(!!skip);
      this.skipText(skipText || "");
    } else {
      this.isFilials(false);

      const {
        dictionaryId,
        dictionaryItems,
        dropdown,
        listType,
        variantType,
        sortType,
        maxVariantsCount,
        galleryEnabled,
        showTooltips,
        disableSelectCategory,
        skip,
        skipText,
      } = data;
  
      this.galleryEnabled(galleryEnabled);
      this.showTooltips(!!showTooltips)
      this.disableSelectCategory(!!disableSelectCategory)
  
  
      if (this.commentIsRequired() === null) {
        this.commentIsRequired(data.galleryCommentRequired)
      }
  
      if (this.dictionaryId() != dictionaryId) {
        this.dictionaryId(dictionaryId);
        this.tmpItems = dictionaryItems;
      } else {
        this.dictionaryTree.setValue(dictionaryItems);
      }
  
      this.dropdown(dropdown);
      this.listType(listType || "tree");
      this.variantType(variantType);
      this.sort(sortType || "default");
      this.maxVariantsCount(maxVariantsCount);
  
      this.skip(!!skip);
      this.skipText(skipText || "");
    }
  }

  getData() {
    const parentData = super.getData();
    const galleryEnabled = this.galleryEnabled() ? 1 : 0;
    let data;
    if (this.isFilials()) {  
      data = {
        ...parentData,

        skip: this.skip(),
        skipText: this.skipText(),

        dictionarySort: this.sort(),

        filials: this.selectedFilials(),
        filialsList: this.filials(),
  
        dropdown: this.dropdown(),
        listType: this.hasFilialsCategories() ? this.listType() : "list",
        variantType: this.variantType(),
  
        maxVariantsCount: this.maxVariantsCount(),
  
        galleryEnabled,
        gallery: galleryEnabled ? parentData.gallery : [],  
      };
    } else { 
      data = {
        ...parentData,

        dictionaryId: this.dictionaryId(),
        dictionaryItems: this.itemsLoading()
          ? this.tmpItems || []
          : this.dictionaryItems(),
        dictionarySort: this.sort(),
        selectedDictionaryTree: this.dictionaryTree ? this.dictionaryTree.getSelectedTreeStructure() : {},
  
        dropdown: this.dropdown(),
        listType: this.hasCategories() ? this.listType() : "list",
        variantType: this.variantType(),
  
        maxVariantsCount: this.maxVariantsCount(),
  
        galleryEnabled,
        gallery: galleryEnabled ? parentData.gallery : [],  
      };
    }
    return data;
  }

  isValid() {
    if (!this.defaultValidation()) return false;

    if (this.galleryEnabled()) {
      if (!super.isValid()) return false;
    }

    if (this.isFilials()) {
      return this.selectedFilials.isValid();
    } else {
      return this.dictionaryId.isValid() && this.dictionaryItems.isValid();
    }
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}

class FilialQuestion extends ClassifierQuestion {
  constructor(controller, config) {
    super(controller, config);

    this.dictionaryId('filials');
  }
}

export { FilialQuestion, ClassifierQuestion };
