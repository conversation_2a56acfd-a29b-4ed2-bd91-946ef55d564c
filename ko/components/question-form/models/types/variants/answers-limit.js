const { computed } = ko;

export function AnswersLimit({ required, list, maxVal }) {
    const min = ko.computed(() => {
        return required() ? 1 : 0;
      });
      const max = ko.computed(() => {
        if (maxVal) {
          if (maxVal() && maxVal().length) {
            return maxVal() - 1
          }
          
        }
        return list().length;
      });
      const value = ko.observable("");

      min.subscribe((v) => {
        if (value() !== "" && value() < v) {
          value(v);
        }
      });
      max.subscribe((v) => {
        if (value() !== "" && value() > v) {
          value(v);
        }
      });

      return {
        min, max, value
      }
}