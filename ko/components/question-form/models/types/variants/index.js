import GalleryQuestion from "../gallery";
import { FoquzMultipleLoader } from "Models/file-loader/multiple-loader";

import { ASSESSMENT_TYPE_VARIANTS } from "@/components/question-form/data/assessment-types";
import { VARIANTS_QUESTION } from "@/data/question-types";
import { VARIANTS_TYPE_MULTIPLE } from "@/components/question-form/data/variant-types";

import { AnswersLimit } from "./answers-limit";
import { Donor } from "../../donor";
import TextField from "@/components/question-form/models/text-field";
import { VariantsListModel } from "@/components/question-form/models/variants-list";
import "@/dialogs/add-variants-list-dialog";

class VariantsQuestion extends GalleryQuestion {
  constructor(controller, config = {}) {
    config.galleryConfig = {
      freeRemove: true,
    };

    

    super(controller, config);

    // question type
    this.type = VARIANTS_QUESTION;

    // variants
    this.defaultVariantsType = VARIANTS_TYPE_MULTIPLE;
    this.variantsType = ko.observable(this.defaultVariantsType);
    this.variantsList = VariantsListModel({useTooltips: true});

    this.isMultiple = ko.computed(() => {
      return this.variantsType() == VARIANTS_TYPE_MULTIPLE;
    });

    this.maxPointsCalcMethod = ko.observable(false);
    this.enableFile = ko.observable(false);

    // custom variant
    this.customAnswerEnabled = ko.observable(false);
    this.customAnswerField = new TextField({
      withLabel: true,
      defaultLabel: this.translator.t("Свой вариант"),
    });

    this.emptyAnswerField = new TextField({
      withLabel: true,
      defaultLabel: this.translator.t("Ничего из перечисленного"),
      hideDetails: true
    });

    this.requiredComments = ko.observableArray([]);
    this.emptyVariants = ko.observableArray([]);

    // donor
    this.donor = Donor(controller.donors);
    this.blockRecipients = controller.blockRecipients;
    this.disableDonors = ko.computed(() => {
      if (this.isFullBlocked) return true;
      if (ko.toJS(this.isBlocked)) return true;
      if (this.blockRecipients) return true;
      if (!this.donor.donorsList().length) return true;
      return false;
    });
    this.donorVariants = ko.observableArray([]);

    this.donor.useDonor.subscribe((v) => {
      this.variantsList.update([], {tooltip: true});
    });
    this.donor.donorId.subscribe((v) => {
      const donorVariants = this.donor.getDonorVariants(v, {tooltip: true});
      
      this.donorVariants(donorVariants);
    });

    // options
    this.dropdown = ko.observable(false);
    this.randomOrder = ko.observable(false);

    this.useTooltips = ko.observable(false);
    this.showTooltips = ko.observable(false);
    this.selfVariantDescription = ko.observable('');

    // limit
    this.answersCountLimit = AnswersLimit({
      required: this.required,
      list: this.variantsList.list,
    });

    this.answersCountLimitMin = AnswersLimit({
      required: this.required,
      list: this.variantsList.list,
      maxVal: this.answersCountLimit.value
    });

    // gallery
    this.galleryEnabled = ko.observable(false);
    this.commentEnabled = ko.observable(false);

    this.commentIsRequired = ko.observable(null);
    this.commentLabel = ko.observable("");

    this.emptyVariantEnabled = ko.observable(false);
    this.emptyVariantPoints = ko.observable('');
    this.selfVariantNothing = ko.observable(false);

    this.skip = ko.observable(false);
    this.skipText = ko.observable("");

    this.usesDictionaryElements = ko.computed(() => {
      if (this.donor.useDonor()) {
        return !!this.donorVariants().find(el => el.dictionaryElementId);
      }
      return !!this.variantsList.list().find(el => el.dictionaryElementId);
    }, this);

    this.dictionaryBadgeTooltipText = ko.computed(() => {
      if (!controller.question()) {
        return '';
      }
      return `
        ${controller.question().dictionaryElementId() ? `Связка вопроса с элементом ${controller.dictionary().name} справочника ${controller.dictionary().name}` : ''}
        ${controller.question().usesDictionaryElements() ? `${controller.question().dictionaryElementId() ? '.' : ''} В вопросе есть варианты, связанные с элементами справочника ${controller.dictionary().name}` : ''}
      `;
    }, this);
    const loaderConfig = {
      presets: ["videoVar","image"],
      multiple: true,
      
    };
    this.loader = new FoquzMultipleLoader(loaderConfig)

    this.loader.on("select", ({file}) => {
      
      var fd = new FormData();
      for (let index = 0; index < file.length; index++) {
        const element = file[index];

        if (element.size / 1024 / 1024 < 5) {
          fd.append(`files[${index}]`, element);
        }
        
        
      }
      
      var self = this;
  
      $.ajax({
        url: `/foquz/api/questions/upload-detail-files?access-token=${window.ACCESS_TOKEN}`,
        type: "post",
        data: fd,
        contentType: false,
        processData: false,
        success: function (response) {

          response.files.forEach(i => {
            if (i.previewUrl == "/uploads/") {
              i.previewUrl = '/img/audio-file-back.png'
            }
            self.variantsList.addVariant(self.variantsList.list().length)
            const variant = self.variantsList.list()[self.variantsList.list().length - 1]
            variant.file(i)
            variant.value(i.origin_name.replace(/\.[^/.]+$/, ""))
          })
        },
      });
    });

    this.donorVariants.subscribe((v) => {
      if (Array.isArray(v)) {
        v.forEach(variant => {
          if (variant.description && typeof variant.description === 'function') {
            variant.description.subscribe((v) => {
              this.onChange(true);
            });
          }
        });
      }
    });

    // subscriptions
    [
      this.enableFile,
      this.variantsType,
      this.customAnswerEnabled,
      this.emptyVariantEnabled,
      this.galleryEnabled,
      this.answersCountLimit.value,
      this.answersCountLimitMin.value,
      this.dropdown,
      this.randomOrder,
      this.donorVariants,
      this.skip,
      this.skipText,
      this.commentEnabled,
      this.commentLabel,
      this.commentIsRequired,
      this.maxPointsCalcMethod,
      this.requiredComments,
      this.emptyVariants,
      this.selfVariantNothing,
      this.useTooltips,
      this.selfVariantDescription,
    ].forEach((f) => {
      this.subscriptions.push(f.subscribe(() => this.onChange(true)));
    });
    this.useTooltips.subscribe((v) => {
      if (v) {
        this.showTooltips(true)
      }
    })
    this.subscriptions.push(
      this.variantsList.isChanged.subscribe((v) => this.onChange(true)),
      this.customAnswerField.isChanged.subscribe((v) => this.onChange(true)),
      this.emptyAnswerField.isChanged.subscribe((v) => {
        return this.onChange(true)
      }),
      this.donor.isChanged.subscribe((v) => this.onChange(true)),
      this.emptyVariantEnabled.subscribe((v) => {
        if (!v) {
          this.emptyVariants([])
          this.selfVariantNothing(false)
        }
      }),
    );
  }

  toggleReqComments(id, persistentId) {
    const finalId = persistentId || id

    if (this.requiredComments().includes(finalId)) {
      this.requiredComments.remove(finalId)
    } else {
      this.requiredComments.push(finalId)
    }

  }

  addAllComments() {
    this.variantsList.list().forEach(v => {
      const finalId = v.persistentId || v.id;
      if (!this.requiredComments().includes(finalId)) {
        this.requiredComments.push(finalId)
      }
    })
    if (this.customAnswerEnabled() && !this.requiredComments().includes('is-self-answer')) {
      this.requiredComments.push('is-self-answer')
    }
  }

  toggleEmptyVariants(id) {
    if (this.emptyVariants().includes(id)) {
      this.emptyVariants.remove(id)
    } else {
      this.emptyVariants.push(id)
    }
  }
  updateCommentRequired() {
    this.commentField.updateReuired(this.commentIsRequired())
  }

  removeVariant(v, cb) {
    const hasAnswers = parseInt(this.countAnswers) > 0;
    const recipients = this.controller.recipients && this.controller.recipients();

    if (!hasAnswers) {
      cb();
      return;
    }

    if (recipients?.length) {
      const recipientsText = recipients
        .map((q) => {
          return `<div>«${q.index}. ${q.question.descriptionText}»</div>`;
        })
        .join("");

      this.confirm({
        title: "Удаление варианта ответа",
        text: `По этому вопросу уже собирается статистика. Вариант ответа будет удалён без возможности восстановления также у вопросов-реципиентов:
        <div class="bold">${recipientsText}</div>
        `,
        mode: "danger",
        confirm: "Удалить",
      }).then(() => {
        setTimeout(() => {
          cb();
        }, 500);
      });
    } else {
      this.confirm({
        title: "Удаление варианта ответа",
        text: "По этому вопросу уже собирается статистика. Вариант ответа будет удален без возможности восстановления.",
        mode: "danger",
        confirm: "Удалить",
      }).then(() => {
        setTimeout(() => {
          cb();
        }, 500);
      });
    }
  }

  addVariantsFromList() {
    this.openDialog({
      name: "add-variants-list-dialog",
      params: {
        hasAnswers: parseInt(this.countAnswers) > 0,
        checked: this.variantsList.list().filter(el => el.dictionaryElementId).map(el => `${el.dictionaryElementId}`),
        questionController: this.controller,
        questionMode: this.mode,
        hasDictionary: this.controller?.poll?.dictionary_count
      },
      events: {
        submit: async (result) => {
          if (result.inputType === 'dictionary') {
            this.controller.dictionaryElements()
              .filter(el => result.newDetails.find(detail => detail == el.id))
              .forEach(el => {
                if (this.variantsList.list().find(variant => variant.dictionaryElementId == el.id)) {
                  return;
                }
                this.variantsList.addExternalVariant(
                  el.title,
                  undefined,
                  {
                    dictionary_element_id: el.id,
                  }
                )
              });
            return;
          }
          const details = this.getData().variants
            .reduce((acc, item, i) => {
              if (!item) return acc;
              if (typeof item === "object") {
                const items = [
                  {
                    ...item,
                    position: i + 1,
                    need_extra: 'needExtra' in item && item.needExtra() ? 1 : 0,
                    needExtra: undefined,
                  },
                ];
                return [...acc, ...items];
              }
              return [...acc, item];
            }, []);
          const data = await $.post(
            `/foquz/api/questions/prepare-details?access-token=${APIConfig.apiKey}`,
            {
              newDetails: result.newDetails,
              replaceDetails: result.replaceDetails,
              FoquzQuestionDetail: details,
            },
          );
          this.variantsList.update(
            data.detail_answers
              .filter((v) => !v.is_deleted)
              .map((v) => {
                return {
                  id: v.id,
                  donorId: v.question_detail_id,
                  value: v.variant,
                  points: v.points,
                  needExtra: v.need_extra,
                  type: v.type ? +v.type : 0
                };
              }),
              {tooltip: true}
          );
          /* if (this.emptyVariantEnabled() && !data.detail_answers.some(i => i.type == '1')) {
            const emptyData = this.emptyAnswerField.getData()
            this.variantsList.addEmptyVariant(emptyData)
          } */
        },
      },
    });
  }

  onDonorsTogglerClick() {
    if (this.isFullBlocked) return false;
    if (ko.toJS(this.isBlocked)) {
      this.tryChangeBlockedParam();
    } else if (this.blockRecipients) {
      this.tryChangeBlockedParam();
    } else if (!this.donor.donorsList().length) {
      this.noDonorsInfo();
    } else return true;
  }

  getLogicVariants(ids) {
    return this.variantsList
      .list()
      .filter((variant) => {
        return ids.find((id) => variant.id == id);
      })
      .map((variant) => variant.value);
  }

  updateData(data) {
    data.galleryCommentEnabled = data.variantsCommentEnabled;
    data.galleryCommentLengthRange = data.variantsCommentLengthRange;
    data.galleryCommentPlaceholder = data.variantsCommentPlaceholder;
    data.galleryCommentLabel = this.commentLabel();
    data.galleryCommentRequired = data.variantsCommenRequired
    super.updateData(data);

    const {
      variantsType,
      variants,
      variantsCustomAnswerEnabled,
      variantsCustomAnswerLengthRange,
      variantsCustomAnswerPlaceholder,
      customAnswerLabel,
      variantsAnswersCountLimit,
      variantsAnswersCountLimitMin,
      dropdown,
      randomOrder,
      galleryEnabled,
      donorId,
      donorVariantsType,
      maxPointsCalcMethod,
      enableFile,
      requiredComments,
      emptyVariants,
      selfVariantNothing,
      showTooltips,
      selfVariantDescription
    } = data;

    this.enableFile(enableFile)
    this.galleryEnabled(galleryEnabled);
    this.variantsType(variantsType);
    this.maxPointsCalcMethod(maxPointsCalcMethod)
    this.requiredComments(requiredComments)
    this.emptyVariants(emptyVariants || [])
    this.selfVariantNothing(selfVariantNothing)
    
    this.useTooltips(!!showTooltips)
    this.selfVariantDescription(selfVariantDescription)

    if (this.commentIsRequired() === null) {
      this.commentIsRequired(data.variantsCommentRequired)
    }

    this.commentLabel(data.variantsCommentLabel)

    this.donor.update({ donorId, donorVariantsType });

    if (donorId) {
      const donorQestion = window.QUESTIONS.find(i => i.id == donorId)
      const donorType = window.QUESTIONS.find(i => i.id == donorId).main_question_type

      const donorVariants = this.donor.getDonorVariants(donorId, {tooltip: true}).map(i => {
        if (i.id === 'custom') {
          const customVarian = variants.find(el => el.donorId === null && el.dictionary_element_id === null)
          if (customVarian?.description) {
            i.description(customVarian?.description);
            i.modified = true;
          }
          const randomExclusion = !!customVarian?.random_exclusion
          i.randomExclusion(randomExclusion)
          return i;
        }
        const description = variants.find(el => 
          el.donorId === i.id 
          || el.dictionary_element_id === i.donorVariantId
          || el.dictionary_element_id === Number.parseInt(i?.donor_variant_id)
        )?.description || ''

        if (i.description && typeof i.description === 'function' && description) {
          i.description(description);
          i.modified = true;
        } else if (description === '' && typeof i.description === 'function') {
          i.description('')
        }

        const randomExclusion = !!variants.find(el => 
          el.donorId === i.id 
          || el.dictionary_element_id === i.donorVariantId
          || el.dictionary_element_id === Number.parseInt(i?.donor_variant_id)
        )?.random_exclusion

        i.randomExclusion(randomExclusion)
        return i;
      })

      let currentOrder
      if (donorType !== 19) {

        currentOrder = variants.map((v) => v.donorId || "custom");
        
        donorVariants.sort((a, b) => {
          const aIndex = currentOrder.indexOf(a.id);
          const bIndex = currentOrder.indexOf(b.id);

          if (bIndex === -1) {
            if (aIndex === -1) return 0;
            return -1;
          }
          if (aIndex === -1) return 1;
          return aIndex - bIndex;
        });
      } else {
        const donorIds = donorVariants.map(i => +i.id)
        const temp_variants = variants.filter(i => donorIds.includes(i.dictionary_element_id)).sort((a, b) => a.position - b.position)
        currentOrder = temp_variants.map((v) => v.dictionary_element_id);
        donorVariants.sort((a, b) => {
          const aIndex = currentOrder.indexOf(+a.id);
          const bIndex = currentOrder.indexOf(+b.id);

          if (bIndex === -1) {
            if (aIndex === -1) return 0;
            return -1;
          }
          if (aIndex === -1) return 1;
          return aIndex - bIndex;
        });
      }

        
        donorVariants.forEach((variant) => {
          const data = variants.find((v) => {
            if (variant.id === "custom") return !v.donorId;
            return v.donorId === variant.id;
          });

          if (data) {
            variant.points(data.points);
            variant._recipientId = data.id
          }

        });
      
      this.donorVariants(donorVariants);
    }
    
    this.emptyVariantEnabled(variants.some(v => v.type === 1) || selfVariantNothing);

    if (enableFile) {
      variants.map(i => {
        if (i.preview_url && i.file_url) {
          i.file = {
            previewUrl: i.preview_url,
            fileUrl: i.file_url,
            id: i.file_id
          }
        }
        
        return i
      })
    }

    this.variantsList.update(variants, {tooltip: true});

    /* if (this.emptyVariantEnabled()) {
      const emptyVariantLabel = this.variantsList.getVariants().find(i => i.type ===1).value
      this.emptyAnswerField.updateData({label: emptyVariantLabel === 'Ничего из перечисленного' ? '' : emptyVariantLabel})
    } */

    this.customAnswerEnabled(variantsCustomAnswerEnabled);
    
    this.customAnswerField.updateData({
      range: variantsCustomAnswerLengthRange,
      placeholder: variantsCustomAnswerPlaceholder,
      label: customAnswerLabel,
    });

    this.answersCountLimit.value(variantsAnswersCountLimit ? variantsAnswersCountLimit+'' : null);
    this.answersCountLimitMin.value(variantsAnswersCountLimitMin ? variantsAnswersCountLimitMin+'' : null);

    this.dropdown(dropdown);
    this.randomOrder(randomOrder);

    this.skip(!!data.skip);
    this.skipText(data.skipText || "");
  }

  getData() {
    const parentData = super.getData();
    const variantsType = parseInt(this.variantsType());
    const galleryEnabled = this.galleryEnabled() ? 1 : 0;
    
    let data = {
      ...parentData,
      variantsAssessmentType: ASSESSMENT_TYPE_VARIANTS,
      variantsType,
      dropdown: this.dropdown(),
      randomOrder: this.randomOrder(),
      variantsAnswersCountLimit: this.answersCountLimit.value(),
      variantsAnswersCountLimitMin: this.answersCountLimitMin.value(),
      galleryEnabled,
      gallery: galleryEnabled ? parentData.gallery : [],
    };

    data.skip = this.skip();
    data.skipText = this.skipText();

    if (this.donor.useDonor()) {
      const donorId = ko.unwrap(this.donor.donorId)
      const donorQuestion = window.QUESTIONS.find(i => i.id == donorId)
      const isDonorClassifierWithListType = donorQuestion?.main_question_type === 19 && donorQuestion?.dictionary_list_type === 'list'

      data = {
        ...data,
        ...this.donor.getData(),
        variants: ko.toJS(this.donorVariants).map((v) => {
          if (v.id === "custom") {
            return {
              value: v.value,
              points: v.points || "",
              recipientId: v._recipientId,
              description: ko.unwrap(v.description),
              random_exclusion: v.randomExclusion ? 1 : 0
            };
          }

          return {
            id: v.id,
            recipientId: v._recipientId,
            value: v.value,
            points: v.points || "",
            random_exclusion: v.randomExclusion ? 1 : 0,
            description: ko.unwrap(v.description),
            ...((donorQuestion?.variants_with_files || ko.unwrap(v.enableFile)) && {
              file: ko.unwrap(v.file),
              file_id: v.file?.id || null,
              file_url: v.file?.fileUrl || null,
              preview_url: v.file?.previewUrl || null,
            })
          };
        }),
        variantsCustomAnswerEnabled: false,
        variantsEmptyVariantEnabled: false,
        variantsCustomAnswerPlaceholder: "",
        variantsCustomAnswerLengthRange: [0, 0],
        customAnswerLabel: "",
        isDonorClassifierWithListType,
      };

      if (!!donorQuestion?.variants_with_files) {
        data.donorEnableFile = true
      }
      
    } else {
      const customAnswer = this.customAnswerField.getData();
      const variants = this.variantsList.getVariants();

      data = {
        ...data,
        variants,
        variantsCustomAnswerEnabled: this.customAnswerEnabled(),
        variantsEmptyVariantEnabled : this.emptyVariantEnabled(),
        variantsCustomAnswerPlaceholder: customAnswer.placeholder,
        variantsCustomAnswerLengthRange: customAnswer.range,
        variantsCommentLengthRange: parentData.galleryCommentLengthRange,
        customAnswerLabel: customAnswer.label,
        donor: null,
        galleryCommentRequaired: this.commentIsRequired(),
      };
    }

    data.comment = {
      enabled: this.commentEnabled(),

      ...this.commentField.getData(),
      label: this.commentLabel(),
    };
    data.maxPointsCalcMethod = this.maxPointsCalcMethod()
    data.enableFile = this.enableFile()

    data.requiredComments = this.requiredComments()
    data.emptyVariants = this.emptyVariants()
    data.selfVariantNothing = this.selfVariantNothing()
    data.show_tooltips = this.useTooltips()
    data.selfVariantDescription = this.selfVariantDescription()
    data.requiredComments = this.requiredComments()

    return data;
  }

  isValid() {
    if (!this.enableFile()) {
      if (!this.defaultValidation()) return false;

      if (this.galleryEnabled()) {
        if (!super.isValid()) return false;
      }

      if (this.donor.useDonor()) {
        return true;
      }

      return this.variantsList.isValid();
    } else {
      if (!this.defaultValidation()) return false;
      return this.variantsList.isValidWithFile()
    }
    
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}

export default VariantsQuestion;
