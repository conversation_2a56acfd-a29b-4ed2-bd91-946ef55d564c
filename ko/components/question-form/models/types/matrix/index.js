import { uniq as _uniq } from 'lodash';
import "Components/input/color-picker";

import GalleryQuestion from "../gallery";
import { MATRIX_QUESTION } from "Data/question-types";
import { MatrixController } from "../../../controllers/matrix-controller";
import { VariantsController } from "../../../controllers/variants-controller";
import { Donor } from "../../donor";
import { Translator } from "@/utils/translate";

import "./style.less";

const ValidationTranslator = Translator("validation");

class MatrixQuestion extends GalleryQuestion {
  constructor(controller, config = {}) {
    config.galleryConfig = {
      freeRemove: true,
    };

    super(controller, config);

    this.type = MATRIX_QUESTION;
    this.matrixType = ko.observable("standart");

    this.multipleChoice = ko.observable(0);
    this.matrix = new MatrixController({}, this);

    // donor
    this.donor = Donor(controller.donors);
    this.blockRecipients = controller.blockRecipients;
    this.disableDonors = ko.computed(() => {
      if (this.isFullBlocked) return true;
      if (ko.toJS(this.isBlocked)) return true;
      if (this.blockRecipients) return true;
      if (!this.donor.donorsList().length) return true;
      return false;
    });
    this.donorVariants = [];

    this.donor.donorId.subscribe((v) => {
      if (v) {
        this.donorVariants = this.donor.getDonorVariants(v);
        this.matrix.setRows(this.donorVariants.map((v) => ko.toJS(v.value)));
      } else {
        this.matrix.resetRows();
        this.donorVariants = [];
      }
    });

    this.maxPointsCalcMethod = ko.observable(false);
    this.maxPointsCalcRecipient = ko.observable(false)

    this.skip = ko.observable(false);
    this.skipText = ko.observable("");
    this.skipVariant = ko.observable(false);

    this.galleryEnabled = ko.observable(false);

    this.clarifyingQuestionEnabled = ko.observable(false);
    this.clarifyingQuestionText = ko.observable("").extend({
      required: {
        message: () => ValidationTranslator.t("Обязательное поле")(),
        onlyIf: () => this.clarifyingQuestionEnabled(),
      },
    });
    this.clarifyingQuestionForAllCols = ko.observable(true);
    this.clarifyingQuestionForAllRows = ko.observable(true);
    this.clarifyingQuestionController = new VariantsController(
      {
        withCustomAnswer: true,
        withVariantsTypeSelect: true,
        withAddVariantButton: true,
        addFirstVariant: true,
        variantsType: "single",
        withTextAnswer: true,
      },
      this
    );
    this.clarifyingQuestionIsRequired = ko.observable(1);

    this.clarifyingQuestionEnabled.subscribe((v) => {
      if (v) this.commentEnabled(false);
    });
    this.commentEnabled.subscribe((v) => {
      if (v) this.clarifyingQuestionEnabled(false);
    });

    this.commentIsRequired = ko.observable(null);

    this.randomOrder = ko.observable(false);
    this.minRowsReq = this.matrix.minRowsReq;
    this.dropdown = ko.observable(false);
    this.selectPlaceholder = ko.observable('');

    this.matrixRowsDictionary = ko.observable({});

    this.usesDictionaryElements = ko.computed(() => {
      if (this.donor.useDonor()) {
        return !!this.donorVariants.find(el => el.dictionaryElementId);
      }
      return !!Object.keys(this.matrixRowsDictionary()).length;
    }, this);

    this.dictionaryBadgeTooltipText = ko.computed(() => {
      if (!controller.question()) {
        return '';
      }
      return `
        ${controller.question().dictionaryElementId() ? `Связка вопроса с элементом ${controller.dictionary().name} справочника ${controller.dictionary().name}` : ''}
        ${controller.question().usesDictionaryElements() ? `${controller.question().dictionaryElementId() ? '.' : ''} В вопросе есть варианты, связанные с элементами справочника ${controller.dictionary().name}` : ''}
      `;
    }, this);

    [
      this.galleryEnabled,
      this.clarifyingQuestionEnabled,
      this.clarifyingQuestionIsRequired,
      this.clarifyingQuestionText,
      this.randomOrder,
      this.dropdown,
      this.selectPlaceholder,
      this.skip,
      this.skipText,
      this.skipVariant,
      this.minRowsReq,
      this.multipleChoice,
      this.commentIsRequired,
      this.maxPointsCalcMethod,
      this.maxPointsCalcRecipient,
      this.matrixType
    ].forEach((f) =>
      f.subscribe((v) => this.onChange())
    );

    this.subscriptions.push(
      this.matrix.isChanged.subscribe((v) => this.onChange(true)),
      this.donor.isChanged.subscribe((v) => {
        this.onChange(true);
      })
    );

    [this.clarifyingQuestionController].forEach((c) =>
      c.isChanged.subscribe((v) => this.onChange())
    );

    this.skip.subscribe((v) => {
      if (!v) {
        this.skipVariant(v);
      }
    });
  }

  updateCommentRequired() {
    this.commentField.updateReuired(this.commentIsRequired())
  }

  onDonorsTogglerClick() {
    if (this.isFullBlocked) return false;
    if (ko.toJS(this.isBlocked)) {
      this.tryChangeBlockedParam();
    } else if (this.blockRecipients) {
      this.tryChangeBlockedParam();
    } else if (!this.donor.donorsList().length) {
      this.noDonorsInfo();
    } else return true;
  }

  updateData(data) {
    data.gallery = data.matrixGallery;
    
    data.galleryCommentEnabled = data.matrixCommentEnabled;
    data.galleryCommentLabel = data.matrixCommentLabel;
    data.galleryCommentLengthRange = data.matrixCommentLengthRange;
    data.galleryCommentPlaceholder = data.matrixCommentPlaceholder;
    data.galleryCommentRequired = data.matrixCommentRequired;

    super.updateData(data);

    const { matrixGalleryEnabled, matrix, donorId, donorVariantsType } = data;

    this.galleryEnabled(matrixGalleryEnabled);

    this.maxPointsCalcMethod(data.maxPointsCalcMethod)
    this.maxPointsCalcRecipient(data.maxPointsCalcRecipient)

    if (this.commentIsRequired() === null) {
      this.commentIsRequired(data.matrixCommentRequired)
    }

    let clarifyingQuestion = data.clarifyingQuestion;

    this.clarifyingQuestionEnabled(clarifyingQuestion.enabled);
    this.clarifyingQuestionIsRequired(clarifyingQuestion.required);
    this.clarifyingQuestionText(clarifyingQuestion.text || "");
    this.clarifyingQuestionController.updateData({
      type: clarifyingQuestion.variantsType,
      variants: clarifyingQuestion.variants,
      customAnswerEnabled: clarifyingQuestion.customAnswerEnabled,
      customAnswerRange: clarifyingQuestion.customAnswerLengthRange,
      customAnswerPlaceholder: clarifyingQuestion.customAnswerPlaceholder,
      customAnswerLabel: clarifyingQuestion.customAnswerLabel,
    });

    this.donor.update({
      donorId,
      donorVariantsType,
    });

    if (donorId) {
      this.donorVariants = this.donor.getDonorVariants(donorId);
      const donorRows = matrix.donorRows.map((r) => `${r}`);
      const rows = [...this.donorVariants];

      rows.sort((a, b) => {
        const aIndex = donorRows.indexOf(a.id.toString());
        const bIndex = donorRows.indexOf(b.id.toString());

        if (bIndex === -1) {
          if (aIndex === -1) return 0;
          return -1;
        }
        if (aIndex === -1) return 1;
        return aIndex - bIndex;
      });

      this.matrix.updateData({
        ...matrix,
        rows: rows.map((r) => ko.toJS(r.value)),
      });
    } else {
      this.matrix.updateData(matrix);
    }
    this.matrixRowsDictionary(
      Object.keys(matrix.rows_dictionary || {}).reduce((acc, el) => {
        if (!this.matrix.rows().find(row => row.label() === el)) {
          return acc;
        }
        return {
          ...acc,
          [el]: matrix.rows_dictionary[el],
        }
      }, {}),
    );

    this.clarifyingQuestionForAllCols(false);
    this.clarifyingQuestionForAllRows(false);
    if (_uniq(this.matrix.columns().map(el => el.needExtra())).length === 1) {
      this.clarifyingQuestionForAllCols(this.matrix.columns()[0].needExtra());
    }
    if (_uniq(this.matrix.rows().map(el => el.needExtra())).length === 1) {
      this.clarifyingQuestionForAllRows(this.matrix.rows()[0].needExtra());
    }

    if (matrix.type && ['standart', 'marker'].includes(matrix.type)) {
      this.matrixType(matrix.type);
    } else {
      this.matrixType('standart');
    }

    this.skip(!!data.skip);
    this.skipText(data.skipText || "");
    this.skipVariant(!!data.skipVariant);

    this.randomOrder(data.randomOrder);
    this.dropdown(data.dropdown);
    this.selectPlaceholder(data.selectPlaceholder);
    if (data.minRowsReq) {
      this.minRowsReq(data.minRowsReq);
    }
  }

  getData() {
    let data = super.getData();

    data.matrixGalleryEnabled = this.galleryEnabled() ? 1 : 0;
    data.matrixGallery = this.galleryEnabled ? data.gallery : [];

    data.matrixCommentEnabled = data.galleryCommentEnabled;
    data.matrixCommentLabel = data.galleryCommentLabel;
    data.matrixCommentLengthRange = data.galleryCommentLengthRange;
    data.matrixCommentPlaceholder = data.galleryCommentPlaceholder;
    data.matrixCommentRequired = data.galleryCommentRequired;

    this.matrixRowsDictionary(
      Object.keys(this.matrixRowsDictionary() || {}).reduce((acc, el) => {
        if (!this.matrix.rows().find(row => row.label() === el)) {
          return acc;
        }
        return {
          ...acc,
          [el]: this.matrixRowsDictionary()[el],
        }
      }, {}),
    );

    data.matrix = {
      ...this.matrix.getData(),
      type: this.matrixType(),
      donorRows: [],
      rows_dictionary: this.matrixRowsDictionary(),
    };

    if (this.donor.useDonor()) {
      const donorId = ko.unwrap(this.donor.donorId)
      const donorQestion = window.QUESTIONS.find(i => i.id == donorId)
      const isDonorClassifierWithListType = donorQestion?.main_question_type === 19 && donorQestion?.dictionary_list_type === 'list'

      data = {
        ...data,
        ...this.donor.getData(),
      };

      const rows = this.matrix.rows().map((v) => v.label());
      let donorRows = [...ko.toJS(this.donorVariants)];
      donorRows.sort((a, b) => {
        const aIndex = rows.indexOf(a.value);
        const bIndex = rows.indexOf(b.value);
        return aIndex - bIndex;
      });
      donorRows = donorRows.map((v) => v.id);

      data.matrix.donorRows = donorRows;

      if (isDonorClassifierWithListType) {
        data.isDonorClassifierWithListType = true
      }
    }

    data.randomOrder = this.randomOrder();
    data.dropdown = this.dropdown();
    data.selectPlaceholder = this.selectPlaceholder();

    data.clarifyingQuestion = {
      enabled: this.clarifyingQuestionEnabled(),
      required: this.clarifyingQuestionIsRequired(),
      text: this.clarifyingQuestionText(),
      ...this.clarifyingQuestionController.getData(),
    };

    data.skip = this.skip();
    data.skipText = this.skipText();
    data.skipVariant = this.skipVariant();
    data.minRowsReq = this.minRowsReq();
    data.maxPointsCalcMethod = this.maxPointsCalcMethod();
    data.maxPointsCalcRecipient = this.maxPointsCalcRecipient();

    return data;
  }

  addVariantsFromDictionary() {
    this.openDialog({
      name: "add-variants-list-dialog",
      params: {
        hasAnswers: parseInt(this.countAnswers) > 0,
        mode: 'dictionary',
        dictionary: this.controller.dictionary,
        headerText: 'Добавление вариантов ответов из справочника',
        checked: Object.values(this.matrixRowsDictionary()),
      },
      events: {
        submit: async (result) => {
          this.controller.dictionaryElements()
            .filter(el => result.newDetails.find(detail => detail == el.id))
            .forEach(el => {
              if (Object.values(this.matrixRowsDictionary() || {}).includes(`${el.id}`)) {
                return;
              }
              let title = el.title;
              if (this.matrix.rows().find(row => el.title === row.label())) {
                title = `__foquz_dictionary_element${el.title}`;
              }
              this.matrix.addRow(title);
              this.matrixRowsDictionary({
                ...this.matrixRowsDictionary(),
                [title]: el.id,
              });
            });
        },
      },
    });
  }

  isValid() {
    if (!this.matrix.isValid()) return false;
    if (!this.clarifyingQuestionText.isValid()) return false;
    if (this.clarifyingQuestionEnabled()) {
      if (!this.clarifyingQuestionController.isValid()) return false;
    }
    if (this.galleryEnabled()) {
      return super.isValid();
    }
    return this.defaultValidation();
  }

  onChangeClarifyingQuestionForAllCols() {
    this.matrix.columns().forEach(variant => {
      variant.needExtra(this.clarifyingQuestionForAllCols());
    });
  }

  onChangeClarifyingQuestionForAllRows() {
    this.matrix.rows().forEach(variant => {
      variant.needExtra(this.clarifyingQuestionForAllRows());
    });
  }

  onChangeNeedExtra(data, index, type = 'cols') {
    const matrixSource = type === 'cols' ? this.matrix.columns() : this.matrix.rows();
    const forAllSource = type === 'cols' ? this.clarifyingQuestionForAllCols : this.clarifyingQuestionForAllRows;
    if (
      data.needExtra() &&
      !forAllSource() &&
      !matrixSource.find(el => !el.needExtra())
    ) {
      forAllSource(true);
      return;
    }
    if (
      !data.needExtra() &&
      forAllSource() &&
      !matrixSource.find((el, idx) => idx !== index && !el.needExtra())
    ) {
      forAllSource(false);
      return;
    }
  }
}

export default MatrixQuestion;
