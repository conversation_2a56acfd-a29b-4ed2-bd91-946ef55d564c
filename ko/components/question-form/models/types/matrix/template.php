<template id="question-form-template-matrix">
  <!-- ko let: { $translator: question.translator } -->
  <div>
    <div class="f-fs-1 f-color-service mb-4"><span data-bind="text: $translator.t('В этом варианте вопроса можно выбрать только один ответ для каждой строки')"></span></div>

    <!-- ko ifnot: question.isAuto || question.isSystem || question.mode === 'cpoint' -->
    <div class="form-group">
      <div data-bind="
        click: function() {
          return question.onDonorsTogglerClick();
        }">
        <fc-switch params="checked: question.donor.useDonor, 
                disabled: question.disableDonors || question.isFullBlocked,
                label: 'Использовать варианты ответов респондента из другого вопроса',
                hint: 'Использовать варианты ответов респондента из другого вопроса'"></fc-switch>
      </div>
    </div>
    <!-- /ko -->


      <div class="row">
          <div class="col">
              <fc-variants-type params="
          value: question.multipleChoice,
          disabled: question.isBlocked() || question.isFullBlocked,
        " class="mb-4"></fc-variants-type>
          </div>
      </div>


    <!-- ko if: question.donor.useDonor -->
    <div>
      <div class="form-group">
        <fc-label params="text: 'Вопрос-донор', hint: 'Вопрос-донор'"></fc-label>
        <fc-select
          params="
            options: question.donor.donorsList,
            value: question.donor.donorId,
            disabled: question.isBlocked() || question.isFullBlocked,
          "
          data-bind="
            click: function() {
              if (question.isBlocked() && !question.isFullBlocked) {
                question.tryChangeBlockedParam();
              } else {
                return true;
              }
            },
          "
        ></fc-select>
      </div>

      <div class="form-group">
        <fc-label params="text: 'Варианты ответов'"></fc-label>
        <fc-donor-variants-type params="
            value: question.donor.donorVariantsType, 
            isFullBlocked: question.isFullBlocked,
            disabled: question.isBlocked() || question.isFullBlocked" data-bind="click: function() {
                if (question.isBlocked() && !question.isFullBlocked) 
                    question.tryChangeBlockedParam();
                else return true;
            }" class="mb-4"></fc-donor-variants-type>
      </div>
    </div>
    <!-- /ko -->


    <matrix-controller params="controller: question.matrix, blockRows: question.donor.useDonor"></matrix-controller>

    <!-- ko if: question.withPoints -->
    <div class="mb-20p">
        <foquz-checkbox params="checked: question.maxPointsCalcMethod, disabled: question.isFullBlocked">
            <span data-bind="text: question.translator.t('Учитывать в итоговой сумме баллов, если вопрос не отображался для респондента')"></span>
            <question-button params="text: question.translator.t('Настройки отображения вопроса можно настроить в логике опроса. По умолчанию все скрытые логикой отображения вопросы учитываются в итоговом подсчете баллов')">
            </question-button>
        </foquz-checkbox>
    </div>
    <!-- ko if: question.donor.useDonor -->
    <div class="mb-20p">
        <foquz-checkbox params="checked: question.maxPointsCalcRecipient, disabled: question.isFullBlocked">
            <span data-bind="text: question.translator.t('Считать максимум баллов за вопрос, исходя из фактически унаследованных вариантов')"></span>
            <question-button params="text: question.translator.t('По умолчанию максимум баллов за вопрос считается по всем вариантам, которые могут унаследоваться')">
            </question-button>
        </foquz-checkbox>
    </div>
    <!-- /ko -->
    <!-- /ko -->

    <div class="my-4" style="margin-bottom: 25px;">
      <foquz-checkbox params="checked: question.randomOrder, disabled: question.isFullBlocked">
        <span data-bind="text: $translator.t('Случайный порядок строк в матрице')"></span>
        <question-button params="text: $translator.t('Строки матрицы для каждого пользователя при прохождении будут предложены в случайном порядке')"></question-button>
      </foquz-checkbox>
    </div>

    <div class="my-4" style="margin-bottom: 27px;">
      <fc-switch params="
          checked: question.dropdown,
          disabled: question.isFullBlocked,
          label: 'Варианты ответов выпадающим списком',
          hint: 'Варианты ответов выпадающим списком',
        "></fc-switch>
    </div>
    <!-- ko template: {
      foreach: templateIf(question.dropdown(), $data),
      afterAdd: slideAfterAddFactory(400),
      beforeRemove: slideBeforeRemoveFactory(400),
    } -->
    <div class="mb-4">
      <!-- ko template: {
        name: 'placeholder-template',
        data: {
          placeholder: question.selectPlaceholder,
          disabled: question.isFullBlocked,
          translator: question.translator,
        }
      } -->
      <!-- /ko -->
    </div>
    <!-- /ko -->
  </div>

  <!-- ko if: (question.required() && question.matrix.rows().length && question.minRowsReq) -->
  <div class="row">
    <div class="col">
      <fc-label params="text: 'Минимальное кол-во строк с ответом'"></fc-label>
      <div class="d-flex mb-4 align-items-center">
        <fc-range params="
            value: question.minRowsReq,
            min: 1,
            max: question.matrix.rows().length,
            step: 1,
            disabled: question.matrix.rows().length === 1 || question.isFullBlocked,
          "></fc-range>
        <div class="ml-4 bold" data-bind="text: question.minRowsReq()"></div>
      </div>
    </div>
  </div>
  <!-- /ko -->

  <hr class="mx-0" style="margin-top: 0">

  <div>
    <div class="form-group">
      <fc-switch params="checked: question.skip, label: $translator.t('Пропуск ответа'), disabled: question.isFullBlocked"></fc-switch>
    </div>
    <!-- ko template: {
       foreach: templateIf(question.skip(), $data),
       afterAdd: slideAfterAddFactory(400),
       beforeRemove: slideBeforeRemoveFactory(400)
    } -->
    <div class="form-group">
      <fc-label params="text: $translator.t('Текст'), hint: $translator.t('Текст')"></fc-label>
      <fc-input
        params="
          value: question.skipText,
          counter: true,
          maxlength: 125,
          placeholder: $translator.t('Затрудняюсь ответить'),
          disabled: question.isFullBlocked,
        "
      ></fc-input>
    </div>
    <!-- /ko -->
    <!-- ko if: question.skip() -->
    <div class="mb-20p">
      <foquz-checkbox
        params="
          checked: question.skipVariant,
          disabled: question.isFullBlocked,
        "
      >
        <span data-bind="text: question.translator.t('Пропуск для каждой оценки')"></span>
        <question-button params="text: question.translator.t('Пропуск для каждой оценки')">
        </question-button>
      </foquz-checkbox>
    </div>
    <!-- /ko -->
  </div>

  <hr class="mx-0">


  <!-- ko template: { name: 'gallery-question-gallery-template' } -->
  <!-- /ko -->

  <hr>
  <!-- Комментарий/Уточняющий вопрос -->
  <div class="row clarifying-answer-settings__switchers">
    <div class="col">
      <div class="pt-1">
        <switch class="mb-0" params="
            checked: question.clarifyingQuestionEnabled,
            disabled: question.isBlocked() || question.isFullBlocked
          " data-bind="
            click: function() {
              if (question.isFullBlocked) return false;
              if (question.isBlocked()) question.tryChangeBlockedParam();
              else return true;
            }
          ">
          <span data-bind="text: $translator.t('Уточняющий вопрос')"></span>
        </switch>
      </div>
    </div>
    <div class="col">
      <div class="pt-1">
        <switch class="mb-0" params="
            checked: question.commentEnabled,
            disabled: question.isFullBlocked || (question.isBlocked() && question.clarifyingQuestionEnabled())
          " data-bind="
            click: function() {
              if (question.isFullBlocked) return false;
              if (question.isBlocked()  && question.clarifyingQuestionEnabled()) question.tryChangeBlockedParam();
              else return true;
            }
          ">
          <span data-bind="text: $translator.t('Комментарий')"></span>
        </switch>
      </div>
    </div>
  </div>
  <!-- ko template: {
    foreach: question.clarifyingQuestionEnabled,
    afterAdd: fadeAfterAddFactory(200),
    beforeRemove: fadeBeforeRemoveFactory(200)
  } -->
  <div class="row">
    <div class="col">
      <div class="f-check">
        <input
          type="checkbox"
          id="extra-required"
          class="f-check-input"
          data-bind="
            checked: question.clarifyingQuestionIsRequired,
            disable: question.isFullBlocked,
          "
        />
        <label for="extra-required" class="f-check-label" data-bind="text: $translator.t('Обязательный')"></label>
      </div>
    </div>
  </div>

  <div class="row pt-4">
    <div class="col mb-4">
      <fc-label params="text: 'Для столбцов'"></fc-label>
      <div class="clarifying-answer-settings__selected-variants">
        <div class="f-check">
          <input type="checkbox" id="extra-for-all-cols" class="f-check-input" data-bind="
              checked: question.clarifyingQuestionForAllCols,
              disable: question.isFullBlocked,
              event: {
                change: function(data, event) { question.onChangeClarifyingQuestionForAllCols.bind(question)(data); },
              },
            " />
          <label for="extra-for-all-cols" class="f-check-label" data-bind="text: $translator.t('Всех столбцов')"></label>
        </div>
        <!-- ko foreach: question.matrix.columns -->
        <div class="f-check">
          <input type="checkbox" class="f-check-input" data-bind="
              checked: $data.needExtra,
              disable: question.isFullBlocked,
              attr: {
                id: `extra-for-col-${$index()}`,
              },
              event: {
                change: function(data, event) { question.onChangeNeedExtra(data, $index(), 'cols'); },
              },
            " />
          <label class="f-check-label" data-bind="
              text: $index() + 1,
              attr: {
                for: `extra-for-col-${$index()}`,
              },
            "></label>
        </div>
        <!-- /ko -->
      </div>
    </div>
  </div>

  <div class="row pt-4">
    <div class="col mb-4">
      <fc-label params="text: 'Для строк'"></fc-label>
      <div class="clarifying-answer-settings__selected-variants">
        <div class="f-check">
          <input type="checkbox" id="extra-for-all-rows" class="f-check-input" data-bind="
              checked: question.clarifyingQuestionForAllRows,
              disable: question.isFullBlocked,
              event: {
                change: function(data, event) { question.onChangeClarifyingQuestionForAllRows.bind(question)(data); },
              },
            " />
          <label for="extra-for-all-rows" class="f-check-label" data-bind="text: $translator.t('Всех строк')"></label>
        </div>
        <!-- ko foreach: question.matrix.rows() -->
        <div class="f-check">
          <input type="checkbox" class="f-check-input" data-bind="
              checked: $data.needExtra,
              disable: question.isFullBlocked,
              attr: {
                id: `extra-for-row-${$index()}`,
              },
              event: {
                change: function(data, event) { question.onChangeNeedExtra(data, $index(), 'rows'); },
              },
            " />
          <label class="f-check-label" data-bind="
              text: $index() + 1,
              attr: {
                for: `extra-for-row-${$index()}`,
              },
            "></label>
        </div>
        <!-- /ko -->
      </div>
    </div>
  </div>
  <!-- /ko -->
  <!-- Уточняющий вопрос -->
  <!-- ko template: {
    foreach: templateIf(question.clarifyingQuestionEnabled(), $data),
    afterAdd: slideAfterAddFactory(200, 150),
    beforeRemove: slideBeforeRemoveFactory(150)
  } -->
  <div class="form-group mt-3">
    <label class="form-label" for="clarifyingQuestion" data-bind="text: $translator.t('Уточняющий вопрос')"></label>
    <button class="btn-question" tabindex="10" data-bind="
        tooltip,
        tooltipPlacement: 'top',
        tooltipText: $translator.t('Можно добавить вопрос, который дополнительно будет задан респонденту для уточнения его ответа. Уточняющий вопрос должен быть всегда с вариантами ответов')
      " type="button"></button>
    <div class="chars-counter chars-counter--type_textarea" data-bind="charsCounter, charsCounterCount: question.clarifyingQuestionText().length">
      <textarea class="form-control" data-bind="
          textInput: question.clarifyingQuestionText,
          css: {
              'is-invalid': controller.formControlErrorStateMatcher(question.clarifyingQuestionText),
              'is-valid': controller.formControlSuccessStateMatcher(question.clarifyingQuestionText)
          },
          autosizeTextarea,
          disable: question.isFullBlocked
        " id="clarifyingQuestion" maxlength="500"></textarea>
      <div class="chars-counter__value"></div>
    </div>
    <!-- ko if: controller.formControlErrorStateMatcher(question.clarifyingQuestionText) -->
    <div class="form-error" data-bind="text: question.clarifyingQuestionText.error()"></div>
    <!-- /ko -->
  </div>
  <div class="">
    <question-form-variants-list params="controller: question.clarifyingQuestionController, disabled: question.isFullBlocked"></question-form-variants-list>
  </div>
  <!-- /ko -->
  <!-- /Уточняющий вопрос -->
  <!-- ko template: {
      foreach: templateIf(question.commentEnabled(), $data),
      afterAdd: slideAfterAddFactory(200),
      beforeRemove: slideBeforeRemoveFactory(200)
  } -->
  <div>
        <div class="row pb-4">
            <div class="col">
            <div class="f-check">
                <input
                type="checkbox"
                id="comment-required"
                class="f-check-input"
                data-bind="
                    checked: question.commentIsRequired,
                    disable: question.isFullBlocked,
                    event: { change: function() { 
                    question.updateCommentRequired() } }
                "
                />
                <label for="comment-required" class="f-check-label" data-bind="text: $translator.t('Обязательный')"></label>
            </div>
            </div>
        </div>
    <div class="form-group">
      <fc-label params="text: 'Наименование поля', hint: 'Наименование поля'"></fc-label>
      <fc-input params="value: question.commentLabel, maxlength: 120, counter: true, placeholder: 'Ваш комментарий', disabled: question.isFullBlocked"></fc-input>
    </div>

    <!-- ko component: {
        name: 'text-field',
        params: {
            controller: question.commentField,
            intervalText: $translator.t('Кол-во символов в комментарии'),
            disabled: question.isFullBlocked
        }
    } -->
    <!-- /ko -->
  </div>
  <!-- /ko -->
  <!-- /Комментарий -->

  <!-- /ko -->
</template>
