
import { FIRST_CLICK_TEST } from "Data/question-types";
import { FoquzImageLoader } from "Models/file-loader/image-loader";
import GalleryQuestion from "../gallery";
import { Translator } from "@/utils/translate";

class FirstClickTestQuestion extends GalleryQuestion {
  constructor(controller, config) {
    super(controller, config);

    this.type = FIRST_CLICK_TEST;
    this.translator = Translator("question");

    this.updating = ko.observable(false);

    // Image properties
    this.imageFile = ko.observable(null);
    this.imagePreview = ko.observable("");
    this.clickAreas = ko.observableArray([]);

    // First Click Test settings
    this.mobileDisplay = ko.observable("width"); // "width" or "height"
    this.minClicks = ko.observable(1);
    this.maxClicks = ko.observable("");
    this.displayTime = ko.observable("");
    this.buttonText = ko.observable("");
    this.allowClickCancel = ko.observable(false);

    // Standard options
    this.skipOption = ko.observable(false);
    this.skipText = ko.observable("");
    this.commentOption = ko.observable(false);

    this.changeClickAreasButtonText = ko.pureComputed(() => {
      const areasLength = this.clickAreas().length;

      if (areasLength === 0) {
        return ko.unwrap(this.translator.t("Добавить области клика"));
      }

      return ko.unwrap(this.translator.t("Изменить области клика")) + " (" + areasLength + ")";
    });

    this.initializeImageLoader();
    this.setupValidation();
    this.setupSubscriptions();
  }

  initializeImageLoader() {
    this.imageLoader = new FoquzImageLoader(this.imagePreview, {
      presets: ["imageWithSvg"],
      errors: {
        format: "Можно загружать файлы форматов: jpg, jpeg, png, gif, svg",
      },
    });

    this.imageLoader.on("select", ({ file }) => {
      this.imageLoader.updatePreview(file);
      this.handleImageSelect(file);
    });

    this.imageLoader.on("preview", ({ url }) => {
      this.imagePreview(url);
    });

    // Add remove method for media-load-button
    this.imageLoader.remove = () => {
      this.removeImage();
    };
  }

  setupValidation() {
    // Min clicks validation
    this.minClicks.extend({
      required: {
        params: true,
        message: "Минимальное количество кликов обязательно",
      },
      min: {
        params: 1,
        message: "Минимальное количество кликов должно быть больше 0",
      },
      max: {
        params: 999,
        message: "Максимальное значение: 999",
      },
    });

    // Max clicks validation
    this.maxClicks.extend({
      validation: {
        validator: (value) => {
          if (!value) return true; // Optional field
          const min = parseInt(this.minClicks()) || 1;
          const max = parseInt(value);
          return max >= min;
        },
        message: "Максимальное количество кликов должно быть >= минимального",
      },
    });
  }

  setupSubscriptions() {
    // Clear max clicks if less than min
    this.minClicks.subscribe((newMin) => {
      const max = parseInt(this.maxClicks());
      if (max && max < newMin) {
        this.maxClicks("");
      }
    });

    // Subscribe to changes for onChange event
    [
      this.mobileDisplay,
      this.minClicks,
      this.maxClicks,
      this.displayTime,
      this.buttonText,
      this.allowClickCancel,
      this.skipOption,
      this.skipText,
      this.commentOption,
    ].forEach((f) =>
      f.subscribe(() => {
        if (this.updating()) return;
        this.onChange();
      })
    );
  }

  handleImageSelect(file) {
    this.imageFile(file);
    this.clickAreas([]); // Clear existing areas when new image is selected

    // Upload through inherited gallery system
    this.loadByFile(file);

    if (!this.updating()) {
      this.onChange();
    }
  }

  removeImage() {
    this.imageFile(null);
    this.imagePreview("");
    this.clickAreas([]);
    this.imageLoader.preview("");

    // Clear gallery using inherited method
    this.galleryController.variants([]);

    if (!this.updating()) {
      this.onChange();
    }
  }

  openFullscreen() {
    // Open image in fullscreen using existing fancybox functionality
    if (this.imagePreview()) {
      $.fancybox.open({
        src: this.imagePreview(),
        type: "image",
      });
    }
  }

  openClickAreasDialog() {
    // This will be implemented later when we create the sidesheet
  }

  updateData(data) {
    console.log("1-click-test: updateData", data);
    this.updating(true);
    super.updateData(data);

    // Image data
    if (data.imageFile) this.imageFile(data.imageFile);
    if (data.clickAreas) this.clickAreas(data.clickAreas || []);
    if (data.gallery && data.gallery.length > 0) {
      this.imagePreview(data.gallery[0].preview);
    }

    // First Click settings
    if (data.mobileDisplay !== undefined)
      this.mobileDisplay(data.mobileDisplay);
    if (data.minClicks !== undefined) this.minClicks(data.minClicks);
    if (data.maxClicks !== undefined) this.maxClicks(data.maxClicks);
    if (data.displayTime !== undefined) this.displayTime(data.displayTime);
    if (data.buttonText !== undefined) this.buttonText(data.buttonText);
    if (data.allowClickCancel !== undefined)
      this.allowClickCancel(data.allowClickCancel);

    // Standard options
    if (data.skipOption !== undefined) this.skipOption(data.skipOption);
    if (data.skipText !== undefined) this.skipText(data.skipText);
    if (data.commentOption !== undefined)
      this.commentOption(data.commentOption);

    setTimeout(() => {
      this.updating(false);
    }, 100);
  }

  getData() {
    const data = {
      ...super.getData(),

      // Image data
      imageFile: this.imageFile(),
      imagePreview: this.imagePreview(),
      clickAreas: this.clickAreas().map((area) => ({
        name: area.name,
        x: area.x,
        y: area.y,
        width: area.width,
        height: area.height,
      })),

      // First Click settings
      mobileDisplay: this.mobileDisplay(),
      minClicks: parseInt(this.minClicks()) || 1,
      maxClicks: this.maxClicks() ? parseInt(this.maxClicks()) : "",
      displayTime: this.displayTime() ? parseInt(this.displayTime()) : "",
      buttonText: this.buttonText() || "",
      allowClickCancel: this.allowClickCancel(),

      // Standard options
      skipOption: this.skipOption(),
      skipText: this.skipText() || "",
      commentOption: this.commentOption(),
    };

    console.log("1-click-test: getData", data);

    return data;
  }

  isValid() {
    if (!super.isValid()) return false;

    // Image validation - check either imageFile or gallery (inherited validation)
    if (!this.imageFile() && this.galleryController.variants().length === 0) {
      return false;
    }

    // Min clicks validation
    const minClicks = parseInt(this.minClicks());
    if (!minClicks || minClicks < 1) {
      return false;
    }

    // Max clicks validation
    const maxClicks = this.maxClicks() ? parseInt(this.maxClicks()) : null;
    if (maxClicks && maxClicks < minClicks) {
      return false;
    }

    return true;
  }

  dispose() {
    super.dispose();
    if (this.imageLoader) {
      this.imageLoader.dispose();
    }
  }
}

export default FirstClickTestQuestion;
