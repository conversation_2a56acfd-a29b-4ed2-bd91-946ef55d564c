<template id="question-form-template-text">
  <!-- ko if: question.maskController.noMask  -->
  <div class="hat-radio-group hat-radio-group--dense survey-question__variants-type-selector">
    <!-- ko foreach: { data: question.fieldTypes, as: 'fieldType' } -->
    <div class="hat-radio-group__radio" data-bind="let: { inputId: 'survey-question-variants-type-selector-' + question.unique + '-' + fieldType.id }">
      <input class="hat-radio-group__radio-input" type="radio" data-bind="
                value: fieldType.id,
                disable: question.isFullBlocked,
                checked: question.fieldType,
                attr: { id: inputId, name: 'text-field-type' + question.unique }" />
      <label class="hat-radio-group__radio-label" data-bind="attr: { 'for': inputId }, click: function() {
              if (question.isFullBlocked) return false;

              else return true;
          }">
        <i class="survey-question__variants-type-selector-value-icon" data-bind="class: 'survey-question__variants-type-selector-value-' + fieldType.icon + '-icon'"></i>
        <!-- ko text: fieldType.label -->
        <!-- /ko -->
      </label>
    </div>
    <!-- /ko -->
  </div>
  <!-- /ko -->

  <!-- ko if: question.isMultiline -->
  <text-field-config params="field: question.fieldController, formControlErrorStateMatcher: question.formControlErrorStateMatcher, intervalText: question.translator.t('Допустимое количество символов в ответе'), disabled: question.isFullBlocked"></text-field-config>
  <!-- /ko -->

  <!-- ko ifnot: question.isMultiline -->
  <masked-field-config params="mask: question.maskController, formControlErrorStateMatcher: question.formControlErrorStateMatcher, disabled: question.isFullBlocked" ></masked-field-config>
  <!-- /ko -->

  <hr class="mx-0 my-4">

  <div>
    <div class="form-group">
      <fc-switch params="checked: question.skip, label: $translator.t('Пропуск ответа'), disabled: question.isFullBlocked"></fc-switch>
    </div>

    <!-- ko template: {
   foreach: templateIf(question.skip(), $data),
   afterAdd: slideAfterAddFactory(400),
   beforeRemove: slideBeforeRemoveFactory(400)
} -->
    <div class="form-group">
      <fc-label params="text: $translator.t('Текст'), hint: $translator.t('Текст')"></fc-label>
      <fc-input
          params="
                    value: question.skipText,
                    counter: true,
                    maxlength: 125,
                    placeholder: $translator.t('Затрудняюсь ответить'),
                    disabled: question.isFullBlocked,
                "
      ></fc-input>
    </div>

    <!-- /ko -->
  </div>

  <hr class="mt-0">
  <div class="pt-5p link-field-wrapper" data-bind="component: {
      name: 'link-controller-block',
      params: {
        controller: question.linkController,
        disabled: question.isFullBlocked || !(question.isPaidRate || question.isContactsEnabled),
      }
  }"></div>
</template>
<script setup lang="ts">
</script>
