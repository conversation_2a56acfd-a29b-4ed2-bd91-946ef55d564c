import Question from "../../question";
import { MaskedFieldConfig } from "Models/masked-field-config";
import { TextFieldConfig } from "Models/text-field-config";
import { LinkController } from "../../../controllers/link-controller";
import { TEXT_QUESTION } from "Data/question-types";
import "Components/input/masked-field-config";
import { Translator } from "@/utils/translate";

const FIELD_TYPE_SINGLE_LINE = "0";
const FIELD_TYPE_MULTIPLE_LINE = "1";

const QuestionTranslator = Translator("question");

class TextQuestion extends Question {
  constructor(controller, config) {
    super(controller, config);

    this.type = TEXT_QUESTION;
    this.translator = QuestionTranslator;

    this.updating = ko.observable(false);

    this.fieldTypes = [
      {
        id: FIELD_TYPE_SINGLE_LINE,
        label: QuestionTranslator.t("Однострочное")(),
        icon: "oneline",
      },
      {
        id: FIELD_TYPE_MULTIPLE_LINE,
        label: QuestionTranslator.t("Многострочное")(),
        icon: "multiline",
      },
    ];

    this.fieldType = ko.observable(FIELD_TYPE_SINGLE_LINE);

    this.isMultiline = ko.pureComputed(() => {
      return this.fieldType() == FIELD_TYPE_MULTIPLE_LINE;
    });

    this.maskController = new MaskedFieldConfig();
    this.maskController.link(this.required);
    this.fieldController = new TextFieldConfig();

    this.isMultiline.subscribe((v) => {
      this.fieldController.range([0, v ? 500 : 250]);
    });

    this.skip = ko.observable(false);
    this.skipText = ko.observable("");

    this.linkController = new LinkController(this);

    this.fieldType.subscribe(() => {
      if (this.updating()) return;
      this.onChange();
    });
    this.maskController.on("change", () => {
      if (this.updating()) return;
      
      this.onChange();
    });
    this.fieldController.on("change", () => {
      if (this.updating()) return;
      this.onChange();
    });

    [
      this.skip,
      this.skipText
    ].forEach((f) => f.subscribe((v) => this.onChange()));
  }

  updateData(data) {
    this.updating(true);
    super.updateData(data);

    this.linkController.updateData(data);

    if (data.isMultiline) {
      this.fieldType(FIELD_TYPE_MULTIPLE_LINE);
      this.fieldController.updateData({
        placeholder: data.placeholder,
        range: data.lengthRange,
      });
    } else {
      this.fieldType(FIELD_TYPE_SINGLE_LINE);
      this.maskController.updateData({
        type: data.maskType,
        config: data.maskConfig,
        placeholder: data.placeholder,
        range: data.lengthRange,
      });
    }

    this.skip(!!data.skip);
    this.skipText(data.skipText || "");

    setTimeout(() => {
      this.updating(false);
    }, 100);
  }

  getData() {
    let data = super.getData();

    data = {
      ...data,
      ...this.linkController.getData(),
      isMultiline: this.isMultiline(),
    };

    if (this.isMultiline()) {
      let fieldData = this.fieldController.getData();
      data.maskType = 0;
      data.maskConfig = {};
      data.placeholder = fieldData.placeholder;
      data.lengthRange = fieldData.range;
    } else {
      let maskData = this.maskController.getData();
      data.maskType = maskData.type;
      data.maskConfig = maskData.config;
      data.placeholder = maskData.placeholder;
      data.lengthRange = maskData.range;
    }

    data.skip = this.skip();
    data.skipText = this.skipText();

    return data;
  }

  isValid() {
    if (!super.isValid()) return false;

    if (this.isMultiline()) return true;

    return this.maskController.isValid();
  }
}

export default TextQuestion;
