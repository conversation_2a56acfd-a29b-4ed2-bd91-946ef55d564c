<?= $this->render('_components.php'); ?>



<template id="question-form-template">
  <!-- ko let: { $translator: controller.translator }-->
  <div class="question-form" data-bind="let: {
    question: controller.question(),
  }, class: controller.question() ? 'question-form--' + controller.question().type : '',
  css: {
    'question-form--blocked': controller.isBlocked
  }">
    <!-- ko if: controller.mode !== "cpoint" && question.isSystem -->
    <!-- ko if: question.needRequiredSetting -->
    <div class="form-group switch-form-group">
      <label class="switch form-control">
        <input type="checkbox" data-bind="checked: question.required, disable: controller.isBlocked">
        <span class="switch__slider"></span>
      </label>
      <label class="form-label" data-bind="text: $translator.t('Обязательный')"></label>
    </div>
    <!-- /ko -->

    <!-- ko if: question.type == 16 -->
    <div class="form-group">
      <label class="form-label" for="type" data-bind="text: $translator.t('Тип экрана')"></label>

      <button
        class="btn-question"
        data-bind="
          tooltip,
          tooltipPlacement: 'top',
          tooltipText: $translator.t('Можно изменить тип вопроса, выбрав пункт в раскрывающемся списке. Каждому типу вопроса соответствует свой набор настроек. При смене типа вопроса несовместимые настройки не сохраняются')
        "
        type="button"
        tabindex="10"
      ></button>

      <div class="select2-wrapper question-form__question-type-select" data-bind="click: function() {
              if (question.isBlocked()) {
                if (!controller.isBlocked) question.tryChangeBlockedParam();
              }
            }">

        <select class="block-type-select" data-bind="
              value: question.blockType,
              disable: question.isBlocked() || controller.isBlocked,
              lazySelect2: {
                  containerCssClass: 'form-control',
                  wrapperCssClass: 'question-type-select select2-container--form-control',
              }">

          <option value="start" data-bind="disable: question.disableStartScreen && question.blockType() !== 'start', text: $translator.t('Стартовый экран')"></option>
          <option value="text" data-bind="text: $translator.t('Текстовый блок')"></option>
          <option value="end" data-bind="text: $translator.t('Конечный экран')"></option>
        </select>
      </div>
    </div>
    <!-- /ko -->
    <!-- /ko -->



    <!-- ko if: controller.mode == "cpoint" || !question.isSystem -->
    <div class="row">
      <div class="col-6">
        <div class="form-group survey-question__type-form-group">

          <label class="form-label" for="type" data-bind="text: $translator.t('Тип вопроса')"></label>

          <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Можно изменить тип вопроса, выбрав пункт в раскрывающемся списке. Каждому типу вопроса соответствует свой набор настроек. При смене типа вопроса несовместимые настройки не сохраняются')" type="button" title="" tabindex="10">
          </button>

          <!-- ko if: $translator.loaded -->
          <div class="select2-wrapper question-form__question-type-select" data-bind="click: function() {

            if (question.isBlocked()) {
              if (!controller.isBlocked) question.tryChangeBlockedParam('type');
            } else if (controller.recipients && controller.recipients().length) {
              controller.openDonorTypeModal();
            }
          }">



            <type-select params="value: $component.type,
              disabled: question.isBlocked() || controller.isBlocked || controller.recipients && controller.recipients().length,
              disableStartScreen: controller.disableStartScreen,
              disableFilials: !controller.filials().length,
              disableClassifier: !controller.collections().length,
              onSelect: function(v) {
                  $component.setType(v);
                },"></type-select>


          </div>
          <!-- /ko -->

        </div>
      </div>

      <!-- ko if: question.type == 13 -->
      <div class="col-6">
        <div class="form-group">
          <fc-label params="text: 'Тип матрицы', hint: 'Тип матрицы'"></fc-label>
          <fc-select params="options: [
              { id: 'standart', text: 'Стандартная' },
              { id: 'marker', text: 'Маркерное отображение' },
            ], value: question.matrixType, disabled: question.isFullBlocked"></fc-select>
        </div>
      </div>
      <!-- /ko -->

      <!-- ko if: question.type == 12 -->
      <div class="col-6">
        <div class="form-group" data-bind="click: function() {
          if (question.isBlocked()) {
                if (!controller.isBlocked) question.tryChangeBlockedParam();
              }
        }">
          <fc-label params="text: 'Тип рейтинга', hint: 'Тип рейтинга'"></fc-label>
          <fc-select params="options: [
              { id: 'standart', text: 'Стандартный' },
              { id: 'variants', text: 'Рейтинг для вариантов' },
            ], value: question.ratingType,
            disabled:  question.isBlocked() || controller.isBlocked "></fc-select>
        </div>
      </div>
      <!-- /ko -->

      <!-- ko if: question.type == 20 -->
      <div class="col-6">
        <div class="form-group" data-bind="click: function() {
            if (question.isBlocked()) {
                  if (!controller.isBlocked) question.tryChangeBlockedParam();
                }
          }">
          <fc-label params="text: 'Тип шкалы', hint: 'Тип шкалы'"></fc-label>
          <fc-select params="options: [
                { id: 'standart', text: 'Стандартная' },
                { id: 'variants', text: 'Шкала для вариантов' },
              ], value: question.scaleType,
              disabled:  question.isBlocked() || controller.isBlocked
              "></fc-select>
        </div>
      </div>
      <!-- /ko -->

      <!-- ko if: question.needRequiredSetting -->
      <div class="col-6 d-flex flex-column justify-content-end" style="padding-bottom: 12px;">

        <div class="form-group switch-form-group">
          <label class="switch form-control">
            <input type="checkbox" data-bind="checked: question.required, disable: controller.isBlocked">
            <span class="switch__slider"></span>
          </label>

          <label class="form-label" data-bind="css: {
            'form-label_checked': question.required(),
          }, text: $translator.t('Обязательный')"></label>
        </div>
      </div>
      <!-- /ko -->

      <!-- ko if: question.type == 16 -->
      <div class="col">
        <div class="form-group">
          <label class="form-label" for="type" data-bind="text: $translator.t('Тип экрана')"></label>

          <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Можно изменить тип вопроса, выбрав пункт в раскрывающемся списке. Каждому типу вопроса соответствует свой набор настроек. При смене типа вопроса несовместимые настройки не сохраняются')" type="button" title="" tabindex="10">
          </button>

          <div class="select2-wrapper question-form__question-type-select" data-bind="click: function() {
              if (question.isBlocked()) {
                if (!controller.isBlocked) question.tryChangeBlockedParam();
              }
            }">


            <select class="block-type-select" data-bind="
              value: question.blockType,
              valueAllowUnset: true,
              disable: question.isBlocked() || controller.isBlocked,
              lazySelect2: {
                  containerCssClass: 'form-control',
                  wrapperCssClass: 'select2-container--form-control',
              }
          ">
              <option
                value="start"
                data-bind="
                  disable: question.disableStartScreen() && question.blockType() !== 'start',
                  text: $translator.t('Стартовый экран')
                "
              ></option>
              <option value="text" data-bind="text: $translator.t('Текстовый блок')"></option>
              <option value="end" data-bind="text: $translator.t('Конечный экран')"></option>
              <option value="five-second-test" data-bind="text: $translator.t('Тест 5 секунд')"></option>
            </select>
          </div>
        </div>
      </div>
      <!-- /ko -->
    </div>
    <!-- /ko -->

    <!-- ko if: question.type == 16 -->
    <div class="row">
      <!-- ko template: {
        foreach: templateIf(question.blockType() == 'text' || question.blockType() == 'five-second-test', $data),
        afterAdd: fadeAfterAddFactory(400),
        beforeRemove: fadeBeforeRemoveFactory(400)
      } -->

      <div class="col-6 d-flex">
        <div class="switch-form-group">
          <switch params="checked: question.showNumber, disabled: controller.isBlocked">
            <span data-bind="text: $translator.t('Отображать номер вопроса')"></span>
          </switch>
        </div>
      </div>
      <!-- /ko -->

      <!-- ko if: question.blockType() == 'five-second-test' -->
      <div class="col-6 d-flex">
        <div class="switch-form-group">
          <switch params="checked: question.required, disabled: controller.isBlocked">
            <span data-bind="text:$translator.t('Обязательный')"></span>
          </switch>
        </div>
      </div>
      <!-- /ko -->
    </div>
    <!-- /ko -->

    <!-- ko if: isAuto || !question.pointId() -->
    <div class="form-group">
      <div class="d-flex justify-content-between">
        <label class="form-label" for="name">
          <span data-bind="text: isAuto ? $translator.t('Точка контакта') : $translator.t('Служебное название')"></span>
          <button class="btn-question" data-bind="
              tooltip,
              tooltipPlacement: 'top',
              tooltipText: $translator.t('Краткое название вопроса. Используется для идентификации вопроса в разделах внутри опроса: &quot;Ответы&quot; и &quot;Статистика&quot;'),
            " type="button" title="" tabindex="10"></button>
        </label>
        <span class="f-color-service f-fs-1" data-bind="text: $translator.t('необязательное')"></span>
      </div>

      <div class="chars-counter chars-counter--type_input" data-bind="charsCounter, charsCounterCount: question.alias().length">
        <input class="form-control" data-bind="
            textInput: question.alias,
            disable: controller.isBlocked || (question.isAuto && controller.mode !== 'cpoint') || question.isFullBlocked,
            css: {
                'is-invalid': controller.formControlErrorStateMatcher(question.alias),
                'is-valid': controller.formControlSuccessStateMatcher(question.alias),
            }" id="alias" maxlength="60">


        <div class="chars-counter__value"></div>
      </div>

      <!-- ko if: controller.formControlErrorStateMatcher(question.alias) -->
      <div class="form-error" data-bind="text: question.alias.error()"></div>
      <!-- /ko -->
    </div>
    <!-- /ko -->

    <!-- ko template: {
      foreach: templateIf(question.type != 16 || question.showNumber(), $data),
      afterAdd: fadeAfterAddFactory(400),
      beforeRemove: fadeBeforeRemoveFactory(400)
    } -->
    <div class="form-group">
      <div class="d-flex justify-content-between">
        <label class="form-label" for="name">
          <span data-bind="text: $translator.t('Название вопроса')"></span>
          <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Можно ввести краткое название вопроса, которое будет отображаться рядом с номером вопроса, например: Блюдо')" type="button" title="" tabindex="10">
          </button>
        </label>
        <span class="f-color-service f-fs-1" data-bind="text: $translator.t('необязательное')"></span>
      </div>

      <div class="chars-counter chars-counter--type_input" data-bind="charsCounter, charsCounterCount: question.name().length">
        <input class="form-control" data-bind="
            textInput: question.name,
            css: {
              'is-invalid': controller.formControlErrorStateMatcher(question.name),
              'is-valid': controller.formControlSuccessStateMatcher(question.name)
            },
            disable: controller.isBlocked" id="question-main-title" maxlength="100">

        <div class="chars-counter__value"></div>

      </div>

      <validation-feedback params="show: controller.formControlErrorStateMatcher(question.name), text: question.name.error"></validation-feedback>
    </div>

    <!-- ko if: question.type == 13 -->
    <div class="mb-4" data-bind="visible: !question.isFullBlocked">
      <span class="f-fs-1-5 mb-1 f-color-primary semibold" type="button" data-bind="
      click: function() {
        if(!question.isFullBlocked) {
          question.matrix.setKano()
        }
      }">Заполнить поля вопроса по модели Кано </span>
      <button class="btn-question" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Столбцы будут заменены на шкалу эмоциональных реакций. Введите в строки названия объектов оценки')" type="button" title="" tabindex="10">
      </button>
      <div class="f-color-service f-fs-1">Текущие настройки полей сбросятся</div>
    </div>

    <!-- /ko -->

    <!-- /ko -->

    <!-- ko ifnot: question.type == 16 -->
    <div class="form-group">
      <label class="form-label" for="description" data-bind="text: $translator.t('Текст вопроса')"></label>

      <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Вопрос, который задаётся респонденту')" type="button" title="">
      </button>
      <ckeditor params="
          value: question.description,
          variables: [],
          toolbar: ['bold', 'underline', 'italic', 'strikethrough', 'alignment:left', 'alignment:center', 'alignment:right', 'alignment:justify', 'fontSize'],
          execute: { 'alignment': 'center' },
          options: {
            fontSize: [16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42],
          },
          disabled: question.isFullBlocked,
          error: controller.formControlErrorStateMatcher(question.description)() ? question.description.error : ''
        " data-bind="
          css: {
            'is-invalid': controller.formControlErrorStateMatcher(question.description)
          }
        "></ckeditor>
    </div>
    <!-- /ko -->

    <!-- ko ifnot: question.type == 16 -->
    <div class="form-group subdescription-text-editor">
      <fc-label params="text: 'Дополнительное описание', hint: 'Дополнительное описание', optional: true"></fc-label>
      <ckeditor
        params="
          value: question.subdescription,
          variables: [],
          toolbar: ['bold', 'underline', 'italic', 'strikethrough', 'alignment:left', 'alignment:center', 'alignment:right', 'alignment:justify', 'fontSize'],
          execute: { 'alignment': 'center' },
          options: {
            fontSize: [16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42],
          },
          disabled: question.isFullBlocked,
          error: controller.formControlErrorStateMatcher(question.subdescription)() ? question.subdescription.error : ''
        "
        data-bind="
          css: {
            'is-invalid': controller.formControlErrorStateMatcher(question.subdescription)
          }
        "
      ></ckeditor>
    </div>
    <!-- /ko -->

    <!-- ko let: { template: getTemplate(question) } -->

    <!-- ko template: {
          name: template,
          afterAdd: fadeAfterAddFactory(200, 200),
          beforeRemove: fadeBeforeRemoveFactory(200)
      } -->
    <!-- /ko -->

    <!-- ko if: !question.isSystem && controller.poll && controller.poll.dictionary_id -->
    <hr class="mx-0">
    <div class="hide-question mt-4">
      <div class="row">
        <div class="col-auto" style="min-width: 50%">
          <div class="switch-form-group">
            <label
              class="switch form-control"
              data-bind="
                click: function() {
                  return controller.onDictionaryConnectionSwitchClick(controller);
                },
              "
            >
              <input
                type="checkbox"
                data-bind="
                  checked: question.enableDictionaryConnection,
                  disable: controller.isBlocked || controller.dictionaryConnectionSwitchDisabled,
                "
              />
              <span class="switch__slider"></span>
            </label>
            <label
              class="form-label"
              data-bind="
                click: function() {
                  return controller.onDictionaryConnectionSwitchClick(controller);
                },
                css: {
                  'form-label_checked': question.enableDictionaryConnection,
                },
                text: `Связка вопроса с элементом справочника «${POLL.dictionary_name}»`,
              "
            ></label>
            <button
              class="btn-question"
              tabindex="10"
              data-bind="
                tooltip,
                tooltipPlacement: 'top',
                tooltipText: `Связка вопроса с элементом справочника «${POLL.dictionary_name}»`,
              "
              type="button"
            ></button>
          </div>
        </div>
        <!-- ko template: {
          foreach: templateIf(question.enableDictionaryConnection(), $data),
          afterAdd: fadeAfterAddFactory(200),
          beforeRemove: fadeBeforeRemoveFactory(200),
        } -->
        <div class="col-auto flex-grow-1 mt-15p" style="width: 50%; position: relative">

          <fc-select
            params="
              options: question.controller.dictionary().elements,
              value: question.dictionaryElementId,
              multiple: false,
              placeholder: 'Выберите элемент справочника',
              childrenProp: 'elements',
              disableFoldersSelect: true,
              optionTextProp: 'title',
            "
          ></fc-select>
          <div
            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; cursor: pointer; z-index: 1000"
            data-bind="
              visible: !controller.dictionaryLoaded(),
              click: async function(_, e) {
                ko.dataFor(e.target.parentElement.querySelector('.fc-select > *')).loading(true);
                ko.dataFor(e.target.parentElement.querySelector('.fc-select > *')).openList();
                await controller.loadDictionary();
                ko.dataFor(e.target.parentElement.querySelector('.fc-select > *')).loading(false);
              },
            "
          ></div>
        </div>
        <!-- /ko -->
      </div>
    </div>
    <!-- /ko -->

    <!-- ko if: !question.isSystem && question.type != 16 -->
    <hr class="mx-0">

    <div class="hide-question mt-4">
      <div class="row">
        <div class="col-auto" style="min-width: 50%">
          <div class="form-group switch-form-group">
            <label class="switch form-control">
              <input type="checkbox" data-bind="checked: question.dontShowIfAnswered, disable: controller.isBlocked">
              <span class="switch__slider"></span>
            </label>
            <label class="form-label" data-bind="css: {
                  'form-label_checked': question.dontShowIfAnswered
                }, text: $translator.t('Не отображать вопрос, если ответ уже был получен')"></label>

            <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Если ответ от респондента на вопрос был получен, то при следующем прохождении опроса этот вопрос не будет отображаться')" type="button"></button>
          </div>
        </div>

        <div class="col-auto flex-grow-1" style="width: 50%">
          <!-- ko template: {
                foreach: templateIf(question.dontShowIfAnswered(), $data),
                afterAdd: fadeAfterAddFactory(200),
                beforeRemove: fadeBeforeRemoveFactory(200)
              } -->
          <div>
            <!-- ko ifnot: controller.isBlocked -->
            <div class="d-flex align-items-center">
              <button class="btn btn-default hide-question__reset-answers" data-bind="click: question.resetAnswers, text: $translator.t('Сбросить полученные ответы')"></button>

              <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Эта функция меняет дату проверки, с которой искать ответ респондента на вопрос, на текущую')" type="button">
              </button>
            </div>
            <!-- /ko -->

            <div class="hide-question__reset-note">
              <span data-bind="text: $translator.t('Новые ответы будут собираться с')"></span>
              <span data-bind="text: question.answersFrom"></span>
            </div>
          </div>


          <!-- /ko -->
        </div>
      </div>
    </div>
    <!-- /ko -->

    <!-- /ko -->
  </div>
  <!-- /ko -->
</template>



<?= $this->render('./models/types/rate/template.php'); ?>
<?= $this->render('./models/types/items/template.php'); ?>
<?= $this->render('./models/types/variants/template.php'); ?>
<?= $this->render('./models/types/text/template.php'); ?>
<?= $this->render('./models/types/date/template.php'); ?>
<?= $this->render('./models/types/address/template.php'); ?>
<?= $this->render('./models/types/file/template.php'); ?>
<?= $this->render('./models/types/quiz/template.php'); ?>
<?= $this->render('./models/types/priority/template.php'); ?>
<?= $this->render('./models/types/media-variants/template.php'); ?>
<?= $this->render('./models/types/gallery/template.php'); ?>
<?= $this->render('./models/types/smile/template.php'); ?>
<?= $this->render('./models/types/nps/template.php'); ?>
<?= $this->render('./models/types/matrix/template.php'); ?>
<?= $this->render('./models/types/matrix-3d/template.php'); ?>
<?= $this->render('./models/types/diff/template.php'); ?>
<?= $this->render('./models/types/stars/template.php'); ?>
<?= $this->render('./models/types/star-variants/template.php'); ?>
<?= $this->render('./models/types/rating/template.php'); ?>
<?= $this->render('./models/types/classifier/template.php'); ?>
<?= $this->render('./models/types/inter/template.php'); ?>
<?= $this->render('./models/types/scale/template.php'); ?>
<?= $this->render('./models/types/distribution-scale/template.php'); ?>
<?= $this->render('./models/types/card-sorting/template.php'); ?>
<?= $this->render('./models/types/first-click-test/template.php'); ?>
