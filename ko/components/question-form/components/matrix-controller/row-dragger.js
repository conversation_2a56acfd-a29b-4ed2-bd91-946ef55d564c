function createRowGhost(rowId, rowHeader, helper) {
  let ghostContainer = document.createElement("div");
  ghostContainer.classList.add("matrix-ghost", "matrix-ghost-row");
  let ghostHeader = rowHeader.cloneNode(true);
  ghostHeader.style.width = `${rowHeader.offsetWidth}px`;
  ghostHeader.classList.add("matrix-ghost-row__header");
  ghostContainer.appendChild(ghostHeader);

  let ghostTable = document.createElement("table");
  let ghostTBody = document.createElement("tbody");

  let row = helper.getRow(rowId);
  let ghostRow = row.cloneNode(true);
  ghostTable.style.width = `${row.offsetWidth}px`;
  ghostTBody.appendChild(ghostRow);
  ghostTable.appendChild(ghostTBody);
  ghostContainer.appendChild(ghostTable);

  ghostContainer.style.position = "absolute";
  ghostContainer.style.top = "-1500px";

  document.body.appendChild(ghostContainer);

  return ghostContainer;
}

// Начало перетаскивания
function onDragStart(event, helper) {
  let rowHeader = event.currentTarget;
  event.dataTransfer.effectAllowed = "move";

  // ID перетаскиваемого ряда
  let rowId = helper.getRowId(rowHeader);
  event.dataTransfer.setData("rowId", rowId);

  // Копия ряда, которая будет около курсора
  const rowGhost = createRowGhost(rowId, rowHeader, helper);
  event.dataTransfer.setDragImage(rowGhost, 0, 0);

  // Ряд в таблице
  let row = helper.getRow(rowId);
  row.classList.add("matrix-row--dragging");

  let rowDelete = helper.getRowDelete(rowId);
  rowDelete.classList.add("matrix-row-delete--dragging");

  rowHeader.classList.add("matrix-row-header--dragging");
}

// Прохождение курсора над заголовком ряда
function onDragOver(event, helper) {
  if (event.preventDefault) {
    event.preventDefault();
  }

  const activeRowHeader = document.querySelector(
    ".matrix-row-header--dragging"
  );
  const targetRowHeader = event.currentTarget;

  if (activeRowHeader === targetRowHeader) {
    return;
  }

  const targetCoords = targetRowHeader.getBoundingClientRect();
  const targetCenter = targetCoords.y + targetCoords.height / 2;
  const cursorPosition = event.clientY;

  let targetRowId = helper.getRowId(targetRowHeader);

  let activeRow = document.querySelector(".matrix-row--dragging");
  let targetRow = helper.getRow(targetRowId);

  let activeRowDelete = document.querySelector(".matrix-row-delete--dragging");
  let targetRowDelete = helper.getRowDelete(targetRowId);

  if (cursorPosition >= targetCenter) {
    if (activeRowHeader === targetRowHeader.nextElementSibling) return;

    targetRowHeader.parentNode.insertBefore(
      activeRowHeader,
      targetRowHeader.nextSibling
    );
    targetRow.parentNode.insertBefore(activeRow, targetRow.nextSibling);
    targetRowDelete.parentNode.insertBefore(
      activeRowDelete,
      targetRowDelete.nextSibling
    );
  } else {
    if (activeRowHeader === targetRowHeader.previousElementSibling) return;

    targetRowHeader.parentNode.insertBefore(activeRowHeader, targetRowHeader);
    targetRow.parentNode.insertBefore(activeRow, targetRow);
    targetRowDelete.parentNode.insertBefore(activeRowDelete, targetRowDelete);
  }

  event.dataTransfer.dropEffect = "move";
}

function onDrop(event, helper) {
  if (event.preventDefault) {
    event.preventDefault();
  }
  if (event.stopPropagation) {
    event.stopPropagation();
  }
}

function updatePoints(newPoints) {
  let rows = document.querySelectorAll(".matrix-table__row");
  console.log("newPoints - ", newPoints);
  rows.forEach((row, rowIndex) => {
    let inputs = row.querySelectorAll('input[type="text"]');
    inputs.forEach((input, colIndex) => {
      if (newPoints[rowIndex] && newPoints[rowIndex][colIndex] !== undefined) {
        input.value = newPoints[rowIndex][colIndex];
      }
    });
  });
}

ko.bindingHandlers.matrixRowHeader = {
  init: (el, valueAccessor) => {
    let matrix = valueAccessor();
    let helper = matrix.domHelper;

    // el.setAttribute("draggable", false);

    el.addEventListener(
      "dragstart",
      (event) => onDragStart(event, helper),
      false
    );

    el.addEventListener(
      "dragover",
      (event) => {
        onDragOver(event, helper);
        return false;
      },
      false
    );

    el.addEventListener(
      "drop",
      (event) => {
        onDrop(event, helper);
      },
      false
    );

    el.addEventListener(
      "dragend",
      (event) => {
        const newPoints = matrix.resortRows();
        updatePoints(newPoints);
        document
          .querySelectorAll(".matrix-row-header--dragging")
          .forEach((el) => el.classList.remove("matrix-row-header--dragging"));
        document
          .querySelectorAll(".matrix-row--dragging")
          .forEach((el) => el.classList.remove("matrix-row--dragging"));
        document
          .querySelectorAll(".matrix-row-delete--dragging")
          .forEach((el) => el.classList.remove("matrix-row-delete--dragging"));

        document.querySelectorAll(".matrix-ghost").forEach((el) => el.remove());
      },
      false
    );
  },
};
