import { reverse as _reverse, sortBy as _sortBy } from "lodash";

import "./column-dragger";
import "./row-dragger";

import html from "./template.html";
import "./style";

import { Translator } from "@/utils/translate";

class MatrixDOMHelper {
  constructor(container) {
    this.container = container;
  }

  get rowsHeadersContainer() {
    return this.container().querySelector(".matrix__rows");
  }

  get rowsHeaders() {
    return [...this.rowsHeadersContainer.querySelectorAll("[data-matrix-row]")];
  }

  get rowsDeleteContainer() {
    return this.container().querySelector(".matrix__delete");
  }

  getRowId(el) {
    return el.dataset.matrixRow;
  }

  getRowHeader(rowId) {
    return this.rowsHeadersContainer.querySelector(
      `[data-matrix-row="${rowId}"]`
    );
  }

  getRowDelete(rowId) {
    return this.rowsDeleteContainer.querySelector(
      `[data-matrix-row="${rowId}"]`
    );
  }

  get columnsTable() {
    return this.container().querySelector(".matrix-table");
  }

  get columnsTableHeaderRow() {
    return this.columnsTable.querySelector(".matrix-table__header-row");
  }

  get columnsTableDeleteRow() {
    return this.columnsTable.querySelector(".matrix-table__delete-row");
  }

  get columnsHeaders() {
    return [
      ...this.columnsTableHeaderRow.querySelectorAll("[data-matrix-column]"),
    ];
  }

  get columnsTableRows() {
    return [
      ...this.columnsTable.querySelectorAll(
        ".matrix-table__row, .matrix-table__delete-row"
      ),
    ];
  }

  getRow(rowId) {
    return this.columnsTable.querySelector(
      `.matrix-table__row[data-matrix-row="${rowId}"]`
    );
  }

  getRowElements(rowId) {
    return [
      ...this.container().querySelectorAll(`[data-matrix-row="${rowId}"]`),
    ];
  }

  getColumnElements(columnId) {
    return [
      ...this.container().querySelectorAll(
        `[data-matrix-column="${columnId}"]`
      ),
    ];
  }

  getColumnInRow(columnId, rowElement) {
    return rowElement.querySelector(`[data-matrix-column="${columnId}"]`);
  }

  getColumnId(el) {
    return el.dataset.matrixColumn;
  }

  getColumnHeader(columnId) {
    return this.columnsTableHeaderRow.querySelector(
      `[data-matrix-column="${columnId}"]`
    );
  }
}

class MatrixControllerViewModel {
  constructor(params) {
    this.translator = Translator("question");
    this.controller = params.controller;
    this.blockRows = params.blockRows;
    this.question = this.controller.question;

    this.isBlocked = this.question.isBlocked();
    this.isFullBlocked = this.question.isFullBlocked;
    this.withPoints = this.question.withPoints;


    this.multipleChoice = ko.observable(this.question.matrix.multipleChoice());
    this.matrixType = ko.observable(this.question.matrixType());

    this.question.matrix.multipleChoice.subscribe((newValue) => {
      this.multipleChoice(newValue);
    });

    this.question.matrixType.subscribe((newValue) => {
      this.matrixType(newValue);
    });



    this.formControlErrorStateMatcher =
      this.question.controller.formControlErrorStateMatcher;
    this.formControlSuccessStateMatcher =
      this.question.controller.formControlSuccessStateMatcher;

    this.container = ko.observable(null);
    this.domHelper = new MatrixDOMHelper(this.container);
    this.hasColumnError = ko.pureComputed(() => {
      return this.controller.columns().some((c) => c.errorMatcher());
    });
    this.hasRowError = ko.pureComputed(() => {
      return this.controller.rows().some((c) => c.errorMatcher());
    });
    this.isSorting = ko.observable(false);

    this.scrollContainer = ko.observable(null);
    this.sortDirection = ko.observable(false);

    this.sortDirection.subscribe((v) => {
      let sorted = _sortBy(this.controller.rows(), (el) => el.label());
      if (v) {
        sorted = _reverse(sorted);
      }
      sorted.forEach((row, index) => {
        this.controller.rows.splice(index, 1, row);
      });
    });
  }

  getPoint(row, col) {
    return this.controller.points[row][col];
  }

  resortColumns() {
    this.isSorting(true);
    const oldOrder = {};

    this.controller.columns().forEach((col, index) => {
      oldOrder[col.id] = index;
    });

    let colsOrder = this.domHelper.columnsHeaders.reduce((acc, col, index) => {
      let colId = this.domHelper.getColumnId(col);
      acc[colId] = index;
      return acc;
    }, {});

    let columns = this.controller
      .columns()
      .slice()
      .sort((a, b) => {
        return colsOrder[a.id] - colsOrder[b.id];
      });

    const newPoints = this.controller.points.map((row) => {
      const newRow = Array(columns.length);
      columns.forEach((col, newIndex) => {
        let oldIndex = oldOrder[col.id];
        newRow[newIndex] = row[oldIndex];
      });
      return newRow;
    });

    columns.forEach((col, index) => {
      this.controller.columns.splice(index, 1, col);
    });

    this.controller.points.splice(
      0,
      this.controller.points.length,
      ...newPoints
    );
    this.isSorting(false);
    return ko.toJS(newPoints);
  }
  resortRows() {
    this.isSorting(true);
    const oldOrder = [];
    this.controller.rows().forEach((row, index) => (oldOrder[row.id] = index));

    let rowsOrder = this.domHelper.rowsHeaders.reduce((acc, row, index) => {
      let rowId = this.domHelper.getRowId(row);
      acc[rowId] = index;
      return acc;
    }, {});

    let rows = this.controller.rows.sorted((a, b) => {
      return rowsOrder[a.id] - rowsOrder[b.id];
    });

    const newPoints = Array(this.controller.points.length);
    rows.forEach((row, newIndex) => {
      let oldIndex = oldOrder[row.id];
      newPoints[newIndex] = this.controller.points[oldIndex];
    });

    rows.forEach((row, index) => {
      this.controller.rows.splice(index, 1, row);
    });

    this.controller.points = newPoints;
    this.isSorting(false);
    return ko.toJS(newPoints);
  }

  highlightColumn(columnId) {
    this.domHelper
        .getColumnElements(columnId)
        .forEach((el) => {
          el.classList.add("selected");
          const standartItems = el.querySelectorAll('.matrix-table__standart');
          standartItems.forEach(item => item.classList.add('select-standart'));
        });
  }

  unhighlightColumn(columnId) {
    this.domHelper
        .getColumnElements(columnId)
        .forEach((el) => {
          el.classList.remove("selected");
          const standartItems = el.querySelectorAll('.matrix-table__standart');
          standartItems.forEach(item => item.classList.remove('select-standart'));
        });
  }

  highlightRow(rowId) {
    this.domHelper
      .getRowElements(rowId)
      .forEach((el) => {
        el.classList.add("selected");
        const standartItems = el.querySelectorAll('.matrix-table__standart');
        standartItems.forEach(item => item.classList.add('select-standart'));
      });
  }

  unhighlightRow(rowId) {
    this.domHelper
      .getRowElements(rowId)
      .forEach((el) => {
        el.classList.remove("selected");
        const standartItems = el.querySelectorAll('.matrix-table__standart');
        standartItems.forEach(item => item.classList.remove('select-standart'));
      });
  }

  // afterAdd(element) {
  //   let duration = this.isSorting() ? 0 : 400;
  //   $(element).hide().delay(0).fadeIn(duration);
  // }

  // beforeRemove(element) {
  //   let duration = this.isSorting() ? 0 : 400;
  //   $(element)
  //     .delay(0)
  //     .fadeOut(duration, () => element.remove());
  // }

  addColumn() {
    let column = this.controller.addColumn();
    let scroll = this.scrollContainer();
    setTimeout(() => {
      scroll.instance.update();
      scroll.instance.scroll({ x: "100%" });
    });

    //this.domHelper.getColumnHeader(column.id).querySelector('input').focus();
  }

  removeColumn(column) {
    this.controller.removeColumn(column);
    this.scrollContainer().updateScroll();
  }

  addRow() {
    let row = this.controller.addRow();
    //this.domHelper.getRowHeader(row.id).querySelector('input').focus();
  }

  onHeaderInput() {
    const lastColumn = document.querySelector(
      ".matrix .matrix-table__header-row > .matrix-header:last-child input"
    );
    if (document.activeElement !== lastColumn) {
      return;
    }
    setTimeout(() => {
      const parent = document.querySelector(".matrix__table .os-viewport");
      parent.scrollTo({ left: parent.scrollWidth + 10 });
    }, 200);
  }

  onInit() {}
}

ko.components.register("matrix-controller", {
  viewModel: MatrixControllerViewModel,
  template: html,
});
