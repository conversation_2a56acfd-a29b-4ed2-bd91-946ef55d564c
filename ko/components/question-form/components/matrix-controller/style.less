.matrix-table {
  &__point {
    padding: 0 5px;

    input {
      padding: 0 15px;
      text-align: center;
      min-width: 100px;
      width: 100%;
    }
  }
  &__standart{
    width: 100%;
    height: 100%;
    padding: 5px;

    &-item{
      background: #F2F5F6;
      border-radius: 4px;
      width: 100%;
      height: 100%;
    }
  }
  .select-standart{
    .matrix-table__standart-item{
      background: #C6CFD3;
    }
  }
}

.matrix.matrix--blocked {
  .matrix-header {
    height: 55px;
  }
}

.matrix__drag-indicator__wrapper--horizontal {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.matrix__drag-indicator__wrapper {
  display: flex;
  align-items: center;

  :first-child {
    margin-right: 10px;
  }
}
