<!-- ko let: { $matrix: $component } -->

<div
  class="matrix"
  data-bind="
        element: container,
        descendantsComplete: $component.onInit.bind($component),

        css: {
          'matrix--has-row-error': $component.hasRowError,
          'matrix--has-column-error': $component.hasColumnError,
          'matrix--blocked': $component.isFullBlocked
        }"
>
  <div class="matrix__rows">
    <div class="matrix-header matrix-header__sort">
      <fc-button
        params="
          label: 'Сортировать строки',
          color: 'primary',
          mode: 'text',
          click: function() {
            $matrix.sortDirection(!$matrix.sortDirection());
          },
          disabled: $matrix.isFullBlocked
        "
      ></fc-button>
      <!-- ko if: $matrix.sortDirection -->
      <svg-icon params="name: 'matrix-sort-inverse'" class="svg-icon--lg"></svg-icon>
      <!-- /ko -->
      <!-- ko ifnot: $matrix.sortDirection -->
      <svg-icon params="name: 'matrix-sort'" class="svg-icon--lg"></svg-icon>
      <!-- /ko -->
    </div>
    <!-- ko foreach: { data: $matrix.controller.rows, as: 'row' } -->
    <div
      class="matrix-row"
      data-bind="attr: { 'data-matrix-row': row.id }, matrixRowHeader: $matrix"
      data-matrix-row-header
    >
      <!-- ko ifnot: $matrix.isFullBlocked -->
      <div
        class="matrix__drag-indicator"
        draggable="true"
        data-bind="style: {
          'visibility': $matrix.controller.rows().length > 1 ? '' : 'hidden'
        }"
      >
        <div class="matrix__drag-indicator__wrapper">
          <div data-bind="text: $index() + 1"></div>
          <i class="icon icon-drag-arrow--light"></i>
        </div>
      </div>
      <!-- /ko -->
      <div class="fc-variant-item__value matrix__name-field">
        <input
          class="form-control"
          type="text"
          data-bind="
            textInput: row.showPrintableLabel() ? row.printableLabel : row.label,
            css: {
              'is-invalid': row.errorMatcher,
            },
            attr: {
              placeholder: $matrix.translator.t(
                `${row.placeholder()} {number}`,
                {
                  number: $index() + 1
                },
              ),
            },
            event: {
              blur: function() {
                $matrix.controller.checkRows();
              },
            },
            disable: $matrix.isFullBlocked || $matrix.blockRows() || Object.keys(question.matrixRowsDictionary() || {}).includes(row.label()),
          "
          maxlength="500"
        />
        <!-- ko if: Object.keys(question.matrixRowsDictionary() || {}).includes(row.label()) || (question.donorVariants || []).find(el => el.dictionaryElementId && el.value() === row.label()) -->
        <div class="fc-input__icons">
          <div
            data-bind="
              component: {
                name: 'fc-icon',
                params: {
                  name: 'dictionary-connection',
                  color: 'light',
                },
              },
              tooltip,
              tooltipText: `Связка варианта с элементом справочника «${question.controller.dictionary().name}»`,
            "
            class="fc-input__append-icon"
          ></div>
        </div>
        <!-- /ko -->
        <!-- ko template: {
            foreach: templateIf(row.errorMatcher(), $data),
            afterAdd: fadeAfterAddFactory(200),
            beforeRemove: fadeBeforeRemoveFactory(200)
          } -->
        <div class="form-error" data-bind="text: row.error()"></div>
        <!-- /ko -->
      </div>
    </div>
    <!-- /ko -->
  </div>

  <div class="matrix__table">
    <div class="matrix-table" data-bind="nativeScrollbar, ref: scrollContainer">
      <table>
        <tbody>
          <tr class="matrix-table__header-row">
            <!-- ko foreach: {
                data: $matrix.controller.columns, as: 'column' } -->
            <th
              class="matrix-header"
              data-bind="attr: { 'data-matrix-column': column.id }, matrixColumnHeader: $matrix"
              data-matrix-column-header
            >
              <!-- ko ifnot: $matrix.isFullBlocked -->
              <div
                class="matrix__drag-indicator"
                draggable="true"
                data-bind="style: {
                  'visibility': $matrix.controller.columns().length > 1 ? '' : 'hidden'
                }"
              >
                <div class="matrix__drag-indicator__wrapper--horizontal">
                  <div data-bind="text: $index() + 1"></div>
                  <i class="icon icon-drag-arrow--light"></i>
                </div>
              </div>
              <!-- /ko -->
              <div
                class="matrix__name-field"
                data-bind="let: { autosizeInput: ko.observable(null) }"
              >
                <input
                  class="form-control text-center"
                  type="text"
                  data-bind="
                    textInput: column.label,
                    css: {
                      'is-invalid': column.errorMatcher,
                    },
                    autosizeInput: autosizeInput,
                    event: {
                      blur: function() {
                        $matrix.controller.checkColumns();
                      },
                      focus: function(_, event) {
                        event.target.scrollIntoView({
                          behavior: 'smooth',
                          block: 'nearest',
                          inline: 'center',
                        });
                      },
                      input: $component.onHeaderInput,
                    },
                    disable: $matrix.isFullBlocked,
                  "
                  maxlength="250"
                />

                <!-- ko template: {
                    foreach: templateIf(column.errorMatcher(), $data),
                    afterAdd: fadeAfterAddFactory(200),
                    beforeRemove: fadeBeforeRemoveFactory(200)
                  } -->
                <div class="form-error" data-bind="text: column.error()"></div>
                <!-- /ko -->
              </div>
            </th>
            <!-- /ko -->
          </tr>

          <!-- ko foreach: { data: $matrix.controller.rows, as: 'row' } -->
          <tr
            class="matrix-table__row"
            data-bind="attr: { 'data-matrix-row': row.id }"
          >
            <!-- ko foreach: { data: $matrix.controller.columns, as: 'column' } -->
            <td
              class="matrix-row"
              data-bind="attr: { 'data-matrix-column': column.id }"
            >
              <!-- ko if: $matrix.withPoints -->
              <div class="matrix-table__point">
                <input
                  type="text"
                  class="form-control"
                  data-bind="textInput: $matrix.getPoint(rowIndex(), columnIndex()), 
                  onlyNumbers: { sign: true }, 
                  disable: $matrix.isFullBlocked, attr: {
                    placeholder: $matrix.translator.t('0 баллов')
                  }"
                  maxlength="9"
                />
                
              </div>
              <!-- /ko -->



              <!-- ko ifnot: $matrix.withPoints -->
                <!-- ko if: $matrix.matrixType() == 'standart' -->
                <div class="matrix-table__standart">
                  <p class="matrix-table__standart-item"></p>
                </div>
                <!-- /ko-->
                <!-- ko if: $matrix.matrixType() == 'marker' -->
                  <!-- ko if: $matrix.multipleChoice() == 0-->
                    <div className="f-radio">
                      <input
                              type="radio"
                              class="f-radio-input"
                              data-bind="attr: { id: 'matrix-radio-' + row.id + '-' + column.id, name: 'matrix-radio-group-' + row.id }, disable: $matrix.isFullBlocked"
                      />
                      <label
                              class="f-radio-label"
                              data-bind="attr: {
                          'for': 'matrix-radio-' + row.id + '-' + column.id
                        }"
                      ></label>
                    </div>
                  <!-- /ko -->
                  <!-- ko if: $matrix.multipleChoice() == 1-->
                    <div class="f-check">
                      <input
                              type="checkbox"
                              class="f-check-input"
                              data-bind="attr: {  id: 'matrix-checkbox-' + row.id + '-' + column.id, name: 'matrix-checkbox-group-' + row.id }, disable: $matrix.isFullBlocked"
                      />

                      <label
                              data-bind="attr: { 'for':  'matrix-checkbox-' + row.id + '-' + column.id }"
                              class="f-check-label"
                      ></label>
                    </div>
                  <!-- /ko -->
                <!-- /ko-->
              <!-- /ko -->
            </td>
            <!-- /ko -->
          </tr>
          <!-- /ko -->

          <!-- ko ifnot: $matrix.isFullBlocked -->
          <tr
            class="matrix-table__delete-row"
            data-bind="if: $matrix.controller.columns().length > 1"
          >
            <!-- ko foreach: { data: $matrix.controller.columns, as: 'column'
               } -->
            <td
              class="cursor-pointer"
              data-bind="attr: { 'data-matrix-column': column.id }, event: {
                'mouseenter': function() {
                  $matrix.highlightColumn(column.id);
                  return true;
                },
                'mouseleave': function() {
                  $matrix.unhighlightColumn(column.id);
                  return true;
                }
              }"
            >
              <!-- ko if: !$matrix.isBlocked || column.isNew() -->
              <button
                class="f-icon f-icon-button f-icon--bin f-icon-light"
                data-bind="click: function() {
                    $component.removeColumn(column);
                  }, tooltip, tooltipText: $matrix.translator.t('Удалить')"
              >
                <svg>
                  <use href="#bin-icon"></use>
                </svg>
              </button>
              <!-- /ko -->
            </td>
            <!-- /ko -->
          </tr>
          <!-- /ko -->
        </tbody>
      </table>
    </div>
  </div>

  <!-- ko if: !$matrix.isFullBlocked && !$matrix.blockRows() -->
  <div
    class="matrix__delete"
    data-bind="if: $matrix.controller.rows().length > 1"
  >
    <div class="matrix-header"></div>
    <!-- ko foreach: { data: $matrix.controller.rows, as: 'row' } -->
    <div
      class="matrix-row cursor-pointer"
      data-bind="attr: { 'data-matrix-row': row.id }, matrixRowHeader: $matrix, event: {
                'mouseenter': function() {
                  $matrix.highlightRow(row.id);
                  return true;
                },
                'mouseleave': function() {
                  $matrix.unhighlightRow(row.id);
                  return true;
                }
              }"
      data-matrix-row-delete
    >
      <!-- ko if: !$matrix.isBlocked || row.isNew() -->
      <button
        class="f-icon f-icon-button f-icon--bin f-icon-light"
        data-bind="click: function() {
          $matrix.controller.removeRow(row);
        }, tooltip, tooltipText: $matrix.translator.t('Удалить')"

      >
        <svg>
          <use href="#bin-icon"></use>
        </svg>
      </button>
      <!-- /ko -->
    </div>
    <!-- /ko -->
  </div>
  <!-- /ko -->
</div>

<!-- ko ifnot: $matrix.isFullBlocked -->
<div class="d-flex mx-n2 my-4">
  <!-- ko ifnot: $matrix.blockRows -->
  <button
    class="f-btn f-btn-add flex-grow-1 mx-1"
    data-bind="
      click: function() {
        $component.addRow();
        document.querySelector('.matrix .matrix__rows > .matrix-row:last-child input').focus();
      },
      text: $matrix.translator.t('Добавить строку'),
    "
  ></button>
  <!-- /ko -->
  <button
    class="f-btn f-btn-add flex-grow-1 mx-1"
    data-bind="
      click: function() {
        $component.addColumn();
        document.querySelector('.matrix .matrix-table__header-row > .matrix-header:last-child input').focus();
      },
      text: $matrix.translator.t('Добавить столбец'),
    "
  ></button>
</div>
<!-- ko if: question.controller.poll && question.controller.poll.dictionary_id && !$matrix.blockRows() -->
<button
  class="mt-15p survey-question__variants-control-add-button variants-controller__add-button"
  data-bind="
    click: function() {
      if (question.controller.dictionaryConnectionSwitchDisabled()) {
        question.controller.onDictionaryConnectionSwitchClick(question.controller);
        return;
      }
      question.addVariantsFromDictionary();
    },
    css: {
      'variants-controller__add-button--blocked': question.controller.dictionaryConnectionSwitchDisabled(),
    },
  "
>
  <span class="survey-question__variants-control-add-button-icon"></span>
  <span data-bind="text: question.translator.t('Добавить строку из справочника')"></span>
</button>
<!-- /ko -->
<!-- /ko -->
<!-- /ko -->
