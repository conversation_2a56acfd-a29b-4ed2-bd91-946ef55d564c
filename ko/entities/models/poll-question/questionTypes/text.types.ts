import { PollQuestionVars, IPollQuestion } from "./index.types";
import { TextMask, NameMaskVars } from '../textFieldMask/types';
import { MaskTypes } from "@/constants/question/maskTypes";

export interface PollQuestionTextVars extends PollQuestionVars {
  mask: MaskTypes,
  maskConfig: NameMaskVars,
  placeholder_text: string;
}

export interface IPollQuestionText extends IPollQuestion {
  mask: TextMask;

  skip: boolean;
  skipText: string;
}
