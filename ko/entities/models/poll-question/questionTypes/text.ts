import { FullPollQuestionVars } from "../types";
import { PollQuestion } from "./index";
import { IPollQuestionText } from "./text.types";
import { TextMask } from "../textFieldMask/types";
import { QuestionTextFieldMask } from "../textFieldMask/index";

export class PollQuestionText
  extends PollQuestion
  implements IPollQuestionText
{
  mask: TextMask;

  skip: boolean;
  skipText: string;

  constructor(questionData: FullPollQuestionVars) {
    super(questionData);

    const { mask, maskConfig, placeholder_text, skip, skip_text } = questionData;

    this.mask = QuestionTextFieldMask({
      type: mask,
      nameMask: maskConfig,
      placeholder: placeholder_text
    });

    this.skip = Boolean(skip);
    this.skipText = skip_text;
  }
}
