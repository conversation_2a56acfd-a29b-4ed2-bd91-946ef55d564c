import { VARIANTS_QUESTION } from "Data/question-types";
import { sortByArray } from "../../../utils/array/sort-by-array";
import { ReviewQuestion } from "./question";
import { getRecipientVariants } from "./utils";

export class ReviewQuestionVariants extends ReviewQuestion {
	constructor(data, questions) {
		super(data);
		this.type = VARIANTS_QUESTION;

		this.donorIndex = null;
		this.donorQuestion = null;
		this.comment = "";

		this.variants = data.variants.map((v, i) => {
			return {
				id: "" + v.id,
				points: v.points,
				position: v.position,
				text: v.variant || v.question,
				index: i + 1,
				deleted: v.is_deleted,
				type: v.type,
				without_points: v.without_points,
				file_id: v.file_id,
				file_url: v.file_url,
				preview_url: v.preview_url,
				comment_required: v.comment_required
			};
		});
		this.variantsType = data.variantsType;

		this.skipped = false;

		if (data.donor) {
			const index = questions.findIndex((q) => q.id === data.donor);

			this.donorIndex = index + 1;
			this.donorQuestion = questions[index];

			let variants

			if (this.donorQuestion.type !== 19) {
				variants = getRecipientVariants(data, questions);

				const order = data.variants.map((v) =>
					v.question_detail_id ? `${v.question_detail_id}` : "-1"
				);

				variants = sortByArray(variants, order);

				this.variants = variants.map((v, i) => {
					const variantData = data.variants.find((el) => {
						if (v.id === "-1") return !el.question_detail_id;
						return el.question_detail_id == v.id;
					});
					if (!variantData) return null;
					return {
						...v,
						index: i + 1,
						points: variantData.points,
					};
				}).filter(Boolean);
			} else {
				variants = getRecipientVariants(data, questions);
				variants.forEach(i => {
					const temp = data.variants.find(v => v.dictionary_element_id == i.id)
					i.text = temp.question
					i.deleted = temp.is_deleted
				})
				this.variants = variants
			}
		}

		this.answer = [];
		this.answerPoints = 0;
		this.selfVariant = "";

		this.selfVariantLabel = data.self_variant_text;

		if (data.answer) {
			this.skipped = !!data.answer.skipped;

			if (!this.skipped) {
				let ids = [];
				if (data.answer.selectedIds.length) {
					ids = data.answer.selectedIds
				}
				this.answer = (ids || []).map((v) => "" + v) || [];
				this.answerPoints = data.answer.points || 0;
				this.selfVariant = data.answer.selfVariant;
				this.without_points = data.answer.without_points;

				this.comment = data.answer.comment;

				let answerText = this.answer
					.map((vId) => {
						let variant = this.variants.find((v) => v.id == "" + vId);
						if (variant) {
							return {
								text: variant.text || 'Вариант ' + variant.position,
								id: variant.id
							}
						}
					})
					.filter(Boolean);

				if (this.selfVariant) {
					answerText.push(
						{
							text: this.selfVariant,
							id: 'self-variant'
						}
					);
				}

				this.answerText = answerText.join(", ");
				this.answerReportList = answerText;
			}
			this.answerMaxPoints = data.answer.max_points
		}

		this.correctVariants = this.getCorrectVariants();

		this.correctVariantsText = this.correctVariants
			.map((v) => v.text || 'Вариант ' + v.position)
			.join(", ") || 'не задан';

		this.correctVariantsIds = this.correctVariants.map((v) => v.id);
	}

	get hasPoints() {
		if (!this.maxPoints) return false;
		return true;
	}

	get isMultipleVariants() {
		return this.variantsType == 1;
	}

	getMaxPoints(arr) {
		return arr.reduce(
			(acc, current) => acc + current.points,
			0,
		);
	}

	getCorrectVariants() {
		let variants = [];
		if (this.withPoints) {
			if (this.isMultipleVariants) {
				variants = this.variants.filter((v) => v.points > 0);
			} else {
				let max = null;
				this.variants.forEach((v) => {
					if (v.points <= 0) return;
					if (!max) max = v;
					else {
						if (max.points < v.points) max = v;
					}
				});
				if (max) {
					variants = [max];
				}
			}
		}
		return variants;
	}

	checkAnswer(answer) {
		if (!answer) return false;
		if (answer.skipped) return true;
		if ((!answer.selectedIds || !answer.selectedIds.length) && !answer.selfVariant) return false;
		return true;
	}
}
