import { TEXT_QUESTION } from 'Data/question-types';
import { TextAnswer } from '../../text-answer';
import { ReviewQuestion } from './question';

export class ReviewQuestionText extends ReviewQuestion {
  constructor(data) {
    super(data);

    this.type = TEXT_QUESTION;

    this.skipped = false;

    this.answer = '';
    if (data.answer) {
      this.skipped = !!data.answer.skipped;
      this.answer = new TextAnswer(data.answer.answer);
      this.answerMaxPoints = data.answer.max_points
    }
  }

  checkAnswer(answer) {
    if (!answer) return false;
    if (answer.skipped) return true;
    if (this.mask_type === 5 && !!answer.answer) { // ФИО
      let fields = answer.answer;
      return Object.keys(fields).some(field => {
        return fields[field] && fields[field].length;
      });
    }
    return !!answer.answer;
  }
}
