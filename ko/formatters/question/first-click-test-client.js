import { FIRST_CLICK_TEST } from "Data/question-types";

export default {
  type: FIRST_CLICK_TEST,
  
  format(question) {
    const data = {
      // Basic question data
      id: question.id,
      type: question.main_question_type,
      name: question.name,
      description: question.description,
      required: question.is_required === "1",
      
      // Image data
      imageFile: null,
      imagePreview: "",
      imageUrl: "",
      imageId: null,
      
      // Click areas
      clickAreas: [],
      
      // First Click Test settings
      mobileDisplay: "width",
      minClicks: 1,
      maxClicks: "",
      displayTime: "",
      buttonText: "",
      allowClickCancel: false,
      
      // Standard options
      skipOption: false,
      skipText: "",
      commentOption: false,
    };

    // Parse image data
    if (question.image_file_id && question.image_url) {
      data.imageId = question.image_file_id;
      data.imageUrl = question.image_url;
      data.imagePreview = question.image_url;
    }

    // Parse click areas from JSON
    if (question.click_areas) {
      try {
        const areas = typeof question.click_areas === 'string' 
          ? JSON.parse(question.click_areas) 
          : question.click_areas;
        
        if (Array.isArray(areas)) {
          data.clickAreas = areas.map(area => ({
            name: area.name || "",
            x: parseFloat(area.x) || 0,
            y: parseFloat(area.y) || 0,
            width: parseFloat(area.width) || 0,
            height: parseFloat(area.height) || 0
          }));
        }
      } catch (e) {
        console.warn("Failed to parse click areas:", e);
        data.clickAreas = [];
      }
    }

    // Parse settings
    if (question.mobile_display) {
      data.mobileDisplay = question.mobile_display;
    }

    if (question.min_clicks !== undefined && question.min_clicks !== null) {
      data.minClicks = parseInt(question.min_clicks) || 1;
    }

    if (question.max_clicks !== undefined && question.max_clicks !== null && question.max_clicks !== "") {
      data.maxClicks = parseInt(question.max_clicks) || "";
    }

    if (question.display_time !== undefined && question.display_time !== null && question.display_time !== "") {
      data.displayTime = parseInt(question.display_time) || "";
    }

    if (question.button_text) {
      data.buttonText = question.button_text;
    }

    if (question.allow_click_cancel !== undefined) {
      data.allowClickCancel = question.allow_click_cancel === "1" || question.allow_click_cancel === true;
    }

    // Parse standard options
    if (question.skip_option !== undefined) {
      data.skipOption = question.skip_option === "1" || question.skip_option === true;
    }

    if (question.skip_text) {
      data.skipText = question.skip_text;
    }

    if (question.comment_option !== undefined) {
      data.commentOption = question.comment_option === "1" || question.comment_option === true;
    }

    return data;
  }
};
