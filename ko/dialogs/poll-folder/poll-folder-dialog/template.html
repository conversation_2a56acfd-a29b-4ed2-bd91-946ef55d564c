<foquz-dialog params="ref: modal, dialogWrapper: $component">
  <foquz-dialog-header>
    <span data-bind="text: _t('polls', 'Новая папка')"></span>
  </foquz-dialog-header>

  <div class="foquz-dialog__body">
    <!-- ko if: loading -->
    <fc-spinner class="f-color-primary"></fc-spinner>
    <!-- /ko -->

    <!-- ko ifnot: loading -->

    <div class="form-group">
      <label class="form-label" data-bind="text: _t('Название')"></label>

      <foquz-chars-counter params="value: name, max: 150">
        <input
          class="form-control"
          data-bind="textInput: $parent.name,
                attr: {
                  placeholder: _t('polls', 'Новая папка'),
                },
                css: {
                  'is-invalid': $parent.formControlErrorStateMatcher($parent.name), 'is-valid': $parent.formControlSuccessStateMatcher($parent.name) }"
          maxlength="150"
        />
      </foquz-chars-counter>

      <validation-feedback
        params="show: formControlErrorStateMatcher(name), text: name.error"
      ></validation-feedback>
    </div>

    <div class="form-group mb-0 mb-md-4">
      <label
        class="form-label"
        data-bind="text: _t('polls', 'Местоположение папки')"
        >Местоположение папки</label
      >

      <select
        data-bind="
            value: location,
            select2: {
                minimumResultsForSearch: 0,
                containerCssClass: 'form-control',
                wrapperCssClass: 'select2-container--form-control',
                templateResult: locationTemplateResult,
                dropdownAutoWidth: false
            },
            foreach: locations
        "
      >
        <option
          data-bind="attr: { 'data-level': $data.level, value: $data.id }, disable: $data.inactive"
        >
          <!--ko text: $data.name-->
          <!--/ko-->
        </option>
      </select>
    </div>

    <!-- /ko -->
  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn f-btn-link"
        data-bind="
                click: function() {
                  $dialog.hide('close');
                }, text: _t('Отменить')"
      ></button>

      <button
        type="button"
        class="f-btn f-btn-success"
        data-bind="
                  click: function() {
                    submit();
                  }, text: _t('Сохранить')"
      ></button>
    </div>
  </div>
</foquz-dialog>
