import { DialogWrapper } from "Dialogs/wrapper";
import { useFoldersList } from "@/presentation/hooks/useFoldersList";
import { getPollFolders } from "@/api/poll/get-poll-folders";

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );
    this.formControlSuccessStateMatcher = commonFormControlSuccessStateMatcher(
      this.isSubmitted
    );

    const { list, loading } = useFoldersList(getPollFolders);

    this.loading = loading;
    this.locations = list;
    this.location = ko.observable("0");

    list.subscribe(v => {
      this.location(v.find(i => !i.inactive).id);
    })

    this.name = ko.observable("").extend({
      required: {
        message: _t("Обязательное поле"),
      },
      minLength: {
        params: 2,
        message: _t("main", "Не менее {characters}", {
          characters: _t("main", "{count} символов", {
            count: 2,
          }),
        }),
      },
    });
  }

  onEnter() {
    this.submit();
  }

  locationTemplateResult(state) {
    if (!state.id) {
      return state.text;
    }

    const level = parseInt($(state.element).data("level"));

    return $(
      `<i class="survey-list__new-folder-modal-dialog-location-form-control-folder-icon" style="flex-shrink: 0; margin-left: ${
        level * 14
      }px"></i>` +
        "<span>" +
        state.text +
        "</span>"
    );
  }

  submit() {
    this.isSubmitted(true);
    if (!this.name.isValid()) return;
    let data = {
      name: this.name(),
      location: this.location(),
    };

    $.ajax({
      url: "/foquz/ajax/create-folder",
      method: "POST",
      dataType: "json",
      data: JSON.stringify(data),
      success: (response) => {
        if (response.success) {
          this.emitEvent("create");
          this.hide();
        }
      },
      error: (response) => {
        console.error(response.responseJSON);
      },
    });
  }
}
