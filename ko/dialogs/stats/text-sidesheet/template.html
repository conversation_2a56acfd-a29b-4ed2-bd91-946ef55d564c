<stats-sidesheet class="text-stats" params="ref: modal,
                  dialogWrapper: $component,
                  title: title,
                  question: question,
                  withPoints: withPoints">

  <!-- ko let: { $table: table, $modal: $data } -->
  <media-query params="query: 'tablet+'">
    <interactive-table params="table: $table, fixedHeader: true">
      <table class="table foq-table question-statistics__clients-modal-dialog-table">
        <thead>
          <tr>
            <th>ФИО контакта</th>
            <th>Телефон</th>
            <th>Email</th>
            <!-- ko if: $modal.withPoints -->
            <th>Баллы</th>
            <!-- /ko -->
            <th>Пройден</th>
            <!-- ko if: $modal.hasOrder -->
            <th class="question-statistics__clients-modal-dialog-table-order-id-head-cell">
              № заказа
            </th>
            <th>Дата заказа</th>
            <!-- /ko -->
            <th>Ответ</th>
          </tr>
        </thead>
        <tbody>
          <!-- ko foreach: items -->
          <tr class="question-statistics__clients-modal-dialog-table-row">
            <td data-bind="html: $modal.getText(name)"></td>
            <td class="question-statistics__clients-modal-dialog-table-phone-cell"
                data-bind="html: $modal.getText(phone)">
            </td>
            <td class="question-statistics__clients-modal-dialog-table-email-cell"
                data-bind="html: $modal.getText(email)">
            </td>
            <!-- ko if: $modal.withPoints -->
            <td>
              <div class="mb-5p">
                <span class="bold"
                      data-bind="text: points"></span>
                из
                <span class="bold"
                      data-bind="text: $modal.question.maxPoints"></span>,
                <span data-bind="text: $modal.getPercent(points, $modal.question.maxPoints) + '%'"></span>
              </div>
              <progress-line style="width: 100px"
                             params="progress: $modal.getPercent(points, $modal.question.maxPoints)">
              </progress-line>
            </td>
            <!-- /ko -->
            <td data-bind="html: $modal.getText(passedAt)"></td>
            <!-- ko if: $modal.hasOrder -->
            <td class="question-statistics__clients-modal-dialog-table-order-id-cell">
              <a class="question-statistics__clients-modal-dialog-table-link"
                 data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }">
              </a>
            </td>
            <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
            <!-- /ko -->
            <td>
              <!-- ko if: $data.skipped -->
              <span class="skipped">Отказался от ответа</span>
              <!-- /ko -->
              <!-- ko ifnot: $data.skipped -->
              <span data-bind="html: $modal.getText(answer) || '-'"></span>
              <!-- /ko -->
            </td>
          </tr>
          <!-- /ko -->
        </tbody>
      </table>
    </interactive-table>
  </media-query>

  <media-query params="query: 'mobile'">
    <div class="mobile-table"
         data-bind="nativeScrollbar">
      <interactive-table params="table: $table, horizontal: true">
        <table class="table foq-table fixed-table">
          <tbody>

            <tr>
              <th>ФИО контакта</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(name)"></td>
              <!-- /ko -->
            </tr>
            <tr>
              <th>Телефон</th>
              <!-- ko foreach: items -->
              <td class="question-statistics__clients-modal-dialog-table-phone-cell"
                  data-bind="html: $modal.getText(phone)">
              </td>
              <!-- /ko -->
            </tr>
            <tr>
              <th>Email</th>
              <!-- ko foreach: items -->
              <td class="question-statistics__clients-modal-dialog-table-email-cell"
                  data-bind="html: $modal.getText(email)">
              </td>
              <!-- /ko -->
            </tr>
            <!-- ko if: $modal.withPoints -->
            <tr>
              <th>Баллы</th>
              <!-- ko foreach: items -->
              <td>
                <div class="mb-5p">
                  <span class="bold"
                        data-bind="text: points"></span>
                  из
                  <span class="bold"
                        data-bind="text: $modal.question.maxPoints"></span>,
                  <span data-bind="text: $modal.getPercent(points, $modal.question.maxPoints) + '%'"></span>
                </div>
                <progress-line style="width: 100px"
                               params="progress: $modal.getPercent(points, $modal.question.maxPoints)">
                </progress-line>
              </td>
              <!-- /ko -->
            </tr>
            <!-- /ko -->
            <tr>
              <th>Пройден</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(passedAt)"></td>
              <!-- /ko -->
            </tr>
            <!-- ko if: $modal.hasOrder -->
            <tr>
              <th class="question-statistics__clients-modal-dialog-table-order-id-head-cell">
                № заказа
              </th>
              <!-- ko foreach: items -->
              <td class="question-statistics__clients-modal-dialog-table-order-id-cell">
                <a class="question-statistics__clients-modal-dialog-table-link"
                   data-bind="html: '#' + $modal.getText(order), attr: { href: $modal.getOrderLink(order) }">
                </a>
              </td>
              <!-- /ko -->

            </tr>
            <tr>
              <th>Дата заказа</th>
              <!-- ko foreach: items -->
              <td data-bind="html: $modal.getText(orderCreatedAt)"></td>
              <!-- /ko -->
            </tr>
            <!-- /ko -->
            <tr>
              <th>Ответ</th>
              <!-- ko foreach: items -->
              <td>
                <!-- ko if: $data.skipped -->
                <span class="skipped">Отказался от ответа</span>
                <!-- /ko -->
                <!-- ko ifnot: $data.skipped -->
                <span data-bind="html: $modal.getText(answer || '-')"></span>
                <!-- /ko -->
              </td>
              <!-- /ko -->
            </tr>

          </tbody>

        </table>
      </interactive-table>
    </div>
  </media-query>
  <!-- /ko -->
</stats-sidesheet>
