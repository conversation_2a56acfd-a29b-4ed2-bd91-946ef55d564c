import { get as _get, forIn } from "lodash";

import { DialogsModule } from "@/utils/dialogs-module";
import { DialogWrapper } from "Dialogs/wrapper";
import { Review } from "Models/review";
import { ApiUrl } from "Utils/url/api-url";
import { TextAnswer } from 'Models/text-answer';
import { TagsDirectory } from "Utils/directory/tags";
import "Components/tag-input";


let nextId = 0;

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);
    DialogsModule(this);
    this.handleChildEvent = this.handleChildEvent.bind(this);
    this.initializing = ko.observable(true);
    this.loading = ko.observable(true);
    this.activeQuestion = ko.observable({});
    this.blocked = window.CURRENT_USER.blockAnswers;

    this.id = nextId++;
    this.showSaveMessage = ko.observable(false);

    this.review = null;
    this.answerTags = ko.observableArray([]);
    this.executorMode = params.executorMode;
    this.hideClientData = params.hideClientData;
    this.enableActions = params.enableActions;

    this.history = ko.observable(null);
    this.historyOpened = ko.observable(false);

    this.processingForm = ko.observable(null);

    this.answers = params.answers;
    this.reviewId = params.reviewId;
    this.directories = {
      tags: new TagsDirectory(),
    };

    this.loadReview(params.reviewId).then((review) => {
      this.review = review;
      this.answerTags(review.answer_tags);
      this.directories.tags.load();
      this.review.isRequestProcessingEnabled =
        params.isRequestProcessingEnabled;
      this.review.answerId = params.pollId;

      this.init();
      this.loading(false);

      const url = new URL(window.location.href);
      url.searchParams.delete("reviewId");
      url.searchParams.append("reviewId", this.reviewId);
      window.history.pushState(null, null, url);
    });
  }

  getReviewPointsHtml(review) {
    return `<span class="f-color-service">${_t("Набрано баллов")}: </span>
    ${_t("main", "{num1} из {num2}", {
      num1: `<span class="bold">${review.points().answer_points}</span>`,
      num2: `<span class="bold">${review.points().points_max}</span>`,
    })},
    <span>${review.points().percent}</span>%`;
  }

  openReview(reviewId) {
    this.dialogRoot.add({
      name: "review-sidesheet",
      params: {
        hideClientData: this.hideClientData,
        reviewId,
        executorMode: this.executorMode,
      },
    });
  }

  openHistory() {
    this.historyOpened(true);
    this.history().load(this.review.contactId);
  }

  closeHistory() {
    this.historyOpened(false);
    this.history().reset();
  }

  onElementRender() {
    this.initializing(false);
  }
  handleChildEvent(question) {
    this.activeQuestion(question);
  }
  onReviewEditor() {
    this.dialogRoot.add({
      name: "review-editor",
      params: {
        hideClientData: this.hideClientData,
        review: this.review,
        reviewId: this.review.answerId,
        question: this.activeQuestion(),
        activeQuestion: this.activeQuestion,
        questionNumber: this.review.questions().filter(q => !q.isDeleted).indexOf(this.activeQuestion()) + 1,
        executorMode: this.executorMode,
        questions: this.review.questions(),
        sync: (data) => {
          this.loading(true)
          window.editedQuestion = data.id
          this.loading(false)
        },
        callback: (data, questionId) => {
          this.loading(true);
          const question_ = this.review.questions().find(i => i.id == questionId)
          question_.hasAnswer = true
          data.answer = data.answers[question_.id]

          for (const key in data.answers) {

            if (key*1 !== question_.id) {
              const q = this.review.questions().find(i => i.id == key)
              q.answer = null
              q.answerData = {}
              q.hasAnswer = false
              q.selfVariant = null
              q.files = []
              q.rating = 0
              q.skipped = false
              q.comment = ''
              q.variants = []

              if (data.answer) {
                  data.answer.selectedIds.forEach(i => {
                    const variant = question_.variants.find(v => v.id == i)
                    q.variants.push(variant)
                })
              }
              
            }
              
          }

          if (data.answer) {
            question_.answerPoints = data.answer.points
          }

          this.review.points(data.points)
          
          if (data.answer === null) {
            question_.answer = null
            question_.answerData = {}
            question_.hasAnswer = false
            question_.selfVariant = null
            question_.files = []
            question_.rating = 0
            question_.skipped = false
            question_.comment = ''
          } else if (question_.type == 2) {
            if (typeof question_.answer === 'string' || !question_.answer) {
              question_.answer = {text: ''}
            }
            if (!data.answer) {
              question_.answer = data.answer
            } else {
              question_.answer = new TextAnswer(data.answer.answer)
            }
            question_.skipped = !!data.answer.skipped
          } else if (question_.type == 3) {
            question_.answer = data.answer.answer
          } else if (question_.type == 7) {
            question_.answer = data.answer
            question_.answerData = data.answer.answer.answer
            question_.comment = data.answer.answer.comment
            question_.skipped = !!data.answer.skipped
            question_.rating = data.answer.rating
          } else if (question_.type == 4) {
            question_.answer = data.answer.answer
          } else if (question_.type == 12) {
              question_.answer = data.answer
              if (data.answer) {
                  question_.skipped = !!data.answer.skipped
                  question_.answerData.rating = data.answer.answer.rating
              } else {
                question_.skipped = false
              }
              
          } else if (question_.type == 15) {
            question_.loading(true)
            question_.answer = data.answer
            question_.skipped = !!data.answer.skipped
            question_.rating = data.answer.rating
            question_.comment = data.answer.comment
            question_.loading(false)
          } else if (question_.type == 5) {
            question_.answer = data.answer
            question_.files = data.answer.answer.files
            question_.comment = data.answer.answer.comment
          }  else if (question_.type == 6) {
            question_.answer = data.answer
            question_.hasAnswer = false

            data.answer?.answer?.values.forEach((f, index) => {
              const value = f.value
              const field = question_.fields.find(i => i.id == f.id)
              if (!field) {
                const newField = f
                newField.answer = new TextAnswer(f.value)
                question_.fields.push(newField)
              }
              if (value) {
                if (typeof value === 'string' && value.length) {
                  question_.hasAnswer = true
                }
                if (typeof value === 'object') {
                  for (const key in value) {
                    if (value[key].length) {
                      question_.hasAnswer = true
                    }
                  }
                }
                if (field) {
                  field.answer.text = value
                }
                
              }
            })

            question_.fields.sort(function (a, b) {
              return a.id - b.id;
            });
          } else if (question_.type == 11) {
            question_.answer = data.answer
            if (data.answer) {
              question_.smile = data.answer.answer.smile
              question_.comment = data.answer.answer.comment
              question_.skipped = !!data.answer.skipped
            } else {
              question_.skipped = true
            }
            
            if (question_.smile) {
              question_.smile.url = question_.smile.smile_url
            }
            
            
          } else if (question_.type == 1) {
            if (data.answer?.selectedIds) {
              question_.answer = data.answer.selectedIds
            }
            if (data.answer) {
              question_.comment = data.answer.comment
              question_.skipped = !!data.answer.skipped
              question_.selfVariant = data.answer.selfVariant
            } else {
              question_.skipped = true
            }
            
          }  else if (question_.type == 14) {
           
            if (data.answer) {
              question_.comment = data.answer.comment
              question_.skipped = !!data.answer.skipped
              question_.answer = data.answer
            } else {
              question_.skipped = true
            }
            
          } else {
            if (typeof question_.answer === 'string' || !question_.answer) {
              question_.answer = {text: ''}
            }

            if (!data.answer) {
              question_.answer = data.answer
            } else {

              question_.answer = new TextAnswer(data.answer.answer)
            }            
          }
         
          //this.activeQuestion(question_)
          window.editedQuestion = question_.id
          //this.emitEvent("update", data);
          this.loading(false)

          this.emitEvent("update", { action: "update", reviewId: this.reviewId });
        }
      },
    });
  }
  loadReview(reviewId) {
    let review = Review.get(reviewId);
    if (review) {
      return Promise.resolve(review);
    }
    return new Promise((res) => {
      $.ajax({
        url: ApiUrl("answers/view"),
        data: {
          id: reviewId,
        },
        success: (response) => {
          response.questions.forEach((el, index) => {
            // ToDo рефакторинг
            el.setVariants = _get(this, "answers[index].setVariants", false);
          });
          res(new Review(response));
        },
        error: (response) => {
          console.error(response.responseJSON);
        },
      });
    });
  }

  init() {
    this.getComplaintFancyboxCaption = function () {
      const passedAt = this.review.passedAt;
      const text = this.review.complaint.text;
      return (
        `<div class="review-complaint__fancybox-description">
                        <div class="review-complaint__fancybox-description-passed-at">${passedAt}</div>` +
        (text !== null
          ? `<div class="review-complaint__fancybox-description-text">${text}</div>`
          : "") +
        `</div>`
      );
    };
  }

  submit() {
    this.processingForm().submit();
  }

  onSubmit() {
    this.emitEvent("update", { action: "update", reviewId: this.reviewId });
    this.showSaveMessage(true);
    setTimeout(() => {
      this.showSaveMessage(false);
    }, 4000);
  }

  cancel() {
    this.showSaveMessage(false);
    this.processingForm().cancel();
  }

  async deleteReview() {
    this.confirm({
      title: "Удаление ответов",
      text: "Ответы будут удалены без возможности восстановления. <b>Изменится статистика опроса</b>.",
      confirm: "Удалить",
      mode: "danger",
    }).then(async () => {
      const res = await fetch(
        `/foquz/api/answers/delete?id=${this.reviewId}&access-token=${APIConfig.apiKey}`,
        { method: "DELETE" }
      );
      const data = await res.json();
      this.emitEvent("update", { action: "delete", reviewId: this.reviewId });
      this.hide();
    });
  }

  printReview(filtered) {
    if (filtered && !this.review.dictionary_id) {
      this.info({
        text: "Опция доступна, если настроены <a class='dialog-link' href='https://foquz.ru/foquz/user-wiki/kak-tegirovat-voprosy' target='_blank'>связи вопросов</a> с элементами из справочника",
      });
      return;
    }
    let { href } = window.location;
    href = `/foquz/foquz-poll/print-answer?id=${this.reviewId}${
      filtered ? "&filtered=1" : ""
    }`;
    window.open(href, "_blank");
  }
}
