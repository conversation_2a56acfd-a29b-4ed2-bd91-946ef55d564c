import "inputmask/lib/extensions/inputmask.numeric.extensions";
import moment from 'moment';

export class ViewModel {
  constructor(params, element) {
    this.subscriptions = [];
    this.field = params.value;
    this.question = params.question()
    this.validStep = params.validStep
    this.isRequired = ko.observable(false);
    this.isCanged = params.isCanged;
    this.skipped = params.skipped
    this.maskType = params.question().mask_type;
    this.variantsType = params.question().variantsType
    this.activeMask = ko.observable();
    this.isInvalid = ko.observable(false);
    this.isInvalidPeriod = ko.observable(false)
    this.startDate = ko.observable();
    this.endDate = ko.observable();
    this.intervalText = params.intervalText || "Кол-во символов в ответе";
    this.disabled = params.disabled;
    this.month = ko.observable();
    this.day = ko.observable();

    switch (this.maskType) {
      case 1:
        this.field(this.formatMaskedData(params.question().answer ? params.question().answer.text : ''));
        this.activeMask("+7 (999) 999-9999");
        break;
      case 2:
        this.field(this.formatMaskedData(params.question().answer ? params.question().answer.text : ''));
        break;
      case 3:
        this.field(this.formatMaskedData(params.question().answer ? params.question().answer.text : ''));
        break;
      case 4:
        this.field(this.formatMaskedData(params.question().answer ? params.question().answer.text : ''));
        break;
      case 5:
        this.field(this.formatMaskedData(params.question().answer?.text));
        for (const key in this.field()) {
          this.field()[key].subscribe((v) => {
            if (v && v.trim().length > 0) {
              this.skipped(false);
            }
            this.validateField();
          });
        }
        break;
      case 6:
        this.field(this.formatMaskedData(params.question().answer ? params.question().answer.text : ''));
        break;
      case 8:
        this.field(this.formatMaskedData(params.question().answer ? params.question().answer.text : ''));
        const [day, month] = this.field().length ? this.field().split(".") : ['', ''];
        this.month(month*1);
        this.day(day);
        break;
      case 7:
        this.field(this.formatMaskedData(params.question().answer ? params.question().answer.text : ''));
        const [start, end] = this.field().length ? this.field().split("—") : ['',''];
        this.startDate(start);
        this.endDate(end);
        break;
      default:
        this.field(params.question().answer ? params.question().answer.text : '');
    }

    if (this.question.type == 3 && !this.question.onlyDateMonth) {
      this.field(params.question().answer ? params.question().answer : '')
    }

    if (this.question.type == 3 && this.question.onlyDateMonth) {
      this.field(params.question().answer ? params.question().answer : '')
      const [day, month] = this.field().split(".");
      this.month(month*1);
      this.day(day);
    }

    // Подписка на изменение маски
    this.activeMask.subscribe((newMask) => {
      const input = $(element).find('input[data-bind*="mask"]');
      input.inputmask("remove"); // Удаление предыдущей маски
      input.inputmask(newMask); // Установка новой маски
    });

    // Начальная установка маски
    setTimeout(() => {
      const input = $(element).find('input[data-bind*="mask"]');
      input.inputmask(this.activeMask());
    }, 0);

    this.inited = true

    this.field.subscribe((v) => {
      this.isCanged(true)
      this.inited = false
      console.log('isInvalid subscribe', v)
      if (v !== undefined) {
        console.log('isInvalid validateField')
        this.validateField()
      }
    });

    this.field.subscribe((v) => {
      this.isCanged(true);

      if (this.maskType == 5) {
        const hasValue = Object.keys(v).some(key => {
          const value = v[key]();
          return value && value.trim().length > 0;
        });
        if (hasValue) {
          this.skipped(false);
        }
      }
      else if (v && v.length) {
        this.skipped(false);
      }

      this.validateField();
    });

    this.skipped.subscribe((v) => {
      this.isCanged(true);
      if (v) {
        this.clearIsSkipped()
      }
      setTimeout(() => {
        this.validateField();
      }, 0);
    });

    this.startDate.subscription = this.startDate.subscribe((newStart) => {
      this.isCanged(true);
      this.updateField();
    });

    this.endDate.subscription = this.endDate.subscribe((newEnd) => {
      this.isCanged(true);
      this.updateField();
    });

    this.day.subscription = this.day.subscribe((newDay) => {
      this.isCanged(true);
      this.updateField();
    });

    this.month.subscription = this.month.subscribe((newMonth) => {
      this.isCanged(true);
      this.updateField();
    });
    this.validateField();
  }

  clearSkippedDayMount(){
    this.day.subscription && this.day.subscription.dispose();
    this.month.subscription && this.month.subscription.dispose();

    this.day('');
    this.month('');
    this.field('');

    // Восстанавливаем подписки
    this.day.subscription = this.day.subscribe((newDay) => {
      this.isCanged(true);
      this.updateField();
    });
    this.month.subscription = this.month.subscribe((newMonth) => {
      this.isCanged(true);
      this.updateField();
    });
  }

  clearSkippedFIO(){
    // Временно отключаем подписки на поля ФИО
    const fieldSubscriptions = [];
    for (const key in this.field()) {
      if (this.field()[key] && typeof this.field()[key].subscription === 'function') {
        this.field()[key].subscription.dispose();
      }
    }

    // Очищаем поля
    for (const key in this.field()) {
      if (this.field()[key] && typeof this.field()[key] === 'function') {
        this.field()[key]('');
      }
    }

    // Восстанавливаем подписки
    for (const key in this.field()) {
      if (this.field()[key] && typeof this.field()[key] === 'function') {
        this.field()[key].subscription = this.field()[key].subscribe((value) => {
          if (value && value.trim().length > 0) {
            this.skipped(false);
          }
          this.validateField();
        });
      }
    }
  }

  clearSkippedDateRange(){
    // Временно отключаем подписки, чтобы избежать множественных обновлений
    this.startDate.subscription && this.startDate.subscription.dispose();
    this.endDate.subscription && this.endDate.subscription.dispose();

    this.startDate('');
    this.endDate('');
    this.field('');

    // Восстанавливаем подписки
    this.startDate.subscription = this.startDate.subscribe((newStart) => {
      this.isCanged(true);
      this.updateField();
    });
    this.endDate.subscription = this.endDate.subscribe((newEnd) => {
      this.isCanged(true);
      this.updateField();
    });
  }


  clearIsSkipped(){
      if (this.maskType == 8 || (this.question.type == 3 && this.question.onlyDateMonth)) {
        this.clearSkippedDayMount()
      }
      else if (this.maskType == 7) {
        this.clearSkippedDateRange()
      }
      else if (this.maskType == 5 && typeof this.field() === 'object') {
        this.clearSkippedFIO()
      }
      else {
        this.field('');
      }
  }


  updateField() {
    if (this.maskType === 7) {
      const startDate = this.startDate();
      const endDate = this.endDate();
      const field = (startDate || endDate)
        ? `${startDate}—${endDate}`
        : '';
      this.field(field);
    }
    if (this.maskType === 8 || (this.question.type == 3 && this.question.onlyDateMonth)) {
      const day = this.day();
      const month = this.month();
      const field = (day || month)
        ? `${day}.${month}`
        : '';
      this.field(field);
    }
  }
  formatMaskedData(data) {
    if (this.maskType === 5) {
      const res = {};
      for (const key in this.question.maskConfig) {
        if (this.question.maskConfig[key].visible) {
          res[key] = ko.observable(data && data[key] ? data[key] : "");
          res[key].subscription = res[key].subscribe((v) => {
            if (v && v.trim().length > 0) {
              this.skipped(false);
            }
            this.validateField();
          });
        }
      }
      return res;
    } else {
      return data;
    }
  }

  validateField() {
    // Если шаг пропущен, сбрасываем все ошибки и помечаем шаг как валидный
    if (this.skipped()) {
      this.isRequired(false)
      this.isInvalid(false)
      this.isInvalidPeriod(false)
      this.validStep(true)
      return
    }

    // Если маска не равна 5 (т.е. не ФИО), проверяем на обязательность поля по его длине
    if (this.maskType !== 5) {
      this.isRequired(this.question.isRequired && !this.field().length);
    } else {
      // Обработка ФИО (maskType 5)
      const nameRequired = !this.skipped() && this.question.maskConfig.name.required;
      const surnameRequired = !this.skipped() && this.question.maskConfig.surname.required;
      const patronymicRequired = !this.skipped() && this.question.maskConfig.patronymic.required;

      this.isRequired(
          this.question.isRequired &&
          ((this.field().name && !this.field().name().length && nameRequired) ||
              (this.field().surname && !this.field().surname().length && surnameRequired) ||
              (this.field().patronymic && !this.field().patronymic().length && patronymicRequired))
      );
    }

    // Если поле обязательное, проверяем соответствие содержимого типу маски
    if (this.question.isRequired) {
      if (this.maskType === 2) {

        // Валидация Email
        const emailPattern = /^\S+@\S+\.\S+$/;
        this.isInvalid(!emailPattern.test(this.field()));

      } else if (this.maskType === 4) {
        // Валидация URL
        const urlPattern = /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/;
        this.isInvalid(!urlPattern.test(this.field()));
      } else if (this.maskType === 6 || (this.question.type == 3 && !this.question.onlyDateMonth)) {

        // Валидация полной даты DD.MM.YYYY
        const [d, m, y] = this.field().split('.');
        this.isInvalid(!moment(this.field(), 'DD.MM.YYYY').isValid() || !m.length || y.length < 4);
      } else if (this.maskType === 1) {

        // Валидация телефона (считаем валидным если полностью введён номер)
        this.isInvalid(this.field().replace('_', '').length < 17);
      } else if (this.maskType === 8 || (this.question.type == 3 && this.question.onlyDateMonth)) {

        // Валидация дня и месяца (например, "12.08")
        this.isRequired(!(this.day() && this.month()));
        const daysInMonth = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
        this.isInvalid(
            this.day().length && // Проверяем, введен ли день
            (this.day() * 1 > daysInMonth[this.month() * 1 - 1] || this.day() * 1 < 1) || // Проверка не превышает ли день допустимое число в этом месяце или меньше 1
            (this.day().length && !this.month()) // Проверка, введен день, но не выбран месяц
        );
      } else if (this.maskType === 7) {

        // Валидация периода дат
        this.isRequired((this.startDate()?.length !== 10) || (this.endDate()?.length !== 10));
        this.isInvalid(!moment(this.startDate(), 'DD.MM.YYYY').isValid() || !moment(this.endDate(), 'DD.MM.YYYY').isValid());

        // Проверка, что дата начала не позже даты конца
        if (!this.isInvalid()) {
          const start = new Date(this.startDate().split('.').reverse().join('.')).getTime();
          const end = new Date(this.endDate().split('.').reverse().join('.')).getTime();
          this.isInvalidPeriod(start > end);
        }
      }
    } else {
      // Если поле не обязательное, валидируем только при наличии значения

      if (this.maskType === 6 || (this.question.type == 3 && !this.question.onlyDateMonth)) {

        // Валидация даты (необязательное поле)
        const [d, m, y] = this.field().split('.');
        if (d || m || y) {
          this.isInvalid(!moment(this.field(), 'DD.MM.YYYY').isValid() || !m.length || y.length < 4);
        } else {
          this.isInvalid(false);
        }

      } else if (this.maskType === 8 || (this.question.type == 3 && this.question.onlyDateMonth)) {

        // Валидация день-месяц
        const daysInMonth = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
        const empty = !this.day().length && !this.month();

        if (empty) {
          this.isInvalid(false);
        } else {
          // Если введён день и он выходит за пределы допустимых значений (например, 32 января или 0 марта)
          this.isInvalid(
              this.day().length &&
              (this.day() * 1 > daysInMonth[this.month() * 1 - 1] || this.day() * 1 < 1) || // не больше, чем максимум в выбранном месяце и не меньше одного
              !this.month() || !this.day().length  // введен день или месяц
          );
        }

      } else if (this.maskType === 7) {

        // Валидация периода (если что-то введено)
        if (this.field().replace('—', '').length) {
          this.isRequired((this.startDate()?.length !== 10) || (this.endDate()?.length !== 10));
          this.isInvalid(!moment(this.startDate(), 'DD.MM.YYYY').isValid() || !moment(this.endDate(), 'DD.MM.YYYY').isValid());

          if (!this.isInvalid()) {
            const start = new Date(this.startDate().split('.').reverse().join('.')).getTime();
            const end = new Date(this.endDate().split('.').reverse().join('.')).getTime();
            this.isInvalidPeriod(start > end);
          }
        } else {

          // Если поле пустое — сбрасываем ошибки
          this.isRequired(false);
          this.isInvalid(false);
          this.isInvalidPeriod(false);
        }

      } else if (this.maskType === 1) {

        // Валидация телефона (если что-то введено)
        if (this.field().length) {
          this.isInvalid(this.field().replace('_', '').length < 17);
        } else {
          this.isInvalid(false);
        }

      } else if (this.maskType === 2) {

        // Валидация Email
        if (this.field().length) {
          const emailPattern = /^\S+@\S+\.\S+$/;
          this.isInvalid(!emailPattern.test(this.field()));
        } else {
          this.isInvalid(false);
        }

      } else if (this.maskType === 4) {

        // Валидация URL
        if (this.field().length) {
          const urlPattern = /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/;
          this.isInvalid(!urlPattern.test(this.field()));
        } else {
          this.isInvalid(false);
        }
      }
    }

    // Устанавливаем финальный статус шага — валиден, если нет ошибок
    this.validStep(!this.isInvalid() && !this.isRequired() && !this.isInvalidPeriod());
  }

  dispose() {
    this.startDate.subscription && this.startDate.subscription.dispose();
    this.endDate.subscription && this.endDate.subscription.dispose();
    this.day.subscription && this.day.subscription.dispose();
    this.month.subscription && this.month.subscription.dispose();

    if (this.maskType === 5 && typeof this.field() === 'object') {
      for (const key in this.field()) {
        if (this.field()[key] && typeof this.field()[key].subscription === 'function') {
          this.field()[key].subscription.dispose();
        }
      }
    }

    this.subscriptions.forEach((s) => s.dispose());
  }
}
