<div>
  <div class="row">
  <!-- ko if: maskType === 0 && variantsType === 0 -->
   <div class="col-12 col-md-8">
    <div class="chars-counter chars-counter--type_input">
      <input class="form-control" maxlength="3000" data-bind="textInput: field, css: {'is-invalid': isRequired}" />
    </div>
    <span class="error-message" data-bind="visible: isRequired"
      >Обязательное поле</span>
   </div>
  <!-- /ko -->

  <!-- ko if: maskType === 0 && variantsType === 1 -->
  <div class="col-12 col-md-8">
    <div class="chars-counter chars-counter--type_input">
      <textarea
        class="form-control form-control_full sm autosize-textarea edit-simple-text"
        data-bind="textInput: field, autosizeTextarea, css: {'is-invalid': isRequired}"
        style="min-height: 72px; overflow-y: hidden; height: 70px"
        maxlength="3000"
        minlength="0"
        placeholder=""
      ></textarea>
      <span class="error-message" data-bind="visible: isRequired"
      >Обязательное поле</span
    >
    </div>
  </div>
  <!-- /ko -->
  <!-- ko if: maskType === 2 -->
  <div class="col-12 col-md-8">
    <div class="chars-counter chars-counter--type_input ">
      <input
        class="form-control edit-control-sm"
        data-bind="textInput: field,
      attr:{
        placeholder: '<EMAIL>'
      }, css: {'is-invalid': isInvalid() || isRequired()}"
      />
    </div>
    <span class="error-message" data-bind="visible: isInvalid() && !isRequired()"
      >Неверный формат</span
    >
    <span class="error-message" data-bind="visible: isRequired"
      >Обязательное поле</span
    >
  </div>
  <!-- /ko -->
  <!-- ko if: maskType === 3 -->
  <div class="col-12 col-md-4">
    <div class="chars-counter chars-counter--type_input">
      <input class="form-control" type="number" data-bind="textInput: field, css: {'is-invalid': isInvalid() || isRequired()}" />
    </div>
    <span class="error-message" data-bind="visible: isRequired "
      >Обязательное поле</span>
  </div>
  <!-- /ko -->

  <!-- ko if: maskType === 8 || (question.type == 3 && question.onlyDateMonth) -->
   <div class="col-12 col-md-8">
    <div class="row">
      <div class="day-input">
        <div class="chars-counter chars-counter--type_input">
          <input class="form-control" data-bind="textInput: day, onlyNumbers, css: {'is-invalid': (isInvalid() && !day().length) || isRequired()}" />
        </div>
      </div>
      <div class="col-md-6">
        <div class="chars-counter chars-counter--type_input month-input" data-bind="css: {'invalid': isInvalid() && !month()}">
          <month-picker
          style="width: 100%"
          params="value: month, start: 'today', allowEmptyMonth: !question.isRequired, selectedMonth: month"
        ></month-picker>
        </div>
      </div>
    </div>
    <span class="error-message" data-bind="visible: isInvalid() && !isRequired()"
      >Неверный формат</span
    >
    <span class="error-message" data-bind="visible: isRequired()"
      >Обязательное поле</span>
   </div>

  <!-- /ko -->
  <!-- ko if: maskType === 6 || (question.type == 3 && !question.onlyDateMonth) -->
  <div class="col-12 col-md-4">
    <fc-date-picker
    style="width: 100%"
    params="value: field, invalid: isInvalid() || isRequired()"
    data-bind="css: {'is-invalid': isInvalid && !skipped()}"
  ></fc-date-picker>
  <span class="error-message" data-bind="visible: isInvalid() && !isRequired()"
      >Неверный формат</span>
      <span class="error-message" data-bind="visible: isRequired"
      >Обязательное поле</span
    >
  </div>

  <!-- /ko -->
  <!-- ko if: maskType === 7 -->
  <div class="col-12 masked-field--period">
    <div class="d-flex align-items-center">
      <fc-date-picker
        params="value: startDate, invalid: isInvalid() || isInvalidPeriod() || isRequired()"
        class="edit-control-sm"
      ></fc-date-picker>
      <span class="ml-10p mr-10p">–</span>
      <fc-date-picker
        params="value: endDate, invalid: isInvalid() || isInvalidPeriod() || isRequired()"
        class="edit-control-sm"
      ></fc-date-picker>
    </div>
    <span
      class="error-message"
      data-bind="visible: (isInvalid() || isInvalidPeriod()) && !isRequired()"
    >Неверный формат</span>
    <span
      class="error-message"
      data-bind="visible: isRequired() "
    >Обязательное поле</span>
  </div>
  

  <!-- /ko -->
  <!-- ko if: maskType === 1 -->
  <div class="col-12 col-md-4">
    <div class="chars-counter chars-counter--type_input edit-control-sm">
      <input
        class="form-control"
        data-bind="textInput: field,
        attr:{
          placeholder: '+7 (___) ___-____',
          mask: activeMask
        }, css: {'is-invalid': isInvalid() || isRequired()}
        "
      />
    </div>
    <span class="error-message" data-bind="visible: isInvalid() && !isRequired() "
      >Неверный формат</span
    >
    <span class="error-message" data-bind="visible: isRequired"
      >Обязательное поле</span
    >
  </div>
  <!-- /ko -->

  <!-- ko if: maskType === 4 -->
  <div class="col-12 col-md-8">
    <div class="chars-counter chars-counter--type_input">
      <input
        class="form-control"
        data-bind="textInput: field,
      attr:{
        placeholder: 'http://example.com',
        mask: activeMask
      }, css: {'is-invalid': isInvalid() || isRequired()}"
      />
    </div>
    <span class="error-message" data-bind="visible: isInvalid() && !isRequired()"
      >Неверный формат</span
    >
    <span class="error-message" data-bind="visible: isRequired"
      >Обязательное поле</span
    >
  </div>
  <!-- /ko -->

    <!-- ko if: maskType === 5 -->
    <div class="col-12 col-md-8">
      <div class="chars-counter chars-counter--type_input">
        <!-- ko if: typeof field().surname === 'function' -->
        <label class="form-label" for="answer-surname">Фамилия</label>
        <div class="mb-4">
          <input class="form-control" id="answer-surname"
                 data-bind="textInput: field().surname,
                        css: {'is-invalid': !skipped() && question.maskConfig.surname.required && !field().surname().length}" />
          <span class="error-message"
                data-bind="visible: !skipped() && question.maskConfig.surname.required && !field().surname().length">
        Обязательное поле
      </span>
        </div>
        <!-- /ko -->
        <!-- ko if: typeof field().name === 'function' -->
        <label class="form-label" for="answer-name">Имя</label>
        <div class="mb-4">
          <input class="form-control"
                 data-bind="textInput: field().name,
                        css: {'is-invalid': !skipped() && question.maskConfig.name.required && !field().name().length}"
                 id="answer-name"/>
          <span class="error-message"
                data-bind="visible: !skipped() && question.maskConfig.name.required && !field().name().length">
        Обязательное поле
      </span>
        </div>
        <!-- /ko -->
        <!-- ko if: typeof field().patronymic === 'function' -->
        <label class="form-label" for="answer-patronymic">Отчество</label>
        <div class="mb-4">
          <input class="form-control"
                 data-bind="textInput: field().patronymic,
                        css: {'is-invalid': !skipped() && question.maskConfig.patronymic.required && !field().patronymic().length}"
                 id="answer-patronymic"/>
          <span class="error-message"
                data-bind="visible: !skipped() && question.maskConfig.patronymic.required && !field().patronymic().length">
        Обязательное поле
      </span>
        </div>
        <!-- /ko -->
      </div>
    </div>
    <!-- /ko -->

  <!-- ko if: question.skip -->
  <div class="col-12 skip-row mb-25p">
    <div class="f-check">
      <input type="checkbox" id="comment-required" class="f-check-input" data-bind="
          checked: skipped
      ">
      <label for="comment-required" class="f-check-label" data-bind="text: question.skipText || 'Затрудняюсь ответить'"></label>
    </div>
  </div>
  <!-- /ko -->

  <!-- <button class="btn btn-primary" data-bind="click:validateField">чек</button> -->
</div>
</div>
