import { get as _get, forEach } from "lodash";

import { DialogsModule } from "@/utils/dialogs-module";
import { DialogWrapper } from "Dialogs/wrapper";
import { Review } from "Models/review";
import { ApiUrl } from "Utils/url/api-url";
const token = $('meta[name="csrf-token"]').attr("content");

let nextId = 0;

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);
    DialogsModule(this);
    this.initializing = ko.observable(true);
    this.loading = ko.observable(true);
    this.params = params;
    this.questionNumber = ko.observable(params.questionNumber);
    this.isPending = ko.observable(false)
    this.questions = params.questions
    this.activeQuestion = params.activeQuestion
    this.slider = ko.observable(null);
    this.updating = ko.observable(false)
    this.proccessing = ko.observable(false)
    this.isCanged = ko.observable(false)
    this.review = params.review

    this.sortedQuestions = ko.pureComputed(() => {
      let questions = this.questions;
      let notDeleted = questions.filter((q) => !q.isDeleted || q.hasAnswer);
      let deleted = questions.filter((q) => q.isDeleted && !q.hasAnswer);
      return [...notDeleted, ...deleted];
    });

    this.blocked = window.CURRENT_USER.blockAnswers;

    this.id = nextId++;
    this.showSaveMessage = ko.observable(false);
    this.answerId = params.reviewId

    this.executorMode = params.executorMode;
    this.hideClientData = params.hideClientData;
    this.enableActions = params.enableActions;
    this.question = ko.observable(params.question);
    this.comment = ko.observable(null)
    this.skipped = ko.observable(false)
    this.fields = ko.observableArray([])
    this.textValue = ko.observable(this.question().answer ? this.question().answer.text : null)

    this.extra = ko.observable(false);
    this.extraValue = ko.observable(null)
    this.extraSelfValue = ko.observable(null)
    this.extraVariants = ko.observableArray([])
    this.extraValid = ko.observable()

    
    this.history = ko.observable(null);
    this.historyOpened = ko.observable(false);
    this.objValue = ko.observable(this.question().answer ? this.question().answer.answer?.values : null)
    this.validStep = ko.observable(true);
    this.changeParams = ko.observable(false);

    
    this.processingForm = ko.observable(null);

    this.answers = params.answers;
    this.reviewId = params.reviewId;
    this.activeBullet = $('.review-questions-slider').find('.swiper-pagination-bullet-active').attr('aria-label').replace('Go to slide ', '') - 1


    this.updateQuestionInfo();
    this.setSliderPosition();
  }

  setSliderPosition() {
    setTimeout(() => {
      const activeBullet = $('.review-questions__nav-edit').find('.swiper-pagination-bullet')[this.activeBullet]
      $(activeBullet).click()
    }, 500);
  }

  updateSliderPosition() {
    this.activeBullet = $('.review-questions__nav-edit')?.find('.swiper-pagination-bullet-active')?.attr('aria-label')?.replace('Go to slide ', '') - 1
  }

  updateQuestionInfo() {
    this.extra = ko.observable(false);
    this.extraValue = ko.observable(null)
    this.extraSelfValue = ko.observable(null)
    this.extraVariants = ko.observableArray([])
    this.extraValid = ko.observable()
    this.fields = ko.observableArray([])
    this.changeParams(true)
    this.validStep = ko.observable(true);
    this.changeParams(false)
    this.textValue = ko.observable(this.question().answer ? this.question().answer.text : null)
    this.objValue = ko.observable(this.question().answer ? this.question().answer.answer?.values : null)
    if (this.question().type == 3) {
      this.textValue(this.question().answer)
    }
    if (this.question().type == 12) {
      this.textValue(this.question().answer ? this.question().answer.answer.rating : null)
      this.comment(this.question().answer ? this.question().answer.answer.comment : null)
      this.skipped(this.question().answer ? this.question().answer.skipped : 0)
    }

    if (this.question().type == 14) {
      this.question().differentialRows.forEach(i => {
        const key = i.id

        const obj = {
          id: key,
          value: ko.observable(this.question().answer && this.question().answer.answer.answer ? this.question().answer.answer.answer[key] : null)
        }
        this.fields.push(obj)
      })
      
      this.comment(this.question().answer ? this.question().answer.answer.comment : null)
      this.skipped(this.question().answer ? this.question().answer.skipped : 0)
    }

    if (this.question().type == 7) {
      for (const k in this.question().variants) {
        const originalItem = this.question().variants[k]
        const key = originalItem.id
        if (key != 'extra') {
          const originalItem = this.question().variants.find(i => i.id*1 == key)
          let value
          if (!this.question().answer) {
            value = 0
          } else if (!this.question().answer.answer.answer) {
            value = 0
          } else {
            value = this.question().answer?.answer.answer && this.question().answer.answer.answer[key] !== 'null' ? +this.question().answer.answer.answer[key] : null
          }
          
          const obj = {
            id: key,
            value: ko.observable(value),
            need_extra: originalItem.need_extra,
            can_skip: this.question().skipVariant,
            name: originalItem.text,
            skipped: ko.observable(value === null ? true : false)
          }

          obj.value.subscribe(v => {
            this.isCanged(true)
            if (v) {
              this.skipped(0)
              obj.skipped(false)
            }
          })

          if (obj.need_extra && this.question().clarifyingQuestion) {
            obj.extraVariants = ko.observableArray(this.question().clarifyingQuestionVariants.map(i => i))
            obj.extra = ko.computed(() => {
              if (!obj.value()) {
                return false
              }
              
              return obj.value() >= (this.question().config.extra_question_rate_from || 0) && obj.value() <= (this.question().config.extra_question_rate_to || this.question().config.count);
            })
            
            obj.extraValue = ko.observable('')
            if (this.question().variantsType !== 2) {
              
              obj.extraValue = ko.observableArray([])
              obj.extraSelfValue = ko.observable(null)
              if (this.question().answer?.answer.answer && this.question().answer.answer.answer.extra && this.question().answer.answer.answer.extra[key] && this.question().answer.answer.answer.extra[key].length) {
                obj.extraValue = ko.observableArray(this.question().answer.answer.answer.extra[key].map(i => i))
              }

              if (this.question().answer?.answer.answer && this.question().answer.answer.answer.extra && typeof this.question().answer.answer.answer.extra[key] === 'object' && !this.question().answer.answer.answer.extra[key].length) {
                
                for (const k in this.question().answer.answer.answer.extra[key]) {
                  if (k != 'self_variant') {
                    obj.extraValue.push(this.question().answer.answer.answer.extra[key][k])
                  } else {
                    obj.extraValue.push('-1')
                    obj.extraSelfValue(this.question().answer.answer.answer.extra[key][k])
                  }
                  
                }
              }
            }  else if (this.question().answer?.answer?.answer?.extra && this.question().answer.answer.answer.extra[key]) {
              obj.extraValue = ko.observable(this.question().answer.answer.answer.extra[key].answer)
            }
          }
  
          if (this.question().isSelfAnswer && obj.extraVariants) {
            obj.extraVariants.push({
              variant: this.question().self_variant_text.length ? this.question().self_variant_text : 'Свой вариант',
              id: -1
            })
          }

          

          obj.skipped.subscribe(v => {
            this.isCanged(true)
            if (v) {
              obj.value(null)
            }
          })



          this.fields.push(obj)
        }
        
      }

      this.comment(this.question().answer ? this.question().answer.answer.comment : null)
      this.skipped(this.question().answer ? this.question().answer.skipped : 0)
      this.skipped.subscribe(v => {
        this.isCanged(true)
        if (v) {
          this.fields().forEach(i => i.value(null))
        } else {
          this.fields().forEach(i => {
            if (!i.value()) {
              i.value(0)
            }
          })
        }
      })
    }

    if (this.question().type == 15) {
      this.textValue(this.question().answer ? this.question().answer.rating : null)
      this.comment(this.question().answer ? this.question().answer.comment : null)
      this.skipped(this.question().answer ? this.question().answer.skipped : 0)

      if (this.question().config?.extra_question_rate_from && this.question().config?.extra_question_rate_to) {
        this.extra = ko.computed(() => {
          if (this.question().config?.extra_question_rate_from && this.question().config?.extra_question_rate_to) {
            if (!this.textValue()) {
              return false
            }
            return this.textValue() >= this.question().config.extra_question_rate_from && this.textValue() <= this.question().config.extra_question_rate_to;
          }
          return false
        })
      }

      if (this.question().clarifyingQuestion !== null) {
        this.extraValue = ko.observable('')
        if (this.question().variantsType !== 2) {
          this.extraValue = ko.observableArray(this.question().answer ? [...this.question().answer.selectedIds] : [])
          this.extraVariants(this.question().variants.map(i => i))
          
          if (this.question().isSelfAnswer) {
            this.extraVariants.push({
              variant: this.question().self_variant_text.length ? this.question().self_variant_text : 'Свой вариант',
              id: -1
            })
            if (this.question().answer) {
              this.extraSelfValue(this.question().answer.selfVariant)
            }
            
          }
          if (this.question().answer?.selfVariant) {
            this.extraValue.push('-1')
          }
        } else {
          this.extraValue(this.question().answer ? this.question().answer.comment || '' : '')
        }
        
      }
    }

    if (this.question().type == 11) {
      this.textValue(this.question().answer ? this.question().answer.answer.rating : null)
      this.comment(this.question().answer ? this.question().answer.answer.comment : null)
      this.skipped(this.question().answer ? this.question().answer.skipped : 0)
    }

    if (this.question().type == 1) {
      if (this.question().answer) {
        this.textValue = ko.observableArray([...this.question().answer])
      } else {
        this.textValue = ko.observableArray([])
      }
      
      this.selfAnswer = ko.observable(this.question().selfVariant ? this.question().selfVariant : '')
      this.comment(this.question().comment)
      this.skipped(this.question().skipped)
      console.log(this.question())
    }

    if (this.question().type == 2) {
      this.skipped(this.question().skipped)
    }

    if (this.question().type == 5) {
      this.textValue = ko.observableArray(this.question().answer ? [...this.question().answer.answer.files] : [])
      this.comment(this.question().comment)
      this.skipped(this.question().skipped)
    }

    if (this.question().type == 6) {
      this.question().formFields.forEach((i, index) => {
        const obj = i
        
        
        let value = ''
        if (this.question().answer) {
          value = this.question().answer.answer.values.find(v => i.id == v.id)?.value
        }
        i.value = ko.observable(value || '')

        let error = false
        if (i.isRequired && (typeof obj.value() === 'string' && !obj.value().length)) {
          error = true
        }
        i.error = ko.observable(error)

        if (i.maskType == 8) {
          const [day, month] = obj.value().length ? value.split(".") : ['',''];
          obj.month = ko.observable(month*1);
          obj.day = ko.observable(day);
          let invalid = false

          if (i.isRequired && !obj.value().length) {
            invalid = true
          }

          obj.isInvalid = ko.observable(invalid)

          obj.month.subscribe(v => {
            this.isCanged(true)
            obj.value(`${obj.day()}.${obj.month()}`)
            const daysInMonth = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
            if (obj.isRequired) {
              obj.isInvalid(obj.day().length && (obj.day()*1 > daysInMonth[obj.month()*1 - 1] || v*1 < 1));
            }
            
          })

          obj.day.subscribe(v => {
            this.isCanged(true)
            obj.value(`${obj.day()}.${obj.month()}`)

            const daysInMonth = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

            if (obj.isRequired || v.length) {
              obj.isInvalid(v.length && (v*1 > daysInMonth[obj.month()*1 - 1] || v*1 < 1) || !obj.month().length);
            }

            if (!obj.isRequired && !v.length) {
              obj.isInvalid(false);
            }
            

            if (!v && obj.isRequired) {
              obj.error(true)
              obj.isInvalid(false)
            } else {
              obj.error(false)
            }
          })
          
        } else if (i.maskType == 7) {
          obj.isInvalidPeriod = ko.observable(false);
          obj.isInvalid = ko.observable(false);
          const [start, end] = value ? value.split("—") : ['',''];
          obj.startDate = ko.observable(start);
          obj.endDate = ko.observable(end);

          obj.startDate.subscribe(v => {
            this.isCanged(true)
            obj.value(`${obj.startDate()}—${obj.endDate()}`)
          })
          obj.endDate.subscribe(v => {
            this.isCanged(true)
            obj.value(`${obj.startDate()}—${obj.endDate()}`)
          })
          obj.value.subscribe(v => {
            this.isCanged(true)
            if (obj.isRequired || v.length) {
              obj.isInvalid(!moment(obj.startDate(), 'DD.MM.YYYY').isValid() || !moment(obj.endDate(), 'DD.MM.YYYY').isValid());
            }

            if (!obj.isRequired && !v.length) {
              obj.isInvalid(false);
            }
           
            if (!obj.isInvalid()) {
              const start = new Date(obj.startDate().split('.').reverse().join('.')).getTime()
              const end = new Date(obj.endDate().split('.').reverse().join('.')).getTime()
              obj.isInvalidPeriod(start > end)
            }

            if (obj.isRequired && (!obj.startDate().length || !obj.endDate().length)) {
              obj.error(true)
              obj.isInvalid(false)
              obj.isInvalidPeriod(false)
            } else {
              obj.error(false)
            }
          })
        } else if (i.maskType == 5) {
          for (const key in i.maskConfig) {
            if (i.maskConfig[key].visible == "true") {
              obj[key] = ko.observable(i.value() ? i.value()[key] : "")
              obj[key].subscribe(v => {
                this.isCanged(true)
                if (!v && i.maskConfig[key].required == "true") {
                  obj.error(true)
                } else {
                  obj.error(false)
                }
              })
            }
          }
        } else if (i.maskType == 1) {
          obj.isInvalid = ko.observable(false)
          i.value.subscribe(v => {
            this.isCanged(true)
            if (obj.isRequired || v.length) {
              obj.isInvalid(v.replace('_', '').length < 17);
            } 
            if (!obj.isRequired && !v.length) {
              obj.isInvalid(false);
            }
            
            if (!v && obj.isRequired) {
              obj.error(true)
              obj.isInvalid(false)
            } else {
              obj.error(false)
            }
          })
        } else if (i.maskType == 2) {
          obj.isInvalid = ko.observable(false)
          
          i.value.subscribe(v => {
            this.isCanged(true)
            const emailPattern = /^\S+@\S+\.\S+$/;
            if (obj.isRequired || v.length) {
              obj.isInvalid(!emailPattern.test(v))
            }

            if (!obj.isRequired && !v.length) {
              obj.isInvalid(false)
            }
            
            if (!v && obj.isRequired) {
              obj.error(true)
              obj.isInvalid(false)
            } else {
              obj.error(false)
            }
          })
        } else if (i.maskType == 4) {
          obj.isInvalid = ko.observable(false)
          
          i.value.subscribe(v => {
            this.isCanged(true)
            if (obj.isRequired || v.length) {
              const urlPattern =
              /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/;
              obj.isInvalid(!urlPattern.test(v))
            }

            if (!obj.isRequired && !v.length) {
              obj.isInvalid(false)
            }
            
            if (!v && obj.isRequired) {
              obj.error(true)
              obj.isInvalid(false)
            } else {
              obj.error(false)
            }
          })
        } else if (i.maskType == 6) {
          obj.isInvalid = ko.observable(false)
          
          i.value.subscribe(v => {
            this.isCanged(true)
            if (obj.isRequired || v.length) {
              const [d,m,y] = v.split('.')
              obj.isInvalid(!moment(v, 'DD.MM.YYYY').isValid() || !m.length || y.length < 4)
            }

            if (!obj.isRequired && !v.length) {
              obj.isInvalid(false)
            }
            
            if (!v && obj.isRequired) {
              obj.error(true)
              obj.isInvalid(false)
            } else {
              obj.error(false)
            }
          })
        } else {
          i.value.subscribe(v => {
            this.isCanged(true)
            if (!v && obj.isRequired) {
              obj.error(true)
            } else {
              obj.error(false)
            }
          })
        }
        
  
        this.fields.push(obj)
      })
    }

    if (this.question().type == 6) {
      this.changeParams(true)
      this.validStep = ko.computed(() => {
        return !this.fields().some(f => (f.error && f.error()) || (f.isInvalid && f.isInvalid()));
      })
      this.changeParams(false)
    }

    if (this.question().type == 7) {
      this.changeParams(true)
      this.validStep = ko.pureComputed(() => {
        const comentRequaired = this.question().commentRequired;
        if (comentRequaired && !this.comment()) {
          return false;
        }
        const questionRequaired = this.question().isRequired;
        this.fields().forEach(f => {
          if (f.extraValue) {
            console.log(f.extraValue())
          }
          
        })
        const valid = this.skipped() || !this.fields().some(f => {
          const extraRequired =  f.extraValue && f.extraSelfValue && f.extraValue() && f.extraValue().includes('-1');
          if (extraRequired) {
            const extraValue = f.extraSelfValue();
            if (!extraValue) return true;
          } 
          if (f.extraValue && typeof f.extraValue() === "string") {
            if (this.question().extraRequired && f.extra() && !f.extraValue().length) {
              return true
            }
          }

          if (f.extraValue && typeof f.extraValue() !== "string") {
            if (this.question().extraRequired && f.extra() && !f.extraValue().length) {
              return true
            }
          }
          const isSkipped = f.skipped && f.skipped();
          const value = f.value();
          if (questionRequaired && !isSkipped && !value) return true;
          return false;
        });
        return valid;
        
      })
      this.changeParams(false)
    }
  }

  getReviewPointsHtml(review) {
    return `<span class="f-color-service">${_t("Набрано баллов")}: </span>
    ${_t("main", "{num1} из {num2}", {
      num1: `<span class="bold">${review.points().answer_points}</span>`,
      num2: `<span class="bold">${review.points().points_max}</span>`,
    })},
    <span>${review.points().percent}</span>%`;
  }


  loadReview(reviewId) {
    let review = Review.get(reviewId);
    if (review) {
      return Promise.resolve(review);
    }
    return new Promise((res) => {
      $.ajax({
        url: ApiUrl("answers/view"),
        data: {
          id: reviewId,
        },
        success: (response) => {
          response.questions.forEach((el, index) => {
            // ToDo рефакторинг
            el.setVariants = _get(this, "answers[index].setVariants", false);
          });
          res(new Review(response));
        },
        error: (response) => {
          console.error(response.responseJSON);
        },
      });
    });
  }

  init() {
    this.getComplaintFancyboxCaption = function () {
      const passedAt = this.review.passedAt;
      const text = this.review.complaint.text;
      return (
        `<div class="review-complaint__fancybox-description">
                        <div class="review-complaint__fancybox-description-passed-at">${passedAt}</div>` +
        (text !== null
          ? `<div class="review-complaint__fancybox-description-text">${text}</div>`
          : "") +
        `</div>`
      );
    };
  }

  async submit() {
    this.isPending(true)
    const anwerId = new URLSearchParams(window.location.href).get("reviewId");

    const getParams = `answer_id=${anwerId}&question_id=${this.question().id}&access-token=${window.APIConfig.apiKey}`;

    const formData = new FormData;

    if (this.question().type == 3) {
      if (this.question().date_type != 2) {
        formData.append(`date`, this.textValue())
      }
      if (this.question().date_type == 2) {
        const [date, time] = this.textValue().split(' ')
        formData.append(`date`, date.replace('.', '').length ? date : null)
        formData.append(`time`, time.length ? time : null)
      }

    } else if (this.question().type == 14) {
      const empty = !this.fields().find(({ value }) => value() !== 'null' && value() !== null);
      if (this.skipped()) {
        formData.append(`skipped`, 1)
      } else if (!empty) {
        this.fields().forEach(i => {
          formData.append(`answer[${i.id}]`, i.value())
        })
      }
      if (this.comment()) {
        formData.append(`comment`, this.comment())
      }
    } else if (this.question().type == 6) {
      this.fields().forEach(f => {
        if (f.maskType !== 5) {
          formData.append(`${f.id}`, f.value())
        } else {
          for (const key in f.maskConfig) {
            if (f.maskConfig[key].visible == 'true') {
              formData.append(`${f.id}[${key}]`, f[key]())
            }
          }
        }
      })
    } else if (this.question().type == 7) {
      if (this.comment()) {
        formData.append(`comment`, this.comment())
      }

      if (this.skipped()) {
        formData.append(`skipped`, 1)
      } else {
        this.fields().forEach(i => {
          let value = i.value();
          if (!value) {
            value = i.skipped()
                ? null
                : 0;
          }
          formData.append(`detail_item[${i.id}]`, value)
          if (i.need_extra && i.extra && i.extra()) {
            if (typeof i.extraValue() !== 'string') {
              if (!i.extraValue().includes('-1')) {
                i.extraValue().forEach((v, index) => {
                  formData.append(`detail_item[extra][${i.id}][${index}]`, v)
                })
              } else {
                i.extraValue().forEach((v, index) => {
                  if (v !== '-1') {
                    formData.append(`detail_item[extra][${i.id}][${index}]`, v)
                  } else {
                    formData.append(`detail_item[extra][${i.id}][self_variant]`, i.extraSelfValue())
                  }
                })
              }
            } else {
              formData.append(`detail_item[extra][${i.id}][answer]`, i.extraValue())
            }


          }
        })
      }

    } else if (this.question().type == 12) {
      if (this.skipped()) {
        formData.append(`skipped`, 1)
      } else {
        formData.append(`rating`, this.textValue() !== null ? this.textValue() : '-1')
        if (this.comment() !== null) {
          formData.append(`comment`, this.comment())
        }
      }

    } else if (this.question().type == 15) {
      if (this.skipped()) {
        formData.append(`skipped`, 1)
      } else {
        if (this.textValue() !== null) {
          formData.append(`rating`, this.textValue())
        }

        if (this.comment() && this.question().commentEnabled) {
          formData.append(`answer`, this.comment())
        }

        if (this.extra()) {
          const extraType = this.question().variantsType

          if (extraType !== 2 && typeof this.extraValue === 'function' && this.extraValue().length) {
            this.extraValue().forEach((item, index) => {
              if (item == -1) {
                formData.append(`detail_item[]`, 'is_self_answer')
                formData.append(`self_variant`, this.extraSelfValue())
              } else {
                formData.append(`detail_item[]`, item)
              }
            })
          } else {
            formData.append(`answer`, this.extraValue())
          }
        }
      }

    } else if (this.question().type == 5) {
      this.textValue().forEach(i => {
        const id = typeof i.id === 'function' ? i.id() : i.id
        formData.append(`detail_item[]`, id)
      })
      if (this.comment() !== null) {
        formData.append(`answer`, this.comment())
      }
    } else if (this.question().type == 11) {
      if (this.skipped()) {
        formData.append(`skipped`, 1)
      } else {
        formData.append(`rating`, this.textValue() ? this.textValue() : '0')
        let smileId = ''
        if (this.textValue()) {
          smileId = this.question().smiles[this.textValue() - 1].id
        }

        if (this.comment() !== null) {
          formData.append(`comment`, this.comment())
          if (!this.textValue()) {
            smileId = this.comment()
          }
        }
        formData.append(`answer`, smileId)
      }

    } else if (this.question().type == 1) {
      if (this.skipped()) {
        formData.append(`answer`, '')
        formData.append(`skipped`, 1)
      } else {
        this.textValue().forEach((item, index) => {
          if (item == -1) {
            formData.append(`detail_item[]`, 'is_self_answer')
            formData.append(`self_variant`, this.selfAnswer())
          } else {
            formData.append(`detail_item[]`, item)
          }
        })
        if (this.comment() !== null) {
          formData.append(`answer`, this.comment())
        }
      }
    } else if (this.question().type == 2) {
      if (this.skipped()) {
        formData.append(`skipped`, 1)
        formData.append(`answer`, '')
      } else {
        if (this.question().mask_type == 7) {
          formData.append(`answer`, this.textValue().replace('—', '').length ? this.textValue() : '')
        } else if (this.question().mask_type == 8) {
          formData.append(`answer`, this.textValue().replace('.', '').length ? this.textValue() : '')
        } else if (typeof this.textValue() === 'string') {
          formData.append(`answer`, this.textValue())
        } else {
          for (const key in this.textValue()) {
            formData.append(`answer[${key}]`, this.textValue()[key]())
          }
        }
      }
    } else if (typeof this.textValue() === 'string') {
      formData.append(`answer`, this.textValue())
    } else {
      for (const key in this.textValue()) {
        formData.append(`answer[${key}]`, this.textValue()[key]())
      }
    }
    this.updateSliderPosition()

    this.proccessing(true)

    const self = this
    $.ajax({
      url : `/foquz/api/answers/save-answer?${getParams}`,
      data : formData,
      type : "POST",
      processData: false,
      contentType: false,
      success : function (res) {
        self.params.callback(res.model, self.activeQuestion().id)
        self.updateQuestionInfo()
        self.isPending(false)
        self.proccessing(false)
        self.isCanged(false)
        self.showSaveMessage(true);
        self.setSliderPosition()
        setTimeout(() => {
          self.showSaveMessage(false);
        }, 4000);
      }
    });
  }

  onSubmit(data) {
    this.emitEvent("update", data);
    this.showSaveMessage(true);
    setTimeout(() => {
      this.showSaveMessage(false);
    }, 4000);
  }

  changeQuestion(data) { 
    if (!this.isCanged()) {
      this.updating(true)
      this.question(data)
      this.activeQuestion(data)
      this.updateQuestionInfo()
      requestAnimationFrame(() => {
        this.updating(false)
        this.isCanged(false)
        this.questionNumber(this.review.questions().filter(q => !q.isDeleted).indexOf(this.activeQuestion()) + 1)
        this.params.sync(data)
      })
      
    } else {
      this.confirm({
        title: "Данные не сохранены",
        text: "<span class='review-edit-confirm'>Данные редактирования текущего ответа не сохранены и будут потеряны без возможности восстановления.<span>",
        confirm: "Перейти",
      }).then(() => {
        this.updating(true)
        this.question(data)
        this.activeQuestion(data)
        this.updateQuestionInfo()
        this.updating(false)
        this.isCanged(false)
        this.questionNumber(this.review.questions().filter(q => !q.isDeleted).indexOf(this.activeQuestion()) + 1)
        this.params.sync(data)
      });
    }
    
    
    
  }

  cancel() {
    this.showSaveMessage(false);
    this.processingForm().cancel();
  }

  async deleteAnswer() {
    const self = this
    this.confirm({
      title: "Сброс ответа",
      text: "<span class='delete-confirm-text'>Ответ на вопрос будет удалён без возможности восстановления.</span>",
      confirm: "Сбросить",
      mode: "danger",
    }).then(async () => {
      self.updateSliderPosition()
      self.proccessing(true)

      const anwerId = new URLSearchParams(window.location.href).get("reviewId");
      const getParams = `answer_id=${anwerId}&question_id=${self.question().id}&access-token=${window.APIConfig.apiKey}`
      const res = await fetch(
        `/foquz/api/answers/save-answer?${getParams}`,
        { method: "POST" }
      );
      const parced = await res.json()
      self.params.callback(parced.model, self.activeQuestion().id)
      
      self.updateQuestionInfo()
      self.proccessing(false)
      self.showSaveMessage(true);
      self.isCanged(false)
      self.setSliderPosition()
        setTimeout(() => {
          self.showSaveMessage(false);
        }, 4000);
    });
  }

  printReview(filtered) {
    if (filtered && !this.review.dictionary_id) {
      this.info({
        text: "Опция доступна, если настроены <a class='dialog-link' href='https://foquz.ru/foquz/user-wiki/kak-tegirovat-voprosy' target='_blank'>связи вопросов</a> с элементами из справочника",
      });
      return;
    }
    let { href } = window.location;
    href = `/foquz/foquz-poll/print-answer?id=${this.reviewId}${
      filtered ? "&filtered=1" : ""
    }`;
    window.open(href, "_blank");
  }
}
