<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="review-details-modal__tab-content foquz-dialog__body review-edit-modal">
    <div class="h-100">
      <!-- ko if: proccessing -->
      <div class="h-100 d-flex align-items-center justify-content-center">
        <fc-spinner class="f-color-primary"></fc-spinner>
      </div>
      <!-- /ko -->
      <!-- ko ifnot: proccessing -->
      <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
        <!-- ko if: question().recipients.length -->
        <div class="recipients-wrapper">
          <div class="container">
            <div class="mb-5p">При редактировании этого ответа сбросятся ответы в вопросах, для которых он является донором:</div>
            <!-- ko foreach: question().recipients -->
            <div class="">
              <span data-bind="text: $data.number + '. '"></span>
              <span data-bind="text: $data.name"></span>
            </div>
            <!-- /ko -->
          </div>
        </div>
        <!-- /ko -->
        
        <div class="container  pt-4">
          <h2 class="pb-0 mb-15p foquz-dialog__edit-title" data-bind="text: 'Редактирование ответа', click: function () {console.log(review)}"></h2>
          <!-- ko if: review && review.withPoints() && review.processingId -->
              <div
              class="f-color-text mb-4 review-details__points"
              data-bind="html: getReviewPointsHtml(review)"
            ></div>
          <!-- /ko -->
          <div class="review-questions__nav review-questions__nav-edit">
            <div
              class="swiper-container"
              data-bind="
                  ref: slider,
                  swiper: {
                    slidesPerView: 'auto',
                    slidesPerGroupAuto: true,
                    spaceBetween: 12,
                    shortSwipes: false,
                    touchAngle: 10,
          
                    pagination: {
                      el: '.swiper-pagination',
                      clickable: true,
                    },
                  }"
            >
              <div class="swiper-pagination"></div>
              <div class="swiper-wrapper">
                <!-- ko foreach: sortedQuestions -->
                <review-question-preview
                  class="swiper-slide"
                  params="index: $index(),
                   question: $data,
                   review: $parent.review,
                   active: $parent.activeQuestion() == $data,
                   onClick: function() {
                      $parent.changeQuestion($data)
                   }"
                ></review-question-preview>
                <!-- /ko -->
              </div>
            </div>
          </div>
          <!-- ko if: ['8', '9','10', '13', '16', '17', '18'].includes(question().type) -->
          <div class="no-edit-text">Редактирование ответа на вопрос этого типа пока в разработке</div>
          <!-- /ko -->

          <!-- ko ifnot: ['8', '9','10', '13', '16', '17', '18'].includes(question().type) -->
          <p class="foquz-dialog__edit-question-title" data-bind="css:{'mb-25p': (!review.withPoints() && !question().without_points) || question().correctVariantsText == '', 'mb-15p': (review.withPoints() || question().without_points) && question().correctVariantsText !== ''}">
            <span class="mr-5p" data-bind="text: questionNumber() + '.'"></span>
            <span data-bind="text:question().description"></span>
            <!-- ko if: question().isRequired -->
            <span class="question-required">*</span>
            <!-- /ko -->
          </p>

          <!-- ko if: !updating() -->
          <!-- ko if: (review.withPoints() || question().without_points) && question().correctVariantsText !== '' -->
          <div class="review-question-view__meta mb-25p">
            <!-- ko if: question().hasAnswer && question().maxPoints && !question().without_points -->
            <div class="mr-4 mb-2">
              <span
                class="f-color-service"
                data-bind="text: _t('Баллов за ответ') + ':'"
              ></span>
              <span
                data-bind="html: _t('main', '{num1} из {num2}', {
                num1: '<span class=\'bold\'>' + (question().answerPoints || 0) + '</span>',
                num2: '<span class=\'bold\'>' + (question().maxPoints) + '</span>',
              }), css: {'empty': question().answerPoints == 0 || !question().answerPoints}"
              ></span>
              <!-- ko if: question().answerPoints == 0 || !question().answerPoints -->
              <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 7C0 8.933 0.783502 10.683 2.05025 11.9497C3.317 13.2165 5.067 14 7 14C10.866 14 14 10.866 14 7C14 5.067 13.2165 3.317 11.9497 2.05025C10.683 0.783502 8.933 0 7 0C3.13401 0 0 3.13401 0 7Z" fill="#FF0200"/>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M3 7C3 6.44772 3.44772 6 4 6H10C10.5523 6 11 6.44772 11 7C11 7.55228 10.5523 8 10 8H4C3.44772 8 3 7.55228 3 7Z" fill="white"/>
              </svg>
              <!-- /ko -->
            </div>
            <!-- /ko -->

            <!-- ko if: question().hasAnswer && question().without_points -->
            <div class="mr-4 mb-2">
              <span
                class="f-color-service"
                data-bind="text: _t('Баллов за ответ') + ':'"
              ></span>
              <span
                data-bind="html: _t('main', '<span>' + 'выбранные варианты не участвуют в подсчёте' + '</span>')"
              ></span>
            </div>
            <!-- /ko -->

            <div class="mb-2">
              <span
                class="f-color-service"
                data-bind="text: _t('Правильный ответ') + ':', click: function () {console.log(question())}"
              ></span>
              <span data-bind="text: question().correctVariantsText || question().correctVariantText"></span>
            </div>
          </div>
          <!-- /ko -->
          <!-- /ko -->

          
          <!-- ko if: !updating() -->
          <!-- ko if: question().type == 2 || (question().type == 3 && question().date_type == 0) -->
          <text-field-edit params="isCanged:isCanged, question:question(), validStep: validStep, value: textValue, skipped: skipped" data-bind="event: {change: function () {console.log('change')}}"></text-field-edit>
          <!-- /ko -->
          <!-- ko if: question().type == 3 && question().date_type != 0 -->
          <date-field-edit params="isCanged:isCanged, question:question(), validStep: validStep, value: textValue" data-bind="event: {change: function () {console.log('change')}}"></date-field-edit>
          <!-- /ko -->
          <!-- ko if: question().type == 12 -->
          <nps-field-edit params="isCanged:isCanged, question:question(), validStep: validStep, value: textValue, comment: comment, skipped: skipped" data-bind="event: {change: function () {console.log('change')}}"></nps-field-edit>
          <!-- /ko -->
          <!-- ko if: question().type == 11 -->
          <smile-field-edit params="isCanged:isCanged, question:question(), validStep: validStep, value: textValue, comment: comment, skipped: skipped" data-bind="event: {change: function () {console.log('change')}}"></smile-field-edit>
          <!-- /ko -->
          <!-- ko if: question().type == 6 -->
          <quiz-field-edit params="isCanged:isCanged, question:question(), validStep: validStep, fields: fields, comment: comment, skipped: skipped" data-bind="event: {change: function () {console.log('change')}}"></quiz-field-edit>
          <!-- /ko -->
          <!-- ko if: question().type == 4 -->
          <adres-field-edit params="isCanged:isCanged, question:question(), validStep: validStep, skipped: skipped, value: textValue" data-bind="event: {change: function () {console.log('change')}}"></adres-field-edit>
          <!-- /ko -->
           <!-- ko if: question().type == 1 -->
          <variant-field-edit params="isCanged:isCanged, question:question(), validStep: validStep, skipped: skipped, value: textValue, selfAnswer: selfAnswer, comment: comment" data-bind="event: {change: function () {console.log('change')}}"></variant-field-edit>
          <!-- /ko -->
           <!-- ko if: question().type == 5 -->
          <file-field-edit params="isCanged:isCanged, question:question(), validStep: validStep, skipped: skipped, value: textValue, comment: comment" data-bind="event: {change: function () {console.log('change')}}"></file-field-edit>
          <!-- /ko -->
           <!-- ko if: question().type == 15 -->
          <star-field-edit params="isCanged:isCanged, question: question(), validStep: validStep, value: textValue, comment: comment, skipped: skipped, extraValue: extraValue, extraVariants: extraVariants, extraValid: extraValid, extraSelfValue: extraSelfValue, extra: extra"></star-field-edit>
          <!-- /ko -->
          <!-- ko if: question().type == 7 -->
          <star-variants-field-edit params="isCanged:isCanged, question: question(), validStep: validStep, fields: fields, comment: comment, skipped: skipped, extraValue: extraValue, extraVariants: extraVariants, extraValid: extraValid, extraSelfValue: extraSelfValue, extra: extra"></star-variants-field-edit>
          <!-- /ko -->
          <!-- ko if: question().type == 14 -->
          <diff-field-edit params="isCanged:isCanged, question:question(), validStep: validStep, fields: fields, comment: comment, skipped: skipped" data-bind="event: {change: function () {console.log('change')}}"></diff-field-edit>
          <!-- /ko -->
          <!-- /ko -->
          <!-- /ko -->
        </div>
      </div>
      <!-- /ko -->
    </div>
  </div>
  <!-- ko if: !blocked && !historyOpened() && !window.CURRENT_USER.watcher -->
  <div class="foquz-dialog__footer fixed-footer fixed">
    <div class="d-flex mx-3">
      <div class="d-flex align-items-center w-100">
        <!-- ko if: !proccessing() -->
        <!-- ko if: question().hasAnswer -->
        <button
          type="button"
          class="fc-btn-b fc-btn-b--md fc-btn-b--danger fc-btn-b--mode_text fc-btn-b--label"
          data-bind="
              click: function() {
                deleteAnswer();
              },
            "
        >
          <!-- ko text: _t('Сбросить ответ') -->
          <!-- /ko -->
        </button>
        <!-- /ko -->
        <!-- /ko -->
        <button
          type="button"
          class="f-btn ml-auto"
          data-bind="
              click: function() {
                $dialog.hide();
              },
            "
        >
          <foquz-icon params="icon: 'bin'" class="f-btn-prepend"></foquz-icon>
          <!-- ko text: _t('Отменить') -->
          <!-- /ko -->
        </button>
        <!-- ko if: !changeParams() -->
        <button
          type="submit"
          class="f-btn f-btn-success"
          data-bind="
              click: function() {
                submit();
              },
              attr: {disabled: !validStep() || isPending()},
              css: {
                'f-btn--loading': isPending,
              }
            "
        >
          <foquz-icon params="icon: 'save'" class="f-btn-prepend"></foquz-icon>
          <!-- ko text: _t('Сохранить') -->
          <!-- /ko -->
        </button>
        <!-- /ko -->
      </div>
    </div>

    <success-message params="show: showSaveMessage"></success-message>
    
  </div>
  <!-- /ko -->
  
  
</sidesheet>
