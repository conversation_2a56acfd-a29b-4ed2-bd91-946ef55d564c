import { FoquzComponent } from 'Models/foquz-component';

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);

    this.review = params.review;
    this.directories = params.directories;
    this.answerTags = params.answerTags;
    this.hideClientData = params.hideClientData;
    this.answerTagsOpen = ko.observable(false);
    this.answerTagsPanelOpen = ko.observable(false);
    this.answerCustomFields = params.review.answerCustomFields;
      
    this.answerTags.subscribe((answerTags) => {
      if (!Array.isArray(answerTags) || (answerTags.length <= 5)) {
        this.answerTagsOpen(false);
      }
    })

    this.answerTagsOpen.subscribe((flag) => {
      if (!Array.isArray(this.answerTags()) || (this.answerTags().length <= 5)) {
        this.answerTagsPanelOpen(false);
        return;
      }
      if (flag) {
        this.answerTagsPanelOpen(true);
      } else {
        setTimeout(() => {
          this.answerTagsPanelOpen(false);
        }, 250)
      }
    })
  }
  formatDate(date) {
    return date.split(' ').map(i => `<span>${i}</span>`).join(' ');
  }
}
