@import 'Style/colors';
@import 'Style/breakpoints';

.ui-tooltip {
  display: none!important;
}

.review-data {
  .review-date {
    .svg-icon {
      flex-shrink: 0;
      margin-right: 8px;
      vertical-align: middle;
    }
    & > span {
      display: flex;
      align-items: center;
    }
  }
  .client-info {
    .badge {
      align-self: flex-start;
      margin-right: 12px;
    }
  }
  .review-notifications__list {
    line-height: 1;
  }
  .review-lang {
    font-size: 12px;
    font-weight: 500;
    color: #73808D;
  }
  .only-mobile({
    .review-date {
      flex-grow: 1;
      flex-basis: 25%;
      align-self: flex-start;

      .svg-icon {
        margin-bottom: 4px;
      }
      .date {
        display: flex;
        flex-direction: column;
      }
    }
    .review-date > span {
      flex-direction: column;
      align-items: flex-start;
    }
    .badge {
      margin-top: 2px;
    }

  });

  .review-details-modal__info-block--dates {
    padding-top: 10px;
    padding-bottom: 10px;

    .review-date {
      white-space: nowrap;
    }
  }
  .review-details-modal__info-block--notifications {
    &.compact {
      flex-grow: 0;
      min-width: 95px;
    }
  }

  .review-details-modal__info-block--client{
    justify-content: flex-start;
    max-width: 346px;
  }
  .review-details-modal__info-block--answerTags {
    width: 33.33%;
    &_open {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      z-index: 10;
      box-shadow: 0px 5px 20px 0px #73808D66;
    }
    .clients__tag-input-content {
      padding: 15px 20px;
      flex-grow: 1;
    }
    .only-mobile({
      .clients__tag-input-content {
        padding: 0;
      }
    });
    .clients__tag-input-item {
      color: #73808D;
    }
    .clients__tag-input-item-remove-button, .clients__tag-input-add-tag-button {
      background: #fff!important;
    }
    .clients__tag-input-content::before {
      content: '';
      display: inline-block;
      width: 17px;
      height: 17px;
      margin-right: 15px;
      background-size: 100% 100% !important;
      background-repeat: no-repeat !important;
      background-position: center;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='17' height='17' viewBox='0 0 17 17' fill='none'%3E%3Cpath d='M5 5H5.01M14.6056 9.73899L9.74375 14.6021C9.6178 14.7283 9.46823 14.8283 9.30359 14.8966C9.13896 14.9649 8.96248 15 8.78426 15C8.60604 15 8.42956 14.9649 8.26493 14.8966C8.10029 14.8283 7.95072 14.7283 7.82477 14.6021L2 8.78264V2H8.78087L14.6056 7.82629C14.8582 8.08045 15 8.42427 15 8.78264C15 9.14102 14.8582 9.48483 14.6056 9.73899Z' stroke='%2373808D' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
    }
  }
}
