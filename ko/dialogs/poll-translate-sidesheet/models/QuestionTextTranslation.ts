import { IPollQuestionText } from "@/entities/models/poll-question/questionTypes/text.types";
import { NameField } from "@/entities/structures/name-mask/types";
import {
  QuestionSavedTranslation,
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
} from "../types";
import { QuestionTranslation } from "./QuestionTranslation";
import {
  QuestionTextFieldTranslation,
  QuestionTextFieldResult,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import {
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
} from "../types";
import {DEFAULT_TEXTS} from "@/dialogs/poll-translate-sidesheet/constants/defaultTexts";

type MaskConfigResult = Partial<
  NameField<{
    name: TextFieldTranslationResult;
    placeholder: TextFieldTranslationResult;
  }>
>;

type QuestionTextTranslationFields = QuestionBaseFieldsTranslation & {
  placeholder?: TextFieldTranslation;
  namePlaceholder?: TextFieldTranslation;
  surnamePlaceholder?: TextFieldTranslation;
  patronymicPlaceholder?: TextFieldTranslation;
  nameText?: TextFieldTranslation;
  surnameText?: TextFieldTranslation;
  patronymicText?: TextFieldTranslation;
  skipText?: TextFieldTranslation;
  isNameMask: boolean;
};

type QuestionTextTranslationResult = QuestionBaseFieldsTranslationResult & {
  placeholder_text?: TextFieldTranslationResult;
  labels?: MaskConfigResult;
  skip_text?: TextFieldTranslationResult;
};

type QuestionTextSavedTranslation = QuestionSavedTranslation & {
  placeholder_text?: string;
  labels?: string;
  skip_text?: string;
};

export class QuestionTextTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionTextTranslationFields;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionText;
    const {
      mask,
      skip,
      skipText
    } = question;
    const { placeholder, isNameMask } = mask;

    console.log({ mask });

    if (isNameMask) {
      this.fields.isNameMask = true;
      const { name, surname, patronymic } = mask.nameMask;

      if (name.visible) {
        this.fields.nameText = QuestionTextFieldTranslation("Имя");
        this.fields.namePlaceholder = QuestionTextFieldTranslation(
          name.placeholder
        );
      }

      if (surname.visible) {
        this.fields.surnameText = QuestionTextFieldTranslation("Фамилия");
        this.fields.surnamePlaceholder = QuestionTextFieldTranslation(
          surname.placeholder
        );
      }
      if (patronymic.visible) {
        this.fields.patronymicText = QuestionTextFieldTranslation("Отчество");
        this.fields.patronymicPlaceholder = QuestionTextFieldTranslation(
          patronymic.placeholder
        );
      }
    } else if (!mask.isDateMonthMask) {
      this.fields.placeholder = QuestionTextFieldTranslation(placeholder);
    }

    if (skip) {
      this.fields.skipText = QuestionTextFieldTranslation(
          skipText || DEFAULT_TEXTS.skipTextVariants
      );
    }
  }

  getData(): QuestionTextTranslationResult {
    const result: QuestionTextTranslationResult = {
      ...super.getData(),
    };

    if (this.fields.placeholder) {
      result.placeholder_text = QuestionTextFieldResult(
        this.fields.placeholder
      );
    }

    if (this.fields.skipText) {
      result.skip_text = QuestionTextFieldResult(this.fields.skipText);
    }

    if (this.fields.isNameMask) {
      const maskConfig: MaskConfigResult = {};

      if (this.fields.namePlaceholder) {
        maskConfig.name = {
          name: QuestionTextFieldResult(this.fields.nameText),
          placeholder: QuestionTextFieldResult(this.fields.namePlaceholder),
        };
      }

      if (this.fields.surnamePlaceholder) {
        maskConfig.surname = {
          name: QuestionTextFieldResult(this.fields.surnameText),
          placeholder: QuestionTextFieldResult(this.fields.surnamePlaceholder),
        };
      }

      if (this.fields.patronymicPlaceholder) {
        maskConfig.patronymic = {
          name: QuestionTextFieldResult(this.fields.patronymicText),
          placeholder: QuestionTextFieldResult(
            this.fields.patronymicPlaceholder
          ),
        };
      }

      result.labels = maskConfig;
    }

    return result;
  }

  updateTranslation(data: QuestionTextSavedTranslation): void {
    super.updateTranslation(data);

    const { labels, placeholder_text, skip_text } = data;

    let _labels: NameField<{
      name: string;
      placeholder: string;
    }> = null;

    if (typeof labels === "object") {
      _labels = labels;
    } else if (labels) {
      try {
        _labels = JSON.parse(labels);
      } catch(e) {

      }
    }

    updateQuestionTextFieldTranslation(
      this.fields.placeholder,
      placeholder_text
    );

    const name = (_labels && _labels.name) || { name: "", placeholder: "" };
    const surname = (_labels && _labels.surname) || { name: "", placeholder: "" };
    const patronymic = (_labels && _labels.patronymic) || {
      name: "",
      placeholder: "",
    };

    updateQuestionTextFieldTranslation(this.fields.nameText, name.name);
    updateQuestionTextFieldTranslation(
      this.fields.namePlaceholder,
      name.placeholder
    );
    updateQuestionTextFieldTranslation(this.fields.surnameText, surname.name);
    updateQuestionTextFieldTranslation(
      this.fields.surnamePlaceholder,
      surname.placeholder
    );
    updateQuestionTextFieldTranslation(
      this.fields.patronymicText,
      patronymic.name
    );
    updateQuestionTextFieldTranslation(
      this.fields.patronymicPlaceholder,
      patronymic.placeholder
    );
    updateQuestionTextFieldTranslation(this.fields.skipText, skip_text);
  }
}
