import "./style.scss";
import {
  createFrameModal,
  createStyleLink,
  dispatchCustomEvent,
  handlePollLink,
  toFormData,
  getScrollDepthTrigger,
  setupScrollDepthTrigger,
} from "./utils";
import {
  EVENTS,
  URLS,
  COOKIES,
  WIDGET_SETTINGS,
  CSS_CLASSES,
  URL_PARAMS,
  CUSTOM_EVENTS,
} from "./constants";

const LOCAL_TEST_POLL =
  "http://doxsw.local/p/F62c1345c57141?nopreview=1&simple=1";

// данные о странице
function getPageParams(SDK) {
  return {
    location: document.location.href,
  };
}

// кастомные параметры, указанные при загрузке
function getCustomParams(sdk) {
  const { widget, params } = sdk;
  const params_ = {};

  if (widget && typeof widget === "object") {
    Object.entries(widget).forEach(([key, value]) => {
      params_[key] = value;
    });
  }
  if (params && typeof params === "object") {
    Object.entries(params).forEach(([key, value]) => {
      params_[key] = value;
    });
  }

  return params_;
}

function readCookie(name) {
  var name_cook = name + "=";
  var spl = document.cookie.split(";");

  for (var i = 0; i < spl.length; i++) {
    var c = spl[i];

    while (c.charAt(0) == " ") {
      c = c.substring(1, c.length);
    }

    if (c.indexOf(name_cook) == 0) {
      return c.substring(name_cook.length, c.length);
    }
  }

  return null;
}

// обработка ответа сервера
function formatResponse(xhr) {
  const responseData = xhr.responseText || "";

  let data = { show: false };

  try {
    const json = JSON.parse(responseData);

    const foquzCoockie = readCookie(COOKIES.CLIENT_UUID);

    if (!foquzCoockie) {
      document.cookie = `${COOKIES.CLIENT_UUID}=${json.clientUUID}`;
    }

    const { link, key, time, widget } = json;
    const pollUrl = handlePollLink(link, widget);

    let isSimple = false;
    if (pollUrl) {
      const url = new URL(pollUrl);
      if (url.searchParams.get(URL_PARAMS.SIMPLE) == "1") isSimple = true;
    }

    const scrollDepthTrigger = getScrollDepthTrigger(widget);

    data = {
      show: !!link,
      pollUrl,
      isSimple,
      time,
      widget,
      key,
      scrollDepthTrigger,
    };
  } catch (e) {}

  return data;
}

(() => {
  let ROOT = URLS.ROOT;
  let ASSETS_ROOT = URLS.ASSETS_ROOT;

  window.FOQUZ_SDK = window.FOQUZ_SDK || {};
  const SDK = window.FOQUZ_SDK;
  window.FQZWidget = SDK;

  if (SDK._test) {
    ROOT = SDK._test.root || ROOT;
    ASSETS_ROOT = SDK._test.assetsRoot || ASSETS_ROOT;
  }

  // загрузка стилей для виджета
  const loadStyles = createStyleLink(
    `${ASSETS_ROOT}widgets/poll/style_new.css`
  );
  let activeWidget = null;

  window.FQZWidget.triggerEvent = (eventName, eventParams = {}) => {
    if (!eventName) {
      console.error("Event name is required");
      return;
    }
    const mergedParams = {
      ...getPageParams(SDK),
      ...getCustomParams(SDK),
      ...eventParams,
    };
    fetchWidgetSettings(mergedParams, eventName).then((settings) => {
      if (!settings.widget) return;
      if (!settings.show) return;

      // If scroll depth trigger is enabled, set up the scroll trigger
      if (settings.scrollDepthTrigger) {
        const cleanupScrollTrigger = setupScrollDepthTrigger(settings.scrollDepthTrigger, () => {
          const time = settings.time || 0;
          setTimeout(() => {
            openWidget(settings);
          }, time * 1000);
        });
        
        // Clean up scroll trigger when widget is destroyed
        document.addEventListener(CUSTOM_EVENTS.DESTROYED, () => {
          cleanupScrollTrigger();
        }, { once: true });
      } else {
        // No scroll trigger, open widget directly
        const time = settings.time || 0;
        setTimeout(() => {
          openWidget(settings);
        }, time * 1000);
      }
    });
  };

  if (SDK?.preview) {
    window.addEventListener("message", (event) => {
      const { data } = event;
      try {
        const eventData = typeof data === "string" ? JSON.parse(data) : data;

        if (eventData.source === "foquz" && eventData.type === "fz:preview") {
          const { config } = eventData;

          if (config?.params) {
            const { quiz, simpleView, hello, windowSettings, close_by_finish_button } = config.params;
            let isSimple = simpleView === 1;
            let form = "0";
            if (hello) {
              form = "5";
            } else if (isSimple) {
              form = "4";
            }

            let simple = isSimple ? "1" : "0";

            if (hello) {
              simple = "0";
              isSimple = false;
            }

            const widget = {
              form,
              simple,
              window_settings: windowSettings,
              close_by_finish_button: close_by_finish_button ? "1" : "0",
            };

            const pollUrl = window.location.origin + "/poll-vue/" + quiz;

            const previewSettings = {
              show: true,
              pollUrl: handlePollLink(pollUrl, widget),
              isSimple,
              widget,
            };

            openWidget(previewSettings, false, null, true);
          }
        }
      } catch (e) {
        // Silently handle parse errors
      }
    });
  }

  function sendShowStatus(params) {
    return new Promise((resolve) => {
      const formData = toFormData({
        key: params.key,
      });

      let xhr = new XMLHttpRequest();
      xhr.open("GET", `${ASSETS_ROOT}v1/visit/shown?key=${params.key}`);
      xhr.send(formData);

      xhr.onload = function () {
        resolve(xhr);
      };
    });
  }

  // получить настройки виджета
  function fetchWidgetSettings(params, eventName = null) {
    return new Promise((resolve) => {
      const { widget_code, location, ...extra } = params;
      const formData = toFormData({
        widget_code: widget_code,
        url: location,
        cookies: document.cookie,
        params: extra,
        mobile: screen.width < WIDGET_SETTINGS.MOBILE_SCREEN_WIDTH ? 1 : 0,
      });

      if (eventName) {
        formData.append("event", eventName);
      }

      let xhr = new XMLHttpRequest();
      xhr.open("POST", `${ROOT}v1/visit`);
      xhr.send(formData);

      xhr.onload = function () {
        const data = formatResponse(xhr);
        resolve(data);
      };
    });
  }

  // добавляем кнопку
  function appendButton(settings) {
    const el = `<span type="button" class="${
      CSS_CLASSES.WIDGET_PREVIEW_BUTTON
    }" style="font-family: ${settings.widget.font}; font-size: ${
      settings.widget.font_size
    }px; color: ${settings.widget.text_color}; background-color: ${
      settings.widget.background_color
    }; ${
      settings.widget.stroke == 1
        ? "border-width: 1px; border-style: solid; border-color: currentcolor;"
        : "border: none;"
    } font-weight: ${
      settings.widget.bold == 1 ? "bold" : "normal"
    }; font-style: ${
      settings.widget.italic == 1 ? "italic" : "normal"
    };border-radius: 4px;display: flex;align-items: center;line-height: 1;min-height: 35px;padding: ${
      settings.widget.button_type == 0 ? "0px 25px" : "0"
    };white-space: nowrap;cursor: pointer;">
    <div class="${CSS_CLASSES.WIDGET_PREVIEW_TEXT}">
      <span style="${
        settings.widget.button_type != 0 ? "display: none" : ""
      }">${
      settings.widget.button_text || WIDGET_SETTINGS.DEFAULT_BUTTON_TEXT
    }</span>
      <div style="display: none">
        <svg>
          <symbol id="foquz-logo-xs-icon" class="quiz" width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5.31852 17.9999H2.72741C2.26927 17.9999 1.8299 17.8208 1.50595 17.502C1.18199 17.1832 1 16.7508 1 16.2999V10.3499C1 9.89907 1.18199 9.46668 1.50595 9.14787C1.8299 8.82906 2.26927 8.64995 2.72741 8.64995H5.31852M11.3645 6.94996V3.54998C11.3645 2.87369 11.0915 2.22509 10.6055 1.74687C10.1196 1.26866 9.46055 1 8.77334 1L5.31852 8.64995V17.9999H15.0611C15.4777 18.0045 15.882 17.8608 16.1994 17.5953C16.5168 17.3298 16.7261 16.9603 16.7885 16.5549L17.9804 8.90495C18.018 8.6613 18.0013 8.41253 17.9315 8.17587C17.8617 7.9392 17.7404 7.72031 17.5761 7.53435C17.4118 7.34838 17.2084 7.1998 16.9799 7.0989C16.7514 6.99799 16.5034 6.94717 16.253 6.94996H11.3645Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </symbol>
          <symbol id="foquz-logo-xs" width="43" height="9" viewBox="0 0 43 9" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M5.12512 1.78826C5.0963 1.90718 4.98932 1.99098 4.86632 1.99098H1.99566V3.75228H4.66213L4.23531 5.45327H1.99566V8.77079H0V0.229676H5.16483C5.33709 0.229676 5.46399 0.389902 5.42363 0.556438L5.12512 1.78826ZM14.9658 4.46358C14.9658 7.16585 13.0292 8.99953 10.4077 8.99953C7.798 8.99953 5.86139 7.16585 5.86139 4.46358C5.86139 1.72511 7.798 0 10.4077 0C13.0292 0 14.9658 1.72511 14.9658 4.46358ZM12.8049 4.46368C12.8049 2.96777 11.8248 1.85791 10.4078 1.85791C8.99072 1.85791 8.02242 2.96777 8.02242 4.46368C8.02242 6.00783 9.00253 7.11769 10.4078 7.11769C11.813 7.11769 12.8049 6.00783 12.8049 4.46368ZM25.7192 8.50559C25.7192 8.6518 25.6 8.77032 25.4529 8.77032H20.6296C17.9727 8.77032 16.166 7.18998 16.166 4.39119C16.166 1.79749 18.1144 0 20.7005 0C23.2984 0 25.1996 1.78543 25.1996 4.355C25.1996 5.65788 24.5619 6.65917 23.9597 7.12966V7.16585L25.4474 7.13525C25.5966 7.13218 25.7192 7.25158 25.7192 7.39993V8.50559ZM23.0623 4.41566C23.0623 2.93182 22.094 1.87022 20.7006 1.87022C19.3544 1.87022 18.3389 2.93182 18.3389 4.41566C18.3389 5.94775 19.3072 7.00936 20.6888 7.00936C22.0586 7.00936 23.0623 5.94775 23.0623 4.41566ZM34.1106 5.54978C34.1106 7.60061 32.6936 9 30.509 9C28.3126 9 26.9428 7.60061 26.9428 5.54978V0.494414C26.9428 0.348203 27.062 0.229676 27.209 0.229676H28.684C28.8311 0.229676 28.9503 0.348203 28.9503 0.494414V5.38088C28.9503 6.29773 29.3872 7.14219 30.5208 7.14219C31.6662 7.14219 32.0913 6.29773 32.0913 5.38088V0.494414C32.0913 0.348203 32.2105 0.229676 32.3576 0.229676H33.8444C33.9914 0.229676 34.1106 0.348203 34.1106 0.494414V5.54978ZM42.2308 8.50605C42.2308 8.65226 42.1116 8.77079 41.9646 8.77079H35.9078C35.7607 8.77079 35.6416 8.65226 35.6416 8.50605V7.07736C35.6416 7.01777 35.6618 6.95993 35.6989 6.91318L39.2801 2.40783C39.4181 2.23424 39.2938 1.97891 39.0713 1.97891H36.0022C35.8552 1.97891 35.736 1.86039 35.736 1.71418V0.494414C35.736 0.348203 35.8552 0.229676 36.0022 0.229676H41.8819C42.0289 0.229676 42.1481 0.348203 42.1481 0.494414V1.79009C42.1481 1.84987 42.1278 1.90789 42.0904 1.9547L38.4169 6.55601C38.2784 6.7295 38.4026 6.98536 38.6254 6.98536H41.9646C42.1116 6.98536 42.2308 7.10389 42.2308 7.2501V8.50605Z" fill="currentColor" />
          </symbol>
          <symbol id="foquz-logo-sm" width="12" height="19" viewBox="0 0 12 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M11.3238 3.44366C11.2601 3.7064 11.0238 3.89155 10.752 3.89155H4.40935V7.7831H10.3008L9.3578 11.5414H4.40935V18.8713H0V0H11.4115C11.7921 0 12.0725 0.354016 11.9834 0.721973L11.3238 3.44366Z" fill="currentColor"></path>
          </symbol>
        </svg>
      </div>
      <svg class="${CSS_CLASSES.WIDGET_PREVIEW_LOGO} ${
      CSS_CLASSES.WIDGET_PREVIEW_LOGO_LG
    }" ${
      settings.widget.button_type != 0
        ? 'width="19" height="19"'
        : 'width="43" height="9"'
    } style="${
      settings.widget.button_type != 0
        ? "margin-left: 8px;margin-right: 8px;"
        : "margin-left: 12px;"
    }">
        ${
          settings.widget.button_type != 0
            ? '<use xlink:href="#foquz-logo-xs-icon" href="#foquz-logo-xs-icon"></use>'
            : settings.widget.position == 0
            ? '<use xlink:href="#foquz-logo-xs" href="#foquz-logo-xs"></use>'
            : '<use xlink:href="#foquz-logo-sm" href="#foquz-logo-sm"></use>'
        }
      </svg>
    </div>
    </span>`;
    const elLeft = `<span type="button" class="${
      CSS_CLASSES.WIDGET_PREVIEW_BUTTON
    }" style="font-family: ${settings.widget.font}; font-size: ${
      settings.widget.font_size
    }px; color: ${settings.widget.text_color}; background-color: ${
      settings.widget.background_color
    }; ${
      settings.widget.stroke == 1
        ? "border-width: 1px; border-style: solid; border-color: currentcolor;"
        : "border: none"
    } font-weight: ${
      settings.widget.bold == 1 ? "bold" : "normal"
    }; font-style: ${
      settings.widget.italic == 1 ? "italic" : "normal"
    };border-radius: 0 0 6px 6px;overflow: hidden; box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.25); display: flex;align-items: center;line-height: 1;min-height: 35px;padding: ${
      settings.widget.button_type == 0 ? "0px 15px" : "0"
    };white-space: nowrap;cursor: pointer;">
    <div class="${
      CSS_CLASSES.WIDGET_PREVIEW_TEXT
    }" style="display: flex;align-items: center;">
      <svg class="${CSS_CLASSES.WIDGET_PREVIEW_LOGO} ${
      CSS_CLASSES.WIDGET_PREVIEW_LOGO_LG
    }" ${
      settings.widget.button_type != 0
        ? 'width="19" height="19"'
        : 'width="12" height="19"'
    } style="transform: rotate(90deg); ${
      settings.widget.button_type == 0
        ? "margin-right: 12px"
        : "margin-left: 8px;margin-right: 8px;"
    }">
        ${
          settings.widget.button_type != 0
            ? '<use xlink:href="#foquz-logo-xs-icon" href="#foquz-logo-xs-icon"></use>'
            : settings.widget.position == 0
            ? '<use xlink:href="#foquz-logo-xs" href="#foquz-logo-xs"></use>'
            : '<use xlink:href="#foquz-logo-sm" href="#foquz-logo-sm"></use>'
        }
      </svg>
      <span style="${
        settings.widget.button_type != 0 ? "display: none" : ""
      }">${
      settings.widget.button_text || WIDGET_SETTINGS.DEFAULT_BUTTON_TEXT
    }</span>
      <div style="display: none">
        <svg>
          <symbol id="foquz-logo-xs-icon" class="quiz" width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5.31852 17.9999H2.72741C2.26927 17.9999 1.8299 17.8208 1.50595 17.502C1.18199 17.1832 1 16.7508 1 16.2999V10.3499C1 9.89907 1.18199 9.46668 1.50595 9.14787C1.8299 8.82906 2.26927 8.64995 2.72741 8.64995H5.31852M11.3645 6.94996V3.54998C11.3645 2.87369 11.0915 2.22509 10.6055 1.74687C10.1196 1.26866 9.46055 1 8.77334 1L5.31852 8.64995V17.9999H15.0611C15.4777 18.0045 15.882 17.8608 16.1994 17.5953C16.5168 17.3298 16.7261 16.9603 16.7885 16.5549L17.9804 8.90495C18.018 8.6613 18.0013 8.41253 17.9315 8.17587C17.8617 7.9392 17.7404 7.72031 17.5761 7.53435C17.4118 7.34838 17.2084 7.1998 16.9799 7.0989C16.7514 6.99799 16.5034 6.94717 16.253 6.94996H11.3645Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </symbol>
          <symbol id="foquz-logo-xs" width="43" height="9" viewBox="0 0 43 9" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M5.12512 1.78826C5.0963 1.90718 4.98932 1.99098 4.86632 1.99098H1.99566V3.75228H4.66213L4.23531 5.45327H1.99566V8.77079H0V0.229676H5.16483C5.33709 0.229676 5.46399 0.389902 5.42363 0.556438L5.12512 1.78826ZM14.9658 4.46358C14.9658 7.16585 13.0292 8.99953 10.4077 8.99953C7.798 8.99953 5.86139 7.16585 5.86139 4.46358C5.86139 1.72511 7.798 0 10.4077 0C13.0292 0 14.9658 1.72511 14.9658 4.46358ZM12.8049 4.46368C12.8049 2.96777 11.8248 1.85791 10.4078 1.85791C8.99072 1.85791 8.02242 2.96777 8.02242 4.46368C8.02242 6.00783 9.00253 7.11769 10.4078 7.11769C11.813 7.11769 12.8049 6.00783 12.8049 4.46368ZM25.7192 8.50559C25.7192 8.6518 25.6 8.77032 25.4529 8.77032H20.6296C17.9727 8.77032 16.166 7.18998 16.166 4.39119C16.166 1.79749 18.1144 0 20.7005 0C23.2984 0 25.1996 1.78543 25.1996 4.355C25.1996 5.65788 24.5619 6.65917 23.9597 7.12966V7.16585L25.4474 7.13525C25.5966 7.13218 25.7192 7.25158 25.7192 7.39993V8.50559ZM23.0623 4.41566C23.0623 2.93182 22.094 1.87022 20.7006 1.87022C19.3544 1.87022 18.3389 2.93182 18.3389 4.41566C18.3389 5.94775 19.3072 7.00936 20.6888 7.00936C22.0586 7.00936 23.0623 5.94775 23.0623 4.41566ZM34.1106 5.54978C34.1106 7.60061 32.6936 9 30.509 9C28.3126 9 26.9428 7.60061 26.9428 5.54978V0.494414C26.9428 0.348203 27.062 0.229676 27.209 0.229676H28.684C28.8311 0.229676 28.9503 0.348203 28.9503 0.494414V5.38088C28.9503 6.29773 29.3872 7.14219 30.5208 7.14219C31.6662 7.14219 32.0913 6.29773 32.0913 5.38088V0.494414C32.0913 0.348203 32.2105 0.229676 32.3576 0.229676H33.8444C33.9914 0.229676 34.1106 0.348203 34.1106 0.494414V5.54978ZM42.2308 8.50605C42.2308 8.65226 42.1116 8.77079 41.9646 8.77079H35.9078C35.7607 8.77079 35.6416 8.65226 35.6416 8.50605V7.07736C35.6416 7.01777 35.6618 6.95993 35.6989 6.91318L39.2801 2.40783C39.4181 2.23424 39.2938 1.97891 39.0713 1.97891H36.0022C35.8552 1.97891 35.736 1.86039 35.736 1.71418V0.494414C35.736 0.348203 35.8552 0.229676 36.0022 0.229676H41.8819C42.0289 0.229676 42.1481 0.348203 42.1481 0.494414V1.79009C42.1481 1.84987 42.1278 1.90789 42.0904 1.9547L38.4169 6.55601C38.2784 6.7295 38.4026 6.98536 38.6254 6.98536H41.9646C42.1116 6.98536 42.2308 7.10389 42.2308 7.2501V8.50605Z" fill="currentColor" />
          </symbol>
          <symbol id="foquz-logo-sm" width="12" height="19" viewBox="0 0 12 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M11.3238 3.44366C11.2601 3.7064 11.0238 3.89155 10.752 3.89155H4.40935V7.7831H10.3008L9.3578 11.5414H4.40935V18.8713H0V0H11.4115C11.7921 0 12.0725 0.354016 11.9834 0.721973L11.3238 3.44366Z" fill="currentColor"></path>
          </symbol>
        </svg>
      </div>
    </div>
    </span>`;
    const elRight = `<span type="button" class="${
      CSS_CLASSES.WIDGET_PREVIEW_BUTTON
    }" style="font-family: ${settings.widget.font}; font-size: ${
      settings.widget.font_size
    }px; color: ${settings.widget.text_color}; background-color: ${
      settings.widget.background_color
    }; ${
      settings.widget.stroke == 1
        ? "border-width: 1px; border-style: solid; border-color: currentcolor;"
        : "border: none"
    } font-weight: ${
      settings.widget.bold == 1 ? "bold" : "normal"
    }; font-style: ${
      settings.widget.italic == 1 ? "italic" : "normal"
    };border-radius: 0 0 6px 6px;overflow: hidden; box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.25); display: flex;align-items: center;line-height: 1;min-height: 35px;padding: ${
      settings.widget.button_type == 0 ? "0px 15px" : "0"
    };white-space: nowrap;cursor: pointer;">
    <div class="${
      CSS_CLASSES.WIDGET_PREVIEW_TEXT
    }" style="display: flex;align-items: center;">
      <span style="${
        settings.widget.button_type != 0 ? "display: none" : ""
      }">${
      settings.widget.button_text || WIDGET_SETTINGS.DEFAULT_BUTTON_TEXT
    }</span>
      <div style="display: none">
        <svg>
          <symbol id="foquz-logo-xs-icon" class="quiz" width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5.31852 17.9999H2.72741C2.26927 17.9999 1.8299 17.8208 1.50595 17.502C1.18199 17.1832 1 16.7508 1 16.2999V10.3499C1 9.89907 1.18199 9.46668 1.50595 9.14787C1.8299 8.82906 2.26927 8.64995 2.72741 8.64995H5.31852M11.3645 6.94996V3.54998C11.3645 2.87369 11.0915 2.22509 10.6055 1.74687C10.1196 1.26866 9.46055 1 8.77334 1L5.31852 8.64995V17.9999H15.0611C15.4777 18.0045 15.882 17.8608 16.1994 17.5953C16.5168 17.3298 16.7261 16.9603 16.7885 16.5549L17.9804 8.90495C18.018 8.6613 18.0013 8.41253 17.9315 8.17587C17.8617 7.9392 17.7404 7.72031 17.5761 7.53435C17.4118 7.34838 17.2084 7.1998 16.9799 7.0989C16.7514 6.99799 16.5034 6.94717 16.253 6.94996H11.3645Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
          </symbol>
          <symbol id="foquz-logo-xs" width="43" height="9" viewBox="0 0 43 9" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M5.12512 1.78826C5.0963 1.90718 4.98932 1.99098 4.86632 1.99098H1.99566V3.75228H4.66213L4.23531 5.45327H1.99566V8.77079H0V0.229676H5.16483C5.33709 0.229676 5.46399 0.389902 5.42363 0.556438L5.12512 1.78826ZM14.9658 4.46358C14.9658 7.16585 13.0292 8.99953 10.4077 8.99953C7.798 8.99953 5.86139 7.16585 5.86139 4.46358C5.86139 1.72511 7.798 0 10.4077 0C13.0292 0 14.9658 1.72511 14.9658 4.46358ZM12.8049 4.46368C12.8049 2.96777 11.8248 1.85791 10.4078 1.85791C8.99072 1.85791 8.02242 2.96777 8.02242 4.46368C8.02242 6.00783 9.00253 7.11769 10.4078 7.11769C11.813 7.11769 12.8049 6.00783 12.8049 4.46368ZM25.7192 8.50559C25.7192 8.6518 25.6 8.77032 25.4529 8.77032H20.6296C17.9727 8.77032 16.166 7.18998 16.166 4.39119C16.166 1.79749 18.1144 0 20.7005 0C23.2984 0 25.1996 1.78543 25.1996 4.355C25.1996 5.65788 24.5619 6.65917 23.9597 7.12966V7.16585L25.4474 7.13525C25.5966 7.13218 25.7192 7.25158 25.7192 7.39993V8.50559ZM23.0623 4.41566C23.0623 2.93182 22.094 1.87022 20.7006 1.87022C19.3544 1.87022 18.3389 2.93182 18.3389 4.41566C18.3389 5.94775 19.3072 7.00936 20.6888 7.00936C22.0586 7.00936 23.0623 5.94775 23.0623 4.41566ZM34.1106 5.54978C34.1106 7.60061 32.6936 9 30.509 9C28.3126 9 26.9428 7.60061 26.9428 5.54978V0.494414C26.9428 0.348203 27.062 0.229676 27.209 0.229676H28.684C28.8311 0.229676 28.9503 0.348203 28.9503 0.494414V5.38088C28.9503 6.29773 29.3872 7.14219 30.5208 7.14219C31.6662 7.14219 32.0913 6.29773 32.0913 5.38088V0.494414C32.0913 0.348203 32.2105 0.229676 32.3576 0.229676H33.8444C33.9914 0.229676 34.1106 0.348203 34.1106 0.494414V5.54978ZM42.2308 8.50605C42.2308 8.65226 42.1116 8.77079 41.9646 8.77079H35.9078C35.7607 8.77079 35.6416 8.65226 35.6416 8.50605V7.07736C35.6416 7.01777 35.6618 6.95993 35.6989 6.91318L39.2801 2.40783C39.4181 2.23424 39.2938 1.97891 39.0713 1.97891H36.0022C35.8552 1.97891 35.736 1.86039 35.736 1.71418V0.494414C35.736 0.348203 35.8552 0.229676 36.0022 0.229676H41.8819C42.0289 0.229676 42.1481 0.348203 42.1481 0.494414V1.79009C42.1481 1.84987 42.1278 1.90789 42.0904 1.9547L38.4169 6.55601C38.2784 6.7295 38.4026 6.98536 38.6254 6.98536H41.9646C42.1116 6.98536 42.2308 7.10389 42.2308 7.2501V8.50605Z" fill="currentColor" />
          </symbol>
          <symbol id="foquz-logo-sm" width="12" height="19" viewBox="0 0 12 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M11.3238 3.44366C11.2601 3.7064 11.0238 3.89155 10.752 3.89155H4.40935V7.7831H10.3008L9.3578 11.5414H4.40935V18.8713H0V0H11.4115C11.7921 0 12.0725 0.354016 11.9834 0.721973L11.3238 3.44366Z" fill="currentColor"></path>
          </symbol>
        </svg>
      </div>
      <svg class="${CSS_CLASSES.WIDGET_PREVIEW_LOGO} ${
      CSS_CLASSES.WIDGET_PREVIEW_LOGO_LG
    }" ${
      settings.widget.button_type != 0
        ? 'width="19" height="19"'
        : 'width="12" height="19"'
    } style="transform: rotate(-90deg); ${
      settings.widget.button_type == 0
        ? "margin-left: 12px"
        : "margin-left: 8px;margin-right: 8px;"
    }">
        ${
          settings.widget.button_type != 0
            ? '<use xlink:href="#foquz-logo-xs-icon" href="#foquz-logo-xs-icon"></use>'
            : settings.widget.position == 0
            ? '<use xlink:href="#foquz-logo-xs" href="#foquz-logo-xs"></use>'
            : '<use xlink:href="#foquz-logo-sm" href="#foquz-logo-sm"></use>'
        }
      </svg>
    </div>
    </span>`;
    const elWrapper = document.createElement("button");
    elWrapper.style.border = "none";
    elWrapper.style.padding = 0;
    elWrapper.style.outline = "none";
    elWrapper.style.background = "transparent";
    if (settings.widget.position != 0) {
      elWrapper.style.position = "fixed";
      elWrapper.style.zIndex = "9999";
      elWrapper.style.top = "50%";
      elWrapper.style.position = "fixed";
      elWrapper.style.transform = "translateY(-50%)";
      if (settings.widget.position == 1) {
        elWrapper.style.left = "0px";
        elWrapper.style.transform = "translateY(150%) rotate(-90deg)";
        elWrapper.style.transformOrigin = "left top";
      } else {
        elWrapper.style.right = "0px";
        elWrapper.style.transform = "translateY(150%) rotate(90deg)";
        elWrapper.style.transformOrigin = "right top";
      }
    }
    elWrapper.addEventListener("click", function () {
      openWidget(settings, true);
    });
    if (settings.widget.position == 0) {
      elWrapper.innerHTML = el;
    }
    if (settings.widget.position == 1) {
      elWrapper.innerHTML = elLeft;
    }

    if (settings.widget.position == 2) {
      elWrapper.innerHTML = elRight;
    }

    const timer = settings.time || 0;
    if (settings.show) {
      setTimeout(() => {
        if (SDK.widget.id_parent_element && settings.widget.position == 0) {
          const target = document.getElementById(SDK.widget.id_parent_element);
          if (target) {
            target.innerHTML = "";
            target.appendChild(elWrapper);
            if (timer) {
              sendShowStatus();
            }
          } else {
            return;
          }
        } else {
          document.body.appendChild(elWrapper);
          if (timer) {
            sendShowStatus();
          }
        }
      }, timer * 1000);
    }
  }

  function getPollInfo(params) {
    return new Promise((resolve) => {
      const formData = toFormData({
        key: params.key,
      });

      let xhr = new XMLHttpRequest();
      xhr.open(
        "GET",
        `${ASSETS_ROOT}foquz/api/poll/widget-poll-info?key=${params.key}&new=1`
      );
      xhr.send(formData);

      xhr.onload = function () {
        resolve(xhr);
      };
    });
  }

  function onRatingClick(event, settings) {
    let ratingItem = event.target.closest("[data-rating]");

    if (!ratingItem) return;
    let rating = ratingItem.dataset.rating;
    openWidget(settings, true, rating);
  }

  // добавляем шкалу
  function appendScale(settings) {
    getPollInfo(settings).then((r) => {
      const res = JSON.parse(r.responseText);
      if (res.ratingScale) {
        if (SDK.widget.id_parent_element) {
          const target = document.getElementById(SDK.widget.id_parent_element);
          const style = `font-family: ${settings.widget.font}; font-size: ${
            settings.widget.font_size
          }px; color: ${settings.widget.text_color};font-weight: ${
            settings.widget.bold == 1 ? "bold" : "normal"
          }; font-style: ${settings.widget.italic == 1 ? "italic" : "normal"}`;
          if (target) {
            target.setAttribute("style", style);
            const timer = settings.time || 0;
            if (settings.show) {
              setTimeout(() => {
                target.innerHTML = res.ratingScale;
                target.addEventListener("click", function (e) {
                  onRatingClick(e, settings);
                });
                if (timer) {
                  sendShowStatus();
                }
              }, timer * 1000);
            }
          }
        }
      }
    });
  }

  // открыть виджет
  /**
   * @param {Object} settings
   * @param {boolean} showLoadingState
   * @param {string|null} answer
   * @param {boolean} isPreview
   */
  function openWidget(settings, showLoadingState, answer, isPreview = false) {
    if (activeWidget) {
      activeWidget.element.remove("full-view");
      activeWidget.destroy();
    }

    let { show, pollUrl: initialPollUrl, isSimple, widget, quiz } = settings;

    if (!show) return;

    loadStyles.then(() => {
      const modal = createFrameModal(
        showLoadingState,
        widget.form === WIDGET_SETTINGS.FORM_TYPES.HELLO_MODE,
        settings
      );

      window.addEventListener("message", (event) => {
        try {
          const message =
            typeof event.data === "string"
              ? JSON.parse(event.data)
              : event.data;

          if (!message?.source || message.source !== "foquz") return;

          if (message.type === EVENTS.CLOSE_BY_FINISH_BUTTON) {
            modal.hide();
          }
        } catch (e) {
          // Silently handle parse errors
        }
      });

      activeWidget = modal;
      const isSimpleView =
        widget.form === WIDGET_SETTINGS.FORM_TYPES.SIMPLE ||
        widget.form === WIDGET_SETTINGS.FORM_TYPES.SIMPLE_PAGE_STOP;
      if (isSimpleView) {
        modal.element.classList.remove("full-view");
      }
      if (
        (widget.form === "3" && widget.simple != 1) ||
        (widget.form === "1" && widget.simple != 1) ||
        (widget.form === "0" && widget.simple != 1) ||
        (widget.form === "2" && widget.simple != 1)
      ) {
        modal.element.classList.add("full-view");
      } else if (isSimpleView) {
        modal.element.classList.add("full-view-lite");
      } else if (widget.form === "5") {
        modal.element.classList.add("full-view-hello");
      }
      document.body.appendChild(modal.element);

      const pollUrl = buildPollUrl(initialPollUrl, widget, answer);
      modal.setPollUrl(pollUrl);
      modal.show();
    });
  }

  // Helper function to build the poll URL with all necessary parameters
  function buildPollUrl(baseUrl, widget, answer) {
    const url = new URL(baseUrl);

    if (
      widget.form === "4" ||
      widget.form === "5" ||
      (widget.simple === "1" && widget.form !== "3")
    ) {
      url.searchParams.set("simple", "1");
    }

    if (widget.form === "5") {
      url.searchParams.set("helo-mode", "1");
    }

    if (answer) {
      url.searchParams.set("fz_answer", answer);
    }

    if (SDK.preview) {
      url.searchParams.set("preview-mode", "1");
    }

    return url.toString();
  }

  // функция для загрузки виджета
  window.FOQUZ_SDK.openWidget = (params) => {
    // @NOTE: функцию оставил для совместимости со старой версией виджета
  };

  // немедленная загрузка виджета
  const params = {
    ...getPageParams(SDK),
    ...getCustomParams(SDK),
  };

  // Only fetch widget settings if not in preview mode
  if (!SDK.preview) {
    fetchWidgetSettings(params).then((settings) => {
      let cleanupScrollTrigger = () => {};

      if (settings.scrollDepthTrigger) {
        cleanupScrollTrigger = setupScrollDepthTrigger(settings.scrollDepthTrigger, () => {
          if (settings.widget?.appearance === "1") {
            appendButton(settings);
          } else {
            openWidget(settings);
          }
          if (settings.time) {
            sendShowStatus(settings);
          }

          cleanupScrollTrigger();
        });
      } else if (settings.widget?.appearance === "1" && 
          settings.widget?.form !== WIDGET_SETTINGS.FORM_TYPES.RATING_SCALE) {
        appendButton(settings);
      } else if (settings.widget?.form == WIDGET_SETTINGS.FORM_TYPES.RATING_SCALE) {
        appendScale(settings);
      } else if (settings.widget) {
        const timer = settings.time || 0;
        setTimeout(() => {
          openWidget(settings);
          if (timer) {
            sendShowStatus(settings);
          }
        }, timer * 1000);
      }

      // Cleanup scroll trigger when widget is destroyed
      document.addEventListener(CUSTOM_EVENTS.DESTROYED, () => {
        if (cleanupScrollTrigger) {
          cleanupScrollTrigger();
        }
      }, { once: true });
    });
  }

  // скрипт загружен
  if (typeof SDK.onLoad === "function") {
    SDK.onLoad();
  }

  dispatchCustomEvent(CUSTOM_EVENTS.LOADED);
})();
