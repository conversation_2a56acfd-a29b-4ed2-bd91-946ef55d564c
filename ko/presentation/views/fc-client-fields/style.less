@import 'Style/breakpoints';
@import 'Style/colors';

.fc-client-fields {
  display: block;
  position: relative;
  width: 100%;
  font-size: 12px;
  font-weight: 500;
  color: @f-color-service;

  &__line {
    display: flex;

    .fc-client-fields-item {
      white-space: nowrap;
      margin-right: 15px;
      vertical-align: middle;

      &__icon {
        vertical-align: middle;
      }

      &__data {
        margin-left: 5px;
        vertical-align: middle;
      }
    }

    .filial-name {
      font-weight: 700;
    }
    .filial-category-name {
      font-weight: 400;
    }

    &--clone {
      position: absolute;
      overflow: hidden;
      left: 0;
      right: 40px;
      top: 100%;
      visibility: hidden;
    }
  }

  &-dropdown {
    .tippy-content {
      padding: 24px 20px;
    }
    &__wrapper {
      width: 250px;
      font-size: 12px;
      font-weight: 500;
      margin-left: -20px;
      color: @f-color-service;
      display: flex;
      flex-direction: column;
    }
    .fc-client-fields-item {
      margin-left: 20px;
    }
    &__scroll {
      flex-grow: 1;
      margin-right: -10px;
      padding-right: 10px;
      max-height: 400px;
    }

    .os-scrollbar-vertical {
      display: block;
    }

    .fc-client-fields-item {
      display: flex;
      align-items: flex-start;

      &:not(:last-child){
        margin-bottom: 15px;
      }

      &__icon {
        flex-shrink: 0;
      }

      &__data {
        margin-left: 12px;
        word-break: break-word;
      }
    }

    .fc-client-field {

      &:not(:last-child){
        margin-bottom: 12px;
      }

      &__name {
        font-weight: 400;
        margin-bottom: 4px;
      }
    }

    [data-dropdown-close] {
      display: none;
    }

    &__customFields{
      width: 200px;
      font-size: 12px;
      margin-left: 0;
      color: @f-color-service;
      display: flex;
      flex-direction: column;
      margin-right: 0 !important;
    }

    .only-mobile({


      [data-dropdown-close] {
        display: block;
        width: 100%;
      }

      &__wrapper {
        width: 260px;
        margin-right: -20px;
      }

      &__customFields{
        width: 242px;
      }

      .fc-client-fields-item {
        margin-right: 20px;
      }
    });
  }

  &__custom{
    &-block{
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      column-gap: 10px;
    }
  }

  &__custom-field-item{
    //text-wrap: nowrap;
    &:not(:last-child) {
      margin-bottom: 7px;
    }

    &--key{
      font-weight: 400;
    }

    &--value{
      font-weight: 500;
    }
  }

  &-custom-item__list{
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    column-gap: 10px;
  }
}
