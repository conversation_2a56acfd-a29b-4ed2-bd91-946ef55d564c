<!-- ko if: showOnlyCustomFields -->
<div class="fc-client-fields__custom-line">
     <div class="fc-client-fields__custom-block">
          <svg-icon class="fc-client-fields-item__icon" params="name: 'signs'"></svg-icon>
          <!-- ko foreach: { data: firstTwoFields, as: 'field' } -->
          <div class="fc-client-fields__custom-field-item-content">
                    <span class="fc-client-fields__custom-field-item--key"
                          data-bind="text: field.id + ':'"></span>
               <span class="fc-client-fields__custom-field-item--value"
                     data-bind="text: field.value"></span>
          </div>
          <!-- /ko -->

          <!-- ko if: hasMoreThanTwoFields -->
          <div data-bind="dropdown, dropdownClass: 'fc-client-fields-dropdown', dropdownMode: 'modal'">
               <button class="button-ghost" data-dropdown-target>
                    <svg-icon params="name: 'ellipsis'" class="f-color-service"></svg-icon>
               </button>

               <template>
                    <div class="fc-client-fields-dropdown__wrapper fc-client-fields-dropdown__customFields"
                         data-tooltip-container>
                         <div class="fc-client-fields-dropdown__scroll" data-bind="nativeScrollbar">
                              <!-- ko foreach: remainingFields -->
                              <div class="fc-client-fields__custom-field-item">
                <span class="fc-client-fields__custom-field-item--key"
                      data-bind="text: id + ':'"></span>
                                   <span class="fc-client-fields__custom-field-item--value"
                                         data-bind="text: value"></span>
                              </div>
                              <!-- /ko -->
                         </div>

                         <div class="text-center flex-shrink-0"><button data-dropdown-close
                                                                        class="f-btn f-btn-link"
                                                                        data-bind="text: _t('Закрыть')"></button></div>
                    </div>
               </template>
          </div>
          <!-- /ko -->
     </div>
</div>
<!-- /ko -->

<!-- ko ifnot: showOnlyCustomFields -->

<!-- ko descendantsComplete: $component.onInit.bind($component )-->
<div class="fc-client-fields__line fc-client-fields__line--clone"
     data-bind="element: line">

     <!-- ko if: values.gender -->
     <div class="fc-client-fields-item"
          data-item="gender">
          <!-- ko if: values.gender == 1 -->
          <svg-icon class="fc-client-fields-item__icon"
                    params="name: 'gender-male'"></svg-icon>
          <!-- /ko -->
          <!-- ko if: values.gender == 2 -->
          <svg-icon class="fc-client-fields-item__icon"
                    params="name: 'gender-female'"></svg-icon>
          <!-- /ko -->
     </div>
     <!-- /ko -->

     <!-- ko if: values.birthday -->
     <div class="fc-client-fields-item"
          data-item="birthday">
          <svg-icon class="fc-client-fields-item__icon"
                    params="name: 'client-birthday'"></svg-icon>
          <span class="fc-client-fields-item__data"
                data-bind="text: values.birthday"></span>
     </div>
     <!-- /ko -->

     <!-- ko if: values.filials -->
     <div class="fc-client-fields-item"
          data-item="filials">
          <svg-icon class="fc-client-fields-item__icon"
                    params="name: 'client-filials'"></svg-icon>
          <span class="fc-client-fields-item__data"
                data-bind="html: filialsString"></span>
     </div>
     <!-- /ko -->

     <!-- ko if: values.lastOrderDate -->
     <div class="fc-client-fields-item"
          data-item="lastOrderDate">
          <svg-icon class="fc-client-fields-item__icon"
                    params="name: 'client-last-order'"></svg-icon>
          <span class="fc-client-fields-item__data"
                data-bind="text: values.lastOrderDate"></span>
     </div>
     <!-- /ko -->

     <!-- ko if: values.ltv -->
     <div class="fc-client-fields-item"
          data-item="ltv">
          <svg-icon class="fc-client-fields-item__icon"
                    params="name: 'client-ltv'"></svg-icon>
          <span class="fc-client-fields-item__data"
                data-bind="text: values.ltv  + ' ₽'"></span>
     </div>
     <!-- /ko -->

     <!-- ko if: values.addedAt -->
     <div class="fc-client-fields-item"
          data-item="addedAt">
          <svg-icon class="fc-client-fields-item__icon"
                    params="name: 'client-added'"></svg-icon>
          <span class="fc-client-fields-item__data"
                data-bind="text: values.addedAt"></span>
     </div>
     <!-- /ko -->

     <!-- ko if: values.updatedAt -->
     <div class="fc-client-fields-item"
          data-item="updatedAt">
          <svg-icon class="fc-client-fields-item__icon"
                    params="name: 'client-updated'"></svg-icon>
          <span class="fc-client-fields-item__data"
                data-bind="text: values.updatedAt"></span>
     </div>
     <!-- /ko -->

     <!-- ko if: values.tags -->
     <div class="fc-client-fields-item"
          data-item="tags">
          <svg-icon class="fc-client-fields-item__icon"
                    params="name: 'client-tags'"></svg-icon>
          <span class="fc-client-fields-item__data"
                data-bind="text: tagsString"></span>
     </div>
     <!-- /ko -->

     <!-- ko if: values.additional -->
     <div class="fc-client-fields-item"
          data-item="additional">
          <svg-icon class="fc-client-fields-item__icon"
                    params="name: 'client-fields'"></svg-icon>
          <span class="fc-client-fields-item__data">
               <!-- ko foreach: values.additional -->
               <span class="fc-client-field">
                    <span class="fc-client-field__name"
                          data-bind="text: $parent.getFieldName($data.id) + ':'"></span>
                    <span class="fc-client-field__value"
                          data-bind="text: value"></span>
               </span>
               <!-- /ko -->
          </span>
     </div>
     <!-- /ko -->


</div>


<div class="fc-client-fields__line">

     <!-- ko if: values.gender -->
     <!-- ko if: fields.gender -->
     <div class="fc-client-fields-item">
          <!-- ko if: values.gender == 1 -->
          <svg-icon class="fc-client-fields-item__icon"
                    params="name: 'gender-male'"
                    data-bind="tooltip, tooltipText: _t('Мужской пол')"></svg-icon>
          <!-- /ko -->
          <!-- ko if: values.gender == 2 -->
          <svg-icon class="fc-client-fields-item__icon"
                    params="name: 'gender-female'"
                    data-bind="tooltip, tooltipText: _t('Женский пол')"></svg-icon>
          <!-- /ko -->
     </div>
     <!-- /ko -->
     <!-- /ko -->

     <!-- ko if: values.birthday -->
     <!-- ko if: fields.birthday -->
     <div class="fc-client-fields-item"
          data-bind="tooltip, tooltipText: _t('Дата рождения') + ': ' + values.birthday">
          <svg-icon class="fc-client-fields-item__icon"
                    params="name: 'client-birthday'"></svg-icon>
          <span class="fc-client-fields-item__data"
                data-bind="text: values.birthday"></span>
     </div>
     <!-- /ko -->
     <!-- /ko -->

     <!-- ko if: values.filials -->
     <!-- ko if: fields.filials -->
     <div class="fc-client-fields-item"
          data-bind="tooltip, tooltipText: _t('Филиалы контакта') + ': ' + filialsString">
          <svg-icon class="fc-client-fields-item__icon"
                    params="name: 'client-filials'"></svg-icon>
          <span class="fc-client-fields-item__data"
                data-bind="html: filialsString"></span>
     </div>
     <!-- /ko -->
     <!-- /ko -->

     <!-- ko if: values.lastOrderDate -->
     <!-- ko if: fields.lastOrderDate -->
     <div class="fc-client-fields-item"
          data-bind="tooltip, tooltipText: _t('Дата последнего заказа') + ': ' + values.lastOrderDate">
          <svg-icon class="fc-client-fields-item__icon"
                    params="name: 'client-last-order'"></svg-icon>
          <span class="fc-client-fields-item__data"
                data-bind="text: values.lastOrderDate"></span>
     </div>
     <!-- /ko -->
     <!-- /ko -->

     <!-- ko if: values.ltv -->
     <!-- ko if: fields.ltv -->
     <div class="fc-client-fields-item"
          data-bind="tooltip, tooltipText: _t('LTV') + ': ' + values.ltv + ' ₽'">
          <svg-icon class="fc-client-fields-item__icon"
                    params="name: 'client-ltv'"></svg-icon>
          <span class="fc-client-fields-item__data"
                data-bind="text: values.ltv + ' ₽'"></span>
     </div>
     <!-- /ko -->
     <!-- /ko -->

     <!-- ko if: values.addedAt -->
     <!-- ko if: fields.addedAt -->
     <div class="fc-client-fields-item"
          data-bind="tooltip, tooltipText: _t('Дата добавления контакта') + ': ' + values.addedAt">
          <svg-icon class="fc-client-fields-item__icon"
                    params="name: 'client-added'"></svg-icon>
          <span class="fc-client-fields-item__data"
                data-bind="text: values.addedAt"></span>
     </div>
     <!-- /ko -->
     <!-- /ko -->

     <!-- ko if: values.updatedAt -->
     <!-- ko if: fields.updatedAt -->
     <div class="fc-client-fields-item"
          data-bind="tooltip, tooltipText: _t('Дата обновления контакта') + ': ' + values.updatedAt">
          <svg-icon class="fc-client-fields-item__icon"
                    params="name: 'client-updated'"></svg-icon>
          <span class="fc-client-fields-item__data"
                data-bind="text: values.updatedAt"></span>
     </div>
     <!-- /ko -->
     <!-- /ko -->

     <!-- ko if: values.tags -->
     <!-- ko if: fields.tags -->
     <div class="fc-client-fields-item"
          data-bind="tooltip, tooltipText: _t('Теги контакта')">
          <svg-icon class="fc-client-fields-item__icon"
                    params="name: 'client-tags'"></svg-icon>
          <span class="fc-client-fields-item__data"
                data-bind="text: tagsString"></span>
     </div>
     <!-- /ko -->
     <!-- /ko -->

     <!-- ko if: values.additional -->
     <!-- ko if: fields.additional -->
     <div class="fc-client-fields-item">
          <svg-icon class="fc-client-fields-item__icon"
                    params="name: 'client-fields'"></svg-icon>
          <span class="fc-client-fields-item__data">
               <!-- ko foreach: values.additional -->
               <span class="fc-client-field">
                    <span class="fc-client-field__name"
                          data-bind="text: $parent.getFieldName($data.id) + ':'"></span>
                    <span class="fc-client-field__value"
                          data-bind="text: value"></span>
               </span>
               <!-- /ko -->
          </span>
     </div>
     <!-- /ko -->
     <!-- /ko -->

     <!-- ko if: hasHidden -->
     <div data-bind="dropdown, dropdownClass: 'fc-client-fields-dropdown', dropdownMode: 'modal'" style="height: 16px">
          <button class="button-ghost"
                  data-dropdown-target>
               <svg-icon params="name: 'ellipsis'"
                         class="f-color-service"></svg-icon>
          </button>

          <template>
               <div class="fc-client-fields-dropdown__wrapper"
                    data-tooltip-container>
                    <div class="fc-client-fields-dropdown__scroll"
                         data-bind="nativeScrollbar">
                         <!-- ko if: values.gender -->
                         <!-- ko ifnot: fields.gender -->
                         <div class="fc-client-fields-item">
                              <!-- ko if: values.gender == 1 -->
                              <svg-icon class="fc-client-fields-item__icon"
                                        params="name: 'gender-male'"
                                        data-bind="tooltip, tooltipText: _t('Мужской пол')"></svg-icon>
                              <!-- /ko -->
                              <!-- ko ifnot: values.gender == 2 -->
                              <svg-icon class="fc-client-fields-item__icon"
                                        params="name: 'gender-female'"
                                        data-bind="tooltip, tooltipText: _t('Женский пол')"></svg-icon>
                              <!-- /ko -->
                         </div>
                         <!-- /ko -->
                         <!-- /ko -->
                         <!-- ko if: values.birthday -->
                         <!-- ko ifnot: fields.birthday -->
                         <div class="fc-client-fields-item">
                              <div class="fc-client-fields-item__icon"
                                   data-bind="tooltip, tooltipText: 'Дата рождения: ' + values.birthday"
                                   data-placemen>
                                   <svg-icon params="name: 'client-birthday'"></svg-icon>
                              </div>
                              <div class="fc-client-fields-item__data"
                                   data-bind="text: values.birthday"></div>
                         </div>
                         <!-- /ko -->
                         <!-- /ko -->
                         <!-- ko if: values.filials -->
                         <!-- ko ifnot: fields.filials -->
                         <div class="fc-client-fields-item">
                              <div class="fc-client-fields-item__icon"
                                   data-bind="tooltip, tooltipText: _t('Филиалы контакта') + ': ' + filialsString">
                                   <svg-icon params="name: 'client-filials'"></svg-icon>
                              </div>
                              <div class="fc-client-fields-item__data">
                                   <!-- ko foreach: values.filials -->
                                   <div class="client-filial"
                                        data-bind="html: $data.name"></div>
                                   <!-- /ko -->
                              </div>
                         </div>
                         <!-- /ko -->
                         <!-- /ko -->
                         <!-- ko if: values.lastOrderDate -->
                         <!-- ko ifnot: fields.lastOrderDate -->
                         <div class="fc-client-fields-item">
                              <div class="fc-client-fields-item__icon"
                                   data-bind="tooltip, tooltipText: _t('Дата последнего заказа') + ': ' + values.lastOrderDate">
                                   <svg-icon params="name: 'client-last-order'"></svg-icon>
                              </div>
                              <div class="fc-client-fields-item__data"
                                   data-bind="text: values.lastOrderDate"></div>
                         </div>
                         <!-- /ko -->
                         <!-- /ko -->
                         <!-- ko if: values.ltv -->
                         <!-- ko ifnot: fields.ltv -->
                         <div class="fc-client-fields-item">
                              <div class="fc-client-fields-item__icon"
                                   data-bind="tooltip, tooltipText: _t('LTV') + ': ' + values.ltv + ' ₽'">
                                   <svg-icon params="name: 'client-ltv'"></svg-icon>
                              </div>
                              <div class="fc-client-fields-item__data"
                                   data-bind="text: values.ltv + ' ₽'"></div>
                         </div>
                         <!-- /ko -->
                         <!-- /ko -->
                         <!-- ko if: values.addedAt -->
                         <!-- ko ifnot: fields.addedAt -->
                         <div class="fc-client-fields-item">
                              <div class="fc-client-fields-item__icon"
                                   data-bind="tooltip, tooltipText: _t('Дата добавления контакта') + ': ' + values.addedAt">
                                   <svg-icon params="name: 'client-added'"></svg-icon>
                              </div>
                              <div class="fc-client-fields-item__data"
                                   data-bind="text: values.addedAt"></div>
                         </div>
                         <!-- /ko -->
                         <!-- /ko -->
                         <!-- ko if: values.updatedAt -->
                         <!-- ko ifnot: fields.updatedAt -->
                         <div class="fc-client-fields-item">
                              <div class="fc-client-fields-item__icon"
                                   data-bind="tooltip, tooltipText: _t('Дата обновления контакта') + ': ' + values.updatedAt">
                                   <svg-icon params="name: 'client-updated'"></svg-icon>
                              </div>
                              <div class="fc-client-fields-item__data"
                                   data-bind="text: values.updatedAt"></div>
                         </div>
                         <!-- /ko -->
                         <!-- /ko -->
                         <!-- ko if: values.tags -->
                         <!-- ko ifnot: fields.tags -->
                         <div class="fc-client-fields-item">
                              <div class="fc-client-fields-item__icon "
                                   data-bind="tooltip, tooltipText: _t('Теги контакта')">
                                   <svg-icon params="name: 'client-tags'"></svg-icon>

                              </div>
                              <div class="fc-client-fields-item__data">
                                   <!-- ko foreach: values.tags -->
                                   <div class="client-tag"
                                        data-bind="text: $data.name"></div>
                                   <!-- /ko -->
                              </div>
                         </div>
                         <!-- /ko -->
                         <!-- /ko -->
                         <!-- ko if: values.additional -->
                         <!-- ko ifnot: fields.additional -->
                         <div class="fc-client-fields-item">
                              <div class="fc-client-fields-item__icon">
                                   <svg-icon params="name: 'client-fields'"></svg-icon>
                              </div>
                              <div class="fc-client-fields-item__data">
                                   <!-- ko foreach: client.additionalFields -->
                                   <div class="fc-client-field">
                                        <div class="fc-client-field__name"
                                             data-bind="text: $parent.getFieldName($data.id)"></div>
                                        <div class="fc-client-field__value"
                                             data-bind="text: value"></div>
                                   </div>
                                   <!-- /ko -->
                              </div>
                         </div>
                         <!-- /ko -->
                         <!-- /ko -->
                    </div>


               </div>
               <div class="text-center flex-shrink-0"><button data-dropdown-close
                                                              class="f-btn f-btn-link"
                                                              data-bind="text: _t('Закрыть')"></button></div>
          </template>
     </div>
     <!-- /ko -->


</div>

<!-- /ko -->
<!-- /ko -->
