import { FoquzComponent } from "Models/foquz-component";
import { ClientFieldsCollection } from "Models/data-collection/client-fields";

export class ViewModel extends FoquzComponent {
  constructor(params, element) {
    super(params);

    this.showOnlyCustomFields = params.showOnlyCustomFields || false;
    this.customFields = params.customFields || {};

    if (this.showOnlyCustomFields) {
      // Логика только для кастомных полей
      this.allCustomFields = Object.keys(this.customFields).map(key => ({
        id: key,
        value: this.customFields[key]
      }));

      this.firstTwoFields = ko.pureComputed(() =>
          this.allCustomFields.slice(0, 2)
      );

      this.remainingFields = ko.pureComputed(() =>
          this.allCustomFields.slice(2)
      );

      this.hasMoreThanTwoFields = ko.pureComputed(() =>
          this.allCustomFields.length > 2
      );

      this.hasAnyFields = ko.pureComputed(() =>
          this.allCustomFields.length > 0
      );
    } else {
      // Логика только для обычных полей
      this.initializing = ko.observable(true);
      this.client = params.client;
      this.hideClientData = params.hideClientData;
      this.values = {};

      [
        !this.hideClientData && "gender",
        !this.hideClientData && "birthday",
        "lastOrderDate",
        "ltv",
        "addedAt",
        "updatedAt",
      ].filter(Boolean).forEach((key) => {
        if (this.client.systemFields[key])
          this.values[key] = this.client.systemFields[key];
      });

      ["tags", "filials"].forEach((key) => {
        if (this.client.systemFields[key].length)
          this.values[key] = this.client.systemFields[key];
      });

      this.tagsString = (this.values.tags || [])
          .map((tag) => tag.name)
          .join(", ");

      this.filialsString = this.client.systemFields.filials.map((f) => {
        let text = "";
        if (f.categoryName)
          text += `<span class="filial-category-name">${f.categoryName}</span>/`;
        text += `<span class="filial-name">${f.name}</span>`;
        return text;
      });

      if (!this.hideClientData && this.client.additionalFields.length) {
        this.values.additional = this.client.additionalFields;
      }

      if (this.client.apiFields.length) {
        this.values.api = this.client.apiFields;
      }

      this.fields = {};
      Object.keys(this.values).forEach(
          (key) => (this.fields[key] = ko.observable(false))
      );

      if (this.fields.api) this.fields.api(false);

      this.hasHidden = ko.pureComputed(() => {
        return Object.values(this.fields)
            .filter((_, index) => Object.keys(this.fields)[index] !== "api")
            .some((v) => !v());
      });

      this.collection = ClientFieldsCollection.getInstance();
      this.collection.on("load", () => {
        this.initializing(false);
      });
      this.collection.load();
    }

    this.line = ko.observable(null);
    this.throttledCheck = _.throttle(() => {
      this.check();
    }, 400);
  }

  getFieldName(id) {
    if (this.showOnlyCustomFields) {
      return id;
    }
    return this.collection.getById(id)?.text || "";
  }

  check() {
    if (this.showOnlyCustomFields) return;

    let line = this.line();
    if (!line) return;

    const items = [...line.children].filter(item => item.dataset.item);
    const right = line.getBoundingClientRect().right;

    const visible = [];
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.getBoundingClientRect().right > right) {
        break;
      }
      visible.push(item.dataset.item);
    }

    Object.keys(this.fields).forEach((key) => {
      if (key === "api") {
        this.fields.api(false);
      } else {
        this.fields[key](visible.includes(key));
      }
    });
  }

  onInit() {
    if (!this.showOnlyCustomFields) {
      this.check();
      window.addEventListener("resize", this.throttledCheck);
    }
  }

  dispose() {
    if (!this.showOnlyCustomFields) {
      window.removeEventListener("resize", this.throttledCheck);
    }
  }
}
