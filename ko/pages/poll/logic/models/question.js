import * as questionTypes from "@/data/question-types";
import { Condition } from "./condition";
import { getInterblockName } from "Data/interblock-types";
import { DialogsModule } from "Utils/dialogs-module";
import { Translator } from "@/utils/translate";
import * as ViewSidesheet from "../components/view-sidesheet";
import { registerComponent } from "@/utils/engine/register-component";
import { getCollection } from "@/api/collections/collection";

import { formatCollection } from "@/utils/collections/format-collection";
import { filterCollectionTree } from "@/utils/collections/filter-collection-tree";

registerComponent("view-sidesheet", ViewSidesheet);
const QuestionsTranslator = Translator("questions");

export class Question {
  constructor(data, index, ctx) {
    this.ctx = ctx;
    this.index = ko.observable(index);
    DialogsModule(this);
    [
      "poll_id",
      "id",
      "name",
      "description",
      "service_name",
      "is_system",
      "is_required",
      "is_tmp",
      "main_question_type",
      "rating_type",
      "intermediateBlock",
      "pointName",
      "point_id",
      "from_one",
      "set_variants",
      "type",
      "is_inter",
      "skip",
      "skip_variant",
    ].forEach((k) => (this[utils.string.snakeToCamel(k)] = data[k]));

    if (data.detail_answers) {
      data.detail_answers = data.detail_answers.filter((a) => !a.is_deleted);
    }

    this.data = data;
    this.anchor = ko.observable(null);

    this.customVariantText = data.self_variant_text;

    this.isIntermediate = !!this.intermediateBlock;
    this.screenType = null;
    this.exclusions = ko.observableArray(ctx.poll.randomExclusion)

    if (this.isIntermediate) {
      this.screenType = getInterblockName(this.intermediateBlock.screen_type);
      this.showNumber = this.intermediateBlock.show_question_number;
    }

    if (this.isIntermediate) {
      if (this.screenType != "text" || !this.showNumber) this.name = "";
    }

    this.link = `/foquz/foquz-question/update?id=${this.id}&pollId=${this.pollId}`;
    this.donorId = data.donor || data.donor_rows || data.donor_columns;
    this.donor = ko.observable(null);
    this.donorIndex = ko.observable(null);
    this.firstDonor = ko.observable(null);

    this.tooltip = ko.observable(data.tooltip || null);

    ko.computed(() => {
      const list = ctx.questionsList();
      if (!this.donorId) return;
      const donor = list[this.donorId];
      if (!donor) return;
      this.donor(donor.question);
      this.donorIndex(donor.index);

      function getFirstDonor(donor) {
        if (!donor) return null;
        if (!donor.donorId) return donor;
        const parentDonor = list[donor.donorId];
        if (parentDonor) return getFirstDonor(parentDonor.question, list);
        return null;
      }

      this.firstDonor(getFirstDonor(donor.question));
    });

    this.savedConditions = data.questionLogic || [];
    this.conditions = ko.observableArray([]);

    const viewLogic = data.questionViewLogic ? [...data.questionViewLogic] : [];
    viewLogic.sort((a, b) => a.sort - b.sort);

    this.viewConditions = ko.observableArray(data.questionViewLogic || []);

    this.hasLogic = ko.pureComputed(() => {
      return this.conditions().length > 0;
    });

    this.hasView = ko.computed(() => {
      return this.viewConditions().length > 0;
    });

    this.hasConditions = ko.computed(() => {
      return this.hasLogic() || this.hasView();
    });

    this.hasEndLogic = ko.pureComputed(() => {
      if (this.screenType === "end") {
        if (this.viewConditions().length) return true;
      }

      return this.conditions().some((c) => c.isEndLogic());
    });

    this.order = ko.observable(null);

    this.opened = ko.observable(false);

    this.locked = ko.observable(false);

    this.isValid = ko.computed(() => {
      return !this.conditions().some((c) => !c.isValid());
    });

    this.sorting = ko.observable(false);

    this.isLast = ko.observable(false);

    this.tree = ko.observable(null);
    this.initing = ko.observable(false);
    if (this.isClassifier) {
      this.initing(true);
      getCollection(data.dictionary_id).then((list) => {
        const tree = formatCollection(list.elements);
        const items = data.detail_answers.map((i) => i.id);
        const filteredTree = filterCollectionTree(tree, items);
        this.tree(filteredTree);
        this.initing(false);
      });
    }

    setTimeout(() => {
      this.setLogic(this.savedConditions);
      if (ctx && this.exclusions().length) {
        this.setInitLock(this.exclusions().includes(this.id))
      }
      
    });
  }

  get title() {
    if (this.isIntermediate) return this.serviceName;
    return this.description;
  }

  get required() {
    if (this.isIntermediate) return false;
    return this.isRequired;
  }

  get isLogicType() {
    return !!this.logicType;
  }

  get logicType() {
    if (
      this.mainQuestionType == questionTypes.RATE_QUESTION &&
      this.ratingType == 1
    ) {
      return "stars";
    }
    if (this.mainQuestionType == questionTypes.VARIANTS_QUESTION) {
      return "variants";
    }
    if (this.mainQuestionType == questionTypes.FILIALS_QUESTION) {
      return "filials";
    }
    if (
      this.mainQuestionType == questionTypes.NPS_QUESTION &&
      !this.setVariants
    ) {
      return "nps";
    }
    if (this.mainQuestionType == questionTypes.STARS_QUESTION) {
      return "star-rating";
    }
    if (this.mainQuestionType == questionTypes.RATING_QUESTION) {
      return "star-rating";
    }
    if (this.mainQuestionType == questionTypes.SMILE_QUESTION) {
      return "smile";
    }
    if (this.mainQuestionType == questionTypes.CLASSIFIER_QUESTION) {
      return "classifier";
    }
    if (this.mainQuestionType == questionTypes.SCALE_QUESTION &&
      !this.setVariants) {
      return "scale";
    }
    return false;
  }

  get isInlineVariants() {
    return this.logicType !== "variants" && this.logicType !== "filials";
  }

  get isMatrix() {
    return (
      this.mainQuestionType == questionTypes.MATRIX_QUESTION ||
      this.mainQuestionType == questionTypes.STAR_VARIANTS_QUESTION ||
      (this.mainQuestionType == questionTypes.NPS_QUESTION && this.setVariants) ||
      (this.mainQuestionType == questionTypes.SCALE_QUESTION && this.setVariants)
    );
  }

  get isClassifier() {
    return this.mainQuestionType == questionTypes.CLASSIFIER_QUESTION;
  }

  afterConditionAdd(el) {
    if (this.sorting()) $(el).show();
    else $(el).hide().slideDown(400);
  }

  beforeConditionRemove(el) {
    if (this.sorting()) $(el).remove();
    else
      $(el).slideUp(400, () => {
        $(el).remove();
      });
  }

  get isScale() {
    if (this.mainQuestionType == questionTypes.SCALE_QUESTION && !this.setVariants) {
      return true;
    }
    return false;
  }

  hasCustomVariant() {
    if (this.mainQuestionType == questionTypes.VARIANTS_QUESTION) {
      return this.data.is_self_answer;
    }
    return false;
  }

  get getScaleData() {
    if (this.mainQuestionType == questionTypes.SCALE_QUESTION && !this.setVariants) {
      return {
        minValue: this.data.scaleRatingSetting.start,
        maxValue: this.data.scaleRatingSetting.end,
        stepValue: this.data.scaleRatingSetting.step
      }
    }
    return null
  }

  getVariants() {
    if (!this.isLogicType) {
      if (this.isMatrix) {
        if (this.mainQuestionType == questionTypes.NPS_QUESTION) {
          const variants = this.data.detail_answers;
          const starsCount = this.fromOne ? 10 : 11;
          return {
            rows: variants.map((v) => {
              return {
                id: v.id,
                text: v.variant || v.question,
              };
            }),
            cols: Array(starsCount)
              .fill(null)
              .map((_, i) => {
                return {
                  id: this.fromOne ? i + 1 : i,
                  text: this.fromOne ? i + 1 : i,
                  nps: true,

                };
              }),
          };
        } else if (
          this.mainQuestionType == questionTypes.STAR_VARIANTS_QUESTION
        ) {
          const variants = this.data.detail_answers;
          const starsCount = this.data.starRatingOptions.count;
          return {
            rows: variants.map((v) => {
              return {
                id: v.id,
                text: v.variant || v.question,
              };
            }),
            cols: Array(starsCount)
              .fill(null)
              .map((_, i) => {
                return {
                  id: i + 1,
                  text: i + 1,
                  star: true,
                };
              }),
          };
        } else if (
          this.mainQuestionType == questionTypes.SCALE_QUESTION
        ) {
          const variants = this.data.detail_answers;
          const scaleRatingSetting = this.data.scaleRatingSetting;

          console.log('Matrix Scale question', variants, scaleRatingSetting);
          return {
            rows: variants.map((v) => {
              return {
                id: v.id,
                text: v.variant || v.question,
                pointsRange: ko.observableArray([scaleRatingSetting.start, scaleRatingSetting.end])
              };
            }),
            cols: [{
              id: 1,
              text: "Диапазон ответа",
              scale: true,
            }]
          }
        } else {
          const settings = this.data.matrixSettings;
          return {
            rows: settings.rows.map((r) => ({ id: r, text: r })),
            cols: settings.cols.map((c) => ({ id: c, text: c })),
          };
        }
      }
      return [];
    }

    if (this.logicType == "stars") {
      return Array(5)
        .fill()
        .map((_, i) => {
          let id = i + 1;
          let stars = Array(id).fill();

          return {
            id,
            variant: id,
            stars,
          };
        });
    }
    if (this.logicType == "variants") {
      const source = this.firstDonor() ? this.firstDonor() : this;

      let variants = [...source.data.detail_answers];

      if (source.hasCustomVariant()) {
        const text =
          source.data.self_variant_text ||
          QuestionsTranslator.t("Свой вариант")();
        variants.push({
          id: 0,
          custom: true,
          variant: text,
        });
      }

      return variants;
    }
    if (this.logicType == "filials") {
      let filials = [];

      if (this.data.detail_question) {
        try {
          const parsedFilials = JSON.parse(this.data.detail_question);
          if (Array.isArray(parsedFilials)) {
            const filialsSet = new Set(parsedFilials);
            filials = [...filialsSet];
          }
        } catch (e) {
          console.error(e);
        }
      }

      const variants = filials.map((filialId) => {
        return {
          id: +filialId,
          variant: ko.computed(() => {
            const filials = this.ctx.filials ?
              this.ctx.filials() :
              this.ctx.activeQuestion().controller.filials();
            const filial = filials.find((f) => f.id === filialId);
            return filial?.name;
          }),
        };
      });

      return ko.computed(() => {
        const list = ko.toJS(variants);
        list.sort((a, b) => (a.variant < b.variant ? -1 : 1));
        return list;
      });
    }
    if (this.logicType == "nps") {
      const list = Array(11)
        .fill()
        .map((_, i) => {
          return {
            id: i,
            variant: i,
          };
        });
      if (this.fromOne) {
        list.shift();
      }
      return list;
    }
    if (this.logicType == "scale") {
      const result = [this.data.scaleRatingSetting.start, this.data.scaleRatingSetting.end]

      return { scaleSetting: result }

    }
    if (this.logicType == "star-rating") {
      let starsCount = this.data.starRatingOptions.count;
      return Array(starsCount)
        .fill()
        .map((_, i) => {
          return {
            id: i + 1,
            variant: i + 1,
          };
        });
    }

    if (this.logicType == "smile") {
      return this.data.smiles.map((s, i) => {
        return {
          ...s,
          id: i + 1,
          type: this.data.smile_type,
        };
      });
    }

    if (this.logicType == "classifier") {
      return this.data.detail_answers.map((v) => {
        return {
          id: v.id,
          variant: v.value,
        };
      });
    }

    return [];
  }

  open() {
    this.opened(true);
  }

  addCondition() {
    this.opened(true);
    this.conditions.push(new Condition(null, this));
  }

  addViewCondition() {
    this.openDialog({
      name: "view-sidesheet",
      params: {
        question: this,
        questions: this.ctx.defaultQuestions(),
        blocked: this.ctx.isWatcher,
        onQuestionUpdated: (conditions) => {
          this.viewConditions(conditions);
        },
      },
    });
  }

  removeCondition(condition) {
    this.conditions.remove(condition);
  }

  resortConditions(event) {
    setTimeout(() => {
      this.sorting(true);

      const movedItem = this.conditions()[event.data.oldIndex];
      this.conditions.remove(movedItem);
      this.conditions.splice(event.data.newIndex, 0, movedItem);

      $(event.data.dragEvent.originalSource).remove();

      this.sorting(false);
    }, 100);
  }

  getLogic() {
    return this.conditions().map((c) => {
      return c.getData()
    });
  }

  setLogic(conditions) {
    console.log('setLogic data', conditions);
    while (this.conditions().length > conditions.length) {
      this.conditions.pop();
    }

    let currentConditions = this.conditions();
    conditions.forEach((c, i) => {
      if (currentConditions[i]) currentConditions[i].update(c);
      else this.conditions.push(new Condition(c, this));
    });
  }

  resetLogic() {
    this.setLogic(this.savedConditions);
  }

  saveLogic(conditions) {
    this.savedConditions = conditions;
  }

  scrollTo() {
    this.opened(true);
    requestAnimationFrame(() => {
      let anchor = this.anchor();
  
      let invalidCondition = this.conditions().find((c) => !c.isValid());
      if (invalidCondition) anchor = invalidCondition.anchor();
  
      anchor.scrollIntoView({
        behavior: "smooth",
      });
    });
  }

  clearLogic() {
    this.conditions([]);
    this.viewConditions([]);
    this.opened(false);
  }

  setInitLock(value) {
    this.locked(value)
  }

  toggleLock(poll, questions) {
    const self = this

    this.locked(!this.locked())

    if (questions.length) {
      const locked = questions.filter(q => q.locked()).map(q => q.id)
      $.ajax({
        url: ApiUrl("poll/save-display-settings", { id: poll }),
        method: "POST",
        data: {
          random_exclusion: locked.length ? locked : null,
        },
        success: (response) => {
          console.log(response)
          self.ctx.randomExclusion(locked)
        },
        error: (response) => {
          console.error(response.responseJSON);
        },
      })
    }
  }
}
