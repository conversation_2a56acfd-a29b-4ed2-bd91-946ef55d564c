import Question<PERSON>ormController from "Components/question-form";

import {
  CLASSIFIER_QUESTION,
  INTER_BLOCK,
  MATRIX_3D_QUESTION,
  SMILE_QUESTION,
  STARS_QUESTION,
  VARIANTS_QUESTION
} from "Data/question-types";

import { toLogicPage } from "@/router/poll";

import { questionPreviewFormatter } from "Components/question-form/utils/preview-formatter";
import { questionServerFormatter } from "Components/question-form/utils/server-formatter";
import { questionClientFormatter } from "Components/question-form/utils/client-formatter";

import "./modals/change-modal";
import "./modals/unsaved-message-modal";
import "./modals/leave-modal";

import "Components/question-form-paginator";

import "Components/poll-preview";
import { isEndScreen, isInterblock } from "Utils/questions/interblock";
import { isOrderTrigger } from "Data/triggers";

import "@/dialogs/copy-questions-sidesheet";
import "@/dialogs/points-sidesheet";
import "@/dialogs/poll-translate-sidesheet";
import "@/dialogs/contact-point-name-dialog";
import "../../../components/question-form/dialogs/donor-type-dialog"

import { PollActivateEvent, PollPublishEvent } from "Utils/events/poll";
import { ApiUrl } from "Utils/url/api-url";
import { LogicPage } from "Models/logic/page";
import { questionDirectPreviewFormatter } from "Components/question-form/utils/direct-preview-formatter";
import { DialogsModule } from "Utils/dialogs-module";

// TODO TASK-4484
//import { ContactPointsCollection } from "Models/data-collection/contact-points";
import { isTypeWithPoints } from "Utils/questions/points";

import "./style.less";

import "./style.less";

import { Translator } from "@/utils/translate";
import { addFilesToFormData, toFormData } from "@/utils/api/form-data";
import { request } from "../../../utils/api/request";
import { PollModel } from "@/entities/models/poll";

import { useSearchParams } from "@/utils/url/use-search-params";

const PageTranslator = Translator("questions");

class ViewModel {
  constructor() {
    PageModel(this);
    DialogsModule(this);

    const [searchParams] = useSearchParams();

    // @NOTE: Превью на Vue работает по умолчанию
    // @NOTE: Если нужно использовать старое превью, то нужно добавить параметр old-preview в URL
    this.isVuePreviewMode = !('old-preview' in searchParams());

    this.translator = PageTranslator;
    this.poll = PollModel(POLL);
    this.useLangs = this.poll.langs.filter((l) => !l.isDefaultLang).length;

    PollPublishEvent.on((params) => {
      if (params.id == POLL_ID) {
        window.pollIsPublished = true;

        if (this.isVuePreviewMode) {
          this.updateLiveQuestion();
        }
      }
    });

    this.modals = ko.observableArray([]);
    this.isSubmitted = ko.observable(false);
    this.unsavedChangesModalOpen = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(
      this.isSubmitted
    );
    this.formControlSuccessStateMatcher = commonFormControlSuccessStateMatcher(
      this.isSubmitted
    );

    // TODO TASK-4484
    // this.collections = {
    //   contactPoints: new ContactPointsCollection(),
    // };
    // this.collections.contactPoints.load();

    // данные в сыром виде с сервера
    this.defaultData = ko.observable();

    this.fullPreview = ko.observable(null);
    this.subscriptions = [];
    this.name = ko.observable(POLL_NAME);
    this.isNameEditing = ko.observable(false);
    this.activePreviewType = ko.observable(0);
    this.pollIsAuto = ko.observable(POLL_IS_AUTO);
    this.pagesMode = POLL.displaySetting && POLL.displaySetting.type == 2;
    this.pages = POLL.displayPages.map((p) => new LogicPage(p));
    this.isLoading = ko.observable(false);

    this.showSuccessMessage = ko.observable(false);
    this.errorMessage = ko.observable(null);
    this.isSaved = ko.observable(false);
    this.isNew = ko.observable(true);
    this.isBlocked = !CURRENT_USER || CURRENT_USER.blockActions;
    this.inited = ko.observable(false);

    this.questions = ko.observableArray(QUESTIONS);

    window.questions = this.questions;
    window.withPoints = !!POLL.point_system;
    this.withPoints = !!POLL.point_system;

    let qOrder = this.questions().map((q) => q.id);
    this.pages.forEach((p) => {
      let pQuestions = p.questions();
      pQuestions.sort((a, b) => {
        return qOrder.indexOf(a.id) - qOrder.indexOf(b.id);
      });

      p.questions(pQuestions);
    });

    window.vm = this;

    this.blinked = ko.observableArray([]);
    this.currentQuestionId = ko.observable(null);
    this.currentQuestionId.subscribe(this.onActiveQuestionChange.bind(this));
    let currentQuestionId = QUESTION_ID;
    let hasQuestion = this.questions().some((q) => q.id == currentQuestionId);
    if (!hasQuestion) currentQuestionId = this.questions()[0].id;
    this.questionSource = ko.observable("common");

    this.questionSourceId = ko.observable(null);
    this.questionSourceName = ko.pureComputed(() => {
      let id = this.questionSourceId();
      if (!id) return "";
      // TODO TASK-4484
      // let point = this.collections.contactPoints.getById(id);
      // if (point) return point.name;
      return "";
    });
    this.contactPointId = ko.observable(null);
    this.tmpContactPoint = ko.observable(null);
    this.tmpQuestion = ko.observable(null);
    this.contactPointLoading = ko.observable(false);

    this.indexes = ko.observable({});

    this.donors = ko.computed(() => {
      const list = this.questions();
      const currentQuestionId = this.currentQuestionId();
      if (!currentQuestionId) return [];

      const indexes = this.indexes();

      const currentQuestionIndex = list.findIndex(
        (q) => q?.id === currentQuestionId
      );

      const questionsBefore = list
        .slice(0, currentQuestionIndex)
        .map((q, i) => {
          return {
            index: indexes[q.id],
            question: questionClientFormatter(q),
          };
        });

      return questionsBefore.filter(
        (q) => q.question.type.toString() === VARIANTS_QUESTION || q.question.type.toString() === CLASSIFIER_QUESTION
      );
    });

    this.recipients = ko.computed(() => {
      const list = this.questions();
      const currentQuestionId = this.currentQuestionId();
      if (!currentQuestionId) return [];

      const indexes = this.indexes();

      return list
        .filter((q) => {
          if (q?.id === currentQuestionId) {
            return false;
          }
          return q?.main_question_type == MATRIX_3D_QUESTION ?
            q.donor_rows === currentQuestionId || q.donor_columns === currentQuestionId :
            q?.donor === currentQuestionId;
        })
        .map((q) => {
          return {
            index: indexes[q.id],
            question: questionClientFormatter(q),
          };
        });
    });

    this.rendered = false

    this.currentQuestionRecipients = ko.computed(() => {
      return this.recipients().map((q) => {
        return {
          questionName: `${q.index}. ${
            q.question.descriptionText || q.question.description || q.question.name || q.question.alias
          }`,
        };
      });
    });

    window.onbeforeunload = () => {
      if (!this.isBlocked && !this.isSaved()) {
        return true;
      }
    };

    this.addQuestionButtonDisabled = ko.pureComputed(() => {
      return this.isNew();
    });

    const blockRecipients =
      POLL.displayPages.length ||
      (POLL.displaySetting && POLL.displaySetting.random_order);

    this.controller = new QuestionFormController({
      poll: POLL,
      isAuto: this.pollIsAuto(),
      withPoints: !!POLL.point_system,
      hasOrder: this.pollIsAuto() && isOrderTrigger(POLL.trigger),
      formControlErrorStateMatcher:
        this.formControlErrorStateMatcher.bind(this),
      formControlSuccessStateMatcher:
        this.formControlSuccessStateMatcher.bind(this),
      noDonorsInfo: () => {
        this.info({
          text: "Чтобы использовать эту настройку, до текущего вопроса должен располагаться хотя бы один вопрос типа «Варианты ответов» или типа «Классификатор».",
        });
      },
      tryChangeBlockedParam: (question) => {
        if (Number(question.countAnswers) > 0) {
          this.info({
            text: this.translator.t(
              !this.poll.isPublished ? `Изменить параметр нельзя, так как по вопросам текущего опроса собирается статистика. Для этого нужно <a href="${window.location.origin + '/foquz/foquz-poll/stats?id=' + this.poll.id}" target='_blank'>очистить статистику</a> через кнопку «Действия».` : "Изменить параметр нельзя, так как по вопросам текущего опроса собирается статистика."
            ),
          });
          return;
        }

        // TODO TASK-4484
        // if (question.pointId()) {
        //   let point = this.collections.contactPoints.getById(
        //     question.pointId()
        //   );

        //   this.info({
        //     text: this.translator.t(
        //       "Параметр недоступен для изменения, так как вопрос связан с точкой контакта {point}",
        //       {
        //         point: `<span class="bold">${point ? point.name : ""}</span>`,
        //       }
        //     ),
        //   });
        // } else
        
        if (blockRecipients) {
          this.info({
            text: `<div>Нельзя использовать ответы из другого вопроса, если
            в разделе «Логика» выбрана опция «Разделить вопросы
            по страницам вручную» или «Случайный порядок страниц».</div>
            <div class="mt-15p">
              <a class="font-weight-500" style="text-decoration: none; color: var(--f-color-primary);" href="${toLogicPage(
              POLL.id
            )}">Перейти к настройке логики</a>
            </div>`,
          });
        } else {
          this.info({
            text: this.translator.t(
              "Изменить параметр нельзя, так как по вопросам текущего опроса собирается статистика."
            ),
          });
        }
      },
      activateQuestion: (id) => {
        setTimeout(() => {
          this.onActivateQuestion(id);
        }, 1000);
      },
      api: {
        loadImageByFileUrl: (id) => {
          return Promise.resolve(
            `/foquz/api/questions/image-upload?access-token=${ACCESS_TOKEN}&id=${id}`
          );
        },
        loadVideoByFileUrl: (id) => {
          return Promise.resolve(
            `/foquz/api/questions/video-upload?access-token=${ACCESS_TOKEN}&id=${id}`
          );
        },
        loadYoutubeVideoUrl: (id) => {
          return Promise.resolve(
            `/foquz/api/questions/upload-youtube?access-token=${ACCESS_TOKEN}&id=${id}`
          );
        },
        loadMediaByUrl: (id) => {
          return Promise.resolve(
            `/foquz/api/questions/upload-by-link?access-token=${ACCESS_TOKEN}&id=${id}`
          );
        },
        changeMediaLabelUrl: (id) => {
          return Promise.resolve(
            `/foquz/api/questions/change-label?access-token=${ACCESS_TOKEN}&id=${id}`
          );
        },
      },
      blocked: this.isBlocked,
      donors: this.donors,
      recipients: this.recipients,
      blockRecipients,
    });

    this.controller.on(
      QuestionFormController.events.changeScreenType,
      (data) => {
        let currentQuestionId = this.currentQuestionId();
        let question = this.questions().find((q) => q.id == currentQuestionId);

        let questionPage = null;
        if (this.pagesMode) {
          questionPage = this.getQuestionPage(currentQuestionId);
        }

        if (data.to == "start") {
          if (questionPage) {
            questionPage.removeQuestion(currentQuestionId);
          }

          this.questions.remove(question);
          this.questions.unshift(question);

          this.controller.disableStartScreen(true);

          return;
        }

        if (data.to == "end") {
          if (questionPage) {
            questionPage.removeQuestion(currentQuestionId);
          }

          this.questions.remove(question);
          this.questions.push(question);

          return;
        }

        if (data.from == "start") {
          if (this.pagesMode) {
            this.pages[0].addQuestion(currentQuestionId, "inbeginning");
            this.updatePagesOrder();
          }

          this.controller.disableStartScreen(false);

          return;
        }

        if (data.from == "end") {
          if (this.pagesMode) {
            this.pages[this.pages.length - 1].addQuestion(currentQuestionId);
            this.updatePagesOrder();
          }
          if (data.to === "text") {
            this.resortQuestions();
          }

          return;
        }

        if (data.to == "text" || data.from == "text") {
          this.resortQuestions();
        }
      }
    );
    this.controller.on(
      QuestionFormController.events.changeShowNumber,
      (showNumber) => {
        const currentQuestionId = this.currentQuestionId();
        const questions = this.questions();
        const question = questions.find((q) => q.id == currentQuestionId);
        this.questions(questions)
      }
    )

    this.controller.on('FORCE_PREVIEW_UPDATE', () => {
      this.updateLiveQuestion()
    })

    // ToDo
    this.indexes = ko.pureComputed(() => {
      const list = this.questions();
      let question = this.controller.question();

      const indexes = {};

      const orderedQuestions = list.filter((q) => {
        if (question && q?.id === question.id) {
          if (question.type == INTER_BLOCK) {
            if (question.blockType === "start" || question.blockType === "end")
              return false;
            return question.showNumber;
          }
          return true;
        } else {
          if (q?.main_question_type == INTER_BLOCK) {
            if (!q?.intermediateBlock) return false;
            return q?.intermediateBlock.screen_type == 1;
          }
          return true;
        }
      });

      orderedQuestions.forEach((q, i) => {
        if (q) {
          indexes[q.id] = i + 1;
        }
      });

      return indexes;
    });

    // TODO TASK-4484
    // this.tryChangePointParam = () => {
    //   let point = this.collections.contactPoints.getById(
    //     this.controller.question().pointId()
    //   );

    //   this.info({
    //     text: this.translator.t(
    //       "Параметр недоступен для изменения, так как вопрос связан с точкой контакта {point}",
    //       {
    //         point: `<span class="bold">${point ? point.name : ""}</span>`,
    //       }
    //     ),
    //   });
    // };

    this.getLogicPage = (logic) => {
      if (this.pagesMode) {
        let pId = logic.jump_display_page_id;
        let pageIndex = this.pages.findIndex((p) => p.id == pId);
        if (pageIndex > -1) {
          let page = this.pages[pageIndex];

          return `${pageIndex + 1}. ${
            page.name() ||
            this.translator.t("Название страницы {number}", {
              number: pageIndex + 1,
            })
          }`;
        }
      } else {
        let qId = logic.jump_question_id;

        let counter = 1;

        let questions = this.questions()
          .filter(
            (q) => !q.intermediateBlock || q.intermediateBlock.screen_type === 1
          )
          .map((q, i) => {
            return {
              ...q,
              index:
                !q.intermediateBlock || q.intermediateBlock.show_question_number
                  ? counter++
                  : "*",
            };
          });

        //let questionIndex = this.questions().findIndex((q) => q.id == qId);
        let question = questions.find((q) => q.id === qId);
        if (question)
          return `${question.index}. ${
            question.description || question.name || question.service_name
          }`;

        return "";
      }
    };

    this.getLogicEndPage = (logic) => {
      let endQuestions = this.questions().filter((q) => isEndScreen(q));
      if (this.pagesMode) {
      } else {
        let qId = logic.jump_question_id;
        let questionIndex = endQuestions.findIndex((q) => q.id == qId);
        if (questionIndex > -1) {
          return this.translator.t("Конечный экран {number}", {
            number: questionIndex + 1,
          });
        }
        return "";
      }
    };

    this.getLogicVariants = (question, ids) => {
      let variants = ko.toJS(question.getLogicVariants(ids));

      if (question.type === SMILE_QUESTION) {
        return variants
          .map((v) => {
            let img = `<img src="${v.url}" data-smile="${v.name}">`;
            let label = v.label ? `<span class="ml-2">${v.label}</span>` : "";

            return `<span class="d-inline-flex align-items-center mr-10p" >${img}${label}</span>`;
          })
          .join("");
      }

      return variants.join(", ");
    };

    this.configPoints = () => {
      this.openSidesheet({
        name: "points-sidesheet",
        params: {
          id: POLL_ID,
        },
      });
    };

    this.checkStartScreen = () => {
      return this.questions().some((q) => {
        if (q.main_question_type != INTER_BLOCK) return false;
        if (!q.intermediateBlock) return false;
        return q.intermediateBlock.screen_type == 2;
      });
    };

    this.hasStartScreen = ko.computed(() => {
      return this.questions().some((q) => {
        if (q?.main_question_type != INTER_BLOCK) return false;
        if (!q?.intermediateBlock) return false;
        return q?.intermediateBlock.screen_type == 2;
      });
    });

    // TODO TASK-4484
    // if (this.hasStartScreen()) {
    //   this.collections.contactPoints.blockStartScreens(true);
    // }
    // this.hasStartScreen.subscribe((v) => {
    //   this.collections.contactPoints.blockStartScreens(v);
    // });

    this.questions.subscribe((v) => {
      this.controller.disableStartScreen(this.hasStartScreen());
    });

    this.hasLogic = ko.observable(
      this.questions().some((q) => q.questionLogic && q.questionLogic.length)
    );

    this.hasViewLogic = ko.observable(
      this.questions().some(
        (q) => q.questionViewLogic && q.questionViewLogic.length
      )
    );

    this.hasEndLogic = ko.pureComputed(() => {
      const questions = this.questions();

      const endQuestions = questions.filter((q) => {
        return (
          q &&
          q.main_question_type &&
          q.main_question_type.toString() === INTER_BLOCK &&
          q.intermediateBlock &&
          q.intermediateBlock.screen_type === 3
        );
      });

      if (endQuestions.some((q) => q.questionViewLogic.length)) return true;

      if (!this.hasLogic()) return false;
      let logicQuestions = questions.filter(
        (q) => q.questionLogic && q.questionLogic.length
      );
      return logicQuestions.some((q) => {
        return q.questionLogic.some((c) => c.jump == 3);
      });
    });

    this.isCurrentQuestionHasLogic = ko.computed(() => {
      if (!this.hasLogic()) return false;

      let currentId = this.currentQuestionId();
      let currentQuestion = this.questions().find((q) => {
        return q.id == currentId;
      });
      if (
        currentQuestion &&
        currentQuestion.questionLogic &&
        currentQuestion.questionLogic.length
      )
        return true;
      return false;
    });

    this.currentQuestionViewLogic = ko.computed(() => {
      let currentId = this.currentQuestionId();
      let currentQuestion = this.questions().find((q) => {
        return q?.id == currentId;
      });

      if (!currentQuestion) return false;

      const { questionViewLogic } = currentQuestion;

      if (!questionViewLogic) return false;
      if (!questionViewLogic.length) return false;

      return questionViewLogic.map((condition) => {
        const {
          visibility,
          condition_type,
          condition_question_id,
          variants,
          parameter,
          parameter_condition,
          parameter_value,
        } = condition;

        let visibilityText =
          visibility === 1 ? "Скрывать, если:" : "Показывать только, если:";
        let typeText = "";
        let conditionText = "";
        let conditionDetails = "";

        if (condition_type === 0) {
          const question = this.questions().find((q) => {
            return q.id == condition_question_id;
          });

          const numberedQuestions = this.questions().filter((q) => {
            if (!q.intermediateBlock) return true;
            return q.intermediateBlock.show_question_number;
          });

          const index = numberedQuestions.findIndex(
            (q) => q.id === question.id
          );
          const { description, service_name } = question;

          const model = this.controller.createQuestionModel(
            questionClientFormatter(question)
          );

          typeText = `Ответ на вопрос: ${index + 1}. ${
            description || service_name
          }`;

          conditionText = "Варианты ответа";

          conditionDetails = this.getLogicVariants(model, variants);
        } else if (condition_type === 1) {
          typeText = `Параметр: ${parameter}`;
          const conditions = [
            "Равен",
            "Больше",
            "Меньше",
            "Содержит",
            "Начинается с",
            "Заканчивается на",
          ];
          conditionText = conditions[parameter_condition];
          conditionDetails = parameter_value;
        }

        return {
          visibilityText,
          typeText,
          conditionText,
          conditionDetails,
        };

        if (condition_type === 0) {
          formattedCondition.question = question;
          formattedCondition.variants = variants;
        }

        if (condition_type === 1) {
          formattedCondition.param = parameter;
          formattedCondition.paramCondition = parameter_condition;
          formattedCondition.paramValue = parameter_value;
        }

        return formattedCondition;
      });
    });

    let endScreensCount = ko.computed(() => {
      return this.questions().filter((q) => {
        if (q?.id == this.currentQuestionId()) return false;
        return isEndScreen(q);
      }).length;
    });
    let isCurrentQuestionEndScreen = ko.computed(() => {
      let question = this.controller.question();
      if (question) return isEndScreen(question);
      return false;
    });
    this.isCurrentQuestionEndScreen = isCurrentQuestionEndScreen;
    this.hasMultipleEndScreens = ko.pureComputed(() => {
      return endScreensCount() + (isCurrentQuestionEndScreen() ? 1 : 0) > 1;
    });

    this.updateLiveQuestion = utils.debounce(() => {
      this._updateLiveQuestion();
    }, 500);

    this.controller.isChanged.subscribe((v) => {
      if (!this.inited()) return;

      if (v) {
        this.isSaved(false);
        this.updateLiveQuestion();
      }
    });

    this.questionSource.subscribe((v) => {
      // Переключение источника
      if (v == "common") {
        // Обычный вопрос
        this.contactPointId(null); // Сбросить временную ТК

        if (this.isNew() && this.tmpQuestion()) {
          this.updateCurrentQuestionData(this.tmpQuestion());
        }

        this.tmpQuestion(null);
      } else {
        // Точка контакта
        let currentQuestionId = this.currentQuestionId();
        let question = this.questions().find((q) => q.id == currentQuestionId);

        if (this.isNew()) {
          this.tmpQuestion(question); // Сохранить временный вопрос
        }
      }
    });

    this.contactPointId.subscribe((v) => {
      this.questionSourceId(v);
    });
    this.questionSourceId.subscribe((v) => {
      if (v) {
        if (v == this.contactPointId()) return;

        this.contactPointLoading(true);
        this.loadContactPointData(v).then((cpData) => {
          this.tmpContactPoint(cpData);
          this.contactPointId(v);

          let questionData = {
            ...cpData,
            point_id: cpData.id,
            tmpContactPoint: true,
            is_tmp: true,
          };

          this.updateCurrentQuestionData(questionData);
          this.contactPointLoading(false);
        });
      } else {
        this.contactPointId(null);
        this.tmpContactPoint(null);
      }
    });

    this.hasVisibleQuestion = ko.pureComputed(() => {
      if (POLL_IS_AUTO) return true;
      if (this.questionSource() == "common") return true;
      if (this.contactPointLoading()) return false;
      if (this.contactPointId()) return true;
      return false;
    });

    this.copyDisable = ko.pureComputed(() => {
      return (
        this.isNew() ||
        (this.activeQuestion().isInterblock &&
          this.activeQuestion().blockType() == "start")
      );
    });

    this.currentQuestionId(currentQuestionId);

    this.updateLiveQuestion();

    this.updateQueryParams();

    window.activate = (qId) => {
      this.onActivateQuestion(qId);
    };

    setTimeout(() => {
      console.log("setTimeout 400 context ", this);
      this.inited(true);
    }, 400);

    if (searchParams().pollLang) {
      this.configLangs();
    }

    if (this.isVuePreviewMode) {
      this.initPreviewMessageListeners();
    }
  }

  configLangs() {
    this.openSidesheet({
      name: "poll-translate-sidesheet",
      params: {
        poll: this.poll,
        questions: this.questions(),
        updateLiveQuestion: this.updateLiveQuestion.bind(this),
      },
    });
  }

  updateCurrentQuestionData(qData) {
    let currentQuestionId = this.currentQuestionId();

    let questionIndex = this.questions().findIndex(
      (q) => q.id == currentQuestionId
    );

    let questions = this.questions();
    questions[questionIndex] = {
      ...qData,
      id: currentQuestionId,
    };

    this.questions(questions);

    this.onActiveQuestionChange(currentQuestionId);
  }

  loadContactPointData(cpId) {
    return new Promise((res) => {
      $.ajax({
        url: ApiUrl("contact-points/view", { id: cpId }),
        success: (response) => {
          res(response);
        },
        error: (response) => {
          console.error(response.responseJSON);
        },
      });
    });
  }

  get activeQuestion() {
    return this.controller.question;
  }

  onActivateQuestion(id) {
    window.blockedForChanges = true;

    const action = () => {
      let currentQuestions = this.questions();

      this.isSubmitted(false);
      this.isNew(false);
      this.currentQuestionId(id);

      setTimeout(() => {
        currentQuestions = this.questions();

        this.updateQueryParams();
        window.blockedForChanges = false;
        this.questions(currentQuestions);
      });
    };

    if (this.isSaved()) {
      action();
    } else {
      this.openUnsavedChangesModal(
        () => {
          action();
        },
        () => {
          window.blockedForChanges = false;
        }
      );
    }
  }

  onActiveQuestionChange(questionId) {
    if (!questionId) return;

    this.isLoading(true);

    const activeQuestion = this.questions().find((q) => {
      if (q && q.id) {
        return parseInt(q.id) === parseInt(questionId);
      }
      return false;
    });

    console.log('DEBUG activeQuestion', activeQuestion);

    if (!activeQuestion) return;

    this.updateDefaultData(activeQuestion);

    const data = questionClientFormatter(
      activeQuestion,
      activeQuestion.tmpContactPoint ? "cpoint" : null
    );

    this.isNew(data.isTmp);

    this.controller.setQuestion(data);

    if (!POLL_IS_AUTO) {
      if (data.pointId) {
        this.questionSource("point");
        this.contactPointId(data.pointId);
      } else {
        this.questionSource("common");
        this.contactPointId(null);
      }
    }

    this.isLoading(false);

    this.isSaved(true);
    setTimeout(() => {
      this.isSaved(true);
      this.updateLiveQuestion();
      this.questions(this.questions());
    }, 400);
  }

  updateDefaultData(data) {
    this.defaultData(data);
  }

  openView() {
    this.fullPreview().open({
      size: this.activePreviewType(),
    });
  }

  onOpenView() {
    this._updateFullScreen();
  }

  getDataForPreview() {
    const data = this.controller.getData();
    console.log('getDataForPreview', data)

    // @NOTE: Для Vue превью добавляется дополнительная обработка данных
    let previewData = questionPreviewFormatter(data, this.isVuePreviewMode ? 'vue' : 'default');

    return previewData;
  }

  _updateFullScreen() {
    this._updateLiveQuestion(true);
  }

  _updateLiveQuestion(onlyFullScreen) {
    const sp = new URLSearchParams(location.search.slice(1));
    if (sp.has("stop")) return;

    let activeQuestionId = this.currentQuestionId();
    let activeQuestionPreviewData = this.getDataForPreview();
    window.liveQuestion = activeQuestionPreviewData;
    window.displayPages = ko.toJS(this.pages);
    window.questions = this.questions().map((q) => {
      if (q?.id == activeQuestionId) return activeQuestionPreviewData;
      return questionDirectPreviewFormatter(q);
    });

    // @TODO: Удалить после перехода на Vue
    console.log('_updateLiveQuestion', this.isVuePreviewMode);
    console.log('liveQuestion', window.liveQuestion);
    console.log('displayPages', window.displayPages);
    console.log('questions', window.questions);
    
    

    let arIFrames = document.querySelectorAll(".active iframe");
    arIFrames.forEach(($iframe) => {

      if (this.isVuePreviewMode) {
        // Используем postMessage для режима превью на Vue
        $iframe.contentWindow.postMessage({
          type: 'PREVIEW_DATA',
          data: {
            activeQuestion: activeQuestionPreviewData,
            currentQuestionId: activeQuestionId,
            displayPages: window.displayPages,
          }
        }, '*');

        if (window.pollIsPublished) {
          $iframe.contentWindow.postMessage({ type: "PREVIEW_TEST_MODE" }, "*");
        }
      } else {
      if (!onlyFullScreen && $iframe.id === "full-screen") return;
      if (onlyFullScreen && $iframe.id !== "full-screen") return;
        // Use existing update method for non-preview mode
        if (typeof $iframe.contentWindow.update === "function") {
          if (!this.rendered) {
            $iframe.contentWindow.location.reload(true);
            this.rendered = true
          } else {
            $iframe.contentWindow.update()
          }
        }
      }
    });
  }

  openDonorTypeModal() {
    this.openDialog({
      name: "donor-type-dialog",
      params: {
        text:'Вопрос можно удалить только после удаления всех связей вопроса с вопросами-реципиентами:',
        recipients: this.recipients(),
        toRecipient: (recipient) => {
          this.onActivateQuestion(recipient.question.id);
        },
      },
    });
  }

  delete() {
    if (this.recipients().length) {
      this.openDonorTypeModal()
      return
    }
    let question = this.controller.question();

    let text =
      "<div>" +
      (question.isAuto
        ? this.translator.t("Вопрос будет удален из автоматического опроса.")()
        : this.translator.t(
          "Вопрос будет удален без возможности восстановления."
        )()) +
      "</div>";

    if (question.logic() && question.logic().length)
      text += `<div class="mt-3">${this.translator.t(
        "{before}У этого вопроса настроена логика{after}, и логические условия, связанные с ним, удалятся.",
        {
          before: `<span class="bold f-color-danger">`,
          after: `</span>`,
        }
      )()}
    </div>`;

    this.confirm({
      title: this.translator.t("Удаление вопроса"),
      text,
      confirm: this.translator.t("Удалить"),
      mode: "danger",
    }).then(() => {
      location.href = "/foquz/foquz-question/delete?id=" + question.id();
    });

    return;
  }

  openUnsavedChangesModal(submit, cancel) {
    console.log('openUnsavedChangesModal', this.unsavedChangesModalOpen());
    
    if (this.unsavedChangesModalOpen()) return;
    this.unsavedChangesModalOpen(true);
    this.modalOpens.push({
      dialogTemplateName:
        "survey-question-unsaved-changes-modal-content-template",
      close: (result) => {
        this.unsavedChangesModalOpen(false);
        if (result !== undefined) {
          submit();
        } else {
          if (typeof cancel === "function") cancel();
        }
      },
    });
  }

  cancel() {
    let data = this.defaultData();
    this.controller.updateData(
      questionClientFormatter(data, data.tmpContactPoint ? "cpoint" : null)
    );

    this.isSaved(true);
    this.isSubmitted(false);
  }

  // TODO wtf
  change() {
    this.isSubmitted(true);

    if (this.formModel.isValid()) {
      this.modalOpens.push({
        dialogTemplateName: "survey-question-change-modal-content-template",
        close: function (result) {
          if (result !== undefined) {
            return result.state;
          }
          return false;
        },
      });
    }
  }

  updateQuestionData(params, asContactPoint, contactPointName) {
    return new Promise((res) => {
      // TODO
      const t = this;
      const question = this.controller.question();
      let isChangedPoint = !this.isSaved() && question.pointId();
      let urlParams = {
        id: question.id(),
      };
      if (asContactPoint && contactPointName) {
        params.FoquzQuestion.service_name = contactPointName;
        urlParams.asContactPoint = true;
      }
      let url = ApiUrl("questions/update", urlParams);

      const { additionalFiles, ...questionParams } = params;

      const fd = toFormData(questionParams);
      addFilesToFormData(fd, additionalFiles);

      request(url, {
        method: "post",
        body: fd,
      }).then((response) => {
        if (response.data.error) {
          this.errorMessage(response.data.error)
        }
        PollActivateEvent.emit({ id: POLL.id });
        let questionData = response.data.question;

        // TODO TASK-4484
        // if (asContactPoint && contactPointName) {
        //   this.collections.contactPoints.load("force");

        //   let text = `<div>${this.translator.t(
        //     "Вопрос сохранён как точка контакта {point}.",
        //     {
        //       point: `<span class="bold">${contactPointName}</span>`,
        //     }
        //   )()} ${this.translator.t(
        //     "Настройки новой точки контакта доступны в разделе {section}.",
        //     {
        //       section: `<a target="_blank" href="/foquz/contact-points?nameFilter=${
        //         contactPointName
        //       }">${this.translator.t("Точки контакта")()}</a>`,
        //     }
        //   )()}</div>`;

        //   if (POLL.point_system && isTypeWithPoints(question.type)) {
        //     text += `<div class="mt-3">${this.translator.t(
        //       'Новая точка контакта добавляется в систему <b class="bold">без настроек баллов</b'
        //     )()}></div>`;
        //   }

        //   this.info({
        //     text,
        //   });
        // } else if (isChangedPoint) {
        //   this.info({
        //     text: this.translator.t(
        //       'Настройки существующей точки контакта сохраняются <b class="bold">только для текущего опроса</b>'
        //     ),
        //   });
        // }

        let questionIndex = this.questions().findIndex(
          (q) => q?.id == this.currentQuestionId()
        );

        if (response.data.codes) {
          let addressCodes = response.data.codes;
          questionData.addressCodes = {
            regions: JSON.parse(addressCodes.regions),
            cities: JSON.parse(addressCodes.cities),
            districts: JSON.parse(addressCodes.districts),
            streets: JSON.parse(addressCodes.streets),
          };
        }

        let questions = this.questions();
        questions[questionIndex] = questionData;

        this.questions(questions);

        this.currentQuestionId(questionData.id);

        let correctOrder = this.questions().map((q) => q?.id);
        this.updateQuestionPositions(correctOrder);

        this.onActiveQuestionChange(this.currentQuestionId());

        if (!this.errorMessage()) {
          this.showSuccessMessage(true);
        }


        this.isSaved(true);

        this.isNew(false);
        this.isSubmitted(false);

        res();
      });
    });
  }

  saveAsContactPoint() {
    const question = this.controller.question();
    if (
      (question.type == MATRIX_3D_QUESTION && (question.donorRows.useDonor() || question.donorColumns.useDonor())) ||
      (question.type != MATRIX_3D_QUESTION && question.donor && question.donor.useDonor())
    ) {
      this.info({
        text: "Нельзя сохранить вопрос как точку контакта, так как в настройках вопроса используются ответы из другого вопроса.",
      });
      return;
    }
    this.openDialog({
      name: "contact-point-name-dialog",
      params: {
        name: question.alias() || '',
      },
      events: {
        submit: async (result) => {
          this.save("as-contact-point", result.name);
        },
      },
    });
  }

  save(asContactPoint, contactPointName) {
    return new Promise((res) => {
      this.isSubmitted(true);

      if (!this.controller.isValid()) return false;

      let question = this.controller.question();

      let data = questionServerFormatter(this.controller.getData());

      if (!this.isSaved() && question.isAuto) {
        var t = this;
        this.modalOpens.push({
          dialogTemplateName: "survey-question-change-modal-content-template",
          close: function (result) {
            if (result !== undefined) {
              if (result.state === true) {
                data.save_all = true;
              }
              t.updateQuestionData(data, asContactPoint, contactPointName).then(() => res());
            }
          },
        });
      } else {
        this.updateQuestionData(data, asContactPoint, contactPointName).then(() => res());
      }
    });
  }

  checkEmptyQuestions() {
    const emptyQuestion = this.questions().find((q) => {
      return !q.description && q.main_question_type != INTER_BLOCK;
    });
    if (!emptyQuestion) return true;

    this.onActivateQuestion(emptyQuestion.id);
    this.isSubmitted(true);

    return false;
  }

  getPage(pId) {
    return this.pages.find((p) => p.id == pId);
  }

  getQuestionPage(qId) {
    return this.pages.find((p) => {
      return p.questions().some((q) => q.id == qId);
    });
  }

  copyQuestion() {
    if (this.isNew()) return;
    if (!this.isSaved()) {
      this.info({
        title: "Данные не сохранены",
        text: "Данные в текущем вопросе не сохранены. Перед дублированием вопроса сохраните настройки.",
      });
      return;
    }

    let params = {
      id: this.currentQuestionId(),
    };

    let question = this.questions().find(
      (q) => q.id == this.currentQuestionId()
    );

    let isEndScreen = this.isCurrentQuestionEndScreen();
    if (this.pagesMode && !isEndScreen) {
      if (this.pages.length < 2) {
        params.pageId = this.pages[0].id;
      } else {
        let page = this.getQuestionPage(params.id);
        params.pageId = page.id;
      }
    }

    $.ajax({
      url: ApiUrl("questions/copy", params),
      method: "POST",
      success: (response) => {
        let newQuestion = response.items.new;
        this.questions.push(newQuestion);

        if (this.pagesMode && !isEndScreen) {
          let page = this.getPage(params.pageId);

          page.addQuestion(newQuestion.id);
          this.updatePagesOrder("save");
        }

        this.currentQuestionId(newQuestion.id);

        window.scrollTo({ top: 0, behavior: "smooth" });
      },
    });
  }

  copyQuestions() {
    if (!this.isSaved()) {
      this.info({
        text: this.translator.t(
          "Данные в текущем вопросе не сохранены. Перед копированием вопросов из других опросов сохраните настройки."
        ),
      });
      return;
    }

    this.openSidesheet({
      name: "copy-questions-sidesheet",
      params: {
        poll: POLL_ID,
        pagesMode: this.pagesMode,
        pages: this.pages,
        hasStartScreen: this.hasStartScreen(),
        onCopy: ({ questions, page }) => {
          let params = {
            to: POLL_ID,
          };

          if (this.pagesMode) {
            if (!page || this.pages.length == 1) {
              params.pageId = this.pages[0].id;
            } else {
              params.pageId = page;
            }
          }
          $.ajax({
            method: "POST",
            url: ApiUrl("poll/copy-questions", params),
            data: {
              questions,
            },
            success: (response) => {
              let before = this.questions().map((q) => q.id);
              let after = response.questions.map((q) => q.id);

              let diff = _.difference(after, before);

              this.questions(response.questions);

              if (this.pagesMode) {
                let page = this.getPage(params.pageId);
                diff.forEach((qId) => {
                  let question = response.questions.find((q) => q.id == qId);
                  if (
                    question.main_question_type != 16 ||
                    !question.intermediateBlock ||
                    question.intermediateBlock.screen_type == 1
                  ) {
                    page.addQuestion(qId);
                  }
                });
                this.updatePagesOrder("save");
              }

              this.blinked(diff);

              let currentId = this.currentQuestionId();
              if (after.find((q) => q == currentId)) {
              } else {
                this.currentQuestionId(diff[0]);
              }
            },
          });
        },
      },
    });
  }

  updateQuestionPositions(ids) {
    return new Promise((resolve, reject) => {
      $.post("/foquz/foquz-poll/update-questions-positions?pollId=" + POLL_ID, {
        ids,
      })
      .done(() => resolve())
      .fail((error) => reject(error));
    });
  }

  resortQuestions(ids, save) {
    if (!ids) {
      this.questions(this.questions());
      return;
    }

    ids = ids.map((id) => parseInt(id));
    this.questions().sort((a, b) => {
      return ids.indexOf(a.id) - ids.indexOf(b.id);
    });

    if (save) {
      this.updateQuestionPositions(ids);
    }
  }

  updatePagesOrder(save) {
    let startScreens = [];
    let endScreens = [];

    this.questions().forEach((q) => {
      if (q.main_question_type == 16) {
        if (q.intermediateBlock.screen_type == 2) startScreens.push(q.id);
        else if (q.intermediateBlock.screen_type == 3) endScreens.push(q.id);
      }
    });

    let questionsInPages = [];
    this.pages.forEach((p) => {
      p.questions().forEach((q) => questionsInPages.push(q.id));
    });

    let order = [...startScreens, ...questionsInPages, ...endScreens];

    this.resortQuestions(order, save);
  }

  onAddQuestion(pageId) {
    return new Promise((res) => {
      const action = () => {
        if (this.checkEmptyQuestions()) {
          this.isNew(true);
          this.isSubmitted(false);
          this.isSaved(false);

          let params = {
            pollId: POLL_ID,
          };

          if (this.pagesMode) {
            if (pageId) {
              params.pageId = pageId;
            } else {
              params.pageId = this.pages[0].id;
            }
          }

          $.get("/foquz/foquz-question/create", params, (response) => {
            this.tmpQuestion(null);
            this.questions.push(response.question);
            if (this.pagesMode) {
              let page = this.pages.find((p) => p.id == params.pageId);
              page.addQuestion(response.question.id);

              this.updatePagesOrder("save");
            }

            this.currentQuestionId(response.question.id);

            if (this.isVuePreviewMode) {
              this.updateQueryParams();
            }
            res();
          });
        }
      };

      if (this.isSaved()) {
        action();
      } else {
        this.openUnsavedChangesModal(() => {
          this.isSaved(true);
          action();
        });
      }
    });
  }

  changeActiveIframe() {
    let arIFrames = document.querySelectorAll("iframe");
    if (arIFrames) {
      arIFrames.forEach(($iframe) => {
        $iframe.style.opacity = "0";
        setTimeout(() => {
          $iframe.style.opacity = "1";
        }, 500);
      });
    }
    setTimeout(() => {
      this.updateLiveQuestion();
    }, 200);
  }

  updateQueryParams() {
    let url = window.location.pathname;

    const searchParams = new URLSearchParams(window.location.search);
    searchParams.set("id", this.currentQuestionId());

    url += "?" + searchParams.toString();

    window.history.replaceState({}, "", url);
  }

  /**
   * Инициализирует слушатели сообщений для режима предпросмотра Vue.
   * Обрабатывает навигацию между вопросами через postMessage.
   * @private
   */
  initPreviewMessageListeners() {
    window.addEventListener('message', (event) => {
      if (event.data.type === 'PREVIEW_NAVIGATION') {
        this.handlePreviewNavigation(event.data.data);
      }
    });
  }

  /**
   * Обрабатывает навигацию в режиме предпросмотра.
   * Определяет режим навигации (постраничный или простой) и делегирует обработку соответствующему методу.
   * @param {Object} data - Данные навигации
   * @param {string} data.direction - Направление навигации ('next' или 'prev')
   */
  handlePreviewNavigation(data) {
    const { direction } = data;
    
    if (this.pagesMode) {
      this.handlePageModeNavigation(direction);
    } else {
      this.handleSimpleNavigation(direction);
    }
  }

  /**
   * Обрабатывает навигацию в постраничном режиме.
   * В этом режиме вопросы сгруппированы по страницам.
   * При навигации учитывается структура страниц:
   * - при переходе вперед происходит переход на первый вопрос следующей страницы
   * - при переходе назад происходит переход на последний вопрос предыдущей страницы
   * @param {string} direction - Направление навигации ('next' или 'prev')
   */
  handlePageModeNavigation(direction) {
    const currentQuestionId = this.currentQuestionId();
    const pages = this.pages;
    
    // Find current page and question index
    const currentPageIndex = pages.findIndex(page => 
      page.questions().some(q => q.id === currentQuestionId)
    );

    const goToNextQuestion = () => {
      const questions = this.questions();
      const currentIndex = questions.findIndex(q => q.id === this.currentQuestionId());
      const nextIndex = this.findNextValidQuestionIndex(currentIndex, 'next', questions);
      if (this.isValidQuestionIndex(nextIndex, questions)) {
        this.currentQuestionId(questions[nextIndex].id);
      }
    }

    const goToPrevQuestion = () => {
      const questions = this.questions();
      const currentIndex = questions.findIndex(q => q.id === this.currentQuestionId());
      const prevIndex = this.findNextValidQuestionIndex(currentIndex, 'prev', questions);
      if (this.isValidQuestionIndex(prevIndex, questions)) {
        this.currentQuestionId(questions[prevIndex].id);
      }
    }

    console.log('CURRENT PAGE INDEX', currentPageIndex);
    if (currentPageIndex === -1) {
      if (direction === 'next') {
        goToNextQuestion();
      } else if (direction === 'prev') {
        goToPrevQuestion();
      }
    };
    
    const currentPage = pages[currentPageIndex];
    const currentPageQuestions = currentPage.questions();
    const currentQuestionIndex = currentPageQuestions.findIndex(q => q.id === currentQuestionId);

    const goToNextPage = () => {
      const nextPage = this.findNextValidPage(currentPageIndex);

      if (!nextPage) {
        const firstEndScreenIndex = this.questions().findIndex(q => this.isEndPage(q));
        if (firstEndScreenIndex !== -1) {
          this.currentQuestionId(this.questions()[firstEndScreenIndex].id);
        }
        return;
      }

      if (nextPage && nextPage.questions().length > 0) {
        this.currentQuestionId(nextPage.questions()[0].id);
      }
    }

    const goToPrevPage = () => {
      const prevPage = this.findPrevValidPage(currentPageIndex);
      if (prevPage && prevPage.questions().length > 0) {
        this.currentQuestionId(prevPage.questions()[prevPage.questions().length - 1].id);
      }
    }

    if (direction === 'next') {
      goToNextPage();
    } else if (direction === 'prev') {
      goToPrevPage();
    }
  }

  /**
   * Обрабатывает навигацию в простом режиме (без страниц).
   * В этом режиме все вопросы находятся в одном списке.
   * @param {string} direction - Направление навигации ('next' или 'prev')
   */
  handleSimpleNavigation(direction) {
    const questions = this.questions();
    const currentIndex = questions.findIndex(q => q.id === this.currentQuestionId());
    const nextIndex = this.findNextValidQuestionIndex(currentIndex, direction, questions);
    
    if (this.isValidQuestionIndex(nextIndex, questions)) {
      this.currentQuestionId(questions[nextIndex].id);
    }
  }

  /**
   * Находит следующую валидную страницу после указанного индекса.
   * Пропускает страницы с конечными экранами.
   * @param {number} currentPageIndex - Индекс текущей страницы
   * @returns {Object|null} Следующая валидная страница или null, если такой нет
   */
  findNextValidPage(currentPageIndex) {
    const pages = this.pages;
    let nextIndex = currentPageIndex + 1;
    
    // Find the last non-end page index
    const lastNonEndPageIndex = pages.findLastIndex(page => 
      !this.isEndPage(page.questions()[0])
    );

    // If we're on the last non-end page, allow transition to end pages
    if (currentPageIndex === lastNonEndPageIndex) {
      const firstPageWithEndScreenIndex = pages.findIndex(page => this.isEndPage(page.questions()?.[0]));

      if (firstPageWithEndScreenIndex !== -1) {
        return pages[firstPageWithEndScreenIndex];
      }
    }

    // Otherwise skip end pages as before
    while (nextIndex < pages.length) {
      const page = pages[nextIndex];
      if (!this.isEndPage(page.questions()[0])) {
        return page;
      }
      nextIndex++;
    }
    return null;
  }

  /**
   * Находит предыдущую валидную страницу перед указанным индексом.
   * Пропускает страницы с конечными экранами.
   * @param {number} currentPageIndex - Индекс текущей страницы
   * @returns {Object|null} Предыдущая валидная страница или null, если такой нет
   */
  findPrevValidPage(currentPageIndex) {
    const pages = this.pages;
    let prevIndex = currentPageIndex - 1;
    
    while (prevIndex >= 0) {
      const page = pages[prevIndex];
      if (!this.isEndPage(page.questions()[0])) {
        return page;
      }
      prevIndex--;
    }
    return null;
  }

  /**
   * Находит индекс следующего валидного вопроса в списке вопросов.
   * Пропускает конечные экраны при поиске.
   * @param {number} currentIndex - Текущий индекс
   * @param {string} direction - Направление навигации ('next' или 'prev')
   * @param {Array} questions - Массив вопросов
   * @returns {number} Индекс следующего валидного вопроса
   */
  findNextValidQuestionIndex(currentIndex, direction, questions) {
    let nextIndex = currentIndex;

    // Find the last non-end question index
    const lastNonEndIndex = questions.findLastIndex(q => !this.isEndPage(q));

    // If we're on the last non-end question and going forward,
    // allow the transition
    if (currentIndex === lastNonEndIndex && direction === 'next') {
      const firstEndScreenIndex = questions.findIndex(q => this.isEndPage(q));
      if (firstEndScreenIndex !== -1) {
        return firstEndScreenIndex;
      }
    }

    // Otherwise skip end screens as before
    do {
      nextIndex = this.getNextIndex(nextIndex, direction);
    } while (
      this.isValidQuestionIndex(nextIndex, questions) && 
      this.isEndPage(questions[nextIndex])
    );

    return nextIndex;
  }

  /**
   * Вычисляет следующий индекс на основе направления навигации.
   * @param {number} currentIndex - Текущий индекс
   * @param {string} direction - Направление навигации ('next' или 'prev')
   * @returns {number} Следующий индекс
   */
  getNextIndex(currentIndex, direction) {
    return direction === 'next' ? currentIndex + 1 : currentIndex - 1;
  }

  /**
   * Проверяет, является ли индекс допустимым для массива вопросов.
   * @param {number} index - Проверяемый индекс
   * @param {Array} questions - Массив вопросов
   * @returns {boolean} true, если индекс допустимый
   */
  isValidQuestionIndex(index, questions) {
    return index >= 0 && index < questions.length;
  }

  /**
   * Проверяет, является ли вопрос конечным экраном.
   * @param {Object} question - Объект вопроса
   * @returns {boolean} true, если вопрос является конечным экраном
   */
  isEndPage(question) {
    return question?.intermediateBlock?.screen_type === 3;
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
  }
}

$(function () {
  const viewModel = new ViewModel();

  const $content = $(".survey-question__content");

  viewModel.initializing = ko.observable(true);

  viewModel.onInit = function () {
    viewModel.initializing(false);
    $content.find(".survey-question__view-modal").appendTo($("body"));
  };

  window.onToggle = () => {
    $("body").trigger("update.preview");
  };

  ko.applyBindings(viewModel, $content.get()[0]);
});
