<!-- ko if: type === 0 -->
<!-- ko component: {
  name: 'rate-question-stats',
  params: {
    question: $data,
  }
} -->
<!-- /ko -->
<!-- /ko -->

<!-- ko if: type === 1 -->
<!-- ko component: {
  name: 'variants-question-stats',
  params: {
    question: $data,
  }
} -->
<!-- /ko -->
<!-- /ko -->




<!-- ko if: type === 2 -->
<div data-bind="component: {
    name: 'question-statistics-variant-statistics',
    params: {
        colors: ['#3F51B5', '#536DFE', '#DADFE3'],
        variants: [
            {id: 'withAnswer', question:'С ответом'},
            {id: 'withoutAnswer', question: 'Без ответа'},
            {id: 'skipped', question: 'Респондент отказался от ответа'}
        ],
        value: [
            statistics.answers.length,
            count - statistics.answers.length - statistics.skipped || 0,
            statistics.skipped || 0
        ],
        variantClick: function (id) {
            $root.openClientsModal(
                question_id,
                'Клиенты ' + (
                    id === 'withAnswer' ? 'с ответом' :
                    id === 'withoutAnswer' ? 'без ответа' :
                    'с пропуском ответа'
                ),
                'answer',
                id === 'withAnswer' ? 'not null' :
                id === 'withoutAnswer' ? 'null' :
                'skipped'
            );
            return false;
        }
    }
}" class="question-statistics__question-variant-statistics">
</div>

<a class="question-statistics__question-additional-button
    question-statistics__question-all-answers-button"
   data-bind="click: function () { $root.openTextAnswersModal(question_id); }">
Все ответы
</a>

<!--<div data-bind="component: {-->
<!--                            name: 'question-statistics-variant-statistics',-->
<!--                            params: {-->
<!--                                colors: ['#3F65F1', '#CFD8DC'],-->
<!--                                variants: [{id: 'withAnswer', question:'С ответом'}, {id: 'withoutAnswer', question: 'Без ответа'}],-->
<!--                                value: [statistics.answers.length, count - statistics.answers.length],-->
<!--                                variantClick: function (id) {-->
<!--                                    $root.openClientsModal(-->
<!--                                        question_id,-->
<!--                                        'Клиенты ' + (id == 'withAnswer' ? 'с ответом' : 'без ответа'),-->
<!--                                        'answer',-->
<!--                                        id === 'withAnswer' ? 'not null' : 'null'-->
<!--                                    );-->

<!--                                    return false;-->
<!--                                }-->
<!--                            }-->
<!--                            }" class="question-statistics__question-variant-statistics">-->
<!--</div>-->

<!--<a class="question-statistics__question-additional-button-->
<!--                                      question-statistics__question-all-answers-button" data-bind="click: function () { $root.openTextAnswersModal(question_id); }">-->
<!--  Все ответы-->
<!--</a>-->
<!-- /ko -->

<!-- ko if: type === 3 -->

<!-- ko component: {
  name: 'date-question-stats',
  params: {
    question: $data,
  }
} -->
<!-- /ko -->
<!-- /ko -->

<!-- ko if: type === 4 -->
<!-- ko let: { withAddresses: statistics.addresses.filter(function(a) { return a.address }).length } -->
<div data-bind="component: {
                        name: 'question-statistics-variant-statistics',
                        params: {
                            colors: ['#3F65F1', '#CFD8DC'],
                            variants: [{id: 'withAnswer', question: 'С адресом'}, {id: 'withoutAnswer', question:'Без адреса'}],
                            value: [withAddresses, count - withAddresses],
                            variantClick: function (id) {
                                $root.openClientsModal(
                                    question_id,
                                    'Клиенты ' + (id == 'withAnswer' ? 'с адресом' : 'без адреса'),
                                    'answer',
                                    id === 'withAnswer' ? 'not null' : 'null'
                                );

                                return false;
                            }
                        }
                    }" class="question-statistics__question-variant-statistics">
</div>

<a class="question-statistics__question-additional-button
                                      question-statistics__question-all-addresses-button" data-bind="click: function () { $root.openAddressesModal(question_id); }">
  Все адреса
</a>
<!-- /ko -->
<!-- /ko -->



<!-- ko if: type === 5 -->
<div data-bind="component: {
                        name: 'question-statistics-variant-statistics',
                        params: {
                            colors: ['#3F65F1', '#CFD8DC'],
                            variants: [{id: 'withFiles', question: 'С загрузкой'}, {id: 'withoutFiles', question:'Без загрузки'}],
                            value: [
                                statistics.answers.filter(a => a.files.length > 0).length,
                                count - statistics.answers.filter(a => a.files.length > 0).length
                            ],
                            variantClick: function (id) {
                                $root.openClientsModal(
                                    question_id,
                                    'Клиенты ' + (id === 'withFiles' ? 'с загрузкой' : 'без загрузки'),
                                    'files',
                                    id
                                );

                                return false;
                            }
                        }
                    }" class="question-statistics__question-variant-statistics">
</div>

<a class="question-statistics__question-additional-button
                                      question-statistics__question-all-files-button" data-bind="click: function () { $root.openFilesModal(question_id); }">
  Все файлы
</a>
<!-- /ko -->

<!-- ko if: type === 6 -->
<div data-bind="component: {
                                name: 'question-statistics-variant-statistics',
                                params: {
                                    colors: ['#84ffff', '#82B1FF', '#536DFE', '#3F51B5', '#aadbff', '#bdb2ff', '#ff9dd8', '#ffbdb4', '#f4e4cd', '#e6ffb1', '#bbf9ee', '#c2cfff', '#e7b3ff'],
                                    variants: [
                                        ...properties,
                                        {id: 0, question: 'Ни одно поле не заполнено'}
                                    ],
                                    value: $root.getQuestionVariantStatisticsProfilesValue($data),
                                    variantClick: function (index) {
                                        if (index === 0) {
                                            $root.openClientsModal(
                                                question_id,
                                                'Клиенты без ответа',
                                                'answer',
                                                'null'
                                            );
                                        } else {
                                            property = properties.find(p => p.id === index);
                                            $root.openClientsModal(
                                                question_id,
                                                'Клиенты с ответом &quot;' + property.question +  '&quot;',
                                                'formField',
                                                index
                                            );
                                        }

                                        return false;
                                    }
                                }
                            }" class="question-statistics__question-variant-statistics">
</div>

<a class="question-statistics__question-additional-button
                                      question-statistics__question-all-profiles-button" data-bind="click: function () { $root.openProfilesModal($data); }">
  Все ответы
</a>
<!-- /ko -->

<!-- ko if: type === 7 -->
<!-- ko component: {
  name: 'star-variants-question-stats',
  params: {
    question: $data,
  }
} -->
<!-- /ko -->
<!-- /ko -->


<!-- ko if: type === 8 -->
<!-- ko component: {
  name: 'priority-question-stats',
  params: {
    question: $data,
  }
} -->
<!-- /ko -->
<!-- /ko -->

<!-- ko if: type === 9 -->
<!-- ko component: {
  name: 'media-variants-question-stats',
  params: {
    question: $data,
  }
} -->
<!-- /ko -->
<!-- /ko -->


<!-- ko if: type === 10 -->
<!-- ko component: {
  name: 'gallery-question-stats',
  params: {
    question: $data,
  }

} -->
<!-- /ko -->

<!-- /ko -->


<!-- ko if: type === 11 -->
<!-- ko component: {
  name: 'smile-question-stats',
  params: {
    question: $data,
  }

} -->
<!-- /ko -->
<!-- /ko -->

<!-- ko if: type === 12 -->
<!-- ko component: {
  name: 'nps-question-stats',
  params: {
    question: $data,
  }

} -->
<!-- /ko -->
<!-- /ko -->

<!-- ko if: type === 13 -->
<!-- ko component: {
  name: 'matrix-question-stats',
  params: {
    question: $data,
  }
} -->
<!-- /ko -->
<!-- /ko -->

<!-- ko if: type === 22 -->
<!-- ko component: {
  name: 'card-sorting-question-stats',
  params: {
    question: $data,
  }
} -->
<!-- /ko -->
<!-- /ko -->

<!-- ko if: type === 14 -->
<!-- ko component: {
  name: 'diff-question-stats',
  params: {
    question: $data,
  }
} -->
<!-- /ko -->
<!-- /ko -->

<!-- ko if: type === 15 -->
<!-- ko component: {
  name: 'stars-question-stats',
  params: {
    question: $data,
  }
} -->
<!-- /ko -->
<!-- /ko -->

<!-- ko if: type === 17 -->
<!-- ko component: {
  name: 'filials-question-stats',
  params: {
    question: $data,
  }
} -->
<!-- /ko -->
<!-- /ko -->

<!-- ko if: type === 18 -->
<!-- ko component: {
  name: 'rating-question-stats',
  params: {
    question: $data,
  }
} -->
<!-- /ko -->
<!-- /ko -->

<!-- ko if: type === 19 -->
<!-- ko component: {
  name: 'classifier-question-stats',
  params: {
    question: $data,
  }
} -->
<!-- /ko -->
<!-- /ko -->

<!-- ko if: type === 20 -->
<!-- ko component: {
  name: 'scale-question-stats',
  params: {
    question: $data,
  }

} -->
<!-- /ko -->
<!-- /ko -->

<!-- ko if: type === 21 -->
<!-- ko component: {
  name: 'matrix-3d-question-stats',
  params: {
    question: $data,
  }

} -->
<!-- /ko -->
<!-- /ko -->

<!-- ko if: type === 23 -->
<!-- ko component: {
  name: 'distribution-scale-question-stats',
  params: {
    question: $data,
  }

} -->
<!-- /ko -->
<!-- /ko -->

<?= $this->render('question-types/rate/template.php') ?>
<?= $this->render('question-types/variants/template.php') ?>
<?= $this->render('question-types/date/template.php') ?>
<?= $this->render('question-types/priority/template.php') ?>
<?= $this->render('question-types/media-variants/template.php') ?>
<?= $this->render('question-types/smile/template.php') ?>
<?= $this->render('question-types/nps/template.php') ?>
<?= $this->render('question-types/matrix/template.php') ?>
<?= $this->render('question-types/matrix-3d/template.php') ?>
<?= $this->render('question-types/diff/template.php') ?>
<?= $this->render('question-types/stars/template.php') ?>
<?= $this->render('question-types/rating/template.php') ?>
<?= $this->render('question-types/filials/template.php') ?>
<?= $this->render('question-types/star-variants/template.php') ?>
<?= $this->render('question-types/gallery/template.php') ?>
<?= $this->render('question-types/classifier/template.php') ?>
<?= $this->render('question-types/scale/template.php') ?>
<?= $this->render('question-types/distribution-scale/template.php') ?>
<?= $this->render('question-types/card-sorting/template.php') ?>


<?= $this->render('templates/modals.php') ?>
<?= $this->render('components/rating/template.php') ?>
<script setup>
</script>
