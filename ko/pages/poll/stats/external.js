import "./utils/slider";
import { formatProperties } from "./utils/format-properties";

import "./types/variants";

import "./components/rating";

import { QuestionFactory } from "./question-types";
import "Modals/points-stats-modal-page";
import "Modals/share-link-modal";
import { initCurrentCompany } from "@/api/company/"
import { TagsDirectory } from "Utils/directory/tags";

import "Components/print-footer";
import "Components/tags-filter";
import { Printer } from "Utils/print";

import { PointsModel } from "Models/points";

import { ApiUrl } from "Utils/url/api-url";

import "Components/stats-filters-value";
import "Dialogs/stats/";
import { TextAnswer } from "../../../models/text-answer";
import { FilialsList } from "../../../utils/filials-list";

import { DialogsModule } from "Utils/dialogs-module";

import "./external.less";
import '@/presentation/components/fc-highchart';

Highcharts.setOptions({
  chart: {
    style: {
      fontFamily: "Roboto, Arial, serif",
    },
  },
});

ko.components.register("question-statistics__comments-modal-dialog", {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      const $element = $(componentInfo.element);
      $element.addClass([
        "question-statistics__comments-modal-dialog",
        "question-statistics__comments-modal-dialog--initializing",
      ]);

      const viewModel = new (function () {
        this.modalElement = params.modalElement;

        this.page = ko.observable(1);
        this.dateFrom = params.data.dateFrom;
        this.dateTo = params.data.dateTo;
        this.question_id = params.data.question_id;

        this.isAuto = params.data.isAuto;
        this.hasOrder = params.data.hasOrder;

        this.searchTerm = ko.observable("");
        this.isLoading = ko.observable(true);
        this.isLoadingMore = ko.observable(false);
        this.lastPage = ko.observable(false);
        this.filteredClients = ko.observable([]);

        this.pollId = POLL_ID;

        this.resetSearchTerm = function () {
          this.searchTerm("");
          this.dataSearch();
        };

        this.pagination = function ($container) {
          let $rows = $(
            ".question-statistics__comments-modal-dialog-table-row"
          );
          if (
            $container.scrollTop() > 0 &&
            $container.outerHeight() + $container.scrollTop() >=
            $container.prop("scrollHeight") &&
            $rows.length >= this.page() * 30 &&
            !this.lastPage()
          ) {
            this.page(this.page() + 1);
            this.dataSearch();
          }
        };

        this.dataSearch = function () {
          if (this.page() === 1) {
            this.isLoading(true);
          } else {
            this.isLoadingMore(true);
          }

          let data = {
            page: this.page(),
            q: this.searchTerm(),
          };
          if (this.dateFrom !== null && this.dateTo !== null) {
            data.from = this.dateFrom;
            data.to = this.dateTo;
          }

          $.get(
            "/foquz/foquz-poll/get-comments?id=" + this.question_id,
            data,
            (response) => {
              const searchText = this.searchTerm();
              const searchTextRegExp = new RegExp(searchText, "i");
              const filterableFields = [
                "name",
                "phone",
                "email",
                "passedAt",
                "orderId",
                "orderCreatedAt",
                "comment",
              ];
              var comments = [];
              if (this.page() === 1) {
                comments = response.comments;
                // this.filteredClients(response.comments);
              } else {
                var array = this.filteredClients();
                for (var comment in response.comments) {
                  let commentObj = response.comments[comment];
                  //commentObj.simpleOrderId = commentObj.orderId;
                  array.push(commentObj);
                  comments = array;
                }
                // this.filteredClients(array);
              }
              this.filteredClients(
                comments
                  .filter((c) => {
                    return filterableFields.some((field) =>
                      c[field]
                        .toLowerCase()
                        .includes(this.searchTerm().toLowerCase())
                    );
                  })
                  .map((c) => {
                    const result = {
                      ...c,
                      simpleOrderId: c.orderId,
                    };

                    filterableFields.forEach((field) => {
                      result[field] = c[field].replace(
                        searchTextRegExp,
                        '<span class="question-statistics__comments-modal-dialog-table-highlight">$&</span>'
                      );
                    });

                    return result;
                  })
              );
              this.lastPage(response.lastPage);
              this.isLoading(false);
              this.isLoadingMore(false);
              $(window).resize();
            }
          );
        };
      })();

      viewModel.initializing = ko.observable(true);

      viewModel.onInit = function () {
        $element.removeClass(
          "question-statistics__comments-modal-dialog--initializing"
        );

        viewModel.dataSearch();

        $("#comments-search").keypress(function (event) {
          if (event.which == 13) {
            viewModel.page(1);
            viewModel.dataSearch();
          }
        });

        viewModel.initializing(false);

        setTimeout(() => {
          const $container = $(
            ".question-statistics__comments-modal-dialog-table-wrapper .ps"
          );

          $container.on("scroll", () => {
            viewModel.pagination($container);
          });
        });
      };

      return viewModel;
    },
  },
  template: {
    element: "question-statistics-comments-modal-dialog-template",
  },
});

ko.bindingHandlers.questionStatisticsAddressesModalDialogMap = {
  init: function (element, valueAccessor, allBindings) {
    const address = allBindings
      .get("questionStatisticsAddressesModalDialogMapAddress")
      .replace(
        '<span class="question-statistics__addresses-modal-dialog-table-highlight">',
        ""
      )
      .replace("</span>", "");

    const map = new ymaps.Map(
      element,
      {
        center: [55.010251, 82.958437],
        zoom: 9,
        controls: [],
      },
      {
        searchControlProvider: "yandex#search",
      }
    );

    ymaps.geocode(address).then(function (res) {
      const firstGeoObject = res.geoObjects.get(0),
        coords = firstGeoObject.geometry.getCoordinates(),
        bounds = firstGeoObject.properties.get("boundedBy");

      firstGeoObject.options.set("preset", "islands#redDotIcon");
      firstGeoObject.properties.set(
        "iconCaption",
        firstGeoObject.getAddressLine()
      );

      map.geoObjects.add(firstGeoObject);

      map.setBounds(bounds, {
        checkZoomRange: true,
      });
    });

    ko.utils.domNodeDisposal.addDisposeCallback(element, function () {
      map.destroy();
    });
  },
};

ko.components.register("question-statistics__addresses-modal-dialog", {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      const $element = $(componentInfo.element);
      $element.addClass([
        "question-statistics__addresses-modal-dialog",
        "question-statistics__addresses-modal-dialog--initializing",
      ]);

      const viewModel = new (function () {
        this.modalElement = params.modalElement;

        this.isAuto = params.data.isAuto;
        this.hasOrder = params.data.hasOrder;
        this.page = ko.observable(1);

        this.searchTerm = ko.observable("");
        this.isLoading = ko.observable(true);
        this.isLoadingMore = ko.observable(false);

        this.dateFrom = params.data.dateFrom;
        this.dateTo = params.data.dateTo;

        this.question_id = params.data.question_id;

        this.question = params.data.question;

        this.lastPage = ko.observable(false);

        this.filteredClients = ko.observable([]);

        this.pollId = POLL_ID;

        this.resetSearchTerm = function () {
          this.searchTerm("");
          this.dataSearch();
        };

        this.pagination = function ($container) {
          let $rows = $(
            ".question-statistics__addresses-modal-dialog-list-item"
          );
          if (
            $container.scrollTop() > 0 &&
            $container.outerHeight() + $container.scrollTop() >=
            $container.prop("scrollHeight") &&
            $rows.length >= this.page() * 30 &&
            !this.lastPage()
          ) {
            this.page(this.page() + 1);
            this.dataSearch();
          }
        };

        this.dataSearch = function () {
          if (this.page() === 1) {
            this.isLoading(true);
          } else {
            this.isLoadingMore(true);
          }
          let data = {
            page: this.page(),
            q: this.searchTerm(),
          };
          if (this.dateFrom !== null && this.dateTo !== null) {
            data.from = this.dateFrom;
            data.to = this.dateTo;
          }
          $.get(
            "/foquz/foquz-poll/get-addresses?id=" + this.question_id,
            data,
            (response) => {
              const searchText = this.searchTerm();
              const searchTextRegExp = new RegExp(searchText, "i");
              const filterableFields = [
                "name",
                "phone",
                "email",
                "passedAt",
                "orderId",
                "orderCreatedAt",
                "address",
              ];
              if (this.page() === 1) {
                this.filteredClients(response.answers);
              } else {
                var array = this.filteredClients();
                for (var answer in response.answers) {
                  array.push(response.answers[answer]);
                }
                this.filteredClients(array);
              }
              this.filteredClients(
                this.filteredClients()
                  .filter((c) => {
                    return filterableFields.some((field) =>
                      c[field]
                        .toLowerCase()
                        .includes(this.searchTerm().toLowerCase())
                    );
                  })
                  .map((c) => {
                    const result = {
                      ...c,
                      simpleOrderId: c.orderId,
                    };

                    filterableFields.forEach((field) => {
                      result[field] = c[field].replace(
                        searchTextRegExp,
                        '<span class="question-statistics__addresses-modal-dialog-table-highlight">$&</span>'
                      );
                    });

                    return result;
                  })
              );
              this.lastPage(response.lastPage);
              this.isLoading(false);
              this.isLoadingMore(false);
            }
          );
        };
      })();

      viewModel.initializing = ko.observable(true);

      viewModel.onInit = function () {
        $element.removeClass(
          "question-statistics__addresses-modal-dialog--initializing"
        );

        viewModel.dataSearch();

        viewModel.initializing(false);

        $("#addresses-search").keypress(function (event) {
          if (event.which == 13) {
            viewModel.page(1);
            viewModel.dataSearch();
          }
        });

        setTimeout(() => {
          const $container = $(
            ".question-statistics__addresses-modal-dialog-list-wrapper.ps"
          );

          $container.on("scroll", () => {
            viewModel.pagination($container);
          });
        });
      };

      return viewModel;
    },
  },
  template: {
    element: "question-statistics-addresses-modal-dialog-template",
  },
});

ko.bindingHandlers.questionStatisticsFilesModalDialogFiles = {
  init: function (element, valueAccessor) {
    const {
      urls,
      comment,
      orderId,
      orderCreatedAt,
      name,
      email,
      phone,
      passedAt,
    } = valueAccessor();

    const audioExt = ['mp3', 'ogg', 'wav', 'm4a']

    $(element).on("click", () => {
      $.fancybox.open(
        urls.map((url) => {
          const urlArr = url.split('.')
            const res = { src: url }
            if (audioExt.includes(urlArr[urlArr.length - 1].toLowerCase())) {
              res.type = 'inline',
              res.src = `<audio class="fs-file-audio" controls src="${url}"></audio>`
              res.mainClass = "fs-file-audio-container"
            }
            return res;
        }),
        {
          loop: false,
          buttons: ["rotate", "zoom", "slideShow", "close"],
          caption: () => {
            let items = [name, phone, email].filter(Boolean);
            if (orderCreatedAt !== "" && orderCreatedAt !== undefined) {
              items.push(`Заказ ${orderCreatedAt}`);
            }
            if (orderId !== "" && orderId !== undefined) {
              items.push(`Заказ #${orderId}`);
            }
            items.push(`Пройден ${passedAt}`);

            let commentItem =
              comment !== null
                ? `<div class="question-statistics__files-modal-dialog-list-item-fancybox-comment">${comment}</div>`
                : "";

            items = items
              .map(
                (i) =>
                  `<span class="question-statistics__files-modal-dialog-list-item-fancybox-info">${i}</span>`
              )
              .join("");
            return `
                            <div class="question-statistics__files-modal-dialog-list-item-fancybox">
                                <div class="question-statistics__files-modal-dialog-list-item-fancybox-info-block">
                                    ${items}
                                </div>
                                ${commentItem}
                            </div>
                            `;
          },
        }
      );
    });
  },
};

ko.components.register("question-statistics__files-modal-dialog", {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      const $element = $(componentInfo.element);
      $element.addClass([
        "question-statistics__files-modal-dialog",
        "question-statistics__files-modal-dialog--initializing",
      ]);

      const viewModel = new (function () {
        this.modalElement = params.modalElement;

        this.page = ko.observable(1);
        this.ctx = params.data.ctx;

        this.isAuto = params.data.isAuto;
        this.hasOrder = params.data.hasOrder;

        this.searchTerm = ko.observable("");
        this.isLoading = ko.observable(true);
        this.isLoadingMore = ko.observable(false);
        this.dateFrom = params.data.dateFrom;
        this.dateTo = params.data.dateTo;

        this.question_id = params.data.question_id;

        this.lastPage = ko.observable(false);

        this.filteredClients = ko.observable([]);

        this.pollId = POLL_ID;
        this.statsLinkId = POLL.statsLink.link.replace(/.*stats\/(\w*)/, '$1');

        this.resetSearchTerm = function () {
          this.searchTerm("");
          this.dataSearch();
        };

        this.pagination = function ($container) {
          let $rows = $(".question-statistics__files-modal-dialog-list-item");
          if (
            $container.scrollTop() > 0 &&
            $container.outerHeight() + $container.scrollTop() >=
            $container.prop("scrollHeight") &&
            $rows.length >= this.page() * 72 &&
            !this.lastPage()
          ) {
            this.page(this.page() + 1);
            this.dataSearch();
          }
        };

        this.dataSearch = function () {
          if (this.page() === 1) {
            this.isLoading(true);
          } else {
            this.isLoadingMore(true);
          }
          let data = {
            page: this.page(),
            q: this.searchTerm(),
          };
          if (this.dateFrom !== null && this.dateTo !== null) {
            data.from = this.dateFrom;
            data.to = this.dateTo;
          }
          data = {
            ...data,
            ...this.ctx.getSearchParams(),
          };
          $.get(
            `/foquz/foquz-poll/get-files?id=${this.question_id}&link=${this.statsLinkId}`,
            data,
            (response) => {
              const searchText = this.searchTerm();
              const searchTextRegExp = new RegExp(searchText, "i");
              if (this.page() === 1) {
                this.filteredClients(response.answers);
              } else {
                var array = this.filteredClients();
                for (var answer in response.answers) {
                  array.push(response.answers[answer]);
                }
                this.filteredClients(array);
              }
              this.filteredClients(
                this.filteredClients()
                  .filter((c) => {
                    return (
                      c.firstInfo
                        .toLowerCase()
                        .includes(this.searchTerm().toLowerCase()) ||
                      c.secondInfo
                        .toLowerCase()
                        .includes(this.searchTerm().toLowerCase())
                    );
                  })
                  .map((c) => ({
                    ...c,
                    firstInfoText: c.firstInfo.replace(
                      searchTextRegExp,
                      '<span class="question-statistics__files-modal-dialog-highlight">$&</span>'
                    ),
                    secondInfoText: c.secondInfo
                      .toString()
                      .replace(
                        searchTextRegExp,
                        '<span class="question-statistics__files-modal-dialog-highlight">$&</span>'
                      ),
                  }))
              );
              this.lastPage(response.lastPage);
              this.isLoading(false);
              this.isLoadingMore(false);
            }
          );
        };

        this.getListItemPreviewData = function (client) {
          const result = {
            urls: client.files.map((f) => f.url),
            comment: client.comment,
            name: client.name,
            phone: client.phone,
            email: client.email,
            passedAt: client.passedAt,
          };

          if (this.hasOrder) {
            result.pollId = POLL_ID;
            result.orderId = client.orderId;
            result.orderCreatedAt = client.orderCreatedAt;
          }

          return result;
        };
      })();

      viewModel.initializing = ko.observable(true);

      viewModel.onInit = function () {
        $element.removeClass(
          "question-statistics__files-modal-dialog--initializing"
        );

        viewModel.dataSearch();

        $("#files-search").keypress(function (event) {
          if (event.which == 13) {
            viewModel.page(1);
            viewModel.dataSearch();
          }
        });

        viewModel.initializing(false);

        setTimeout(() => {
          const $container = $(
            ".question-statistics__files-modal-dialog-list-wrapper .ps"
          );

          $container.on("scroll", () => {
            viewModel.pagination($container);
          });
        });
      };

      return viewModel;
    },
  },
  template: {
    element: "question-statistics-files-modal-dialog-template",
  },
});

ko.components.register("question-statistics__profiles-modal-dialog", {
  viewModel: {
    createViewModel: function (params, componentInfo) {
      const $element = $(componentInfo.element);
      $element.addClass([
        "question-statistics__profiles-modal-dialog",
        "question-statistics__profiles-modal-dialog--initializing",
      ]);

      const viewModel = new (function () {
        this.modalElement = params.modalElement;
        this.ctx = params.data.ctx;
        this.page = ko.observable(1);
        this.lastPage = ko.observable(false);

        this.isAuto = params.data.isAuto;
        this.hasOrder = params.data.hasOrder;
        this.dateFrom = params.data.dateFrom;
        this.dateTo = params.data.dateTo;
        this.question_id = params.data.question_id;
        this.properties = params.data.properties;

        this.filteredClients = ko.observable([]);

        this.pollId = POLL_ID;

        this.searchTerm = ko.observable("");
        this.isLoading = ko.observable(true);
        this.isLoadingMore = ko.observable(false);

        this.resetSearchTerm = function () {
          this.searchTerm("");
          this.dataSearch();
        };

        this.pagination = function ($container) {
          let $rows = $(
            ".question-statistics__profiles-modal-dialog-list-item"
          );

          if (
            $container.scrollTop() > 0 &&
            $container.outerHeight() + $container.scrollTop() >=
            $container.prop("scrollHeight") &&
            $rows.length >= this.page() * 30 &&
            !this.lastPage()
          ) {
            this.page(this.page() + 1);
            this.dataSearch();
          }
        };

        this.transformProfiles = function () {
          const searchText = this.searchTerm();
          const searchTextRegExp = new RegExp(searchText, "i");
          const filterableFields = [
            "name",
            "phone",
            "email",
            "passedAt",
            "orderId",
            "orderCreatedAt",
          ];

          return this.filteredClients()
            .filter((c) => {
              return (
                filterableFields.some((field) =>
                  c[field]
                    .toLowerCase()
                    .includes(this.searchTerm().toLowerCase())
                ) ||
                c.properties.some(
                  (p) =>
                    p !== null &&
                    p
                      .toString()
                      .toLowerCase()
                      .includes(this.searchTerm().toLowerCase())
                )
              );
            })
            .map((c) => {
              const result = {
                ...c,
                simpleOrderId: c.orderId,
                properties: c.properties.map((p) =>
                  p !== null
                    ? p
                      .toString()
                      .replace(
                        searchTextRegExp,
                        '<span class="question-statistics__profiles-modal-dialog-table-highlight">$&</span>'
                      )
                    : null
                ),
              };

              filterableFields.forEach((field) => {
                result[field] = c[field].replace(
                  searchTextRegExp,
                  '<span class="question-statistics__profiles-modal-dialog-table-highlight">$&</span>'
                );
              });

              return result;
            });
        };

        this.dataSearch = function () {
          if (this.page() === 1) {
            this.isLoading(true);
          } else {
            this.isLoadingMore(true);
          }
          let data = {
            page: this.page(),
            q: this.searchTerm(),
          };
          if (this.dateFrom !== null && this.dateTo !== null) {
            data.from = this.dateFrom;
            data.to = this.dateTo;
          }

          data = {
            ...data,
            ...this.ctx.getSearchParams(),
          };
          $.get(
            "/foquz/foquz-poll/get-profiles?id=" + this.question_id,
            data,
            (response) => {
              let profiles = [];
              if (this.page() === 1) {
                profiles = response.profiles;
              } else {
                var array = this.filteredClients();
                for (var profile in response.profiles) {
                  array.push(response.profiles[profile]);
                }
                profiles = array;
              }

              profiles = formatProperties(profiles);
              this.filteredClients(profiles);

              this.filteredClients(this.transformProfiles());
              this.lastPage(response.lastPage);
              this.isLoadingMore(false);
              this.isLoading(false);
            }
          );
        };
      })();

      viewModel.initializing = ko.observable(true);

      viewModel.onInit = function () {
        $element.removeClass(
          "question-statistics__profiles-modal-dialog--initializing"
        );

        viewModel.dataSearch();

        $("#profiles-search").keypress(function (event) {
          if (event.which == 13) {
            viewModel.page(1);
            viewModel.dataSearch();
          }
        });

        viewModel.initializing(false);

        setTimeout(() => {
          const $container = $(
            ".question-statistics__profiles-modal-dialog-list-wrapper .ps"
          );

          $container.on("scroll", () => {
            viewModel.pagination($container);
          });
        });
      };

      return viewModel;
    },
  },
  template: {
    element: "question-statistics-profiles-modal-dialog-template",
  },
});

ko.bindingHandlers.questionStatisticsImage = {
  init: function (element, valueAccessor) {
    const { urls, index } = valueAccessor();

    $(element).on("click", () => {
      $.fancybox.open(
        urls.map((url) => ({
          src: url,
        })),
        {
          index,
          loop: false,
          buttons: ["rotate", "zoom", "slideShow", "close"],
        }
      );
    });
  },
};

ko.bindingHandlers.questionStatisticsVideo = {
  init: function (element, valueAccessor) {
    const { urls, index } = valueAccessor();

    $(element).on("click", () => {
      $.fancybox.open(
        urls.map((url) => ({
          src: url,
        })),
        {
          index,
          loop: false,
          buttons: ["close"],
        }
      );
    });
  },
};

ko.bindingHandlers.questionStatisticsRatingStatisticsPopover = {
  init: function (
    element,
    valueAccessor,
    allBindings,
    viewMode,
    bindingContext
  ) {
    $(element).popover({
      html: true,
      sanitize: false,
      placement: "bottom",
      boundary: document,
      template: `
                    <div class="popover question-statistics__rating-statisitcs-popover" role="tooltip">
                        <div class="popover-body"></div>
                    </div>
                `,
      content: `
                    <table data-bind="component: {
                        name: 'question-statistics-rating-statistics-table',
                        params: {
                            value: value,
                            variantsSkips: ${bindingContext.variantsSkips ? 'variantsSkips' : '0'},
                            onRowClick: function (value) {
                                onRowClick(value);
                            },
                            max: ${allBindings.get("max")}
                        }
                    }">
                    </table>
                `,
    });

    $(element).on("inserted.bs.popover", () => {
      const content = $($(element).data("bs.popover").tip)
        .find(".popover-body")
        .children()[0];
      const context = bindingContext.extend({
        onRowClick: (value) => {
          if (
            allBindings.has(
              "questionStatisticsRatingStatisticsPopoverItemClick"
            )
          ) {
            allBindings.get(
              "questionStatisticsRatingStatisticsPopoverItemClick"
            )(value);
          }
        },
      });
      ko.applyBindings(context, content);
    });

    const bodyClickHandler = function (e) {
      if (
        $(e.target) !== element &&
        $(e.target).parents(".popover.in").length === 0
      ) {
        $(element).popover("hide");
      }
    };

    $(element).bind("shown.bs.popover", () => {
      $("body").bind("click", bodyClickHandler);
    });

    $(element).on("hide.bs.popover", () => {
      $("body").unbind("click", bodyClickHandler);
    });
  },
};

$(function () {
  const ViewModel = function () {
    PageModel(this);
    DialogsModule(this);

    this.modals = ko.observableArray([]);

    this.poll = POLL;
    this.statsLinkId = POLL.statsLink.link.replace(/.*stats\/(\w*)/, '$1');

    initCurrentCompany({
      id: this.poll.company_id
    })

    this.timeToPass = null;
    if (this.poll.time_to_pass) {
      let [h, m, s] = this.poll.time_to_pass.split(":");
      h = parseInt(h) || 0;
      m = parseInt(m) || 0;
      s = parseInt(s) || 0;
      let minutes = h * 60 + m;
      this.timeToPass = `${("" + minutes).padStart(2, "0")}:${("" + s).padStart(
        2,
        "0"
      )}`;
    }

    this.daysTrigger = ko.observable(POLL_TRIGGER === 3);

    this.isAuto = POLL_IS_AUTO;
    this.hasOrder = POLL_IS_AUTO && (POLL_TRIGGER == 1 || POLL_TRIGGER == 2);

    this.pointsModel = new PointsModel(this.poll);

    this.openPointsModal = () => {
      this.modals.push({
        name: "points-stats-modal-page",
        params: {
          poll: this.poll,
          filters: this.hasSavedFilters() ? this.getSavedSearchParams() : null,
        },
      });
    };

    // open modal dialog
    this.openStatsModal = (name, params) => {
      console.log('open modal', params, name)
      this.openSidesheet({
        name,
        params: {
          poll: {
            id: POLL_ID,
            isAuto: this.isAuto,
            hasOrder: this.hasOrder,
          },
          searchParams: this.getSearchParams(),
          ...params,
        },
      });
    };

    this.dishes = ko.observableArray([]);
    this.searchTerm = ko.observable("");
    this.sortValue = ko.observable("all");

    this.name = ko.observable(POLL_NAME);
    this.isNameEditing = ko.observable(false);

    this.firstAnswerDate = null;
    this.firstAnswerTime = null;

    if (FIRST_ANSWER) {
      let date = moment(FIRST_ANSWER, "DD.MM.YYYY HH:mm");
      this.firstAnswerDate = date.format("DD.MM.YYYY");
      this.firstAnswerTime = date.format("HH:mm");
    }

    this.lastAnswerDate = null;
    this.lastAnswerTime = null;

    if (LAST_ANSWER) {
      let date = moment(LAST_ANSWER, "DD.MM.YYYY HH:mm");
      this.lastAnswerDate = date.format("DD.MM.YYYY");
      this.lastAnswerTime = date.format("HH:mm");
    }

    this.averageTime = ko.observable(AVG_TIME_PER_ANSWER || "00:00");

    this.periodPickerRanges = {
      Сегодня: [moment(), moment()],
      "С начала недели": [moment().startOf("isoWeek"), moment()],
      "Прошлая неделя": [
        moment().subtract(1, "weeks").startOf("isoWeek"),
        moment().subtract(1, "weeks").endOf("isoWeek"),
      ],
      "Последние 7 дней": [moment().subtract(6, "days"), moment()],
      "Текущий месяц": [moment().startOf("month"), moment()],
      "Прошлый месяц": [
        moment().subtract(1, "month").startOf("month"),
        moment().subtract(1, "month").endOf("month"),
      ],
      "Последний месяц": [moment().subtract(29, "days"), moment()],
    };

    let queryString = window.location.search;
    let urlParams = new URLSearchParams(queryString);
    this.dateFrom =
      urlParams.get("from") === null ? null : urlParams.get("from");
    this.dateTo = urlParams.get("to") === null ? null : urlParams.get("to");

    this.questionnaires = ko.observable(urlParams.get("questionnaires") || "");
    this.savedQuestionnaires = ko.observable(this.questionnaires());

    this.devices = ko.observableArray(urlParams.getAll("devices[]"));
    this.savedDevices = ko.observableArray(this.devices());
    let deviceNames = {
      desktop: "Десктоп",
      tablet: "Планшет",
      mobile: "Смартфон",
    };
    this.savedDevicesNames = ko.pureComputed(() => {
      return this.savedDevices()
        .map((d) => deviceNames[d])
        .join(", ");
    });

    this.tags = ko.observableArray(urlParams.getAll("tags[]"));
    this.tagsOperation = ko.observable(urlParams.get("tagsOperation") || 1);

    this.savedTags = ko.observableArray(this.tags());
    this.savedTagsOperation = ko.observable(this.tagsOperation());


    this.tagsDirectory = new TagsDirectory(this.poll.company_id, this.statsLinkId);
    this.tagsDirectory.load();

    this.savedTagsNames = ko.pureComputed(() => {
      return this.savedTags()
        .map((t) => {
          let tag = this.tagsDirectory.getById(t);
          return tag ? tag.name : null;
        })
        .filter(Boolean)
        .join(", ");
    });

    this.havePeriodError = ko.observable(false);
    this.periodFilter = ko.observable(
      this.dateFrom === null ? "" : this.dateFrom + "-" + this.dateTo
    );
    this.periodFilter.subscribe(() => {
      let values = this.periodFilter().split("-");

      if (values.length > 1) {
        let from = moment(values[0], "DD.MM.YYYY"),
          to = moment(values[1], "DD.MM.YYYY");
        if (
          from.format() !== "Invalid date" &&
          to.format() !== "Invalid date"
        ) {
          if (from.format() > to.format()) {
            this.havePeriodError(true);
          } else {
            this.havePeriodError(false);
          }
        } else {
          this.havePeriodError(true);
        }
      } else {
        let indexOfDot = values[0].indexOf("."),
          dotsCount = (values[0].match(RegExp(".", "g")) || []).length,
          formattedDate = moment(values[0], "DD.MM.YYYY").format();

        if (values[0] in this.periodPickerRanges) {
          this.havePeriodError(false);
        } else if (
          !values[0].length ||
          (indexOfDot !== -1 &&
            indexOfDot === 2 &&
            dotsCount >= 3 &&
            formattedDate !== "Invalid date")
        ) {
          this.havePeriodError(false);
        } else {
          this.havePeriodError(true);
        }
      }
    });
    this.savedPeriodFilter = ko.observable(
      this.periodFilter().replace("-", " – ")
    );
    this.periodFilterPeriodPicker = ko.observable("");
    this.isAjaxSended = ko.observable(false);
    this.isSubmittedFilters = ko.observable(false);
    this.isFilterApplied = ko.observable(false);

    this.periodErrorStateMatcher = () => {
      if (this.periodFilterPeriodPicker()) {
        let startDate = this.periodFilterPeriodPicker()._$startInput[0].value,
          endDate = this.periodFilterPeriodPicker()._$endInput[0].value;
        let rgexp =
          /^\s*(3[01]|[12][0-9]|0?[1-9])\.(1[012]|0?[1-9])\.((?:19|20)\d{2})\s*$/g;
        let rgexpEnd =
          /^\s*(3[01]|[12][0-9]|0?[1-9])\.(1[012]|0?[1-9])\.((?:19|20)\d{2})\s*$/g;
        if (startDate && endDate) {
          return rgexp.test(startDate) && rgexpEnd.test(endDate);
        } else {
          return true;
        }
      } else {
        return true;
      }
    };

    this.getPeriodError = () => {
      return "Неверный формат";
    };

    this.filialsLoaded = ko.observable(false);
    this.hasFilials = ko.observable(false);
    this.filialsList = ko.observableArray([]);
    this.filials = ko.observableArray([]);
    this.savedFilials = ko.observableArray([]);
    this.savedFilialsNames = ko.pureComputed(() => {
      let list = this.filialsList();
      return this.savedFilials()
        .map((id) => {
          if (id == 0) return "Филиал не указан";
          let filial = list.find((f) => f.id == id);
          if (filial) return filial.name;
          return "";
        })
        .filter(Boolean)
        .join(", ");
    });

    if (!this.isAuto || this.hasOrder) {
      $.ajax({
        url: ApiUrl("poll-stats/company-filials", {
          companyId: this.poll.company_id,
          link: this.statsLinkId,
        }),
        method: "GET",
        success: (response) => {
          let items = FilialsList(response.items, {
            undefinedFilialItem: !this.isAuto && response.allFilials,
            selectableCategories: true,
          });

          this.filialsList(items);

          this.filials([
            ...urlParams.getAll("filials[]"),
            ...urlParams.getAll("filialCategories[]").map((c) => "c" + c),
          ]);

          this.savedFilials(this.filials());
          if (response.items.length) this.hasFilials(true);
          this.filialsLoaded(true);
        },
      });
    } else {
      this.filialsLoaded(true);
    }

    this.questions = [];

    this.getSearchParams = () => {
      let params = {};

      let values = this.periodFilter().split("-");

      if (values.length > 1) {
        this.dateFrom = values[0];
        this.dateTo = values[1];
      } else {
        if (values[0] in this.periodPickerRanges) {
          this.dateFrom =
            this.periodPickerRanges[values[0]][0].format("DD.MM.YYYY");
          this.dateTo =
            this.periodPickerRanges[values[0]][1].format("DD.MM.YYYY");
        } else {
          this.dateFrom = values[0] === "" ? null : values[0];
          this.dateTo = values[0] === "" ? null : values[0];
        }
      }

      if (this.dateFrom) this.dateFrom = this.dateFrom.trim();
      if (this.dateTo) this.dateTo = this.dateTo.trim();

      if (this.dateFrom && this.dateTo) {
        params = {
          ...params,
          from: this.dateFrom,
          to: this.dateTo,
        };
      }

      if (this.questionnaires()) {
        params = {
          ...params,
          questionnaires: this.questionnaires(),
        };
      }

      if (this.tags().length) {
        params = {
          ...params,
          tags: this.tags(),
          tagsOperation: this.tagsOperation(),
        };
      }

      if (this.devices().length) {
        params.devices = this.devices();
      }

      if (this.filials().length) {
        let filials = this.filials();
        let categories = filials
          .filter((i) => i[0] === "c")
          .map((i) => i.slice(1));

        params.filials = filials.filter((i) => i[0] !== "c");
        params.filialCategories = categories;
      }

      return params;
    };

    this.getSavedSearchParams = () => {
      return {
        period: this.savedPeriodFilter(),
        questionnaires: this.savedQuestionnaires(),
        devices: this.savedDevicesNames(),
        tagsOperation: this.savedTagsOperation(),
        tags: this.savedTagsNames(),
        filials: this.savedFilialsNames(),
      };
    };

    this.hasSavedFilters = ko.computed(() => {
      return (
        this.savedPeriodFilter() ||
        this.savedQuestionnaires() ||
        this.savedDevicesNames() ||
        this.savedTagsNames() ||
        this.savedFilialsNames()
      );
    });

    this.saveFilters = (params = {}) => {
      if (params.from) {
        this.savedPeriodFilter(params.from + " – " + params.to);
      } else {
        this.savedPeriodFilter("");
      }

      this.savedQuestionnaires(params.questionnaires || "");
      this.savedTags(params.tags || []);
      this.savedTagsOperation(params.tagsOperation || "1");
      this.savedDevices(params.devices || []);
      this.savedFilials(params.filials || []);
    };

    this.submitFilters = () => {
      if (!this.isAjaxSended()) {
        this.isSubmittedFilters(true);
        if (this.periodErrorStateMatcher() && !this.havePeriodError()) {
          this.isAjaxSended(true);
          this.isFilterApplied(true);

          let params = {
            id: POLL_ID,
            ...this.getSearchParams(),
          };
          history.pushState(params, "date-filter", "?" + $.param(params));

          let url = `/foquz/foquz-poll/stats-filter?link=${this.statsLinkId}`;

          this.updateCounts();

          $.post(url, params, (response) => {
            this.questions = response.data
              .filter((q) => {
                if (q.disabled && q.answersCount == 0) return false;
                return true;
              })
              //.filter(q => q.count)
              .map((q) => QuestionFactory(q, this, response.data));
            let dishesQuestion = this.questions.find((q) => {
              return q.assessmentType === 2;
            });
            this.dishesQuestion(dishesQuestion);
            this.isAjaxSended(false);
            this.isSubmittedFilters(false);
            this.isFilterApplied(false);
            this.saveFilters(params);

            this.page = 0;
            this.reloadDishes();
            setTimeout(() => {
              const $container = $(
                ".question-statistics__question-dish-ratings-statistics-table-wrapper.ps"
              );
              $container.on("scroll", () => {
                this.pagination($container);
              });
            });
          });
        }
      }
    };

    this.resetFilters = () => {
      this.periodFilter("");
      this.questionnaires("");
      this.devices([]);
      this.tags([]);
      this.tagsOperation("1");
      this.filials([]);
      this.submitFilters();
    };

    this.counts = {
      sended: ko.observable(0),
      opened: ko.observable(0),
      processed: ko.observable(0),
      done: ko.observable(0),
    };

    this.updateCounts = () => {
      $.ajax({
        url: ApiUrl("poll-stats/statuses"),
        data: {
          id: POLL_ID,
          link: this.statsLinkId,
          ...this.getSearchParams(),
        },
        success: (response) => {
          this.counts.sended(response.sended || 0);
          this.counts.opened(response.opened || 0);
          this.counts.processed(response.processed || 0);
          this.counts.done(response.done || 0);
        },
        error: (response) => {
          console.error(response.responseJSON);
        },
      });
    };

    this.updateCounts();

    this.openCommentsModal = function (question_id) {
      let question = this.questions.find((q) => q.question_id == question_id);

      this.openStatsModal("stats-comments-sidesheet", {
        withPoints: false,
        title: "Комментарии",
        question,
        urlParams: {},
      });
    };

    this.openTextAnswersModal = function (question_id) {

      let question = this.questions.find(
        (q) => q.question_id === question_id
      );

      this.openStatsModal("stats-text-sidesheet", {
        withPoints: question.type == 3 && question.points,
        title: `Текстовый ответ`,
        question: question,
      });
    };

    this.openGalleryModal = function (question) {
      this.openStatsModal("stats-gallery-sidesheet", {
        title: question.name,
        question: question,
      });
    };

    this.openClientsModal = function (
      question_id,
      title,
      field,
      value,
      additional = undefined
    ) {
      let question = this.questions.find((q) => q.question_id == question_id);
      if (additional) {
        this.openStatsModal("stats-dishes-sidesheet", {
          withPoints: false,
          title,
          question,
          urlParams: {
            rating: value,
            dish_id: additional,
          },
        });
      } else {
        if(value == 'skipped'){
          this.openStatsModal("stats-clients-sidesheet", {
            withPoints: false,
            title,
            question,
            urlParams: { skipped: 1 },
          });
        } else {
          this.openStatsModal("stats-clients-sidesheet", {
            withPoints: false,
            title,
            question,
            urlParams: {
              field,
              value,
            },
          });
        }
      }
    };

    this.openGalleryClientsModal = function (
      questionId,
      title,
      field,
      mediaId,
      value,
      additional = undefined
    ) {
      let question = this.questions.find((q) => q.question_id == questionId);

      this.openStatsModal("stats-clients-sidesheet", {
        withPoints: false,
        title,
        question,
        urlParams: {
          field,
          value,
          media_id: mediaId,
        },
      });
    };

    this.openAddressesModal = function (question_id) {
      let question = this.questions.find(
        (q) => q.question_id === question_id
      );

      this.openStatsModal("stats-addresses-sidesheet", {
        withPoints: false,
        title: `Адреса`,
        question: question,
      });
    };

    this.openProfilesModal = function (question) {
      this.openStatsModal("stats-profiles-sidesheet", {
        withPoints: false,
        title: `Анкета`,
        question: question,
      });
    };

    this.openFilesModal = function (question_id) {
      let question = this.questions.find(
        (q) => q.question_id === question_id
      );

      this.openStatsModal("stats-files-sidesheet", {
        withPoints: false,
        title: `Загрузка файлов`,
        question: question,
      });
    };

    this.getRatingStatisticsAverageValue = function (rating) {
      const pointsCount = rating.reduce((count, r) => count + r, 0);
      if (pointsCount === 0) return 0;

      const ratingSize = rating.length;

      const sum = rating.reduce((total, count, index) => {
        return total + count * (ratingSize - index);
      }, 0);

      return sum / pointsCount;
    };

    this.getRatingStatisticsAverageValueAsc = function (rating) {
      const count = rating.reduce((count, r) => count + r, 0);
      if (count === 0) return 0;
      const sum =
        rating[0] * 1 +
        rating[1] * 2 +
        rating[2] * 3 +
        rating[3] * 4 +
        rating[4] * 5;

      return sum / count;
    };

    this.getQuestionClarifyingQuestionVariantStatisticsVariants = function (
      question
    ) {
      if (question.isSelfAnswer) {
        return [
          ...question.variants,
          {
            id: 0,
            question: "Свой вариант",
            legendTableRowClass:
              "question-statistics__question-statistics-legend-table-custom-row",
          },
        ];
      } else {
        return [...question.variants];
      }
    };

    this.getQuestionVariantStatisticsVariants = function (question) {
      if (question.isSelfAnswer) {
        return [
          ...question.variants,
          {
            id: 0,
            question: "Свой вариант",
            legendTableRowClass:
              "question-statistics__question-statistics-legend-table-custom-row",
          },
        ];
      } else {
        return [...question.variants];
      }
    };

    this.getQuestionVariantStatisticsProfilesValue = function (question) {
      const value = question.properties.map((_, index) => {
        return question.statistics.answers.filter((a) => {
          return new TextAnswer(a.properties[index]).hasValue();
        }).length;
      });

      value.push(
        question.statistics.answers.filter((a) =>
          a.properties.every((p) => !new TextAnswer(p).hasValue())
        ).length
      );

      return value;
    };

    this.page = 0;
    this.isStart = ko.observable(true);
    this.isScrolled = ko.observable(false);

    this.dishesQuestion = ko.observable(null);

    this.deleted = ko.observable(STATS_IS_EMPTY);
    this.hasDeleted = ko.observable(false);
    this.deleted.subscribe((v) => {
      if (v) {
        this.hasDeleted(true);
        this.averageTime("00:00");
        this.updateCounts();
      }
    });

    this.clearStats = () => {
      var that = this;
      this.confirm({
        text: 'При очистке статистики опроса все собранные данные будут удалены <span class="f-color-danger bold">без возможности восстановления.</span>',
        title: "Очистить статистику",
        confirmText: "Очистить",
        cancelText: "Отменить",
        mode: "danger",
      }).then(() => {
        $.ajax({
          method: "POST",
          url: `${APIConfig.baseApiUrlPath}poll/clear-stats?id=${POLL_ID}&access-token=${APIConfig.apiKey}`,
          success: (data) => {
            this.deleted(true);

            $(".question-statistics__summary-item-value").text("0");
          },
        });
      });
    };

    this.sortValue.subscribe((newValue) => {
      this.page = 0;
      this.dishes([]);
      this.isStart(true);
      this.isScrolled(false);

      this.reloadDishes();
    });

    this.pagination = ($container) => {
      let $rows = $(
        ".question-statistics__question-dish-ratings-statistics-table-row"
      );
      if (
        this.sortValue() === "all" &&
        $container.scrollTop() > 0 &&
        $container.outerHeight() + $container.scrollTop() >=
        $container.prop("scrollHeight") &&
        $rows.length >= this.page * 10
      ) {
        this.page = this.page + 1;
        this.isScrolled(true);
        this.reloadDishes();
      }
    };

    this.loadDishes = () => {
      this.page = this.page + 1;
      this.isScrolled(true);
      this.reloadDishes();
    };

    this.noMore = ko.observable(false);

    this.reloadDishes = () => {
      let dishesQuestion = this.dishesQuestion();
      this.isStart(false);

      if (!dishesQuestion) return;

      $("#main-loader").show();

      let params = this.getSearchParams();
      params.questionId = dishesQuestion.question_id;
      params.q = this.searchTerm();
      params.sort = this.sortValue();
      params.page = this.page;

      let url = `/foquz/ajax/search-score?` + $.param(params);

      if (this.page == 1) this.noMore(false);

      $.post(url, {}, (response) => {
        if (response.data.lastPage) this.noMore(true);

        if (!this.isScrolled()) {
          this.dishes([]);
        }

        let dishes = response.data.ratings || [];

        ko.utils.arrayForEach(dishes, (model) => {
          this.dishes.push(model);
        });
        this.isStart(true);
        this.isScrolled(false);
        $("#main-loader").hide();
      });
    };

    var self = this;
    $("body").on(
      "keydown",
      ".question-statistics__question-dish-ratings-statistics-search-form-control",
      function (e) {
        if (e.which == 13) {
          e.preventDefault();
          self.clearSearchInput();
        }
      }
    );

    this.clearSearchInput = () => {
      self.page = 0;
      self.reloadDishes();
    };

    this.searchDishRatings = function (dishRatings, searchTerm) {
      return dishRatings.filter((dishRating) =>
        dishRating.dish.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    };

    this.formatQuestionDishRatingsStatisticsTableVoteCount = function (
      voteCount
    ) {
      if (voteCount < 10) {
        return "<10";
      } else if (voteCount < 100) {
        if (voteCount % 10 === 0) {
          return voteCount;
        } else {
          return ">" + (voteCount - (voteCount % 10));
        }
      } else {
        if (voteCount % 100 === 0) {
          return voteCount;
        } else {
          return ">" + (voteCount - (voteCount % 100));
        }
      }
    };

    this.getRatingStatisticsPopoverContent = function (rating) {
      return `
                    <table class="table foq-table question-statistics__rating-statistics-dropdown-menu-table">
                        <tbody>
                            <!-- ko foreach: value -->
                                <tr>
                                    <td>
                                        <div data-bind="component: {
                                            name: 'question-statistics-rating',
                                            params: { value: 5 - $index(), starClass: 'question-statistics__rating-statistics-dropdown-menu-table-rating-star' }
                                        }" class="question-statistics__rating-statistics-dropdown-menu-table-rating">
                                        </div>
                                    </td>
                                    <td data-bind="text: $data"></td>
                                    <td class="question-statistics__rating-statistics-dropdown-menu-table-percentage-cell"
                                        data-bind="text: $component.formatPercentage($data / $component.voteCount())">
                                    </td>
                                </tr>
                            <!-- /ko -->
                        </tbody>
                    </table>
                `;
    };

    this.printer = Printer();

    this.printPage = function () {
      let app = $(".s-app").clone(true);
      this.printer.print([app.get(0)]);
    };

    this.shareLink = function () {
      this.modals.push({
        name: "share-link-modal",
        params: {
          link: POLL.statsLink,
          pollId: POLL_ID,
          mask: true,
        },
      });
    };

    this.getDonorIndex = (donorId) => {
      const index = ko
        .toJS(this.questions)
        .findIndex((q) => q.question_id === donorId);
      if (index > -1) return index + 1;
      return null;
    };

    this.scrollToQuestion = (questionId) => {
      const el = document.querySelector(
        `.question-statistics__question[data-id="${questionId}"]`
      );
      if (!el) return;
      el.scrollIntoView({ behavior: "smooth" });
    };

    if (this.filialsLoaded()) this.submitFilters();
    else {
      this.filialsLoaded.subscribe((v) => {
        this.submitFilters();
      });
    }
  };

  const viewModel = new ViewModel();

  const $content = $(".question-statistics__content");

  viewModel.initializing = ko.observable(true);

  viewModel.onInit = function () {
    viewModel.initializing(false);
    setTimeout(() => {
      const $container = $(
        ".question-statistics__question-dish-ratings-statistics-table-wrapper.ps"
      );

      $container.on("scroll", () => {
        viewModel.pagination($container);
      });
    });

    $(".question-statistics__variant-statistics-legend, .rating-wrapper").each(
      (i, e) => {
        let $table = $(e).find("table");
        let $wrapper = $('<div data-bind="utilNativeScrollbar">');
        $table.wrap($wrapper);
        ko.applyBindingsToNode($table.parent().get(0));
      }
    );
  };

  ko.applyBindings(viewModel, $content.get()[0]);
});
