import { reduce as _reduce, throttle as _throttle } from 'lodash';

import "./utils/slider";
import { formatProperties } from "./utils/format-properties";

import "./modals/gallery-clients";

import "./types/variants";

import "./components/rating";

import { QuestionFactory } from "./question-types";
import "Modals/points-stats-modal-page";
import "Dialogs/points-stats-sidesheet";
import "Dialogs/share-link-dialog";
import "Dialogs/share-widget-dialog";

import "Components/print-footer";
import "Components/tags-filter";
import { Printer } from "Utils/print";

import { TagsDirectory } from "Utils/directory/tags";
import { toMinutesTimeString } from "Utils/date/timestring";

import { PointsModel } from "Models/points";

import { ApiUrl } from "Utils/url/api-url";
import "Components/stats-filters-value";

import "Dialogs/stats/";
import { TextAnswer } from "Models/text-answer";
import { PollPublishEvent, PollStatsUpdateEvent } from "Utils/events/poll";

import { DialogsModule } from "Utils/dialogs-module";

import "./style.less";
import { FilialsList } from "../../../utils/filials-list";

import '@/presentation/components/fc-highchart';
import { initCurrentCompany } from "@/api/company/"

Highcharts.setOptions({
  chart: {
    style: {
      fontFamily: "Roboto, Arial, serif",
    },
  },
});

ko.bindingHandlers.questionStatisticsImage = {
  init: function (element, valueAccessor) {
    const { urls, index } = valueAccessor();

    $(element).on("click", () => {
      $.fancybox.open(
        urls.map((url) => ({
          src: url,
        })),
        {
          index,
          loop: false,
          buttons: ["rotate", "zoom", "slideShow", "close"],
        }
      );
    });
  },
};

ko.bindingHandlers.questionStatisticsVideo = {
  init: function (element, valueAccessor) {
    const { urls, index } = valueAccessor();

    $(element).on("click", () => {
      $.fancybox.open(
        urls.map((url) => ({
          src: url,
        })),
        {
          index,
          loop: false,
          buttons: ["close"],
        }
      );
    });
  },
};

ko.bindingHandlers.questionStatisticsRatingStatisticsPopover = {
  init: function (
    element,
    valueAccessor,
    allBindings,
    viewMode,
    bindingContext
  ) {
    $(element).popover({
      html: true,
      sanitize: false,
      placement: "bottom",
      boundary: document,
      template: `
                    <div class="popover question-statistics__rating-statisitcs-popover" role="tooltip">
                        <div class="popover-body"></div>
                    </div>
                `,
      content: `
                    <table data-bind="component: {
                        name: 'question-statistics-rating-statistics-table',
                        params: {
                            value: value,
                            variantsSkips: ${bindingContext.variantsSkips ? 'variantsSkips' : '0'},
                            onRowClick: function (value) {
                                onRowClick(value);
                            },
                            max: ${allBindings.get("max")}
                        }
                    }">
                    </table>
                `,
    });

    $(element).on("inserted.bs.popover", () => {
      const content = $($(element).data("bs.popover").tip)
        .find(".popover-body")
        .children()[0];
      const context = bindingContext.extend({
        onRowClick: (value) => {
          if (
            allBindings.has(
              "questionStatisticsRatingStatisticsPopoverItemClick"
            )
          ) {
            allBindings.get(
              "questionStatisticsRatingStatisticsPopoverItemClick"
            )(value);
          }
        },
      });
      ko.applyBindings(context, content);
    });

    const bodyClickHandler = function (e) {
      if (
        $(e.target) !== element &&
        $(e.target).parents(".popover.in").length === 0
      ) {
        $(element).popover("hide");
      }
    };

    $(element).bind("shown.bs.popover", () => {
      $("body").bind("click", bodyClickHandler);
    });

    $(element).on("hide.bs.popover", () => {
      $("body").unbind("click", bodyClickHandler);
    });
  },
};

$(function () {
  const ViewModel = function () {
    const filterObj = (question, action, questions, variants, active) => {
      const obj = {
        value: ko.observable(question),
        action: ko.observable(action),
        answers: ko.observableArray(variants || []),
        checked: ko.observable(active),
        questions: questions,
        variants: ko.observableArray([]),
      }
    
      obj.questions.find(q => q.id == question).detail_answers.forEach(element => {
        const variant = {
          id: element.id || 'self',
          text: ko.observable(element.variant)
        }
        obj.variants.push(variant)
      });
      obj.checked.subscribe((v) => {
        this.submitVariantsFilterSettings()
      });
    
      return obj
    }

    this.variantFilterActions = ['Выбрал вариант', 'Не выбрал вариант', 'Пропустил вопрос', 'Затруднился ответить']

    this.updatedVariantsFilters = []

    this.submitVariantsFilterSettings = () => {
      const formData = new FormData
      this.variantsFilters().forEach((item, index) => {
        formData.append(`questions[${index}][question_id]`, item.value())
        formData.append(`questions[${index}][action]`, item.action())
        formData.append(`questions[${index}][active]`, item.checked() ? 1 : 0)
        if (item.answers().length) {
          item.answers().forEach((ansv, i) => {
            formData.append(`questions[${index}][variants][${i}]`, ansv)
          })
        }
      })
  
      const self = this
      $.ajax({
        url : `/foquz/api/poll/save-stat-filter-settings?poll_id=${self.poll.id}&access-token=${window.APIConfig.apiKey}`,
        data : formData,
        type : "POST",
        processData: false,
        contentType: false,
        success : function (res) {
            self.updatedVariantsFilters = res.filter_settings;
        }
    });
    }

    PageModel(this);
    DialogsModule(this);

    this.modals = ko.observableArray([]);
    this.blocked = !CURRENT_USER || CURRENT_USER.blockActions;

    this.poll = POLL;    

    initCurrentCompany({
      id: this.poll.company_id
    })

    this.timeToPass = null;
    if (this.poll.time_to_pass) {
      this.timeToPass = toMinutesTimeString(this.poll.time_to_pass);
    }

    this.isPublished = ko.observable(this.poll.is_published);
    if (!this.isPublished()) {
      PollPublishEvent.on((params) => {
        if (params.id == this.poll.id) {
          this.isPublished(true);
        }
      });
    }

    this.isWatcher = !CURRENT_USER || CURRENT_USER.watcher;
    console.log('isWatcher', this.isWatcher);

    this.pointsModel = new PointsModel(this.poll);
    this.isAuto = POLL_IS_AUTO;
    this.hasOrder = POLL_IS_AUTO && (POLL_TRIGGER == 1 || POLL_TRIGGER == 2);

    this.dialogs = ko.observable(null);

    // open modal dialog
    const openStatsModal = (name, params) => {
      console.log('open modal', params);
      this.openSidesheet({
        name,
        params: {
          poll: {
            id: POLL_ID,
            isAuto: this.isAuto,
            hasOrder: this.hasOrder,
          },
          searchParams: this.getSearchParams(),
          ...params,
        },
      });
    };
    this.openStatsModal = _throttle(openStatsModal, 1000, { 'trailing': false });

    this.openDynamicsModal = (questionParams) => {
      this.openSidesheet({
        name: "stats-dynamics-sidesheet",
        params: {
          searchParams: this.getSearchParams(),
          question: questionParams,
        },
      });
    };

    this.openGalleryDynamicsModal = (question) => {
      this.openDynamicsModal({
        id: question.question_id,
        type: "gallery",
      });
    };

    // open variant modal dialog
    this.openClientsModal = function (
      question_id,
      title,
      field,
      value,
      additional = undefined
    ) {
      let question = this.questions().find((q) => q.question_id == question_id);

      if (additional) {
        this.openStatsModal("stats-dishes-sidesheet", {
          withPoints: false,
          title,
          question,
          urlParams: {
            rating: value,
            dish_id: additional,
          },
        });
      } else {
        if(value == 'skipped'){
          this.openStatsModal("stats-clients-sidesheet", {
            withPoints: false,
            title,
            question,
            urlParams: { skipped: 1 },
          });
        } else {
          this.openStatsModal("stats-clients-sidesheet", {
            withPoints: false,
            title,
            question,
            urlParams: {
              field,
              value,
            },
          });
        }
      }
    };

    this.openCommentsModal = function (question_id) {
      let question = this.questions().find((q) => q.question_id == question_id);

      this.openStatsModal("stats-comments-sidesheet", {
        withPoints: false,
        title: "Комментарии",
        question,
        urlParams: {},
      });
    };

    this.openGalleryClientsModal = function (
      questionId,
      title,
      field,
      mediaId,
      value,
      additional = undefined
    ) {
      let question = this.questions().find((q) => q.question_id == questionId);

      this.openStatsModal("stats-clients-sidesheet", {
        withPoints: false,
        title,
        question,
        urlParams: {
          field,
          value,
          media_id: mediaId,
        },
      });
    };

    this.openPointsModal = () => {
      this.openSidesheet({
        name: "points-stats-sidesheet",
        params: {
          poll: this.poll,
          filters: this.hasSavedFilters() ? this.getSavedSearchParams() : null,
        },
      });
    };

    this.openVariantsFilter = function () {
      this.openSidesheet({
        name: "variants-filter-sidesheet",
        params: {
          poll: {
            id: POLL_ID,
            isAuto: this.isAuto,
            hasOrder: this.hasOrder,
          },
          searchParams: this.getSearchParams(),
          questions: this.questions().filter(item => item.type == 1),
          updatedSettings: this.updatedVariantsFilters,
          callback: (settings) => {
            this.updateVariantsFilterSettings(settings)
          }
        },
      });
    }

    this.dishes = ko.observableArray([]);
    this.searchTerm = ko.observable("");
    this.sortValue = ko.observable("all");

    this.name = ko.observable(POLL_NAME);
    this.isNameEditing = ko.observable(false);

    this.firstAnswerDate = null;
    this.firstAnswerTime = null;

    if (FIRST_ANSWER) {
      let date = moment(FIRST_ANSWER, "DD.MM.YYYY HH:mm");
      this.firstAnswerDate = date.format("DD.MM.YYYY");
      this.firstAnswerTime = date.format("HH:mm");
    }

    this.lastAnswerDate = null;
    this.lastAnswerTime = null;

    if (LAST_ANSWER) {
      let date = moment(LAST_ANSWER, "DD.MM.YYYY HH:mm");
      this.lastAnswerDate = date.format("DD.MM.YYYY");
      this.lastAnswerTime = date.format("HH:mm");
    }

    this.averageTime = ko.observable(AVG_TIME_PER_ANSWER || "00:00");

    this.periodPickerRanges = {
      Сегодня: [moment(), moment()],
      "С начала недели": [moment().startOf("isoWeek"), moment()],
      "Прошлая неделя": [
        moment().subtract(1, "weeks").startOf("isoWeek"),
        moment().subtract(1, "weeks").endOf("isoWeek"),
      ],
      "Последние 7 дней": [moment().subtract(6, "days"), moment()],
      "Текущий месяц": [moment().startOf("month"), moment()],
      "Прошлый месяц": [
        moment().subtract(1, "month").startOf("month"),
        moment().subtract(1, "month").endOf("month"),
      ],
      "Последний месяц": [moment().subtract(29, "days"), moment()],
    };

    let queryString = window.location.search;
    let urlParams = new URLSearchParams(queryString);
    this.dateFrom =
      urlParams.get("from") === null ? null : urlParams.get("from");
    this.dateTo = urlParams.get("to") === null ? null : urlParams.get("to");

    this.questionnaires = ko.observable(urlParams.get("questionnaires") || "");
    this.savedQuestionnaires = ko.observable(this.questionnaires());

    this.devices = ko.observableArray(urlParams.getAll("devices[]") || []);
    this.savedDevices = ko.observableArray(this.devices());
    let deviceNames = {
      desktop: "Десктоп",
      tablet: "Планшет",
      mobile: "Смартфон",
    };
    this.savedDevicesNames = ko.pureComputed(() => {
      return this.savedDevices()
        .map((d) => deviceNames[d])
        .join(", ");
    });

    this.tags = ko.observableArray(urlParams.getAll("tags[]"));
    this.tagsOperation = ko.observable(urlParams.get("tagsOperation") || "1");

    this.savedTags = ko.observableArray(this.tags());
    this.savedTagsOperation = ko.observable(this.tagsOperation());

    this.tagsDirectory = new TagsDirectory();
    this.tagsDirectory.load();

    this.savedTagsNames = ko.pureComputed(() => {
      return this.savedTags()
        .map((t) => {
          let tag = this.tagsDirectory.getById(t);
          return tag ? tag.name : null;
        })
        .filter(Boolean)
        .join(", ");
    });

    this.havePeriodError = ko.observable(false);
    this.periodFilter = ko.observable(
      this.dateFrom === null ? "" : this.dateFrom + "-" + this.dateTo
    );
    this.periodFilter.subscribe(() => {
      let values = this.periodFilter().split("-");

      if (values.length > 1) {
        let from = moment(values[0], "DD.MM.YYYY"),
          to = moment(values[1], "DD.MM.YYYY");
        if (
          from.format() !== "Invalid date" &&
          to.format() !== "Invalid date"
        ) {
          if (from.format() > to.format()) {
            this.havePeriodError(true);
          } else {
            this.havePeriodError(false);
          }
        } else {
          this.havePeriodError(true);
        }
      } else {
        let indexOfDot = values[0].indexOf("."),
          dotsCount = (values[0].match(RegExp(".", "g")) || []).length,
          formattedDate = moment(values[0], "DD.MM.YYYY").format();

        if (values[0] in this.periodPickerRanges) {
          this.havePeriodError(false);
        } else if (
          !values[0].length ||
          (indexOfDot !== -1 &&
            indexOfDot === 2 &&
            dotsCount >= 3 &&
            formattedDate !== "Invalid date")
        ) {
          this.havePeriodError(false);
        } else {
          this.havePeriodError(true);
        }
      }
    });
    this.savedPeriodFilter = ko.observable(
      this.periodFilter().replace("-", " – ")
    );
    this.periodFilterPeriodPicker = ko.observable("");
    this.isAjaxSended = ko.observable(false);
    this.isSubmittedFilters = ko.observable(false);
    this.isFilterApplied = ko.observable(true);

    this.loading = ko.observable(true);

    this.periodErrorStateMatcher = () => {
      if (this.periodFilterPeriodPicker()) {
        let startDate = this.periodFilterPeriodPicker()._$startInput[0].value,
          endDate = this.periodFilterPeriodPicker()._$endInput[0].value;
        let rgexp =
          /^\s*(3[01]|[12][0-9]|0?[1-9])\.(1[012]|0?[1-9])\.((?:19|20)\d{2})\s*$/g;
        let rgexpEnd =
          /^\s*(3[01]|[12][0-9]|0?[1-9])\.(1[012]|0?[1-9])\.((?:19|20)\d{2})\s*$/g;
        if (startDate && endDate) {
          return rgexp.test(startDate) && rgexpEnd.test(endDate);
        } else {
          return true;
        }
      } else {
        return true;
      }
    };

    this.getPeriodError = () => {
      return "Неверный формат";
    };

    this.filialsLoaded = ko.observable(false);

    this.hasFilials = ko.observable(false);
    this.filialsList = ko.observableArray([]);
    this.filials = ko.observableArray([]);
    this.savedFilials = ko.observableArray([]);
    this.savedFilialsNames = ko.pureComputed(() => {
      let list = this.filialsList();
      return this.savedFilials()
        .map((id) => {
          if (id == 0) return "Филиал не указан";
          let filial = list.find((f) => f.id == id);
          if (filial) return filial.name;
          return "";
        })
        .filter(Boolean)
        .join(", ");
    });

    if (!this.isAuto || this.hasOrder) {
      $.ajax({
        url: ApiUrl("poll-stats/company-filials", {
          companyId: this.poll.company_id,
        }),
        method: "GET",
        success: (response) => {
          let items = FilialsList(response.items, {
            undefinedFilialItem: !this.isAuto && response.allFilials,
            selectableCategories: true,
          });

          this.filialsList(items);

          this.filials([
            ...urlParams.getAll("filials[]"),
            ...urlParams.getAll("filialCategories[]").map((c) => "c" + c),
          ]);
          this.savedFilials(this.filials());
          if (response.items.length) this.hasFilials(true);
          this.filialsLoaded(true);
        },
      });
    } else {
      this.filialsLoaded(true);
    }

    this.daysTrigger = ko.observable(POLL_TRIGGER === 3);

    // this.questions = QUESTIONS.map((q) => QuestionFactory(q, this));
    this.questions = ko.observableArray([]);

    this.getSearchParams = () => {
      let params = {};

      let values = this.periodFilter().split("-");

      if (values.length > 1) {
        this.dateFrom = values[0];
        this.dateTo = values[1];
      } else {
        if (values[0] in this.periodPickerRanges) {
          this.dateFrom =
            this.periodPickerRanges[values[0]][0].format("DD.MM.YYYY");
          this.dateTo =
            this.periodPickerRanges[values[0]][1].format("DD.MM.YYYY");
        } else {
          this.dateFrom = values[0] === "" ? null : values[0];
          this.dateTo = values[0] === "" ? null : values[0];
        }
      }

      if (this.dateFrom) this.dateFrom = this.dateFrom.trim();
      if (this.dateTo) this.dateTo = this.dateTo.trim();

      if (this.dateFrom && this.dateTo) {
        params = {
          ...params,
          from: this.dateFrom,
          to: this.dateTo,
        };
      }

      if (this.questionnaires()) {
        params = {
          ...params,
          questionnaires: this.questionnaires(),
        };
      }

      if (this.tags().length) {
        params = {
          ...params,
          tags: this.tags(),
          tagsOperation: this.tagsOperation(),
        };
      }

      if (this.devices().length) {
        params.devices = this.devices();
      }

      if (this.filials().length) {
        let filials = this.filials();
        let categories = filials
          .filter((i) => i[0] === "c")
          .map((i) => i.slice(1));

        params.filials = filials.filter((i) => i[0] !== "c");
        params.filialCategories = categories;
      }

      return params;
    };

    this.getSavedSearchParams = () => {
      return {
        period: this.savedPeriodFilter(),
        questionnaires: this.savedQuestionnaires(),
        devices: this.savedDevicesNames(),
        tagsOperation: this.savedTagsOperation(),
        tags: this.savedTagsNames(),
        filials: this.savedFilialsNames(),
      };
    };

    this.hasSavedFilters = ko.computed(() => {
      return (
        this.savedPeriodFilter() ||
        this.savedQuestionnaires() ||
        this.savedDevicesNames() ||
        this.savedTagsNames() ||
        this.savedFilialsNames()
      );
    });

    this.saveFilters = (params = {}) => {
      if (params.from) {
        this.savedPeriodFilter(params.from + " – " + params.to);
      } else {
        this.savedPeriodFilter("");
      }

      this.savedQuestionnaires(params.questionnaires || "");
      this.savedTags(params.tags || []);
      this.savedTagsOperation(params.tagsOperation || "1");
      this.savedDevices(params.devices || []);
      this.savedFilials(params.filials || []);
    };

    this.dishesQuestion = ko.observable(null);

    this.addQuestion = (question) => {
      return new Promise((res) => {
        this.questions.push(question);
        setTimeout(() => {
          res();
        }, 1);
      });
    };

    this.addQuestions = (questions) => {
      this.questions.removeAll();

      let tick = (index) => {
        let question = questions[index];
        if (question) {
          this.addQuestion(questions[index]).then(() => {
            tick(index + 1);
          });
        }
      };

      tick(0);
    };

    this.submitFilters = () => {
      if (!this.isAjaxSended()) {
        this.isSubmittedFilters(true);
        if (this.periodErrorStateMatcher() && !this.havePeriodError()) {
          this.isAjaxSended(true);
          this.isFilterApplied(true);

          let params = {
            id: POLL_ID,
            ...this.getSearchParams(),
          };

          history.pushState(params, "date-filter", "?" + $.param(params));

          let url = "/foquz/foquz-poll/stats-filter";

          this.updateCounts();

          $.post(url, params, (response) => {
            let questions = response.data
              .filter(q => {
                if (q.disabled && q.answersCount == 0) return false;
                return true;
              })
              //.filter((q) => q.count)
              .map((q) => QuestionFactory(q, this, response.data));
            //this.questions(questions);
            this.addQuestions(questions);

            this.isAjaxSended(false);
            this.isSubmittedFilters(false);
            this.isFilterApplied(false);

            this.page = 0;

            setTimeout(() => {
              this.dishesQuestion(questions.find((q) => q.assessmentType == 2));
              this.saveFilters(params);
              this.reloadDishes();
              const $container = $(
                ".question-statistics__question-dish-ratings-statistics-table-wrapper.ps"
              );

              $container.on("scroll", () => {
                this.pagination($container);
              });
            });
          });
        }
      }
    };

    this.resetFilters = () => {
      this.periodFilter("");
      this.questionnaires("");
      this.devices([]);
      this.tags([]);
      this.tagsOperation("1");
      this.filials([]);
      this.submitFilters();
    };

    this.counts = {
      sended: ko.observable(0),
      opened: ko.observable(0),
      processed: ko.observable(0),
      done: ko.observable(0),
    };

    this.updateCounts = () => {
      $.ajax({
        url: ApiUrl("poll-stats/statuses"),
        data: {
          id: POLL_ID,
          ...this.getSearchParams(),
        },
        success: (response) => {
          this.counts.sended(response.sended || 0);
          this.counts.opened(response.opened || 0);
          this.counts.processed(response.processed || 0);
          this.counts.done(response.done || 0);
        },
        error: (response) => {
          console.error(response.responseJSON);
        },
      });
    };

    this.updateCounts();

    this.openTextAnswersModal = function (question_id) {
      let question = this.questions().find(
        (q) => q.question_id === question_id
      );

      this.openStatsModal("stats-text-sidesheet", {
        withPoints: question.type == 3 && question.points,
        title: `Текстовый ответ`,
        question: question,
      });
    };

    this.openGalleryModal = function (question) {
      this.openStatsModal("stats-gallery-sidesheet", {
        title: question.name,
        question: question,
      });
    };

    this.openAddressesModal = function (question_id) {
      let question = this.questions().find(
        (q) => q.question_id === question_id
      );

      this.openStatsModal("stats-addresses-sidesheet", {
        withPoints: false,
        title: `Адреса`,
        question: question,
      });
    };

    this.openProfilesModal = function (question) {
      this.openStatsModal("stats-profiles-sidesheet", {
        withPoints: false,
        title: `Анкета`,
        question: question,
      });
    };

    this.openFilesModal = function (question_id) {
      let question = this.questions().find(
        (q) => q.question_id === question_id
      );

      this.openStatsModal("stats-files-sidesheet", {
        withPoints: false,
        title: `Загрузка файлов`,
        question: question,
      });
    };

    this.getRatingStatisticsAverageValue = function (rating) {
      const pointsCount = _reduce(
        rating,
        (count, r, k) => {
          if (k === "Пропуск варианта") {
            return count;
          }
          return count + r;
        },
        0,
      );
      if (pointsCount === 0) return 0;

      const ratingSize = Object.keys(rating).length;

      const sum = _reduce(
        rating,
        (total, count, index) => {
          if (index === "Пропуск варианта") {
            return total;
          }
          return total + count * (ratingSize - index);
        },
        0,
      );

      return sum / pointsCount;
    };

    this.getRatingStatisticsAverageValueAsc = function (rating) {
      const count = _reduce(
        rating,
        (count, r, k) => {
          if (k === "Пропуск варианта") {
            return count;
          }
          return count + r;
        },
        0,
      );
      if (count === 0) return 0;
      const sum =
        rating[0] * 1 +
        rating[1] * 2 +
        rating[2] * 3 +
        rating[3] * 4 +
        rating[4] * 5;

      return sum / count;
    };

    this.getQuestionClarifyingQuestionVariantStatisticsVariants = function (
      question
    ) {
      if (question.isSelfAnswer) {
        return [
          ...question.variants,
          {
            id: 0,
            question: "Свой вариант",
            legendTableRowClass:
              "question-statistics__question-statistics-legend-table-custom-row",
          },
        ];
      } else {
        return [...question.variants];
      }
    };

    this.getQuestionVariantStatisticsVariants = function (question) {
      if (question.isSelfAnswer) {
        return [
          ...question.variants,
          {
            id: 0,
            question: "Свой вариант",
            legendTableRowClass:
              "question-statistics__question-statistics-legend-table-custom-row",
          },
        ];
      } else {
        return [...question.variants];
      }
    };

    this.getQuestionVariantStatisticsProfilesValue = function (question) {
      const value = question.properties.map((_, index) => {
        return question.statistics.answers.filter((a) => {
          return new TextAnswer(a.properties[index]).hasValue();
        }).length;
      });

      value.push(
        question.statistics.answers.filter((a) =>
          a.properties.every((p) => !new TextAnswer(p).hasValue())
        ).length
      );

      return value;
    };

    this.page = 0;
    this.isStart = ko.observable(true);
    this.isScrolled = ko.observable(false);

    this.deleted = ko.observable(STATS_IS_EMPTY);
    this.hasDeleted = ko.observable(false);
    this.deleted.subscribe((v) => {
      if (v) {
        this.hasDeleted(true);
        this.averageTime("00:00");
        this.updateCounts();
      }
    });

    this.clearStats = () => {
      var that = this;
      this.confirm({
        text: 'При очистке статистики опроса все собранные данные будут удалены <span class="f-color-danger bold">без возможности восстановления.</span>',
        title: "Очистить статистику",
        confirm: "Очистить",
        mode: "danger",
      }).then(() => {
        $.ajax({
          method: "POST",
          url: `${APIConfig.baseApiUrlPath}poll/clear-stats?id=${POLL_ID}&access-token=${APIConfig.apiKey}`,
          success: (data) => {
            this.deleted(true);

            $(".question-statistics__summary-item-value").text("0");
            PollStatsUpdateEvent.emit({ id: POLL_ID });
          },
        });
      });
    };

    this.sortValue.subscribe((newValue) => {
      this.page = 0;
      this.dishes([]);
      this.isStart(true);
      this.isScrolled(false);

      this.reloadDishes();
    });

    this.pagination = ($container) => {
      let $rows = $(
        ".question-statistics__question-dish-ratings-statistics-table-row"
      );
      if (
        this.sortValue() === "all" &&
        $container.scrollTop() > 0 &&
        $container.outerHeight() + $container.scrollTop() >=
          $container.prop("scrollHeight") &&
        $rows.length >= this.page * 10
      ) {
        this.page = this.page + 1;
        this.isScrolled(true);
        this.reloadDishes();
      }
    };

    this.noMore = ko.observable(false);

    this.reloadDishes = () => {
      let dishesQuestion = this.dishesQuestion();
      this.isStart(false);

      if (!dishesQuestion) return;

      $("#main-loader").show();

      if (this.page == 1) this.noMore(false);

      let params = this.getSearchParams();
      params.questionId = dishesQuestion.question_id;
      params.q = this.searchTerm();
      params.sort = this.sortValue();
      params.page = this.page;

      let url = `/foquz/ajax/search-score?` + $.param(params);

      $.post(url, {}, (response) => {
        if (!this.isScrolled()) {
          this.dishes([]);
        }

        let dishes = response.data.ratings || [];

        ko.utils.arrayForEach(dishes, (model) => {
          this.dishes.push(model);
        });
        this.isStart(true);
        this.isScrolled(false);
        $("#main-loader").hide();
      });
    };

    var self = this;
    $("body").on(
      "keydown",
      ".question-statistics__question-dish-ratings-statistics-search-form-control",
      function (e) {
        if (e.which == 13) {
          e.preventDefault();
          self.clearSearchInput();
        }
      }
    );

    this.clearSearchInput = () => {
      self.page = 0;
      self.reloadDishes();
    };

    this.searchDishRatings = function (dishRatings, searchTerm) {
      return dishRatings.filter((dishRating) =>
        dishRating.dish.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    };

    this.formatQuestionDishRatingsStatisticsTableVoteCount = function (
      voteCount
    ) {
      if (voteCount < 10) {
        return "<10";
      } else if (voteCount < 100) {
        if (voteCount % 10 === 0) {
          return voteCount;
        } else {
          return ">" + (voteCount - (voteCount % 10));
        }
      } else {
        if (voteCount % 100 === 0) {
          return voteCount;
        } else {
          return ">" + (voteCount - (voteCount % 100));
        }
      }
    };

    this.getRatingStatisticsPopoverContent = function (rating) {
      return `
                    <table class="table foq-table question-statistics__rating-statistics-dropdown-menu-table">
                        <tbody>
                            <!-- ko foreach: value -->
                                <tr>
                                    <td>
                                        <div data-bind="component: {
                                            name: 'question-statistics-rating',
                                            params: { value: 5 - $index(), starClass: 'question-statistics__rating-statistics-dropdown-menu-table-rating-star' }
                                        }" class="question-statistics__rating-statistics-dropdown-menu-table-rating">
                                        </div>
                                    </td>
                                    <td data-bind="text: $data"></td>
                                    <td class="question-statistics__rating-statistics-dropdown-menu-table-percentage-cell"
                                        data-bind="text: $component.formatPercentage($data / $component.voteCount())">
                                    </td>
                                </tr>
                            <!-- /ko -->
                        </tbody>
                    </table>
                `;
    };

    this.printer = Printer();

    this.printPage = function () {
      let app = $(".app").clone(true);
      app.find(".app__sidebar").remove();
      this.printer.print([app.get(0)]);
    };

    this.shareLink = function () {
      this.openDialog({
        name: "share-link-dialog",
        params: {
          pollId: POLL_ID,
          mask: true,
        },
      });
    };

    this.widgetLink = function () {
      this.openDialog({
        name: "share-widget-dialog",
        params: {
          pollId: POLL_ID,
          mask: true,
        },
      });
    };

    this.getDonorIndex = (donorId) => {
      const index = this.questions().findIndex(
        (q) => q.question_id === donorId
      );
      if (index > -1) return index + 1;
      return null;
    };

    this.scrollToQuestion = (questionId) => {
      const el = document.querySelector(
        `.question-statistics__question[data-id="${questionId}"]`
      );
      if (!el) return;
      el.scrollIntoView({ behavior: "smooth" });
    };

    if (this.filialsLoaded()) this.submitFilters();
    else {
      this.filialsLoaded.subscribe((v) => {
        this.submitFilters();
      });
    }

    this.variantsFilters = ko.observableArray([]);

    const settings = JSON.parse(FILTER_SETTINGS)

    this.updateVariantsFilterSettings = (settings) => {
      if (settings.length) {
        this.variantsFilters([])
        settings.forEach(item => {
          const obj = filterObj(item.question_id, item.action, QUESTIONS.filter(item => item.main_question_type == 1), item.variants?.map(v => v.toString()) || [], !!item.active)
          this.variantsFilters.push(obj)
        })
        this.updatedVariantsFilters = settings
      } else {
        this.variantsFilters([])
        this.updatedVariantsFilters = []
      }
    }

    this.updateVariantsFilterSettings(settings)
    

  };

  window.translator.load("profile").then(() => {
    const viewModel = new ViewModel();

    const $content = $(".question-statistics__content");

    viewModel.initializing = ko.observable(true);

    viewModel.onInit = function () {
      viewModel.initializing(false);
      setTimeout(() => {
        const $container = $(
          ".question-statistics__question-dish-ratings-statistics-table-wrapper.ps"
        );

        $container.on("scroll", () => {
          viewModel.pagination($container);
        });
      });
    };

    ko.applyBindings(viewModel, $content.get()[0]);
  });
});
