import { FoquzLoaderWithFile } from 'Models/file-loader/loader-with-file';
import { BrowserNotifications } from './browser-notifications';
import { preloadDictionaries } from 'Utils/translate';

import './components/profile-name';
import './components/profile-password';
import './components/profile-email';

import './style.less';

function updateSidebarAvatar(sImage) {
  let arUserAvatars = document.querySelectorAll('.js-user-avatar');
  if (arUserAvatars) {
    arUserAvatars.forEach(($img) => {
      $img.src = sImage;
    });
  }
}
class ViewModel {
  constructor(userData) {
    this.userpic = new FoquzLoaderWithFile((newFile) => {
      return new Promise((res) => {
        const formData = new FormData();
        if (!newFile) {
          formData.append('User[avatar]', '');
        } else {
          formData.append('User[avatar]', newFile);
        }

        $.ajax({
          url: `${APIConfig.baseApiUrlPath}user/update-avatar?access-token=${APIConfig.apiKey}`,
          method: 'POST',
          data: formData,
          processData: false,
          contentType: false,
        });
      });
    }, userData.photo);
    this.userpic.on('preview', ({ url }) => {
      updateSidebarAvatar(url || '/img/user-placeholder-2.png');
    });

    this.nameForm = ko.observable(null);
    this.passwordForm = ko.observable(null);
    this.emailForm = ko.observable(null);

    this.name = ko.observable(userData.name || '');
    this.email = ko.observable(userData.email || '');

    const pageData = window.PAGE_DATA;
    this.browserNotifications = new BrowserNotifications(
      pageData.notifications
    );
  }
}

$(function () {
  const domain = '';

  let userData = {};

  let translatorLoading = window.translator.load('profile');

  $.ajax({
    url: domain + '/foquz/api/user/view',
    type: 'GET',
    data: 'access-token=' + APIConfig.apiKey
  }).done((data) => {
    if (data) {
      userData = {
        photo: data.photo.includes('preview-dummy.png')
          ? null
          : domain + data.photo,
        name: data.name,
        email: data.email
      };

      translatorLoading.then(() => {
        let viewModel = new ViewModel(userData);

        const $content = document.querySelector('.profile');
        viewModel.initializing = ko.observable(true);
        viewModel.onInit = function () {
          setTimeout(() => {
            viewModel.initializing(false);
          }, 950);
        };
        ko.applyBindings(viewModel, $content);
      });
    }
  });
});
