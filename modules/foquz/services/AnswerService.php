<?php

namespace app\modules\foquz\services;

use app\models\DictionaryElement;
use app\models\Filial;
use app\modules\foquz\models\FilialPollKey;
use app\modules\foquz\models\FoquzContact;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzPollAnswerItemFile;
use app\modules\foquz\models\FoquzPollAnswerShowedImage;
use app\modules\foquz\models\FoquzPollKey;
use app\modules\foquz\models\FoquzPollMailingListSend;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionDetail;
use app\modules\foquz\models\FoquzQuestionIntermediateBlockSetting;
use app\modules\foquz\models\FoquzQuestionMatrixElement;
use app\modules\foquz\models\FoquzQuestionSmile;
use app\modules\foquz\models\quotes\FoquzPollLinkQuotes;
use Yii;
use yii\helpers\ArrayHelper;
use yii\web\Cookie;
use yii\web\CookieCollection;

class AnswerService
{
    private array $params;
    private ?CookieCollection $cookies;
    private ?FoquzPoll $poll = null;
    private ?int $filialID = null;
    private array $errors = [];

    private bool $needAuth = false;
    private ?string $authUrl = null;

    public function __construct(array $params = [], CookieCollection $cookies = null)
    {
        if (!empty($params)) {
            $this->params = $params;
        } else {
            $this->params = Yii::$app->request->get();
        }

        if ($cookies) {
            $this->cookies = $cookies;
        } else {
            $this->cookies = Yii::$app->request->cookies;
        }
    }

    /**
     * @param array $params
     * @param bool $isPreview
     * @param $key
     * @param $pollId
     * @return array{FoquzPoll, FoquzPollAnswer, self}
     */
    public static function getInstance(array $params, bool $isPreview, $key, $pollId) : array
    {

        $answerService = new self($params);

        $answer = null;
        if ($isPreview) {
            if (empty($pollId)) {
                $answerService->findPoll($key);
            } else {
                $answerService->findPollById($pollId);
            }

        } else {
            $answer = $answerService->getAnswer($key);
        }

        $poll = $answerService->getPoll();

        return [$poll, $answer, $answerService];
    }


    public static function formatLanguagesForProcess(array $foquzPollLangs): array
    {
        $pollLangs = [];
        foreach ($foquzPollLangs as $pollLanguage) {
            if ($pollLanguage->checked || $pollLanguage->default) {
                $pollLangs[] = [
                    "default"       => $pollLanguage->default,
                    "id"            => $pollLanguage->id,
                    "lang_id"       => $pollLanguage->poll_lang_id,
                    "name"          => $pollLanguage->pollLang ? $pollLanguage->pollLang->name : null,
                    "original_name" => $pollLanguage->pollLang ? $pollLanguage->pollLang->original_name : null,
                    "code"          => $pollLanguage->pollLang ? $pollLanguage->pollLang->code : null,
                    "short_code"    => $pollLanguage->pollLang ? $pollLanguage->pollLang->short_code : null,

                ];
            }
        }
        return $pollLangs;
    }


    public function getAnswer(string $key = null, array $params = []): ?FoquzPollAnswer
    {


        if (!$key && !empty($this->params['key'])) {
            $key = $this->params['key'];
        } elseif (!$key) {
            throw new \InvalidArgumentException('Key is required');
        }

        if (!empty($params)) {
            $this->params = array_merge($this->params, $params);
        }

        $answer = $this->getAnswerByKey($key);
        if (!$answer) {
            $this->findPoll($key);
        }
        if (!$this->poll) {
            $this->errors[] = ['code' => 404, 'message' => 'Анкета не найдена'];
            return null;
        }

        $this->setFilial();

        if (Yii::$app->user->isGuest && $this->poll->need_auth) {
            $this->needAuth = true;

            if (isset($_SERVER['HTTP_HOST']) && $_SERVER['HTTP_HOST'] === 'sibur.foquz.ru') {
                $session = Yii::$app->session;
                if (!$session->isActive) {
                    $session->open();
                }
                $session->set('sso_redirect', Yii::$app->request->referrer);
                $this->authUrl = 'https://sibur.foquz.ru/adfs/saml';
            }

            return null;
        }

        if (!$answer) {
            $answer = $this->getAnswerByClient();
        }
        if (!$answer) {
            $answer = $this->getAnswerByUser();
        }
        if (!$answer) {
            $answer = $this->getAnswerByCookie($key);
        }

        if (!$answer) {
            $answer = $this->createAnswer();
        }


        if (!$answer) {
            $this->errors[] = ['code' => 500, 'message' => 'Ошибка при создании анкеты'];
            return null;
        }

        if (in_array($answer->status, [FoquzPollAnswer::STATUS_NEW, FoquzPollAnswer::STATUS_EMAIL_OPEN], true)) {
            $answer->setUserDevice();
            $answer->status = FoquzPollAnswer::STATUS_OPEN;
            $answer->save();
        }

        if (isset($this->params['fz_answer'])) {
            $this->answerFirstQuestion($answer, $this->params["fz_answer"]);
        }


        return $answer;
    }

    public function findPoll(string $key): void
    {
        if ($this->poll) {
            return;
        }
        $this->poll = FoquzPoll::findOne(['key' => $key]);
        if (!$this->poll) {
            $this->poll = FoquzPollLinkQuotes::findOne(['key' => $key])?->foquzPoll;
            if (!$this->poll) {
                $filialPollKey = FilialPollKey::findOne(['key' => $key]);
                if ($filialPollKey) {
                    $this->poll = $filialPollKey->poll;
                    $this->filialID = $filialPollKey->filial_id;
                }
            }
        }
    }

    public function findPollById(int $id): void
    {
        if ($this->poll) {
            return;
        }
        $this->poll = FoquzPoll::findOne($id);
    }


    private function getAnswerByKey(string $key): ?FoquzPollAnswer
    {
        /** @var FoquzPollMailingListSend|null $sending */
        $sending = FoquzPollMailingListSend::findOne(['key' => $key]);
        if ($sending) {
            if (isset($sending->answer)) {
                $this->poll = $sending->answer->foquzPoll;
                if ($sending->status != FoquzPollMailingListSend::STATUS_OPEN) {
                    $sending->status = FoquzPollMailingListSend::STATUS_OPEN;
                    $sending->save();
                }
            }
            return $sending->answer;
        }
        $answer = FoquzPollAnswer::findOne(['auth_key' => $key]);
        if ($answer) {
            $this->poll = $answer->foquzPoll;
            return $answer;
        }
        $pollKey = FoquzPollKey::findOne(['key' => $key]);


        if ($pollKey) {
            if (isset($pollKey->answer)) {
                $this->poll = $pollKey->answer->foquzPoll;
            }
            return $pollKey->answer;
        }
        return null;
    }

    private function getAnswerByClient(): ?FoquzPollAnswer
    {
        $customer = $this->params['customer'] ?? null;

        if (!$customer || (!$this->poll->dont_send_if_passed_link && !$this->poll->stop_sending_link && !$this->poll->stop_sending_link_time)) {
            return null;
        }

        /** @var FoquzPollAnswer|null $answer */
        $answer = FoquzPollAnswer::find()
            ->joinWith('contact', false)
            ->where(['foquz_contact.is_deleted' => false, 'foquz_contact.company_client_id' => $customer])
            ->andWhere(['foquz_contact.company_id' => $this->poll->company_id])
            ->andWhere(['foquz_poll_answer.foquz_poll_id' => $this->poll->id])
            ->orderBy(['foquz_poll_answer.created_at' => SORT_DESC])
            ->one();

        if (!$answer) {
            return null;
        }

        $add_time = 0;
        if ($this->poll->stop_sending_link_time) {
            $hm = explode(':', $this->poll->stop_sending_link_time);
            if (count($hm) === 2) {
                list($h, $m) = $hm;
                $add_time = ((int)$h * 60 + (int)$m)*60;
            }
        }

        if (
            ($this->poll->stop_sending_link === 'double') ||
            (
                $this->poll->dont_send_if_passed_link &&
                in_array($answer->status, [FoquzPollAnswer::STATUS_IN_PROGRESS, FoquzPollAnswer::STATUS_DONE], true)
            ) ||
            (
                ($this->poll->stop_sending_link || $this->poll->stop_sending_link_time)  &&
                $this->poll->stop_sending_link !== 'double' &&
                time() < strtotime($answer->created_at) + 60 * 60 * 24 * $this->poll->stop_sending_link + $add_time)
        ) {
            return $answer;
        }

        return null;
    }

    private function getAnswerByUser(): ?FoquzPollAnswer
    {
        if (!$this->poll->need_auth || Yii::$app->user->isGuest) {
            return null;
        }

        /** @var FoquzPollAnswer|null $answer */
        $answer = FoquzPollAnswer::find()
            ->where(['user_id' => Yii::$app->user->id, 'foquz_poll_id' => $this->poll->id])
            ->orderBy(['created_at' => SORT_DESC])
            ->one();

        return $answer;
    }

    private function getAnswerByCookie(string $key): ?FoquzPollAnswer
    {
        if ($this->poll->kiosk_mode || isset($this->params['kiosk']) || empty($this->cookies->get('p' . $key)->value)) {
            return null;
        }
        $authKey = $this->cookies->get('p' . $key)->value;
        return FoquzPollAnswer::findOne(['auth_key' => $authKey]);
    }

    private function createAnswer(): ?FoquzPollAnswer
    {
        $customData = [];
        $contact = $this->createContactByParams($customData);


        if (in_array($this->poll->company_id, [3844, 3751])) { //для отдельных компания
            $customData = $this->params;
            unset($customData['id'], $customData['lang']);
        }
        unset($customData['edit']);

        $model = new FoquzPollAnswer([
            'foquz_poll_id'    => $this->poll->id,
            'contact_id'        => $contact?->id,
            'auth_key'         => md5(uniqid('poll', true) . '_' . time()),
            'answer_filial_id' => $this->filialID,
            'ip_address'       => Yii::$app->request->userIP,
            'custom_fields'    => !empty($customData) ? json_encode($customData, JSON_UNESCAPED_UNICODE) : null,
        ]);

        $model->setProcessingTimeByChannelType(FoquzPollAnswer::CHANNEL_TYPE_LINK);

        $model->setUserDevice();

        if ($model->save()) {
            if (!$this->poll->is_auto && !$model->answerChannel) {
                $listSend = new FoquzPollMailingListSend([
                    'answer_id'    => $model->id,
                    'status'       => FoquzPollMailingListSend::STATUS_OPEN,
                    'key'          => md5(time() . uniqid()),
                    'sended'       => date('Y-m-d H:i:s'),
                    'channel_name' => 'Ссылка',
                ]);
                $listSend->save();
            }
            if (!$this->poll->kiosk_mode && !isset($this->params['kiosk'])) {
                Yii::$app->response->cookies->add(new Cookie([
                    'name'     => 'p' . $this->params['key'],
                    'value'    => $model->auth_key,
                    'sameSite' => Cookie::SAME_SITE_NONE,
                    'secure'   => true,
                    'expire'   => time() + 60 * 60 * 24 * 365 * 10,
                ]));
            }


            return $model;
        } else {

        }

        return null;
    }

    private function answerFirstQuestion(FoquzPollAnswer $model, string $answer): bool
    {
        $questions = $model
            ->foquzPoll
            ->getFoquzQuestions()
            ->where(['is_tmp' => false])
            ->andWhere(['is_deleted' => false])
            ->orderBy(['position' => SORT_ASC])->all();
        foreach ($questions as $q) {
            if ($q->main_question_type == FoquzQuestion::TYPE_INTERMEDIATE_BLOCK) {
                continue;
            }
            $question = $q;
            break;
        }

        if (!$question) {
            return false;
        }

        $model->addProcessingIfNeeded();
        $changeStatus = false;
        if (in_array($question->main_question_type, [
            FoquzQuestion::TYPE_ASSESSMENT,
            FoquzQuestion::TYPE_NPS_RATING,
            FoquzQuestion::TYPE_SMILE_RATING,
            FoquzQuestion::TYPE_STAR_RATING,
        ])) {
            $answer = intval($answer);
            if ($answer >= 0 && $answer <= 10) {
                $answerForm = FoquzPollAnswerItem::findOne([
                    'foquz_poll_answer_id' => $model->id,
                    'foquz_question_id'    => $question->id,
                ]);

                if (!$answerForm) {
                    $answerForm = new FoquzPollAnswerItem([
                        'rating'               => 0,
                        'foquz_poll_answer_id' => $model->id,
                        'foquz_question_id'    => $question->id,
                        'question_name'        => $question->service_name
                    ]);
                }

                $answerForm->self_variant = null;
                $answerForm->is_self_variant = false;
                $answerForm->answer = $question->main_question_type == FoquzQuestion::TYPE_SMILE_RATING ?
                    (FoquzQuestionSmile::findAll(['foquz_question_id' => $question->id])[$answer - 1]->id ?? null) :
                    null;
                $answerForm->rating = $answer;

                $answerForm->save();
                $changeStatus = true;
            }
        } else {
            if ($question->main_question_type == FoquzQuestion::TYPE_VARIANTS) {
                $answer = trim($answer);
                $d = FoquzQuestionDetail::find()->where([
                    "foquz_question_id" => $question->id,
                    "question"          => $answer
                ])->one();
                if ($d) {
                    $answer = [strval($d->id)];
                    $answerForm = FoquzPollAnswerItem::findOne([
                        'foquz_poll_answer_id' => $model->id,
                        'foquz_question_id'    => $question->id,
                    ]);

                    if (!$answerForm) {
                        $answerForm = new FoquzPollAnswerItem([
                            'rating'               => null,
                            'foquz_poll_answer_id' => $model->id,
                            'foquz_question_id'    => $question->id,
                            'question_name'        => $question->service_name
                        ]);
                    }

                    $answerForm->self_variant = null;
                    $answerForm->is_self_variant = false;
                    $answerForm->rating = null;
                    $answerForm->detail_item = json_encode($answer);

                    $answerForm->save();

                }
            } else {
                $answer = trim($answer);
                if ($answer) {
                    $answerForm = FoquzPollAnswerItem::findOne([
                        'foquz_poll_answer_id' => $model->id,
                        'foquz_question_id'    => $question->id,
                    ]);

                    if (!$answerForm) {
                        $answerForm = new FoquzPollAnswerItem([
                            'rating'               => null,
                            'foquz_poll_answer_id' => $model->id,
                            'foquz_question_id'    => $question->id,
                            'question_name'        => $question->service_name
                        ]);
                    }

                    $answerForm->self_variant = null;
                    $answerForm->is_self_variant = false;
                    $answerForm->answer = $answer;
                    $answerForm->rating = null;
                    $answerForm->save();
                    $changeStatus = true;
                }
            }
        }

        if ($changeStatus && $model->status === $model::STATUS_OPEN) {
            $model->status = $model::STATUS_IN_PROGRESS;
            $model->save();
            $model->sendEmailNotification();
            $model->sendPushNotification();
        }

        return true;
    }

    private function createContactByParams(&$customData): ?FoquzContact
    {
        if (!empty($this->params['fz_user'])) {
            return $this->createContactByFzUser($customData);
        }

        if (!empty($this->params['customer'])) {
            return $this->createContactByClientID($customData);
        }

        if (!empty($this->params['t'])) {
            return $this->createContactByPhone($customData);
        }

        if (!empty($this->params['email'])) {
            return $this->createContactByEmail($customData);
        }

        return null;
    }

    private function createContactByFzUser(&$customData): ?FoquzContact
    {
        $params = [];

        $user = explode("|", $this->params['fz_user'] ?? '');
        if (!empty($user)) {
            $fields = ['Фамилия', 'Имя', 'Email', 'Телефон', 'CUSTOMER ID'];
            foreach ($fields as $key => $field) {
                if (!isset($user[$key])) {
                    break;
                }
                $params[$field] = trim($user[$key]);
            }
        }

        $this->parseFzParams($params);

        return FoquzContact::createContact($params, $this->poll->company_id, $customData);
    }

    private function createContactByClientID(&$customData): ?FoquzContact
    {
        if (empty(trim($this->params['customer']))) {
            return null;
        }

        $params = [];
        $this->parseFzParams($params);

        return FoquzContact::createContactByCompanyID(
            trim($this->params['customer']),
            $params,
            $this->poll->company_id,
            $customData
        );
    }

    private function createContactByPhone(&$customData): ?FoquzContact
    {
        if (empty(trim($this->params['t']))) {
            return null;
        }
        $params['Телефон'] = trim($this->params['t']);

        return FoquzContact::createContact($params, $this->poll->company_id, $customData);
    }


    private function createContactByEmail(&$customData): ?FoquzContact
    {
        if (empty(trim($this->params['email']))) {
            return null;
        }
        $params['Email'] = trim($this->params['email']);

        return FoquzContact::createContact($params, $this->poll->company_id, $customData);
    }

    private function parseFzParams(&$params): void
    {
        if (!empty($this->params['fz_x_birth_date']) && strtotime($this->params['fz_x_birth_date'])) {
            $params['Дата рождения'] = trim($this->params['fz_x_birth_date']);
        }

        if (!empty($this->params['fz_x_purchase_date']) && strtotime($this->params['fz_x_purchase_date'])) {
            $params['Дата продажи'] = trim($this->params['fz_x_purchase_date']);
        }

        $fields = ['z_open', 'fz_extuid', 'fz_x_cluster_rfm', 'fz_x_cluster_style'];
        foreach ($fields as $field) {
            if (!empty($this->params[$field])) {
                $params[$field] = trim($this->params[$field]);
            }
        }
    }

    private function setFilial(): void
    {
        if ($this->filialID) {
            return;
        }

        if (in_array($this->poll->id, [8091, 8077])) {
            $this->filialID = 186;
            return;
        }

        if (!empty($this->params['filial'])) {
            $filialID = $this->params['filial'];
        } elseif (!empty($this->params['f'])) {
            $filialID = $this->params['f'];
        }

        if (empty($filialID)) {
            return;
        }

        $this->filialID = Filial::find()
            ->where(['company_id' => $this->poll->company_id])
            ->andWhere(['or', ['name' => $filialID], ['crm_id' => $filialID]])
            ->select('id')
            ->scalar();
    }

    /**
     * @return array
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * @return FoquzPoll|null
     */
    public function getPoll(): ?FoquzPoll
    {
        return $this->poll;
    }

    /**
     * @return array
     */
    public function getAuthParams(): array
    {
        return [
            'needAuth' => $this->needAuth,
            'authUrl'  => $this->authUrl,
        ];
    }

    /**
     * Форматирование перевода для прохождения
     * @param array $translate
     * @return array
     */
    public static function formatTranslateForProcess(array $translate): array
    {
        if (!isset($translate["questions"])) {
            return $translate;
        }

        $translate["questions"] = ArrayHelper::index($translate["questions"], "foquz_question_id");
        foreach ($translate["questions"] as $id => $question) {
            if (isset($question["fileLangs"])) {
                $translate["questions"][$id]["fileLangs"] = ArrayHelper::index($translate["questions"][$id]["fileLangs"],
                    "foquz_question_file_id");
            }
            if (isset($question["detailLangs"])) {
                $translate["questions"][$id]["detailLangs"] = ArrayHelper::index($translate["questions"][$id]["detailLangs"],
                    function ($data) {
                        return $data["foquz_question_detail_id"] ?: $data["detail_id"];
                    });
            }
            if (isset($question["settingLangs"])) {
                $translate["questions"][$id]["settingLangs"] = ArrayHelper::index($translate["questions"][$id]["settingLangs"],
                    "setting_id");
            }
            if (isset($question["formLangs"])) {
                $translate["questions"][$id]["formLangs"] = ArrayHelper::index($translate["questions"][$id]["formLangs"],
                    "form_field_id");
            }
            if (isset($question["matrixElements"])) {
                $translate["questions"][$id]["matrixElements"] = ArrayHelper::index($translate["questions"][$id]["matrixElements"],
                    "matrix_element_id");
            }
            if (isset($question["matrixVariants"])) {
                $translate["questions"][$id]["matrixVariants"] = ArrayHelper::index($translate["questions"][$id]["matrixVariants"],
                    "variant_id");
            }
        }
        return $translate;
    }

    /**
     * Магия для фронта над строками просто матрицы
     * @param FoquzQuestion $question
     * @param array $row
     * @return array
     */
    private static function postFormatSimpleMatrixRecipient(FoquzQuestion $question, array $row): array
    {
        $matrixSettings = $question->matrix_settings;
        if (!empty($matrixSettings)) {
            $matrixSettings = json_decode($matrixSettings, true);
        }
        $donorRows = [];
        if (!empty($matrixSettings)) {
            $donorRows = $matrixSettings['donorRows'] ?? [];
        }

        //донор Классификатор
        if (empty($row['detail_answers']) && !empty($row['assessmentVariants'])) {
            $row['detail_answers'] = ArrayHelper::getColumn($row['assessmentVariants'],
                function ($row) use ($donorRows) {
                    $row['dictionary_element_id'] = $row['id'];
                    $row['id'] = null;
                    $row['question'] = $row['value'];
                    unset($row['value']);
                    unset($row['isChecked']);
                    return $row;
                });
        }
        if ($question->mainDonor->main_question_type == FoquzQuestion::TYPE_DICTIONARY && !empty($row['detail_answers'])) {
            foreach ($row['detail_answers'] as $i => $detailAnswer) {
                $position = array_search($detailAnswer['dictionary_element_id'] ?? $detailAnswer['id'], $donorRows);
                $row['detail_answers'][$i]['position'] = $position >= 0 ? $position : $detailAnswer['position'];
            }
        }

        if ($question->mainDonor->main_question_type == FoquzQuestion::TYPE_VARIANTS && !empty($row['detail_answers'])) {
            $customIndex = -1;
            foreach ($row['detail_answers'] as $i => $detailAnswer) {
                if (count($detailAnswer)==1) {
                    $customIndex = $i;
                    continue;//свой вариант
                }
                $position = array_search($detailAnswer['question_detail_id'] ?? $detailAnswer['id'], $donorRows);
                $row['detail_answers'][$i]['position'] = $position >= 0 ? $position : $detailAnswer['position'];
            }
            if ($customIndex>=0) {
                unset($row['detail_answers'][$customIndex]);
                $row['detail_answers'] = array_values($row['detail_answers']);
            }

            $hasSelfVariant = array_search("custom", $donorRows);
            if ($hasSelfVariant >= 0) {
                $row['detail_answers'][] = [
                    'id'             => -1,
                    'question'       => $question->mainDonor->self_variant_text,
                    'recipient_id'   => $question->id,
                    'position'       => $hasSelfVariant,
                    'is_self_answer' => false,
                    'is_deleted'     => 0,

                ];
            }

            if (!empty($donorRows) && empty($matrixSettings['rows'])) { //реципиент орт реципиента
                $row['matrixSettings']->rows = ArrayHelper::getColumn($donorRows, function ($d) use ($row) {
                    $detail = array_filter($row['detail_answers'], function($da) use ($d) { return  $d==$da['id'] || ($da['id']==-1 && $d=='custom'); });
                    return empty($detail) ? '' : array_values($detail)[0]['question'];
                });
            }
        }

        return $row;
    }

    public static function postFormatRecipientFromDictionary(array $row): array
    {
        if (empty($row['detail_answers']) || !is_array($row['detail_answers'])) {
            return $row;
        }
        $dictionaryIds = ArrayHelper::getColumn($row['detail_answers'], 'dictionary_element_id');
        /** @var DictionaryElement[] $dictionaryElements */
        $dictionaryElements = ArrayHelper::index(DictionaryElement::find()->where(['id' => $dictionaryIds])->all(),
            'id');
        foreach ($row['detail_answers'] as $i => $detailAnswer) {
            if (!empty($detailAnswer['dictionary_element_id']) && !empty($dictionaryElements[$detailAnswer['dictionary_element_id']])) {
                $row['detail_answers'][$i]['path'] = $dictionaryElements[$detailAnswer['dictionary_element_id']]->path;
                $row['detail_answers'][$i]['question'] = $dictionaryElements[$detailAnswer['dictionary_element_id']]->title;
            }
        }
        return $row;
    }

    public static function postFormatRecipient3DMatrixFromDictionary(FoquzQuestion $question, array $row): array
    {
        if (!empty($question->mainDonorRows) && $question->mainDonorRows->main_question_type == FoquzQuestion::TYPE_DICTIONARY && !empty($row['matrixElements']) && !empty($row['matrixElements']['rows'])) {
            $dictionaryIds = ArrayHelper::getColumn($row['matrixElements']['rows'], 'donor_dictionary_element_id');
            /** @var DictionaryElement[] $dictionaryElements */
            $dictionaryElements = ArrayHelper::index(DictionaryElement::find()->where(['id' => $dictionaryIds])->all(),
                'id');
            foreach ($row['matrixElements']['rows'] as $i => $matrixRow) {
                if (!empty($matrixRow['donor_dictionary_element_id']) && !empty($dictionaryElements[$matrixRow['donor_dictionary_element_id']])) {
                    $row['matrixElements']['rows'] [$i]['path'] = $dictionaryElements[$matrixRow['donor_dictionary_element_id']]->path;
                    $row['matrixElements']['rows'] [$i]['name'] = $dictionaryElements[$matrixRow['donor_dictionary_element_id']]->title;
                }
            }
        }

        if (!empty($question->mainDonorColumns) && $question->mainDonorColumns->main_question_type == FoquzQuestion::TYPE_DICTIONARY && !empty($row['matrixElements']) && !empty($row['matrixElements']['columns'])) {
            $dictionaryIds = ArrayHelper::getColumn($row['matrixElements']['columns'], 'donor_dictionary_element_id');
            /** @var DictionaryElement[] $dictionaryElements */
            $dictionaryElements = ArrayHelper::index(DictionaryElement::find()->where(['id' => $dictionaryIds])->all(),
                'id');
            foreach ($row['matrixElements']['columns'] as $i => $matrixRow) {
                if (!empty($matrixRow['donor_dictionary_element_id']) && !empty($dictionaryElements[$matrixRow['donor_dictionary_element_id']])) {
                    $row['matrixElements']['columns'] [$i]->path = $dictionaryElements[$matrixRow['donor_dictionary_element_id']]->path;
                    $row['matrixElements']['columns'] [$i]->name = $dictionaryElements[$matrixRow['donor_dictionary_element_id']]->title;
                }
            }
        }

        return $row;
    }


    /**
     * Форматирование вопросов для прохождения
     * @param array $questions
     * @param FoquzPollAnswer|null $answer
     * @return array
     */
    public static function formatQuestionsForProcess(
        array $questions,
        ?FoquzPollAnswer $answer,
        bool $vue = false
    ): array {
        $result = [];
        $answerItems = $answer->foquzAnswer ?? [];
        $answerItems = ArrayHelper::index($answerItems, 'foquz_question_id');
        $isPointSystem = null;

        /** @var FoquzQuestion $question */
        foreach ($questions as $question) {
            /** @var FoquzPollAnswerItem $answerItem */
            $answerItem = $answerItems[$question->id] ?? null;
            if ($answer) {
                $variants = $question->collectVariantsArray($answer, $answerItem, $question->random_variants_order);
            } else {
                $variants = $question->collectDummyVariants($question->random_variants_order);
            }

            if ($answerItem && $question->variants_element_type === FoquzQuestion::VARIANT_ELEMENT_TYPE_RADIO) {
                $detailItem = null;
                if (is_string($answerItem->detail_item)) {
                    $detailItem = json_decode($answerItem->detail_item);
                } elseif (is_array($answerItem->detail_item)) {
                    $detailItem = $answerItem->detail_item;
                }
                $radioButtonCheckedValue = $detailItem[0] ?? ($answerItem->is_self_variant ? 'is_self_answer' : '');
            } else {
                $radioButtonCheckedValue = '';
            }

            $matrixElements = [];
            if (!empty($question->activeMatrixElements)) {
                $elements = ArrayHelper::index($question->activeMatrixElements, null, 'type_id');

                $matrixElements = [
                    'rows'    => $elements[FoquzQuestionMatrixElement::TYPE_ROW] ?? [],
                    'columns' => $elements[FoquzQuestionMatrixElement::TYPE_COLUMN] ?? [],
                ];

                if ($question->donor_rows) {
                    $donor = $question->getMainDonorRows();
                    if ($donor && $donor->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                        /**
                         * @var int $key
                         * @var FoquzQuestionMatrixElement $row
                         */
                        foreach ($matrixElements['rows'] as $key => $row) {
                            $row->name = !str_contains($row->name,
                                ' / ') ? $row->name : mb_substr(mb_strrchr($row->name, ' / '), 2);
                            $matrixElements['rows'][$key] = $row;
                        }
                    }
                }
                if ($question->donor_columns) {
                    $donor = $question->getMainDonorColumns();
                    if ($donor && $donor->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                        /**
                         * @var int $key
                         * @var FoquzQuestionMatrixElement $row
                         */
                        foreach ($matrixElements['columns'] as $key => $row) {
                            $row->name = !str_contains($row->name,
                                ' / ') ? $row->name : mb_substr(mb_strrchr($row->name, ' / '), 2);
                            $matrixElements['columns'][$key] = $row;
                        }
                    }
                }
            }


            $answerItemForResponse = null;
            if ($answerItem !== null) {
                $answerItemForResponse = array_filter(ArrayHelper::merge($answerItem->toArray(),
                    ['isEmpty' => $answerItem->isEmpty]), static function ($key) {
                    return in_array($key, [
                        'id',
                        'foquz_poll_answer_id',
                        'foquz_question_id',
                        'answer',
                        'rating',
                        'detail_item',
                        'self_variant',
                        'is_self_variant',
                        'skipped',
                        'isEmpty'
                    ], true);
                }, ARRAY_FILTER_USE_KEY);
            }

            $files = [];
            if ($question->main_question_type === FoquzQuestion::TYPE_FILE_UPLOAD) {
                if ($answer) {
                    /** @var FoquzPollAnswerItemFile[] $dbFiles */
                    $dbFiles = FoquzPollAnswerItemFile::find()
                        ->where(['foquz_poll_answer_id' => $answer->id, 'foquz_question_id' => $question->id])
                        ->all();
                    foreach ($dbFiles as $dbFile) {
                        $files[] = [
                            'id'          => $dbFile->id,
                            'origin_name' => $dbFile->origin_name,
                            'link'        => $dbFile->file_path,
                            'image'       => $dbFile->image_link,
                            'type'        => $dbFile->type,
                        ];
                    }
                }
                if ($answerItem !== null) {
                    $answerItemForResponse['files'] = $files;
                }
            }

            if (is_null($isPointSystem)) {
                $isPointSystem = $question->poll->point_system;
            }

            $row = [
                'id'                            => $question->position,
                'question_id'                   => $question->id,
                'answer'                        => $answerItemForResponse,
                'alias'                         => $question->service_name,
                'name'                          => $question->name ?? '',
                'description'                   => $question->description ?? '',
                'description_html'              => $question->description_html ?? '',
                'subdescription'                => $question->sub_description ?? '',
                'type'                          => $question->main_question_type,
                'mediaType'                     => $question->getNumberMediaType(),
                'questionContent'               => $question->getNumberMediaType() === 0 ? ($question->text ?? '') : $question->grabMediaContent(),
                'assessmentType'                => $question->rating_type - 1,
                'assessmentVariantsType'        => $question->variants_element_type,
                'assessmentVariants'            => $variants,
                'detail_answers'                => $question->collectVariants(),
                'filials'                       => $question->getFilialsWithCategories(false),
                'dictionaryTree'                => $question->getDictionaryTree(false),
                'priorityAnswer'                => $answerItem->detail_item ?? null,
                'answerType'                    => $question->answerType,
                'forAllRates'                   => $question->for_all_rates,
                'answerText'                    => $question->detail_question,
                'stars'                         => $answerItem->rating ?? null,
                'maskType'                      => $question->mask ?? 0,
                'dateType'                      => $question->date_type,
                'category'                      => $question->show_category,
                'b_name'                        => $question->show_name,
                'gallery'                       => $question->grabMediaContent(),
                'enableGallery'                 => count($question->grabMediaContent()) > 0,
                'smileType'                     => $question->smile_type,
                'variantsType'                  => $question->variants_element_type,
                'comment'                       => $answerItem !== null ? ($answerItem->answer ?? '') : '',
                'comment_label'                 => $question->comment_label ?: 'Ваш комментарий',
                'comment_enabled'               => $question->comment_enabled,
                'comment_required'              => $question->comment_required,
                'isRequired'                    => $question->is_required,
                'isTmp'                         => $question->is_tmp,
                'isValid'                       => !$question->is_required,
                'checkAll'                      => $question->value_all,
                'minDishPrice'                  => $question->min_sum ?? '',
                'isHaveComment'                 => $question->is_self_answer || $question->comment_enabled,
                'isHaveCustomField'             => $question->is_self_answer,
                'filesLength'                   => $question->files_length,
                'fileTypes'                     => $question->file_types,
                'textFieldParam'                => [
                    'min' => $question->comment_minlength,
                    'max' => $question->comment_maxlength ?? 500,
                ],
                'selfVariantParam'              => [
                    'min' => $question->self_variant_minlength,
                    'max' => $question->self_variant_maxlength ?? 500,
                ],
                'isCustomFieldChecked'          => $answerItem !== null ? $answerItem->is_self_variant : false,
                'radioButtonCheckedValue'       => $radioButtonCheckedValue,
                'textFieldValue'                => $answerItem !== null ? $answerItem->self_variant ?? '' : '',
                'value'                         => $answerItem !== null ? $answerItem->answer : '',
                'dateValue'                     => $answerItem !== null ? json_decode($answerItem->answer)->date ?? '' : '',
                'timeValue'                     => $answerItem !== null ? json_decode($answerItem->answer)->time ?? '' : '',
                'files'                         => $answerItem !== null ? $answerItem->answerItemFiles : [],
                'loadedFiles'                   => $files,
                'variants'                      => $variants,
                'values'                        => $question->getQuizzes($answerItem),
                'arRegionsIDs'                  => in_array($question->main_question_type,
                    [FoquzQuestion::TYPE_ADDRESS, FoquzQuestion::TYPE_FORM],
                    true) && $question->addressCodes ? json_decode($question->addressCodes->regions) : [],
                'arDistrictsIDs'                => in_array($question->main_question_type,
                    [FoquzQuestion::TYPE_ADDRESS, FoquzQuestion::TYPE_FORM],
                    true) && $question->addressCodes ? json_decode($question->addressCodes->districts) : [],
                'arCityIDs'                     => in_array($question->main_question_type,
                    [FoquzQuestion::TYPE_ADDRESS, FoquzQuestion::TYPE_FORM],
                    true) && $question->addressCodes ? json_decode($question->addressCodes->cities) : [],
                'arStreetsIDs'                  => in_array($question->main_question_type,
                    [FoquzQuestion::TYPE_ADDRESS, FoquzQuestion::TYPE_FORM],
                    true) && $question->addressCodes ? json_decode($question->addressCodes->streets) : [],
                'placeholderText'               => $question->placeholder_text,
                'selectPlaceholderText'         => $question->select_placeholder_text,
                'selfVariantPlaceholderText'    => $question->self_variant_placeholder_text,
                'chooseType'                    => $question->choose_type,
                'smiles'                        => $question->main_question_type === FoquzQuestion::TYPE_SMILE_RATING ? $question->questionSmiles : [],
                'chooseMedia'                   => $question->main_question_type === FoquzQuestion::TYPE_CHOOSE_MEDIA ? $question->grabChooseContent($question->random_variants_order) : [],
                'maskConfig'                    => $question->grabMaskAnswers($answerItem),
                'npsRatingSetting'              => $question->main_question_type === FoquzQuestion::TYPE_NPS_RATING && $question->npsRatingSetting ? $question->npsRatingSetting : null,
                'differentialRows'              => $question->main_question_type === FoquzQuestion::TYPE_SEM_DIFFERENTIAL && $question->differentialRows ? $question->differentialRows : [],
                'semDifSetting'                 => $question->main_question_type === FoquzQuestion::TYPE_SEM_DIFFERENTIAL && $question->semDifSetting ? $question->semDifSetting : null,
                'matrixElements'                => $matrixElements,
                'reorder_required'              => $question->main_question_type === FoquzQuestion::TYPE_PRIORITY && $question->foquzQuestionPrioritySettings ? $question->foquzQuestionPrioritySettings->reorder_required : false,
                'matrixSettings'                => $question->grabMatrixSettings($question->random_variants_order),
                'showQuestion'                  => ($question->dont_show_if_answered && $answer && $answer->client) ? $answer->client->hasAnswersForQuestion($question->id) : true,
                'questionLogic'                 => $question->questionLogic,
                'questionViewLogic'             => $question->foquzPollQuestionViewLogics,
                'starRatingOptions'             => in_array($question->main_question_type,
                    [FoquzQuestion::TYPE_STAR_RATING, FoquzQuestion::TYPE_VARIANT_STAR, FoquzQuestion::TYPE_RATING],
                    true) ? $question->starRatingOptions : [],
                'min_choose_variants'           => $question->min_choose_variants,
                'max_choose_variants'           => $question->max_choose_variants,
                'self_variant_text'             => $question->self_variant_text,
                'intermediateBlock'             => (int) $question->main_question_type == FoquzQuestion::TYPE_INTERMEDIATE_BLOCK
                    ? $question->intermediateBlock
                    : null,
                'dropdownVariants'              => $question->dropdown_variants,
                'only_date_month'               => $question->only_date_month,
                'random_variants_order'         => $question->random_variants_order,
                'wrongCondition'                => false,
                // Тут стоит false, поскольку раньше отсеивались вопросы, несоответствующие типу доставки для автоматических опросов, решили пока не делать этот функционал
                'endScreenImages'               => $question->foquzQuestionEndScreenLogos,
                'skip'                          => $question->skip,
                'skip_text'                     => $question->skip_text,
                'skip_variant'                  => $question->skip_variant,
                'skip_row'                      => $question->skip_row,
                'skip_column'                   => $question->skip_column,
                'show_tooltips'                 => $question->show_tooltips,
                'skipped'                       => $answerItem->skipped ?? 0,
                'show_numbers'                  => $question->show_numbers,
                'show_labels'                   => $question->show_labels,
                'fromOne'                       => $question->from_one,
                'donor'                         => $question->donor,
                'donor_rows'                    => $question->donor_rows,
                'donor_columns'                 => $question->donor_columns,
                'donorSelected'                 => $question->donor_chosen,
                'donorColumnsSelected'          => $question->donor_cols_chosen,
                'dictionary_id'                 => $question->dictionary_id,
                'dictionary_element_id'         => $question->dictionary_element_id,
                'dictionary_list_type'          => $question->dictionary_list_type,
                'dictionary_sort'               => $question->dictionary_sort,
                'set_variants'                  => $question->set_variants,
                'scaleRatingSetting'            => (($question->main_question_type === FoquzQuestion::TYPE_SCALE && $question->scaleRatingSetting)
                    || $question->main_question_type === FoquzQuestion::TYPE_DISTRIBUTION_SCALE) ? $question->scaleRatingSetting : [],
                'isHaveExtra'                   => !empty($question->detail_question),
                'extra_required'                => $question->extra_required,
                'disable_select_category'       => $question->disable_select_category,
                'variants_with_files'           => $question->variants_with_files,
                'self_variant_comment_required' => $question->self_variant_comment_required,
                'self_variant_nothing'          => $question->self_variant_nothing,
                'self_variant_description'      => $question->self_variant_description,
                'extra_question_type'           => $question->extra_question_type,
                'extra_question_rate_from'      => $question->extra_question_rate_from,
                'extra_question_rate_to'        => $question->extra_question_rate_to,
                'detail_question'               => $question->detail_question,
                'self_variant_file'             => $question->selfVariantFile,
                'min_choose_extra_variants'     => $question->min_choose_extra_variants,
                'max_choose_extra_variants'     => $question->max_choose_extra_variants,
                'hasPoints'                     => $isPointSystem && $question->getMaxPoints() > 0 ? 1 : 0,
                'tooltip'                       => $question->tooltip,
            ];
            $row["langs"] = $question->foquzQuestionLangs;

            if ($question->main_question_type==FoquzQuestion::TYPE_FILIAL) {
                $row['dictionaryTree'] =  $question->getFilialsTree(false);
            }
            if ($question->main_question_type==FoquzQuestion::TYPE_FILIAL) {
                $row['dictionaryTree'] =  $question->getFilialsTree(false);
            }

            if ($question->main_question_type==FoquzQuestion::TYPE_CARD_SORTING_CLOSED) {
                $row['random_categories_order'] = $question->random_categories_order;
                $row['card_column_text'] = $question->card_column_text;
                $row['category_column_text'] = $question->category_column_text;
                $row['cardSortingCategories'] =  $question->cardSortingCategories;
            }

            if ($row['intermediateBlock']) {
                // если система баллов выключена отключить кнопку Отчет о тестировании в конечном экране
                if (!$isPointSystem) {
                    $row['intermediateBlock']['scores_button'] = 0;
                }
                // для блоков типа Тест 5сек вывести флаг показа картинки
                if ($question->intermediateBlock->screen_type === FoquzQuestionIntermediateBlockSetting::SCREEN_TYPE_TEST5S) {
                    /** @var FoquzPollAnswerShowedImage $showedImage */
                    $row['image_showed'] =  0;
                    if ($answer) {
                        $showedImage = $answer->getShowedImage()->andWhere(['foquz_question_id' => $question->id])->one();
                        $row['image_showed'] = $showedImage ? $showedImage->showed : 0;
                    }
                }
            }

            /*
              пока убираем лишние переводы для vue прохождения так, чтобы не сломать старое прохождение
              после перехода надо будет пройти и убрать везде при формировании, чтобы не было лишних запросов
              fixme
            */
            /*
                        if ($row["intermediateBlock"]) {
                            $row["intermediateBlock"] = ArrayHelper::toArray($row["intermediateBlock"]);
                            unset($row["intermediateBlock"]["langs"]);
                        }

                        if ($row["detail_answers"]) {
                            foreach ($row["detail_answers"] as $key => $value) {
                                if (isset($value["detailLangs"])) {
                                    unset($row["detail_answers"][$key]["detailLangs"]);
                                }
                            }
                        }
            */

            if ($vue && $question->main_question_type == FoquzQuestion::TYPE_SIMPLE_MATRIX && !empty($question->donor)) {
                $row = self::postFormatSimpleMatrixRecipient($question, $row);
            }

            if ($vue && !empty($question->donor) && $question->mainDonor->main_question_type == FoquzQuestion::TYPE_DICTIONARY) {
                $row = self::postFormatRecipientFromDictionary($row);
            }

            if ($vue && $question->main_question_type == FoquzQuestion::TYPE_3D_MATRIX && (!empty($question->donor_rows) || !empty($question->donor_columns))) {
                $row = self::postFormatRecipient3DMatrixFromDictionary($question, $row);
            }


            $result[] = $row;
        }
        return $result;
    }
}