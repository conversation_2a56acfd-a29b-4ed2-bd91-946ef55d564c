<?php
declare(strict_types=1);

namespace app\modules\foquz\services\answers\stat\questions;

use app\modules\foquz\models\FoquzQuestion;

/** Статистика для типов вопросов Текстовый и Адрес. */
class TextQuestionStat extends QuestionStat
{
    private array $answers = [];
    private string $key = 'answers';
    private ?string $subKey = null;

    public function __construct(FoquzQuestion $question)
    {
        if ($question->main_question_type == FoquzQuestion::TYPE_ADDRESS) {
            $this->key = 'addresses';
            $this->subKey = 'address';
        }
        parent::__construct($question);
    }

    public function getItemFields(): array
    {
        return [QuestionStat::FIELD_ANSWER];
    }

    public function countItem($item): void
    {
        if (!empty($item[QuestionStat::FIELD_ANSWER])) {
            $this->answers[] = empty($this->subKey) ? '' : [$this->subKey => 1];
        }
        if (!empty($item[QuestionStat::FIELD_ANSWER]) || $item[QuestionStat::FIELD_SKIPPED]) {
            $this->answersCount++;
        }
    }

    protected function getData(): array
    {
        return ['statistics' => [$this->key => $this->answers]];
    }
}