<?php


namespace app\modules\foquz\models;

use yii\base\Model;
use yii\web\UploadedFile;

/**
 * UploadForm is the model behind the upload form.
 */
class UploadForm extends Model
{

    public const SCENARIO_AUDIO = 'audio';
    public const SCENARIO_PICTURE = 'picture';
    public const SCENARIO_SMALL_PICTURE = 'small_picture';
    public const SCENARIO_VIDEO = 'video';
    public const SCENARIO_PICTURE_FIRST_CLICK = 'picture_first_click';

    public const AUDIO_EXTENSIONS = [
        'mp3',
        'ogg',
        'wav',
        'm4a',
        'aac',
        'amr',
    ];

    public const PICTURE_EXTENSIONS = [
        'jpeg',
        'jpg',
        'png',
        'gif',
        'heic',
        'webp',
    ];

    public const VIDEO_EXTENSIONS = [
        'mp4',
        'wmv',
        'mov',
        '3gp',
        'flv',
        'webm',
        'avi',
    ];

    /**
     * @var UploadedFile file attribute
     */
    public $file;
    public int|null $company_filesize_limit = null;

    /**
     * @return array the validation rules.
     */
    public function rules()
    {
        return [
            [['file'], 'file', 'extensions' => self::PICTURE_EXTENSIONS,
                'checkExtensionByMimeType' => false,
                'maxFiles' => 1,
                'tooBig' => \Yii::t('validation', "Размер файла не должен превышать " . ($this->getMaxSize()/1024/1024) . " Мб"),
                'maxSize' => $this->getMaxSize(),
                'on' => self::SCENARIO_PICTURE],
            [['file'], 'file', 'extensions' => self::PICTURE_EXTENSIONS,
                'checkExtensionByMimeType' => false,
                'maxFiles' => 1,
                'tooBig' => \Yii::t('validation', "Размер файла не должен превышать 5 Мб"),
                'maxSize' => 5*1024*1024, 'on' => self::SCENARIO_SMALL_PICTURE],
            [['file'], 'file', 'extensions' => ['jpg','jpeg','png','gif','svg',],
                'checkExtensionByMimeType' => false,
                'maxFiles' => 1,
                'tooBig' => \Yii::t('validation', "Размер файла не должен превышать 5 Мб"),
                'maxSize' => 5*1024*1024, 'on' => self::SCENARIO_PICTURE_FIRST_CLICK],
            [['file'], 'file', 'extensions' => self::AUDIO_EXTENSIONS,
                'checkExtensionByMimeType' => false,
                'maxFiles' => 1,
                'tooBig' => \Yii::t('validation', "Размер файла не должен превышать " . ($this->getMaxSize()/1024/1024) . " Мб"),
                'maxSize' => $this->getMaxSize(),
                'on' => self::SCENARIO_AUDIO],
            [['file'], 'file', 'extensions' => self::VIDEO_EXTENSIONS,
                'checkExtensionByMimeType' => false,
                'maxFiles' => 1,
                'tooBig' => \Yii::t('validation', "Размер файла не должен превышать " . ($this->getMaxSize()/1024/1024) . " Мб"),
                'maxSize' => $this->getMaxSize(),
                'on' => self::SCENARIO_VIDEO],
        ];
    }

    private function getMaxSize(): int
    {
        if (is_null($this->company_filesize_limit)) {
            return 15 * 1024 * 1024;
        }
        return $this->company_filesize_limit * 1024 * 1024;
    }
}
