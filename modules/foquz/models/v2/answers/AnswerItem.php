<?php

namespace app\modules\foquz\models\v2\answers;

use app\components\helpers\DictionariesHelper;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionDetail;
use Yii;
use yii\helpers\ArrayHelper;

class AnswerItem
{
    /** @var FoquzPollAnswer */
    private $answer;
    /** @var FoquzPollAnswerItem|null  */
    private $answerItem;
    /** @var FoquzPollAnswerItem|null  */
    private $donorAnswerItem;
    /** @var AnswersResponse  */
    private $answersResponse;
    /** @var FoquzQuestion */
    private $question;
    /** @var array|string|null  */
    private $answerResponse = null;
    /** @var string|null */
    private $commentResponse = null;
    /** @var string|null */
    private $matrixTypeResponse = null;


    public function __construct(int $questionID, FoquzPollAnswer $answer, AnswersResponse $answersResponse)
    {
        $this->answer = $answer;
        $answerItems = ArrayHelper::index($this->answer->foquzAnswer, 'foquz_question_id');
        $this->answerItem = $answerItems[$questionID] ?? null;
        $this->answersResponse = $answersResponse;
        $this->question = $answersResponse->getQuestion($questionID);
        if ($donor = $this->getMainDonor()) {
            $this->donorAnswerItem = $answerItems[$donor->id] ?? null;
        }
        $this->buildResponse();
    }

    private function buildResponse(): void
    {
        if (empty($this->answerItem)) {
            return;
        }
        switch ($this->question->main_question_type) {
            case FoquzQuestion::TYPE_VARIANTS:
                $this->typeVariants();
                break;
            case FoquzQuestion::TYPE_SIMPLE_MATRIX:
                $this->typeSimpleMatrix();
                break;
            case FoquzQuestion::TYPE_FILE_UPLOAD:
                $this->typeFileUpload();
                break;
            case FoquzQuestion::TYPE_FORM:
                $this->typeForm();
                break;
            case FoquzQuestion::TYPE_DATE:
                $this->typeDate();
                break;
            case FoquzQuestion::TYPE_PRIORITY:
                $this->typePriority();
                break;
            case FoquzQuestion::TYPE_GALLERY_RATING:
                $this->typeGalleryRating();
                break;
            case FoquzQuestion::TYPE_CHOOSE_MEDIA:
                $this->typeChooseMedia();
                break;
            case FoquzQuestion::TYPE_SMILE_RATING:
                $this->typeSmileRating();
                break;
            case FoquzQuestion::TYPE_RATING:
                $this->typeRating();
                break;
            case FoquzQuestion::TYPE_STAR_RATING:
                $this->typeStarRating();
                break;
            case FoquzQuestion::TYPE_VARIANT_STAR:
                $this->typeVariantStar();
                break;
            case FoquzQuestion::TYPE_SCALE:
                $this->typeScale();
                break;
            case FoquzQuestion::TYPE_SEM_DIFFERENTIAL:
                $this->typeSemDifferential();
                break;
            case FoquzQuestion::TYPE_NPS_RATING:
                $this->typeNPSRating();
                break;
            case FoquzQuestion::TYPE_FILIAL:
                $this->typeFilial();
                break;
            case FoquzQuestion::TYPE_DICTIONARY:
                $this->typeDictionary();
                break;
            case FoquzQuestion::TYPE_3D_MATRIX:
                $this->type3DMatrix();
                break;
            case FoquzQuestion::TYPE_TEXT_ANSWER:
                $this->typeTextAnswer();
                break;
            case FoquzQuestion::TYPE_ADDRESS:
                $this->typeAddress();
                break;
        }
        $this->commentResponse = $this->getComment();
    }

    private function getMainDonor(): ?FoquzQuestion
    {
        if (!$this->question->donor) {
            return null;
        }
        $donor = $this->answersResponse->getQuestion($this->question->donor);
        while ($donor->donor && $donor->id !== $donor->donor) {
            $donor = $this->answersResponse->getQuestion($this->question->donor);
        }
        return $donor;
    }

    private function typeVariants(): void
    {
        $answer = [];
        if (!$this->answerItem->detail_item || is_string($this->answerItem->detail_item)) {
            $this->answerItem->detail_item = json_decode($this->answerItem->detail_item, true) ?? [];
        }

        foreach ($this->answerItem->detail_item as $id) {
            $id = (int)$id;
            if ($id === -1) {
                $detail = new FoquzQuestionDetail();
                $detail->id = -1;
                $detail->question = $this->donorAnswerItem->self_variant ?: null;
                $title = $detail->question;
            } elseif ($this->getMainDonor() && $this->getMainDonor()->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                $detail = $this->answersResponse->getDictionaryElement($id);
                $title = $detail->path;
            } else {
                $detail = $this->answersResponse->getDetail($id);
                $title = $detail ? $detail->question : null;
            }

            if (!empty($detail->dictionary_element_id)) {
                $dictionaryLink = $this->getDictionaryLink($detail->dictionary_element_id);
            }

            if (!empty($detail->file)) {
                $file = [
                    'title' => $detail->file->origin_name,
                    'url' => Yii::$app->params['protocol'] . '://' . $this->answersResponse->getAlias() . $detail->file->fileUrl,
                ];
            }

            $answer[] = [
                'title' => $title,
                'dictionary_link' => $dictionaryLink ?? null,
                'file' => $file ?? null,
            ];
        }
        if (!empty($this->answerItem->self_variant)) {
            $answer[] = [
                'title' => $this->answerItem->self_variant,
                'dictionary_link' => null,
            ];
        }
        $this->answerResponse = $answer;
    }

    private function typeSimpleMatrix(): void
    {
        $decodedAnswer = json_decode($this->answerItem->answer, true) ?? [];
        $matrixSetting = json_decode($this->question->matrix_settings, true) ?? [];
        $answer = [];
        foreach ($decodedAnswer as $row => $value) {
            $rowsDictionaryID = $matrixSetting['rows_dictionary'][$row] ?? null;
            if ($donor = $this->getMainDonor()) {
                if ($row == -1) {
                    $row = $this->donorAnswerItem->self_variant ?: '';
                } elseif ($donor->main_question_type !== FoquzQuestion::TYPE_DICTIONARY) {
                    $row = $this->answersResponse->getDetail($row)->question;
                } else {
                    $row = $this->answersResponse->getDictionaryElement($row)->path;
                }
            }
            $clarifyingQuestionAnswer = null;
            $detailItem = $this->answerItem->detail_item;
            if (!is_array($detailItem)) {
                $detailItem = json_decode($detailItem, true) ?? [];
            }
            if ($this->question->detail_question && $this->question->variants_element_type === 2) {
                $clarifyingQuestionAnswer = !empty($detailItem[$row]['answer']) ?
                    $detailItem[$row]['answer'] : null;
            } elseif ($this->question->detail_question) {
                $clarifyingQuestionAnswer = [];
                foreach ($detailItem[$row] ?? [] as $clarifyingQuestionKey => $clarifyingQuestionValue) {
                    if ($clarifyingQuestionKey === 'self_variant') {
                        $clarifyingQuestionAnswer[] = $clarifyingQuestionValue;
                        continue;
                    }
                    if (!is_numeric($clarifyingQuestionValue)) {
                        continue;
                    }
                    $clarifyingQuestionAnswer[] = $this->answersResponse->getDetail((int)$clarifyingQuestionValue)->question;
                }
            }

            if (!empty($rowsDictionaryID)) {
                $dictionaryLink = [
                    'id' => $rowsDictionaryID,
                    'title' => $this->answersResponse->getDictionaryElement($rowsDictionaryID)->title,
                ];
            } else {
                $dictionaryLink = null;
            }

            $answer[] = [
                'title' => $row,
                'values' => $value,
                'clarifying_question' => [
                    'value' => !empty($clarifyingQuestionAnswer) ? $clarifyingQuestionAnswer : null,
                ],
                'dictionary_link' => $dictionaryLink,
            ];
        }
        $this->answerResponse = $answer;
        if (!empty($matrixSetting['type']) && $matrixSetting['type'] === 'importance') {
            $this->matrixTypeResponse = 'Важность';
        } elseif (!empty($matrixSetting['type']) && $matrixSetting['type'] === 'rating') {
            $this->matrixTypeResponse = 'Оценка';
        } else {
            $this->matrixTypeResponse = 'Стандарт';
        }
    }

    private function typeFileUpload(): void
    {
        $answer = [];
        $files = $this->answerItem->answerItemFiles;
        foreach ($files as $file) {
            $answer[] = [
                'name' => $file->origin_name,
                'size' => round((filesize($file->file_full_path) / 1000), 2),
                'url' => Yii::$app->params['protocol'] . '://' .  $this->answersResponse->getAlias() . $file->file_path,
            ];
        }
        $this->answerResponse = [
            'count' => count($files),
            'items' => $answer,
        ];
    }

    private function typeForm(): void
    {
        $answer = [];
        $decodedAnswer = json_decode($this->answerItem->answer, true) ?? [];
        foreach ($decodedAnswer as $id => $value) {
            $field = $this->answersResponse->getFormField($id);
            if (is_array($value)) {
                $value = [
                    'last_name' => $value['surname'] ?: null,
                    'first_name' => $value['name'] ?: null,
                    'patronymic' => $value['patronymic'] ?: null,
                ];
                if (empty($value['last_name']) && empty($value['first_name']) && empty($value['patronymic'])) {
                    $value = null;
                }
            }
            $answer[] = [
                'title' => $field->name,
                'value' => $value ?: null,
            ];
        }
        if (empty(ArrayHelper::getColumn($answer, 'value'))) {
            $answer = null;
        }
        $this->answerResponse = $answer;
    }

    private function typeDate(): void
    {
        $decodedAnswer = json_decode($this->answerItem->answer, true) ?? [];
        $answer = implode(' ', array_filter(array_map(static function ($value) {
            return str_replace(' ', '', $value) ?: null;
        }, $decodedAnswer)));
        $this->answerResponse = $answer;
    }

    private function typePriority(): void
    {
        $answer = [];
        if (!empty($this->answerItem->detail_item) && is_string($this->answerItem->detail_item)) {
            $decodedAnswer = json_decode($this->answerItem->detail_item) ?? [];
        } elseif (!empty($this->answerItem->detail_item) && is_array($this->answerItem->detail_item)) {
            $decodedAnswer = $this->answerItem->detail_item;
        } else {
            $this->answerResponse = json_decode($this->answerItem->answer) ?? [];
            return;
        }
        foreach ($decodedAnswer as $id) {
            $id = (int)$id;
            if ($id === -1) {
                $detail = new FoquzQuestionDetail();
                $title = $this->donorAnswerItem->self_variant ?: null;
            } elseif ($this->getMainDonor() && $this->getMainDonor()->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                $detail = $this->answersResponse->getDictionaryElement($id);
                $title = $detail->path;
            } else {
                $detail = $this->answersResponse->getDetail($id);
                $title = $detail->question;
            }

            if (!empty($detail->dictionary_element_id)) {
                $dictionaryLink = $this->getDictionaryLink($detail->dictionary_element_id);
            }

            $answer[] = [
                'title' => $title,
                'dictionary_link' => $dictionaryLink ?? null,
            ];
        }
        $this->answerResponse = $answer;
    }

    private function typeGalleryRating(): void
    {
        $decodedAnswer = json_decode($this->answerItem->answer) ?? [];
        $answer = [];
        $i = 1;
        foreach ($decodedAnswer as $id => $value) {
            $file = $this->answersResponse->getQuestionFile($id);
            $answer[] = [
                'title' => (string) ($file->description ?: $file->position ?: $i),
                'url' => $file->getLinkWithAlias($this->answersResponse->getAlias()),
                'value' => (int)$value,
            ];
            $i++;
        }
        $this->answerResponse = $answer;
    }

    private function typeChooseMedia(): void
    {
        $decodedAnswer = json_decode($this->answerItem->answer) ?? [];
        $answer = [];
        $i = 1;
        foreach ($decodedAnswer as $id) {
            $file = $this->answersResponse->getQuestionFile($id);
            $answer[] = [
                'title' => (string) $file->description ?: $file->position ?: $i,
                'url' => $file->getLinkWithAlias($this->answersResponse->getAlias()),
            ];
            $i++;
        }
        $this->answerResponse = $answer;
    }

    private function typeSmileRating(): void
    {
        $count = $this->question->smiles_count ?: count($this->answersResponse->getQuestionSmiles($this->question->id));
        if ($this->answerItem->answer) {
            $smile = $this->answersResponse->getSmile($this->answerItem->answer);
        }
        $this->answerResponse = [
            'value' => $this->answerItem->rating ?: null,
            'max' => $count,
            'mark' => !empty($smile->label) ? $smile->label : null,
        ];
    }

    private function typeRating(): void
    {
        $this->typeStarRating();
    }

    private function typeStarRating(): void
    {
        $clarifyingQuestionAnswer = null;
        if ($this->question->detail_question && $this->question->variants_element_type === 2) {
            $clarifyingQuestionAnswer = $this->answerItem->answer ?: null;
        } elseif ($this->question->detail_question) {
            if (!is_array($this->answerItem->detail_item)) {
                $this->answerItem->detail_item = json_decode($this->answerItem->detail_item, true) ?? [];
            }
            $clarifyingQuestionAnswer = [];
            foreach ($this->answerItem->detail_item as $id) {
                if (!is_numeric($id)) {
                    continue;
                }
                $clarifyingQuestionAnswer[] = $this->answersResponse->getDetail((int)$id)->question;
            }
            if ($this->answerItem->self_variant) {
                $clarifyingQuestionAnswer[] = $this->answerItem->self_variant;
            }
        }
        $this->answerResponse = [
            'value' => (int)$this->answerItem->rating ?: null,
            'clarifying_question' => [
                'value' => !empty($clarifyingQuestionAnswer) ? $clarifyingQuestionAnswer : null,
            ],
        ];
    }

    private function typeVariantStar(): void
    {
        $answer = [];
        $decodedAnswer = json_decode($this->answerItem->answer, true) ?? [];
        foreach ($decodedAnswer as $id => $value) {
            if (!is_numeric($id)) {
                continue;
            }
            $id = (int)$id;
            if ($id === -1) {
                $detail = new FoquzQuestionDetail();
                $detail->id = -1;
                $title = $this->donorAnswerItem->self_variant ?: null;
            } elseif ($this->getMainDonor() && $this->getMainDonor()->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                $detail = $this->answersResponse->getDictionaryElement($id);
                $title = $detail->path;
            } else {
                $detail = $this->answersResponse->getDetail($id);
                $title = $detail->question;
            }
            $clarifyingQuestionAnswer = null;
            if ($this->question->detail_question && $this->question->variants_element_type === 2) {
                $clarifyingQuestionAnswer = !empty($decodedAnswer['extra'][$id]['answer']) ?
                    $decodedAnswer['extra'][$id]['answer'] : null;
            } elseif ($this->question->detail_question) {
                $clarifyingQuestionAnswer = [];
                foreach ($decodedAnswer['extra'][$id] ?? [] as $clarifyingQuestionKey => $clarifyingQuestionValue) {
                    if ($clarifyingQuestionKey === 'self_variant') {
                        $clarifyingQuestionAnswer[] = $clarifyingQuestionValue;
                        continue;
                    }
                    if (!is_numeric($id)) {
                        continue;
                    }
                    $clarifyingQuestionAnswer[] = $this->answersResponse->getDetail((int)$clarifyingQuestionValue)->question;
                }
            }

            if (!empty($detail->dictionary_element_id)) {
                $dictionaryLink = $this->getDictionaryLink($detail->dictionary_element_id);
            }

            $answer[] = [
                'title' => $title,
                'value' => !empty($value) && (int)$value !== -1 ? (int) $value : null,
                'clarifying_question' => [
                    'value' => !empty($clarifyingQuestionAnswer) ? $clarifyingQuestionAnswer : null,
                ],
                'dictionary_link' => $dictionaryLink ?? null,
            ];
        }
        $this->answerResponse = $answer;
    }

    private function typeScale(): void
    {
        $nps = $this->question->main_question_type === FoquzQuestion::TYPE_NPS_RATING;
        if (!$this->question->set_variants) {
            $rating = (int)$this->answerItem->rating;
            if ($nps && ($rating === -1 || $this->answerItem->rating === null)) {
                $this->answerResponse = ['value' => null];
                return;
            }
            $this->answerResponse = ['value' => $rating];
            return;
        }
        $answer = [];
        $decodedAnswer = json_decode($this->answerItem->answer, true) ?? [];

        foreach ($decodedAnswer as $id => $value) {
            if (!is_numeric($id)) {
                continue;
            }
            $id = (int)$id;
            if ($id === -1) {
                $detail = new FoquzQuestionDetail();
                $detail->id = -1;
                $detail->question = $this->donorAnswerItem->self_variant ?: null;
                $title = $detail->question;
            } elseif ($this->getMainDonor() && $this->getMainDonor()->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                $detail = $this->answersResponse->getDictionaryElement($id);
                $title = $detail->path;
            } else {
                $detail = $this->answersResponse->getDetail($id);
                $title = $detail->question;
            }
            $rating = (int)$value;
            if ($nps && ($rating === -1 || $value === null)) {
                $rating = null;
            }

            if (!empty($detail->dictionary_element_id)) {
                $dictionaryLink = $this->getDictionaryLink($detail->dictionary_element_id);
            }

            $answer[] = [
                'title' => $title,
                'value' => $rating,
                'dictionary_link' => $dictionaryLink ?? null,
            ];
        }
        $this->answerResponse = $answer;
    }

    private function typeNPSRating(): void
    {
        $this->typeScale();
    }

    private function typeFilial(): void
    {
        $filial_id = !empty($this->answerItem->detail_item[0]) ? $this->answerItem->detail_item[0] : null;
        if (!$filial_id) {
            $this->answerResponse = null;
            return;
        }
        $this->answerResponse = ['title' => $this->answersResponse->getFilial($filial_id)->name];
    }

    private function typeSemDifferential(): void
    {
        $answer = [];
        $decodedAnswer = json_decode($this->answerItem->answer, true) ?? [];
        foreach ($decodedAnswer as $id => $value) {
            $row = $this->answersResponse->getDifferentialRow($id);
            $answer[] = [
                'start_label' => $row->start_label ?: null,
                'end_label' => $row->end_label ?: null,
                'value' => (int)$value ?: null,
            ];
        }
        $this->answerResponse = $answer;
    }

    private function typeDictionary(): void
    {
        $tree = DictionariesHelper::buildApiV2Elements(
            $this->answersResponse->getDictionaryElements(),
                $this->answerItem->detail_item ?? []
        );
        $this->answerResponse = $tree;
    }

    private function type3DMatrix()
    {
        $answer = [];
        $decodedAnswer = json_decode($this->answerItem->answer, true) ?? [];
        $skipText = $this->question->skip_text ?: 'Затрудняюсь ответить';
        foreach ($decodedAnswer as $row => $columns) {
            $rowAnswer = [];
            foreach ($columns as $column => $value) {
                $answerValues = [];
                if (is_array($value)) {
                    foreach ($value as $item) {
                        if ((int)$item === -1) {
                            $answerValues[] = $skipText;
                            continue;
                        }
                        $answerValues[] = $this->answersResponse->getMatrixVariant($item)->name;
                    }
                } elseif ($value === 'null' || $value === '') {
                    $answerValues[] = $skipText;
                }
                $rowAnswer[] = [
                    'title' => $this->answersResponse->getMatrixElement($column)->name,
                    'values' => $answerValues,
                ];
            }
            $answer[] = [
                'title' => $this->answersResponse->getMatrixElement($row)->name,
                'values' => $rowAnswer,
            ];
        }
        $this->answerResponse = $answer;
    }

    private function typeTextAnswer(): void
    {
        $answer = json_decode($this->answerItem->answer) ?? [];
        if (empty($answer) || !is_object($answer)) {
            $answer = $this->answerItem->answer;
        }
        if (is_object($answer)) {
            $answer = [
                'last_name' => $answer->surname ?: null,
                'first_name' => $answer->name ?: null,
                'patronymic' => $answer->patronymic ?: null,
            ];
            if (empty($answer['last_name']) && empty($answer['first_name']) && empty($answer['patronymic'])) {
                $this->answerResponse = null;
                return;
            }
        }
        $this->answerResponse = ['value' => $answer];
    }

    private function typeAddress(): void
    {
        $this->answerResponse = $this->answerItem->answer;
    }

    private function getDictionaryLink($id): ?array
    {
        if (!$id) {
            return null;
        }
        return [
            'id' => $id,
            'title' => $this->answersResponse->getDictionaryElement($id)->title,
        ];
    }

    private function getQuestionDictionaryLink(): ?array
    {
        return $this->getDictionaryLink($this->question->dictionary_element_id);
    }

    private function getComment(): ?string
    {
        switch($this->question->main_question_type) {
            case FoquzQuestion::TYPE_VARIANT_STAR:
            case FoquzQuestion::TYPE_NPS_RATING:
            case FoquzQuestion::TYPE_SMILE_RATING:
            case FoquzQuestion::TYPE_PRIORITY:
            case FoquzQuestion::TYPE_FILIAL:
            case FoquzQuestion::TYPE_GALLERY_RATING:
            case FoquzQuestion::TYPE_CHOOSE_MEDIA:
            case FoquzQuestion::TYPE_SCALE:
            case FoquzQuestion::TYPE_DICTIONARY:
            case FoquzQuestion::TYPE_SIMPLE_MATRIX:
            case FoquzQuestion::TYPE_3D_MATRIX:
                return $this->answerItem->self_variant;
            case FoquzQuestion::TYPE_FILE_UPLOAD:
            case FoquzQuestion::TYPE_RATING:
            case FoquzQuestion::TYPE_VARIANTS:
            case FoquzQuestion::TYPE_STAR_RATING:
                return $this->answerItem->answer;
            case FoquzQuestion::TYPE_SEM_DIFFERENTIAL:
                return $this->answerItem->self_variant !== '' ? $this->answerItem->self_variant : null;
            default:
                return null;
        }
    }

    public function getResponse(): array
    {
        if (
            $this->answerItem &&
            $this->answersResponse->getPoll($this->question->poll_id)->point_system &&
            in_array($this->question->main_question_type, FoquzQuestion::TYPES_POINTS)
        ) {
            $points = [
                'value' => $this->answerItem->points ?: 0,
                'max' => $this->answerItem->max_points ?: 0,
            ];
        } elseif ($this->answersResponse->getPoll($this->question->poll_id)->point_system &&
            in_array($this->question->main_question_type, FoquzQuestion::TYPES_POINTS)) {
            $points = [
                'value' => 0,
                'max' => $this->question->maxPoints,
            ];
        }
        if ($this->answerItem && ($this->answerItem->isEmpty || $this->answerItem->skipped)) {
            $this->answerResponse = null;
        }

        if ($this->answerItem && $this->answerItem->isEmpty) {
            $this->answerResponse = null;
        }

        $response = [
            'service_name' => $this->question->service_name ?: null,
            'type' => $this->question->mainTypeString,
            'question_name' => $this->question->name ?: null,
            'question_text' => $this->question->description,
            'extra_description' => trim($this->question->sub_description) !== '' ? $this->question->sub_description : null,
            'answer' => $this->answerResponse,
            'comment' => trim($this->commentResponse) !== '' ? $this->commentResponse : null,
            'dictionary_link' => $this->getQuestionDictionaryLink(),
            'points' => $points ?? null,
        ];

        return $response;
    }
}
