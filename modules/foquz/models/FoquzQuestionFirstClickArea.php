<?php

namespace app\modules\foquz\models;

use Yii;

/**
 * This is the model class for table "foquz_question_first_click_area".
 *
 * @property int $id
 * @property int|null $question_id
 * @property string|null $name Название области клика
 * @property int|null $height Высота области клика
 * @property int|null $width Ширина области клика
 * @property int|null $x_coord Координата X области клика
 * @property int|null $y_coord Координата Y области клика
 *
 * @property FoquzQuestion $question
 */
class FoquzQuestionFirstClickArea extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'foquz_question_first_click_area';
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['question_id', 'x_coord', 'y_coord'], 'integer'],
            [['name'], 'string', 'max' => 255],
            [['question_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzQuestion::class, 'targetAttribute' => ['question_id' => 'id']],
            [['height'], 'integer', 'min' => 1,  'tooSmall' => 'Число должно быть положительным',],
            [['width'], 'integer', 'min' => 1,  'tooSmall' => 'Число должно быть положительным',],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'question_id' => 'Question ID',
            'name' => 'Name',
            'height' => 'Height',
            'width' => 'Width',
            'x_coord' => 'X Coord',
            'y_coord' => 'Y Coord',
        ];
    }

    public static function createOrUpdate(array $data, int $foquz_question_id): self|null
    {
        $id = (int)$data['id'];
        if ($id === 0) {
            $m = new self();
            $m->question_id = $foquz_question_id;
        } else {
            $m = self::findOne($id);
        }
        if (!$m) {
            return null;
        }
        $m->name = $data['name'];
        $m->height = $data['height'];
        $m->width = $data['width'];
        $m->x_coord = $data['x_coord'];
        $m->y_coord = $data['y_coord'];

        return $m;
    }

    /**
     * Gets query for [[Question]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getQuestion()
    {
        return $this->hasOne(FoquzQuestion::class, ['id' => 'question_id']);
    }
}
