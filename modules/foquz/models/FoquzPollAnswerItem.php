<?php

namespace app\modules\foquz\models;

use app\components\helpers\DictionariesHelper;
use app\models\DictionaryElement;
use app\models\Filial;
use app\modules\foquz\services\api\ExtraQuestionService;
use app\modules\foquz\services\export\usecase\CardSortingClosed;
use PhpOffice\PhpSpreadsheet\RichText\RichText;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\helpers\ArrayHelper;
use yii\web\NotFoundHttpException;

/**
 * This is the model class for table "{{%foquz_poll_answer_item}}".
 *
 * @property int $id
 * @property string $created_at
 * @property int $foquz_poll_answer_id
 * @property int $foquz_question_id
 * @property string $answer
 * @property int $rating
 * @property array $detail_item
 * @property boolean $is_self_variant
 * @property string $self_variant
 * @property string $question_name
 * @property int $points
 * @property int $max_points Максимальное количество баллов за вопрос
 * @property bool $skipped
 * @property float $avg_score
 * @property string|null $points_job_id ID задачи на подсчет баллов
 *
 * @property FoquzPollAnswer $foquzPollAnswer
 * @property FoquzQuestion $foquzQuestion
 * @property FoquzPollAnswerItemFile[] $answerItemFiles
 * @property FoquzPollAnswerHiddenQuestion $hiddenQuestion
 * @property RichText | string $answerColumn
 * @property RichText | string $commentColumn
 * @property mixed $apiAnswer Ответ для API v2
 * @property mixed $answerData Ответ для API v1
 * @property string | null $commentData Комментарий для API
 * @property int|null $itemPercentScore Оценка по вопросу в процентах
 * @property bool $isEmpty Ответ пустой
 */
class FoquzPollAnswerItem extends BaseModel
{
    /** @inheritDoc */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'value' => (new \DateTime('now'))->format('Y-m-d H:i:s'),
                'updatedAtAttribute' => false,
            ]
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%foquz_poll_answer_item}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['foquz_poll_answer_id', 'foquz_question_id'], 'required'],
            [['created_at', 'answer'], 'safe'],
            [['foquz_poll_answer_id', 'foquz_question_id', 'rating', 'points', 'max_points'], 'integer'],
            [['self_variant', 'points_job_id'], 'string'],
            [['is_self_variant', 'skipped'], 'boolean'],
            [['detail_item', 'question_name'], 'safe'],
            [['foquz_poll_answer_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzPollAnswer::className(), 'targetAttribute' => ['foquz_poll_answer_id' => 'id']],
            [['foquz_question_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzQuestion::className(), 'targetAttribute' => ['foquz_question_id' => 'id']],
        ];
    }

    public function fields()
    {
        $fields = parent::fields();
        return $fields;
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'created_at' => 'Created At',
            'foquz_poll_answer_id' => 'Foquz Poll Answer ID',
            'foquz_question_id' => 'Foquz Question ID',
            'answer' => 'Asnwer',
            'is_self_variant' => 'Свой вариант',
            'skipped' => 'Пропущен',
            'points_job_id' => 'ID задачи на подсчет баллов',
            'max_points' => 'Максимальное количество баллов за вопрос',
        ];
    }

    public function beforeSave($insert)
    {
        if (in_array($this->foquzQuestion->main_question_type, [
            FoquzQuestion::TYPE_VARIANT_STAR,
            FoquzQuestion::TYPE_GALLERY_RATING,
            FoquzQuestion::TYPE_SEM_DIFFERENTIAL,
        ], true)) {
            $scores = array_values(array_filter(ArrayHelper::toArray(json_decode($this->answer ?? '') ?? []), function ($item) {
                return is_numeric($item);
            }));
            $this->avg_score = count($scores) ? array_sum($scores) / count($scores) : 0;
        }
        return parent::beforeSave($insert);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFoquzPollAnswer()
    {
        return $this->hasOne(FoquzPollAnswer::className(), ['id' => 'foquz_poll_answer_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFoquzQuestion()
    {
        return $this->hasOne(FoquzQuestion::className(), ['id' => 'foquz_question_id']);
    }


    public function getAverageRating()
    {
        $question = $this->foquzQuestion;
        if ($question->service_name == 'Товар' && $question->is_system) {
            $scores = [];
            $s = 0;
            foreach ($this->foquzPollAnswer->foquzPollDishes as $dish) {
                $s += intval($dish->score);
            }
            return round($s / count($this->foquzPollAnswer->foquzPollDishes));
        } else return $this->rating;
    }

    public function getAnswerItemFiles()
    {
        return $this->hasMany(FoquzPollAnswerItemFile::class, ['foquz_poll_answer_item_id' => 'id']);
    }


    /** Возвращает ответ ввиде столбца (Excel)
     *
     * @param bool $forGoogle
     * @return RichText|string
     * @throws NotFoundHttpException
     */
    public function getAnswerColumn($forGoogle = false)
    {
        $skipText = in_array($this->foquzQuestion->main_question_type, [
            FoquzQuestion::TYPE_VARIANTS,
            FoquzQuestion::TYPE_SIMPLE_MATRIX,
            FoquzQuestion::TYPE_3D_MATRIX,
            FoquzQuestion::TYPE_CHOOSE_MEDIA,
            FoquzQuestion::TYPE_DICTIONARY,
            FoquzQuestion::TYPE_CARD_SORTING_CLOSED,
            FoquzQuestion::TYPE_DISTRIBUTION_SCALE,
            FoquzQuestion::TYPE_TEXT_ANSWER
        ], true) ? 'Затрудняюсь ответить' : 'Не готов(а) оценить';
        $skipText = $this->foquzQuestion->skip_text ?: $skipText;
        if ($this->skipped) {
            return $skipText;
        }
        $richText = new RichText();
        switch ($this->foquzQuestion->main_question_type) {
            case FoquzQuestion::TYPE_ASSESSMENT:
                switch ($this->foquzQuestion->rating_type) {
                    case FoquzQuestion::RATING_TYPE_START:
                        return $this->rating;
                    case FoquzQuestion::RATING_OPTIONS:
                        $answer = [];
                        if ($this->detail_item && is_string($this->detail_item)) {
                            $this->detail_item = @json_decode($this->detail_item, true);
                        }
                        if (is_array($this->detail_item) && count($this->detail_item)) {
                            foreach ($this->detail_item as $id) {
                                $detail = FoquzQuestionDetail::findOne($id);
                                if ($detail) {
                                    $answer[] = $detail->question;
                                }
                            }
                        }
                        if ($this->self_variant) {
                            $answer[] = $this->self_variant;
                        }
                        return implode("\n", $answer);
                    case FoquzQuestion::RATING_DISHES:
                        $textArray = [];
                        foreach ($this->foquzPollAnswer->getFoquzPollDishes()->orderBy('score desc')->all() as $dishScore) {
                            if ($this->foquzPollAnswer->order) {
                                if ($forGoogle) {
                                    $textArray[] = $dishScore->dish->name . ': ' . $dishScore->score;
                                } else {
                                    $richText->createText($dishScore->dish->name . ': ');
                                    $payable = $richText->createTextRun($dishScore->score);
                                    $payable->getFont()->setBold(true);
                                    $richText->createText("\n");
                                }
                            }
                        }
                        return $forGoogle ? implode("\n", $textArray) : $richText;
                }
                break;
            case FoquzQuestion::TYPE_FILIAL:
                $filial = Filial::findOne($this->detail_item);
                if ($filial) {
                    return $filial->name;
                }
                break;
            case FoquzQuestion::TYPE_VARIANTS:
                $answer = [];
                if (is_string($this->detail_item) && $this->detail_item) $this->detail_item = @json_decode($this->detail_item, true);
                if (is_array($this->detail_item) && count($this->detail_item)) {
                    foreach ($this->detail_item as $id) {
                        if (
                            $id != -1 &&
                            (
                                !$this->foquzQuestion->getMainDonor() ||
                                $this->foquzQuestion->getMainDonor()->main_question_type !== FoquzQuestion::TYPE_DICTIONARY
                            ) && $detail = FoquzQuestionDetail::findOne($id)
                        ) {
                            if ($detail && (int)$detail->type !== FoquzQuestionDetail::TYPE_NOTHING) {
                                $answer[] = $detail->question;
                            } elseif ($detail) {
                                $nothing = $detail->question;
                            }
                        } elseif ($id != -1 && $this->foquzQuestion->donor) {
                            $answer[] = DictionaryElement::findOne($id)->title ?? '';
                        } elseif ($id == -1 && $this->foquzQuestion->donor) {
                            $donorAnswer = $this->getDonorAnswer();
                            if ($donorAnswer) {
                                $answer[] = $donorAnswer->self_variant;
                            }
                        }
                    }
                }
                if (!empty($nothing)) {
                    $answer[] = $nothing;
                }
                if ($this->self_variant !== null && $this->self_variant !== '') {
                    $answer[] = $this->self_variant;
                }
                return implode("\n", $answer);
            case FoquzQuestion::TYPE_DICTIONARY:
                $answer = [];
                if ($this->detail_item) {
                    foreach ($this->detail_item as $detailId) {
                        $tree = '';
                        if ($detail = DictionaryElement::findOne($detailId)) {
                            $tree = $detail->title;
                            foreach ($detail->getParents() as $parent) {
                                $tree = $parent->title . ' > ' . $tree;
                            }
                        }
                        $answer[] = $tree;
                    }
                }
                return implode("\n", $answer);
            case FoquzQuestion::TYPE_TEXT_ANSWER:
                switch ($this->foquzQuestion->mask) {
                    case 5:
                        $answer = json_decode($this->answer ?? '');
                        return implode(' ', [$answer->surname ?? '', $answer->name ?? '', $answer->patronymic ?? '']);
                    default:
                        return $forGoogle ? trim($this->answer, '+') : $this->answer;
                }
            case FoquzQuestion::TYPE_ADDRESS:
                return $this->answer;
            case FoquzQuestion::TYPE_FILE_UPLOAD:
                $links = [];
                foreach ($this->answerItemFiles as $file) {
                    $links[] = $file->url;
                }
                $links = implode("\n", $links);
                return $links;
            case FoquzQuestion::TYPE_FORM:
                $decodedAnswer = json_decode($this->answer ?? '', true);
                $textArray = [];
                if (is_array($decodedAnswer)) {
                    foreach ($decodedAnswer as $di => $dv) {
                        $field = FoquzQuestionFormField::findOne($di);
                        $richText->createText(($field->name ?? '') . ': ');
                        if (is_array($dv) || is_object($dv)) {
                            $answer = trim(implode(' ', (array)$dv)) != '' ? trim(implode(' ', (array)$dv)) : ' - ';
                        } else {
                            $answer = $dv != '' ? $dv : ' - ';
                        }
                        $payable = $richText->createTextRun($answer);
                        $payable->getFont()->setBold(true);
                        $richText->createText("\n");
                        if ($forGoogle) {
                            $textArray[] = ($field->name ?? '') . ': ' . $answer;
                        }
                    }
                }
                return $forGoogle ? implode("\n", $textArray) : $richText;
            case FoquzQuestion::TYPE_DATE:
                if (empty($this->answer)) {
                    return '';
                }
                $answer = json_decode($this->answer);
                if (empty($answer)) return '';
                if ($answer->time == '') {
                    return $answer->date;
                } elseif ($answer->date == '') {
                    return str_replace(' ', '', $answer->time);
                } else {
                    if ($this->foquzQuestion->only_date_month) {
                        return $answer->date . ' ' . $answer->time;
                    } else {
                        return date('d.m.Y H:i', strtotime(str_replace(' ', '', $answer->date) . ' ' . str_replace(' ', '', $answer->time)));
                    }
                }
            case FoquzQuestion::TYPE_PRIORITY:
                $question = $this->foquzQuestion;
                if ($this->answer) {
                    return implode("\n", json_decode($this->answer));
                }
                if ($this->detail_item) {
                    $answers = [];
                    foreach ($this->detail_item as $item) {
                        if ($item == '-1') {
                            $answers[] = $this->getDonorAnswer()->self_variant;
                        } elseif ($question->donor && $question->getMainDonor()->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                            $answers[] = DictionaryElement::findOne($item)->fullPath;
                        } else {
                            $detail = FoquzQuestionDetail::findOne($item);
                            if ($detail && (int)$detail->type !== FoquzQuestionDetail::TYPE_NOTHING) {
                                $answers[] = $detail->question;
                            } elseif ($detail) {
                                $nothing = $detail->question;
                            }
                        }
                    }
                    if (!empty($nothing)) {
                        $answers[] = $nothing;
                    }
                    return implode("\n", $answers);
                }
                break;
            case FoquzQuestion::TYPE_VARIANT_STAR:
                if ($this->isEmpty) {
                    return '';
                }
                if ($this->skipped) {
                    return $this->foquzQuestion->skip_text ?: 'Не готов(а) оценить';
                }
                if ($this->answer) {
                    $details = json_decode($this->answer);
                    $answer = [];
                    if ($details) {
                        $donor = $this->foquzQuestion->getMainDonor();
                        foreach ($details as $detailId => $value) {
                            $value = $value === 'null' || $value === '' ? $skipText : $value;
                            if ($detailId == '-1' && $donor) {
                                $answer[] = ($donor->self_variant_text ?: 'Свой вариант') . ': ' . $value;
                            } elseif ($donor && $donor->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                                $answer[] = (DictionaryElement::findOne($detailId)->fullPath ?? '') . ': ' . $value;
                            } else if ($detail = FoquzQuestionDetail::findOne($detailId)) {
                                if ((int)$detail->type !== FoquzQuestionDetail::TYPE_NOTHING) {
                                    $answer[] = $detail->question . ': ' . $value;
                                } else {
                                    $nothing = $detail->question . ': ' . $value;
                                }
                            }
                        }
                        if (!empty($nothing)) {
                            $answer[] = $nothing;
                        }
                    }
                    return implode("\n", $answer);
                }
                break;
            case FoquzQuestion::TYPE_GALLERY_RATING:
                if ($this->answer) {
                    $jsonData = json_decode($this->answer, true);
                    $answer = [];
                    $questionFiles = FoquzQuestionFile::find()
                        ->where(['question_id' => $this->foquz_question_id])
                        ->orderBy('position')
                        ->all();
                    foreach ($questionFiles as $key => $questionFile) {
                        if (isset($jsonData[$questionFile->id])) {
                            $answer[] = 'Файл ' . ($key + 1) . ': ' . $jsonData[$questionFile->id];
                        }
                    }
                    return implode("\n", $answer);
                }
                break;
            case FoquzQuestion::TYPE_CHOOSE_MEDIA:
                if ($this->answer) {
                    $jsonData = json_decode($this->answer, true);
                    $answer = [];
                    $questionFiles = FoquzQuestionFile::find()
                        ->where(['question_id' => $this->foquz_question_id])
                        ->orderBy('position')
                        ->all();
                    foreach ($questionFiles as $key => $questionFile) {
                        if (in_array($questionFile->id, $jsonData)) {
                            $answer[] = $questionFile->type === 'image' ? 'Изображение ' . ($key + 1) : 'Видео ' . ($key + 1);
                        }
                    }
                    return implode("\n", $answer);
                }
                break;
            case FoquzQuestion::TYPE_SMILE_RATING:
                $smile = FoquzQuestionSmile::findOne($this->answer);
                if (!$smile) {
                    return '';
                }
                $smileName = substr($smile->smile_url, strrpos($smile->smile_url, '/'));
                preg_match('/\d+/', $smileName, $matches);
                $smiles = FoquzQuestionSmile::findAll(['foquz_question_id' => $smile->foquz_question_id]);
                switch ($this->foquzQuestion->smile_type) {
                    case FoquzQuestion::SMILE_LIKE:
                        return stripos($smile->smile_url, 'down') !== false ? '0' : '1';
                    case FoquzQuestion::SMILE_HEART:
                        return stripos($smile->smile_url, 'break') !== false ? '0' : '1';
                    case FoquzQuestion::SMILE_CUSTOM:
                        return $matches[0];
                    default:
                        switch (count($smiles)) {
                            case 2:
                                $array = [1 => 1, 5 => 2];
                                return $array[$matches[0]];
                            case 5:
                                return $matches[0];
                            default:
                                if ($this->foquzQuestion->smile_type === 'custom') {
                                    return $matches[0];
                                }
                                $array = [1 => 1, 3 => 2, 5 => 3];
                                return $array[$matches[0]];
                        }
                }
            case FoquzQuestion::TYPE_RATING:
            case FoquzQuestion::TYPE_STAR_RATING:
                if ($this->skipped) {
                    return $this->foquzQuestion->skip_text ?: 'Не готов(а) оценить';
                }

                return (string)$this->rating;
            case FoquzQuestion::TYPE_SIMPLE_MATRIX:
                $question = $this->foquzQuestion;
                if ($this->answer && $this->answer != '""') {
                    $decodedAnswer = json_decode($this->answer);
                    if ($question->donor) {
                        $array = [];
                        if ($question->getMainDonor()->main_question_type !== FoquzQuestion::TYPE_DICTIONARY) {
                            $details = ArrayHelper::map(FoquzQuestionDetail::findAll(['id' => array_keys(ArrayHelper::toArray($decodedAnswer))]), 'id', 'question');
                        } else {
                            $details = ArrayHelper::map(DictionaryElement::findAll(array_keys(ArrayHelper::toArray($decodedAnswer))), 'id', 'title');
                        }
                        $donorAnswer = self::findOne([
                            'foquz_poll_answer_id' => $this->foquz_poll_answer_id,
                            'foquz_question_id' => $question->getMainDonor()->id,
                        ]);
                        if ($donorAnswer) {
                            $selectedIDs = $donorAnswer->detail_item;
                            if (is_string($selectedIDs)) {
                                $selectedIDs = json_decode($selectedIDs, true);
                            }
                        }
                        foreach ($decodedAnswer as $key => $answer) {
                            if (isset($selectedIDs) && (
                                    ($question->donor_chosen && !in_array($key, $selectedIDs)) ||
                                    (!$question->donor_chosen && in_array($key, $selectedIDs))
                                )) {
                                continue;
                            }
                            if (is_array($answer)) {
                                $answer = implode(', ', $answer);
                            }
                            $answer = $answer === 'null' || $answer === null ? $skipText : $answer;
                            if ($key == '-1') {
                                $selfVariant = $question->getMainDonor()->self_variant_text ?: 'Свой вариант';
                                $array[$selfVariant] = $answer;
                            } elseif (isset($details[$key])) {
                                $array[$details[$key]] = $answer;
                            } else {
                                $array[$key] = $answer;
                            }
                        }
                        $decodedAnswer = $array;
                    }
                    if ($forGoogle) {
                        $textArray = [];
                        if (!is_null($decodedAnswer)) {
                            foreach ($decodedAnswer as $di => $dv) {
                                $dv = $dv === 'null' || $dv === null ? $skipText : $dv;
                                $dv = is_array($dv) ? implode(', ', $dv) : $dv;
                                $textArray[] = $di . ': ' . $dv;
                            }
                        }
                        return implode("\n", $textArray);
                    }
                    foreach ($decodedAnswer as $di => $dv) {
                        if (is_array($dv)) {
                            $dv = implode(', ', $dv);
                        }
                        $dv = $dv === 'null' || $dv === null ? $skipText : $dv;
                        $richText->createText($di . ': ');
                        $payable = $richText->createTextRun($dv);
                        $payable->getFont()->setBold(true);
                        $richText->createText("\n");
                    }
                    return $richText;
                }
                return "";
            case FoquzQuestion::TYPE_3D_MATRIX:
                $decodedAnswer = json_decode($this->answer ?? '', true) ?? [];
                $question = $this->foquzQuestion;
                $skipText = $question->skip_text ?: 'Затрудняюсь ответить';
                $matrixElements = ArrayHelper::index($question->matrixElements, null, 'type_id');
                /** @var FoquzQuestionMatrixElement[] $columns */
                $columns = !empty($matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN]) ?
                    $matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN] : [];
                /** @var FoquzQuestionMatrixElement[] $mRows */
                $mRows = !empty($matrixElements[FoquzQuestionMatrixElement::TYPE_ROW]) ?
                    $matrixElements[FoquzQuestionMatrixElement::TYPE_ROW] : [];
                $matrixVariants = FoquzQuestionMatrixElementVariant::find()
                    ->where(['matrix_element_id' => ArrayHelper::getColumn($columns, 'id')])
                    ->all();
                /** @var FoquzQuestionMatrixElementVariant[] $matrixVariants */
                $matrixVariants = ArrayHelper::index($matrixVariants, 'id');
                $textArray = [];
                foreach ($mRows as $rowKey => $mRow) {
                    if (empty($decodedAnswer[$mRow->id])) {
                        continue;
                    }
                    $payable = $richText->createTextRun($mRow->name);
                    $payable->getFont()->setBold(true);
                    $richText->createText("\n");
                    foreach ($columns as $column) {
                        $value = !empty($decodedAnswer[$mRow->id][$column->id]) ? $decodedAnswer[$mRow->id][$column->id] : null;
                        if (!$value) {
                            continue;
                        }
                        if (is_array($value)) {
                            foreach ($value as $key => $item) {
                                if (!empty($matrixVariants[$item]->name)) {
                                    $value[$key] = $matrixVariants[$item]->name;
                                } elseif ($item === 'null') {
                                    $value[$key] = '';
                                } elseif ($item === '-1') {
                                    $value[$key] = $skipText;
                                }
                            }
                            $value = implode(', ', $value);
                        }
                        if ($value === 'null') {
                            $value = '';
                        } elseif ($value === '-1') {
                            $value = $skipText;
                        }
                        $payable = $richText->createTextRun($column->name);
                        $payable->getFont()->setItalic(true);
                        $richText->createText(': ' . $value);
                        $richText->createText("\n");
                        if ($forGoogle) {
                            $textArray[] = $column->name . ': ' . $value . "\n";
                        }
                    }
                    if ($rowKey < count($mRows) - 1) {
                        $richText->createText("\n");
                    }
                }
                return $forGoogle ? implode("\n", $textArray) : $richText;
            case FoquzQuestion::TYPE_SEM_DIFFERENTIAL:
                $textArray = [];
                if ($this->answer) {
                    $jsonData = json_decode($this->answer);
                    foreach ($jsonData as $ji => $jv) {
                        $differentialRow = FoquzQuestionDifferentialRow::findOne($ji);
                        $richText->createText($differentialRow->start_label . ' ');
                        $payable = $richText->createTextRun($jv);
                        $payable->getFont()->setBold(true);
                        $richText->createText(' ' . $differentialRow->end_label . "\n");
                        if ($forGoogle) {
                            $textArray[] = $differentialRow->start_label . ' ' . $jv . ' ' . $differentialRow->end_label;
                        }
                    }
                }

                return $forGoogle ? implode("\n", $textArray) : $richText;
            case FoquzQuestion::TYPE_NPS_RATING:
            case FoquzQuestion::TYPE_SCALE:
            case FoquzQuestion::TYPE_DISTRIBUTION_SCALE:
                if ($this->isEmpty) {
                    return '';
                }
                $isSetVariants = $this->foquzQuestion->set_variants || $this->foquzQuestion->main_question_type === FoquzQuestion::TYPE_DISTRIBUTION_SCALE;
                if ($isSetVariants && $this->answer) {
                    $details = json_decode($this->answer);
                    $answer = [];
                    if ($details) {
                        foreach ($details as $detailId => $value) {
                            if (!$this->foquzQuestion->getMainDonor() || $this->foquzQuestion->getMainDonor()->main_question_type !== FoquzQuestion::TYPE_DICTIONARY) {
                                $detail = FoquzQuestionDetail::findOne($detailId);
                            } else {
                                $dictionaryElement = DictionaryElement::findOne($detailId);
                            }
                            $value = $value === 'null' || $value === '' ? $skipText : $value;
                            if ($detailId == '-1' && $donor = $this->foquzQuestion->getMainDonor()) {
                                $answer[] = ($donor->self_variant_text ?: 'Свой вариант') . ': ' . $value;
                            } elseif ($value === '-1' && !empty($detail)) {
                                $answer[] = $detail->question . ': ';
                            } elseif (!empty($detail) && (int)$detail->type !== FoquzQuestionDetail::TYPE_NOTHING) {
                                $answer[] = $detail->question . ': ' . $value;
                            } elseif (!empty($detail)) {
                                $nothing = $detail->question . ': ' . $value;
                            } elseif (!empty($dictionaryElement)) {
                                $answer[] = $dictionaryElement->fullPath . ': ' . $value;
                            }
                        }
                        if (!empty($nothing)) {
                            $answer[] = $nothing;
                        }
                    }
                    return implode("\n", $answer);
                }

                return (string)$this->rating === '-1' ? '' : (string)$this->rating;
            case FoquzQuestion::TYPE_CARD_SORTING_CLOSED:
                return (new CardSortingClosed())->getAnswerColumnExport1($this->answer, $this->foquzQuestion->id);
            default:
                return '';
        }
    }

    //Функция для удобной отрисовки ответа в API
    public function getAnswerData()
    {
        switch ($this->foquzQuestion->main_question_type) {
            case FoquzQuestion::TYPE_ASSESSMENT:
                switch ($this->foquzQuestion->rating_type) {
                    case FoquzQuestion::RATING_TYPE_START:
                        return $this->rating;
                    case FoquzQuestion::RATING_OPTIONS:
                        $answer = [];
                        if (is_array($this->detail_item) && count($this->detail_item)) {
                            foreach ($this->detail_item as $id) {
                                $detail = FoquzQuestionDetail::findOne($id);
                                if ($detail) {
                                    $answer[] = $detail->question;
                                }
                            }
                        }
                        if ($this->self_variant) {
                            $answer[] = $this->self_variant;
                        }
                        return $answer;
                    case FoquzQuestion::RATING_DISHES:
                        $answer = [];
                        foreach ($this->foquzPollAnswer->getFoquzPollDishes()->orderBy('score desc')->all() as $dishScore) {
                            if ($this->foquzPollAnswer->order) {
                                $answer[] = $dishScore->dish->name . ': ' . $dishScore->score;
                            }
                        }
                        return $answer;
                }
                break;
            case FoquzQuestion::TYPE_VARIANTS:
                $answer = [];
                if (is_string($this->detail_item) && $this->detail_item) {
                    $this->detail_item = @json_decode($this->detail_item, true);
                }
                if (is_array($this->detail_item) && count($this->detail_item)) {
                    foreach ($this->detail_item as $id) {
                        $detail = FoquzQuestionDetail::findOne($id);
                        if ($detail) {
                            $answer[] = $detail->question;
                        }
                    }
                }
                if ($this->self_variant) {
                    $answer[] = $this->self_variant;
                }
                return $answer;
            case FoquzQuestion::TYPE_TEXT_ANSWER:
                switch ($this->foquzQuestion->mask) {
                    case 5:
                        $answer = json_decode($this->answer ?? '');
                        return implode(' ', [$answer->surname ?? '', $answer->name ?? '', $answer->patronymic ?? '']);
                    default:
                        return $this->answer;
                }
            case FoquzQuestion::TYPE_ADDRESS:
                return $this->answer;
            case FoquzQuestion::TYPE_FILE_UPLOAD:
                return $this->getAnswerItemFiles()->count();
            case FoquzQuestion::TYPE_FORM:
                $decodedAnswer = json_decode($this->answer ?? '');
                $answer = [];
                foreach ($decodedAnswer as $di => $dv) {
                    $field = FoquzQuestionFormField::findOne($di);
                    if (is_object($dv)) {
                        $answerString = trim(implode(' ', (array)$dv)) != '' ? trim(implode(' ', (array)$dv)) : ' - ';
                    } else {
                        $answerString = $dv != '' ? $dv : ' - ';
                    }
                    if ($field) { //https://sentry.doxswf.ru/organizations/foquz/issues/8087/?project=8
                        $answer[] = $field->name . ': ' . $answerString;
                    }
                }
                return $answer;
            case FoquzQuestion::TYPE_DATE:
                $answer = json_decode($this->answer);
                if ($answer->time == '') {
                    return $answer->date;
                } elseif ($answer->date == '') {
                    return str_replace(' ', '', $answer->time);
                } else {
                    return str_replace(' ', '', $answer->date) . ' ' . str_replace(' ', '', $answer->time);
                }
            case FoquzQuestion::TYPE_PRIORITY:
                if (!empty($this->detail_item) && is_string($this->detail_item)) {
                    $answers = json_decode($this->detail_item) ?? [];
                } elseif (!empty($this->detail_item) && is_array($this->detail_item)) {
                    $answers = $this->detail_item;
                } elseif (!empty($this->answer)) {
                    return json_decode($this->answer) ?? [];
                }
                $result = [];
                /** @var FoquzQuestionDetail[] $variants */
                if (!$this->foquzQuestion->donor) {
                    $variants = ArrayHelper::index($this->foquzQuestion->questionDetails, 'id');
                } else {
                    $variants = ArrayHelper::index($this->foquzQuestion->getMainDonor()->questionDetails, 'id');
                }
                foreach ($answers as $answer) {
                    $result[] = !empty($variants[$answer]->question) ? $variants[$answer]->question : '';
                }
                return $result;
            case FoquzQuestion::TYPE_GALLERY_RATING:
                $jsonData = json_decode($this->answer ?? '') ?? [];
                $answer = [];
                $i = 1;
                foreach ($jsonData as $id => $rating) {
                    $questionFile = FoquzQuestionFile::findOne($id);
                    $answer[] = $questionFile->description != '' ? $questionFile->description . ' - ' . $rating : 'Элемент ' . $i . ' - ' . $rating;
                    $i++;
                }
                return $answer;
            case FoquzQuestion::TYPE_CHOOSE_MEDIA:
                $jsonData = json_decode($this->answer ?? '') ?? [];
                $answer = [];
                foreach ($jsonData as $data) {
                    $questionFile = FoquzQuestionFile::findOne($data);
                    $answer[] = $questionFile->description != '' ? $questionFile->description : $questionFile->position;
                }
                return $answer;
            case FoquzQuestion::TYPE_SMILE_RATING:
                $smile = FoquzQuestionSmile::findOne($this->answer);
                //if (!$smile) return $this->rating;
                //if ($smile) print_r($smile);
                switch ($this->foquzQuestion->smile_type) {
                    case FoquzQuestion::SMILE_LIKE:
                        return $smile && stristr($smile->smile_url, 'down') != false ? '0' : '1';
                    case FoquzQuestion::SMILE_HEART:
                        return stristr($smile->smile_url, 'break') != false ? '0' : '1';
                    default:
                        $smileName = $smile ? substr($smile->smile_url, strrpos($smile->smile_url, '/')) : '';
                        preg_match('/\d+/', $smileName, $matches);
                        //print_r($matches[0]);
                        //print($this->foquzQuestion->smiles_count."\n");
                        switch ($this->foquzQuestion->smiles_count) {
                            case 2:
                                $array = [
                                    1 => 1,
                                    5 => 2,
                                ];
                                return $array[$matches[0]];
                            case 3:
                                $array = [
                                    1 => 1,
                                    3 => 2,
                                    5 => 3,
                                ];
                                //print($matches[0]);
                                return $array[$matches[0]];
                            case 5:
                                return $matches[0];
                        }
                        return $this->rating;
                }
                break;
            case FoquzQuestion::TYPE_STAR_RATING:
            case FoquzQuestion::TYPE_RATING:
                return (string)$this->rating;
            case FoquzQuestion::TYPE_SCALE:
            case FoquzQuestion::TYPE_VARIANT_STAR:
                if ($this->foquzQuestion->main_question_type == FoquzQuestion::TYPE_SCALE &&
                    !$this->foquzQuestion->set_variants) {
                    return $this->rating;
                }
                $answer = [];
                $answerField = json_decode($this->answer ?? '', true) ?? [];
                if (!$this->foquzQuestion->donor) {
                    $details = $this->foquzQuestion->questionDetails;
                } else {
                    $details = $this->foquzQuestion->getMainDonor()->questionDetails;
                }
                foreach ($details as $detail) {
                    if ($detail->extra_question) {
                        continue;
                    }
                    if (isset($answerField[$detail->id]) && $answerField[$detail->id] !== 'null') {
                        $answer[] = [
                            'name' => $detail->question,
                            'value' => (int)$answerField[$detail->id],
                        ];
                    } elseif (!$this->foquzQuestion->donor) {
                        $answer[] = [
                            'name' => $detail->question,
                            'value' => null,
                        ];
                    }
                }
                if (!empty($answer)) {
                    return $answer;
                }
                return null;
            case FoquzQuestion::TYPE_NPS_RATING:
                if (!$this->foquzQuestion->set_variants) {
                    return (string)$this->rating;
                }
                $answer = [];
                $answerField = json_decode($this->answer ?? '', true) ?? [];
                if (!$this->foquzQuestion->donor) {
                    $details = $this->foquzQuestion->questionDetails;
                } else {
                    $details = $this->foquzQuestion->getMainDonor()->questionDetails;
                }
                foreach ($details as $detail) {
                    if ($detail->extra_question) {
                        continue;
                    }
                    if (isset($answerField[$detail->id]) && $answerField[$detail->id] !== '-1') {
                        $answer[] = [
                            'name' => $detail->question,
                            'value' => (string)$answerField[$detail->id],
                        ];
                    } elseif (!$this->foquzQuestion->donor) {
                        $answer[] = [
                            'name' => $detail->question,
                            'value' => null,
                        ];
                    }
                }
                if (!empty($answer)) {
                    return $answer;
                }
                return null;
            case FoquzQuestion::TYPE_SIMPLE_MATRIX:
                $decodedAnswer = json_decode($this->answer ?? '', true);
                $answer = [];
                if ($this->foquzQuestion->donor) {
                    /** @var FoquzQuestionDetail[] $details */
                    $details = ArrayHelper::index($this->foquzQuestion->getMainDonor()->questionDetails, 'id');
                }
                if (is_array($decodedAnswer)) {
                    foreach ($decodedAnswer as $di => $dv) {
                        if (is_array($dv)) {

                            $dv = @implode(",", $dv);
                        }
                        if ($this->foquzQuestion->donor) {
                            $di = $details[$di]->question ?? '';
                        }
                        $answer[] = $di . ': ' . $dv;
                    }
                } else {
                    return null;
                    //print_r($this->answer); exit;
                }
                return $answer;
            case FoquzQuestion::TYPE_SEM_DIFFERENTIAL:
                $jsonData = json_decode($this->answer ?? '', true);
                $answer = [];
                if (is_array($jsonData)) {
                    foreach ($jsonData as $ji => $jv) {
                        $differentialRow = FoquzQuestionDifferentialRow::findOne($ji);
                        //                    $answer[] = $differentialRow->start_label.' '.$jv.' '.$differentialRow->end_label;
                        $answer[] = $jv;
                    }
                } else {
                    return null;
                }
                return $answer;
            case FoquzQuestion::TYPE_FILIAL:
                $filial_id = !empty($this->detail_item[0]) ? $this->detail_item[0] : null;
                if ($filial = Filial::findOne($filial_id)) {
                    return $filial->name;
                }
                return null;
            case FoquzQuestion::TYPE_DICTIONARY:
                $selectedItems = $this->detail_item ?? [];
                return DictionariesHelper::buildApiElements($this->foquzQuestion->dictionary_id, $selectedItems);
            case FoquzQuestion::TYPE_3D_MATRIX:
                $answers = [];
                $decodedAnswer = json_decode($this->answer ?? '', true) ?? [];
                $question = $this->foquzQuestion;
                $skipText = $question->skip_text ?: 'Затрудняюсь ответить';
                $matrixElements = ArrayHelper::index($question->matrixElements, null, 'type_id');
                /** @var FoquzQuestionMatrixElement[] $columns */
                $columns = !empty($matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN]) ?
                    $matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN] : [];
                /** @var FoquzQuestionMatrixElement[] $mRows */
                $mRows = !empty($matrixElements[FoquzQuestionMatrixElement::TYPE_ROW]) ?
                    $matrixElements[FoquzQuestionMatrixElement::TYPE_ROW] : [];
                $matrixVariants = FoquzQuestionMatrixElementVariant::find()
                    ->where(['matrix_element_id' => ArrayHelper::getColumn($columns, 'id')])
                    ->all();
                /** @var FoquzQuestionMatrixElementVariant[] $matrixVariants */
                $matrixVariants = ArrayHelper::index($matrixVariants, 'id');
                foreach ($mRows as $mRow) {
                    $answerColumns = [];
                    foreach ($columns as $column) {
                        $answerValues = [];
                        $value = !empty($decodedAnswer[$mRow->id][$column->id]) ? $decodedAnswer[$mRow->id][$column->id] : null;
                        if (is_array($value)) {
                            foreach ($value as $item) {
                                $answerValues[] = !empty($matrixVariants[$item]->name) ? $matrixVariants[$item]->name : '';
                            }
                        } elseif ($value === 'null' || $value === '') {
                            $answerValues[] = $skipText;
                        }
                        $answerColumns[] = [
                            'title' => $column->name,
                            'values' => $answerValues,
                        ];
                    }
                    $answers[] = [
                        'title' => $mRow->name,
                        'values' => $answerColumns,
                    ];
                }
                return $answers;
            default:
                return '';
        }
    }

    public function getCommentColumn()
    {
        switch($this->foquzQuestion->main_question_type) {
            case FoquzQuestion::TYPE_ASSESSMENT:
                switch($this->foquzQuestion->rating_type) {
                    case FoquzQuestion::RATING_TYPE_START:
                        if($this->foquzQuestion->detail_question != '') {
                            $answer = [];
                            if (is_array($this->detail_item) && count($this->detail_item)) {
                                foreach($this->detail_item as $id) {
                                    $detail = FoquzQuestionDetail::findOne($id);
                                    if($detail) {
                                        $answer[] = $detail->question;
                                    }
                                }
                            }
                            if($this->self_variant) {
                                $answer[] = $this->self_variant;
                            }
                            return implode("\n", $answer);
                        } else {
                            return $this->self_variant ?? $this->answer;
                        }
                    case FoquzQuestion::RATING_OPTIONS:
                        return '';
                    case FoquzQuestion::RATING_DISHES:
                        return $this->answer;
                }
                break;
            case FoquzQuestion::TYPE_VARIANT_STAR:
                return ExtraQuestionService::getCommentColumnVariantStar($this, true);
            case FoquzQuestion::TYPE_SMILE_RATING:
                return ExtraQuestionService::getCommentColumnSmileRating($this);
            case FoquzQuestion::TYPE_PRIORITY:
            case FoquzQuestion::TYPE_FILIAL:
            case FoquzQuestion::TYPE_DICTIONARY:
            case FoquzQuestion::TYPE_CARD_SORTING_CLOSED:
            case FoquzQuestion::TYPE_DISTRIBUTION_SCALE:
                return $this->self_variant;
            case FoquzQuestion::TYPE_NPS_RATING:
                if ($this->foquzQuestion->extra_question_type === FoquzQuestion::EXTRA_QUESTION_DIFFERENT_EACH) {
                    return ExtraQuestionService::getCommentColumnNps($this, true);
                }
                return $this->self_variant;
            case FoquzQuestion::TYPE_FILE_UPLOAD:
            case FoquzQuestion::TYPE_VARIANTS:
                return $this->answer;
            case FoquzQuestion::TYPE_RATING:
            case FoquzQuestion::TYPE_STAR_RATING:
                $answer = [];
                $decodedDetail = json_decode($this->detail_item ?? '', true) ?? [];
                if (is_array($decodedDetail) && count($decodedDetail)) {
                    foreach ($decodedDetail as $id) {
                        $detail = FoquzQuestionDetail::findOne($id);
                        if ($detail) {
                            $answer[] = $detail->question;
                        }
                    }
                }
                if($this->self_variant) {
                    $answer[] = $this->self_variant;
                }
                if($this->answer) {
                    $answer[] = $this->answer;
                }
                return implode("\n", $answer);
            case FoquzQuestion::TYPE_SIMPLE_MATRIX:
                $answer = [];
                if (is_array($this->detail_item) && count($this->detail_item)) {
                    foreach ($this->detail_item as $ids) {
                        if (count($ids) > 1) {
                            $ans = [];
                            foreach ($ids as $id) {
                                $detail = FoquzQuestionDetail::findOne($id);
                                if ($detail) {
                                    $ans[] = $detail->question;
                                }
                            }
                            $answer[] = implode(';', $ans);
                        } else {
                            $detail = FoquzQuestionDetail::findOne($ids[0] ?? 0);
                            if ($detail) {
                                $answer[] = $detail->question;
                            }
                        }
                    }
                }
                return implode("\n", $answer);
            default:
                return '';
        }
    }

    public function getCommentData()
    {
        switch($this->foquzQuestion->main_question_type) {
            case FoquzQuestion::TYPE_ASSESSMENT:
                switch($this->foquzQuestion->rating_type) {
                    case FoquzQuestion::RATING_TYPE_START:
                        if($this->foquzQuestion->detail_question != '') {
                            $answer = [];
                            if (is_array($this->detail_item) && count($this->detail_item)) {
                                foreach ($this->detail_item as $id) {
                                    $detail = FoquzQuestionDetail::findOne($id);
                                    if ($detail) {
                                        $answer[] = $detail->question;
                                    }
                                }
                            }
                            if($this->self_variant) {
                                $answer[] = $this->self_variant;
                            }
                            return $answer;
                        } else {
                            return $this->self_variant ?? $this->answer;
                        }
                    case FoquzQuestion::RATING_OPTIONS:
                        return '';
                    case FoquzQuestion::RATING_DISHES:
                        return $this->answer;
                }
                break;
            case FoquzQuestion::TYPE_VARIANT_STAR:
            case FoquzQuestion::TYPE_NPS_RATING:
            case FoquzQuestion::TYPE_SMILE_RATING:
            case FoquzQuestion::TYPE_PRIORITY:
            case FoquzQuestion::TYPE_FILIAL:
            case FoquzQuestion::TYPE_GALLERY_RATING:
            case FoquzQuestion::TYPE_SEM_DIFFERENTIAL:
            case FoquzQuestion::TYPE_CHOOSE_MEDIA:
            case FoquzQuestion::TYPE_SCALE:
            case FoquzQuestion::TYPE_DICTIONARY:
            case FoquzQuestion::TYPE_SIMPLE_MATRIX:
            case FoquzQuestion::TYPE_3D_MATRIX:
            case FoquzQuestion::TYPE_CARD_SORTING_CLOSED:
            case FoquzQuestion::TYPE_DISTRIBUTION_SCALE:
                return $this->self_variant;
            case FoquzQuestion::TYPE_FILE_UPLOAD:
            case FoquzQuestion::TYPE_RATING:
            case FoquzQuestion::TYPE_VARIANTS:
                return $this->answer;
            case FoquzQuestion::TYPE_STAR_RATING:
                $answer = [];
                if (is_array($this->detail_item) && count($this->detail_item)) {
                    foreach ($this->detail_item as $id) {
                        $detail = FoquzQuestionDetail::findOne($id);
                        if ($detail) {
                            $answer[] = $detail->question;
                        }
                    }
                }
                if($this->self_variant) {
                    $answer[] = $this->self_variant;
                }
                if($this->answer) {
                    $answer[] = $this->answer;
                }
                return $answer;
            default:
                return '';
        }
    }

    /**
     * Производит расчет максимального балла для вопроса
     * @return int
     */
    public function calculateMaxPoints(): int
    {
        $maxPoint = $this->foquzQuestion->maxPoints;
        if ($maxPoint === null) {
            $maxPoint = 0;
        }
        if (
            $this->foquzQuestion->main_question_type !== FoquzQuestion::TYPE_VARIANTS &&
            $this->foquzQuestion->main_question_type !== FoquzQuestion::TYPE_SIMPLE_MATRIX
        ) {
            return $maxPoint;
        }

        if ($this->foquzQuestion->main_question_type === FoquzQuestion::TYPE_VARIANTS) {
            $withoutPointsVariantID = FoquzQuestionDetail::find()
                ->select('id')
                ->where(['foquz_question_id' => $this->foquz_question_id, 'without_points' => true, 'is_deleted' => false])
                ->scalar();
            if (!$withoutPointsVariantID) {
                return $maxPoint;
            }
            $answerDetails = $this->detail_item;
            if (is_string($answerDetails)) {
                $answerDetails = json_decode($answerDetails) ?? [];
            }
            if (is_object($answerDetails)) {
                $answerDetails = (array)$answerDetails;
            }
            if (!is_array($answerDetails)) {
                $answerDetails = [];
            }
            if ($key = array_search('is_self_answer', $answerDetails)) {
                unset($answerDetails[$key]);
            }
            if (count($answerDetails) === 1 && in_array($withoutPointsVariantID, $answerDetails)) {
                $maxPoint = 0;
            }
            return $maxPoint;
        }

        if ($this->foquzQuestion->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX) {
            if (!$this->foquzQuestion->donor || !$this->foquzQuestion->max_points_calc_recipient) {
                return $maxPoint;
            }
            $answer = json_decode($this->answer ?? '', true) ?? [];
            $matrixSettings = json_decode($this->foquzQuestion->matrix_settings, false);
            if (empty($matrixSettings->points) || empty($matrixSettings->donorRows)) {
                Yii::error('Невозможно получить настройки матрицы для расчета баллов для вопроса ID: ' . $this->foquz_question_id);
                return 0;
            }
            $donorRows = array_flip($matrixSettings->donorRows);
            $points = $matrixSettings->points;
            $multipleChoice = !empty($matrixSettings->multiple_choice);
            $selectedIDs = array_filter(array_keys($answer), static function ($id) {
                return is_numeric($id) && $id > 0;
            });
            $maxPoint = 0;
            foreach ($donorRows as $rowID => $row) {
                if (!in_array($rowID, $selectedIDs)) {
                    continue;
                }
                $rowPoints = $points[$row] ?? [];
                $rowPoints = array_filter($rowPoints, static function ($value) {
                    return is_numeric($value) && $value >= 0;
                });
                if (!$multipleChoice) {
                    $maxPoint += max($rowPoints);
                } else {
                    $maxPoint += array_sum($rowPoints);
                }
            }

            return $maxPoint;
        }

        return $maxPoint;
    }

    /**
     * Производит возврат оценки по вопросу в процентах
     * @return int|null
     */
    public function getItemPercentScore(): ?int
    {
        $question = $this->foquzQuestion;
        switch ($question->main_question_type) {
            case FoquzQuestion::TYPE_ASSESSMENT:
                switch ($question->rating_type) {
                    case FoquzQuestion::RATING_TYPE_START:
                        $min = 1;
                        $max = 5;
                        return ($this->rating - $min) / ($max - $min) * 100;
                    case FoquzQuestion::RATING_DISHES:
                        $percent = [];
                        foreach ($this->foquzPollAnswer->foquzPollDishes as $dishScore) {
                            $percent[] = ($dishScore->score - 1) / (5 - 1) * 100;
                        }
                        return min($percent);
                }
                break;
            case FoquzQuestion::TYPE_GALLERY_RATING:
            case FoquzQuestion::TYPE_SEM_DIFFERENTIAL:
                $answer = json_decode($this->answer ?? '', true) ?? [];
                $answer = array_filter($answer, static function ($value) {
                    return is_numeric($value);
                });
                if (empty($answer)) {
                    return null;
                }
                $rating = min($answer);
                return ($rating - 1) / (5 - 1) * 100;
            case FoquzQuestion::TYPE_SMILE_RATING:
                if ($this->rating == -1 || $this->rating === null || $this->rating === 0) {
                    return null;
                }
                $rating = $this->rating;
                $min = 1;
                $max = count($question->questionSmiles);
                return ($rating - $min) / ($max - $min) * 100;
            case FoquzQuestion::TYPE_RATING:
            case FoquzQuestion::TYPE_STAR_RATING:
                if ($this->rating == -1 || $this->rating === null) {
                    return null;
                }
                $min = 1;
                $max = $question->starRatingOptions->count;
                $rating = $this->rating;
                return ($rating - $min) / ($max - $min) * 100;
            case FoquzQuestion::TYPE_NPS_RATING:
            case FoquzQuestion::TYPE_SCALE:
                if ($question->main_question_type === FoquzQuestion::TYPE_NPS_RATING) {
                    $min = (int) $question->from_one;
                    $max = 10;
                } else {
                    $min = $question->scaleRatingSetting->start ?? 0;
                    $max = $question->scaleRatingSetting->end ?? 100;
                }
                if (!$question->set_variants) {
                    if ($this->rating == -1 || $this->rating === null) {
                        return null;
                    }
                    $rating = $this->rating;
                } else {
                    $answer = json_decode($this->answer ?? '', true) ?? [];
                    $answer = array_filter($answer, static function ($value) {
                        return is_numeric($value) && (int)$value !== -1;
                    });
                    if (empty($answer)) {
                        return null;
                    }
                    $rating = min($answer);
                }
                return ($rating - $min) / ($max - $min) * 100;
            case FoquzQuestion::TYPE_VARIANT_STAR:
                $min = 1;
                $max = $question->starRatingOptions->count;
                $answer = json_decode($this->answer ?? '', true) ?? [];
                $answer = array_filter($answer, static function ($value) {
                    return is_numeric($value)  && (int)$value !== 0;
                });
                if (empty($answer)) {
                    return null;
                }
                $rating = min($answer);
                return ($rating - $min) / ($max - $min) * 100;
        }
        return null;
    }

    /**
     * Получить массив правильных ответов для вопроса типа Варианты ответов
     * @return array
     * @throws NotFoundHttpException
     */
    public function getCorrectAnswerForVariants(): array
    {
        $ret = [];
        $detailsWithPoints = FoquzQuestionDetail::find()
            ->with('file')
            ->where(['foquz_question_id' => $this->foquz_question_id])
            ->andWhere(['is not', 'points', null])
            ->orderBy(['points' => SORT_DESC, 'position' => SORT_ASC])
            ->all();

        if ($detailsWithPoints) {
            $correctAnswerArr = array_map(function ($item) {
                $ret['id'] = $item->id;
                $ret['points'] = $item->points;
                $ret['text'] = $item->question;
                if ($item->file) {
                    $ret['file_url'] = $item->file->fileUrl;
                    $ret['preview_url'] = $item->file->previewUrl;
                }
                return $ret;
            },$detailsWithPoints);

            $negativePoints = array_filter($correctAnswerArr, function ($item) {
                return $item['points'] < 0;
            });
            $positivePoints = array_filter($correctAnswerArr, function ($item) {
                return $item['points'] > 0;
            });

            if ($this->foquzQuestion->variants_element_type === 1) { // несколько ответов
                $ret = ($this->foquzQuestion->max_choose_variants) ? array_slice($positivePoints, 0, $this->foquzQuestion->max_choose_variants) : $positivePoints;
            } else { // 1 ответ
                $ret = [$correctAnswerArr[0]];
            }
            if (count($correctAnswerArr) == count($negativePoints)) {
                $ret = [[
                    'text' => 'не задан'
                ]];
            }
        } else {
            if ($this->foquzQuestion->donor) {
                $donor = $this->foquzQuestion->getMainDonor();
                $answerItem = $this->getDonorAnswer();
                $detailsWithPoints = RecipientQuestionDetail::find()
                    ->where(['recipient_id' => $this->foquzQuestion->id])
                    ->andWhere(['is not', 'points', null])
                    ->orderBy(['points' => SORT_DESC, 'position' => SORT_ASC])
                    ->all();

                if($detailsWithPoints) {
                    $correctAnswerArr = array_map(function ($item) use ($answerItem){
                        $ret = [];
                        if (is_null($item['question_detail_id'])) {
                            $ret['id'] = $item->id;
                            $ret['points'] = $item->points;
                            $ret['text'] = $answerItem->self_variant;
                        } else {
                            $ret['id'] = $item->id;
                            $ret['points'] = $item->points;
                            $ret['text'] = $item->questionDetail->question;
                            if ($item->questionDetail->file) {
                                $ret['file_url'] = $item->questionDetail->file->fileUrl;
                                $ret['preview_url'] = $item->questionDetail->file->previewUrl;
                            }
                        }
                        return $ret;
                    }, $detailsWithPoints);

                    if ($donor->variants_element_type === 1) { // несколько ответов
                        $ret = ($donor->max_choose_variants) ? array_slice($correctAnswerArr, 0, $donor->max_choose_variants) : $correctAnswerArr;

                    } else { // 1 ответ
                        $ret = [$correctAnswerArr[0]];
                    }
                }
            }
        }
        return $ret;
    }

    /**
     * Получить массив правильных ответов для вопроса типа Выбор изображения/видео
     * @return array
     */
    public function getCorrectAnswerForMedia(): array
    {
        $ret = [];
        $detailsWithPoints = FoquzQuestionFile::find()
            ->where(['question_id' => $this->foquz_question_id])
            ->andWhere(['is not', 'points', null])
            ->orderBy(['points' => SORT_DESC, 'position' => SORT_ASC])
            ->all();

        if ($detailsWithPoints) {
            $correctAnswerArr = array_map(function ($item) {
                $ret['id'] = $item->id;
                $ret['points'] = $item->points;
                $ret['text'] = $item->description;
                $ret['file_url'] = $item->getLink();
                $ret['preview_url'] = $item->image;

                return $ret;
            },$detailsWithPoints);

            $negativePoints = array_filter($correctAnswerArr, function ($item) {
                return $item['points'] < 0;
            });
            $positivePoints = array_filter($correctAnswerArr, function ($item) {
                return $item['points'] > 0;
            });

            if ($this->foquzQuestion->variants_element_type === 1) { // несколько ответов
                $ret = ($this->foquzQuestion->max_choose_variants) ? array_slice($positivePoints, 0, $this->foquzQuestion->max_choose_variants) : $positivePoints;
            } else { // 1 ответ
                $ret = [$correctAnswerArr[0]];
            }
            if (count($correctAnswerArr) == count($negativePoints)) {
                $ret = [[
                    'text' => 'не задан'
                ]];
            }
        }
        return $ret;
    }


    /**
     * Получить массив правильных ответов для вопроса типа Простая матрица
     * @param \stdClass $matrix
     * @param FoquzQuestion|null $donor
     * @return array
     */
    public function getCorrectAnswerForSimpleMatrix(\stdClass $matrix, FoquzQuestion|null $donor): array
    {
        $points = $matrix->points;
        $rows = $matrix->rows;
        $cols = $matrix->cols;
        $multiple = $matrix->multiple_choice;
        $ret = [];

        $allNegative = array_reduce($points, function ($carry, $item) {
            $rowNegative = array_reduce($item, function ($carry2, $item2) {
                if (!is_numeric($item2)) {
                    $item2 = 0;
                }
                return $carry2 && $item2 < 0;
            }, true);
            return $carry && $rowNegative;
        }, true);

        if ($allNegative) {
            $ret['text'] = 'не задан';
            return $ret;
        }

        foreach ($points as $k => $row_points) {
            if (!isset($rows[$k]) && $donor) {
                $rows[$k] = $donor->self_variant_text ?: 'свой вариант';
            }
            arsort($row_points);
            $row_points = array_filter($row_points, function ($value) {
                if (!is_numeric($value)) {
                    $value = 0;
                }
                return $value > 0;
            });
            if (count($row_points)) {
                if (!$multiple) {
                    $ret[$k]['text'] = $rows[$k] . ' - ' . $cols[array_key_first($row_points)];
                    $ret[$k]['row'] = $rows[$k];
                    $ret[$k]['col'] = $cols[array_key_first($row_points)];
                    $ret[$k]['points'] = $row_points[array_key_first($row_points)];
                } else {
                    $idx = 0;
                    foreach ($row_points as $key => $point) {
                        $ret[$k][$idx]['text'] = $rows[$k] . ' - ' . $cols[$key];
                        $ret[$k][$idx]['row'] = $rows[$k];
                        $ret[$k][$idx]['col'] = $cols[$key];
                        $ret[$k][$idx]['points'] = $point;
                        $idx++;
                    }
                }
            }
        }
        return $ret;
    }

    /**
     * @throws NotFoundHttpException
     */
    public function getDonorAnswer(): ?FoquzPollAnswerItem
    {
        $donor = FoquzQuestion::findOne($this->foquz_question_id);
        if (!$donor) {
            throw new NotFoundHttpException('Donor not found');
        }

        return self::findOne(['foquz_poll_answer_id' => $this->foquz_poll_answer_id, 'foquz_question_id' => $donor->getMainDonor()]);
    }

    public function getFoquzQuestionSmiles()
    {
        return $this->hasMany(FoquzQuestionSmile::class, ['id' => 'answer']);
    }

    public function getHiddenQuestion()
    {
        return $this->hasOne(FoquzPollAnswerHiddenQuestion::class, ['answer_item_id' => 'id']);
    }

    /**
     * Определяет пустоту ответа
     * @return bool
     */
    public function getIsEmpty(): bool
    {
        if ($this->foquzQuestion->main_question_type === FoquzQuestion::TYPE_FILE_UPLOAD) {
            /** @todo Зачем? написать скрипт для обновленияя */
            return !$this->skipped && ($this->answer === null || $this->answer === '') && !FoquzPollAnswerItemFile::find()
                //->where(['foquz_question_id' => $this->foquz_question_id])
                ->andWhere(['OR',
                    ['AND',
                        ['<', 'created_at', '2024-06-17'], //Дата обновления на проде с сохранением foquz_poll_answer_id
                        ['foquz_poll_answer_item_id' => $this->id]
                    ],
                    ['AND',
                        ['>=', 'created_at', '2024-06-17'], //Дата обновления на проде с сохранением foquz_poll_answer_id
                        ['foquz_poll_answer_item_id' => $this->id]/*,
                        ['foquz_poll_answer_id' => $this->foquz_poll_answer_id]*/
                    ]
                ])
                ->exists();
        }
        $answerArray = json_decode($this->answer ?? '', true);
        $detailArray = $this->detail_item;
        if (is_string($detailArray)) {
            $detailArray = json_decode($detailArray, true);
        }
        if ($this->foquzQuestion->main_question_type !== FoquzQuestion::TYPE_NPS_RATING) {
            $emptyValues = [null, 'null', '', 0, '0'];
            $emptyArrayValues = [null, '', 0, '0'];
        } else {
            $emptyValues = [null, 'null', '', -1, '-1'];
            $emptyArrayValues = [null, '', -1, '-1'];
        }
        if (is_array($answerArray)) {
            $answerArray = array_filter($answerArray, static function ($value) use ($emptyArrayValues) {
                return in_array($value, $emptyArrayValues, false) === false;
            });
        }
        if (is_array($detailArray)) {
            $detailArray = array_filter($detailArray, static function ($value) use ($emptyArrayValues) {
                return in_array($value, $emptyArrayValues, false) === false;
            });
        }
        return (
            $this->foquzQuestion->main_question_type !== FoquzQuestion::TYPE_SCALE &&
            !$this->skipped &&
            (
                in_array($this->answer, [null, ''], true) ||
                (is_array($answerArray) && count($answerArray) === 0) ||
                ($this->foquzQuestion->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX && $this->answer === 'null')
            ) &&
            (in_array($this->detail_item, [null, ''], true) || (is_array($detailArray) && count($detailArray) === 0)) &&
            in_array($this->self_variant, [null, ''], true) &&
            in_array($this->rating, $emptyValues, true)
        );
    }
}
