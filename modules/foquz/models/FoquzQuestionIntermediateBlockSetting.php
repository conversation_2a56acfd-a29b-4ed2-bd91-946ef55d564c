<?php

namespace app\modules\foquz\models;

use app\modules\foquz\services\DiscountPoolCodeService;
use yii\db\ActiveRecord;
use yii\helpers\HtmlPurifier;

/**
 * Class for table foquz_question_intermediate_block_setting
 *
 * @property int $id
 * @property int $question_id
 * @property int $screen_type
 * @property boolean $show_question_number
 * @property string $text
 * @property boolean $complaint_button
 * @property boolean $unsubscribe_button
 * @property string $complaint_button_text
 * @property string $unsubscribe_button_text
 * @property string $poll_button_text
 * @property string $code
 * @property int $pool_id
 * @property boolean $ready_button
 * @property string $ready_button_text
 * @property boolean $close_widget_button Кнопка «Закрыть» для виджета
 * @property string|null $close_widget_button_text Текст кнопки «Закрыть» для виджета
 * @property string $external_link
 * @property bool $agreement
 * @property string $agreement_text
 * @property boolean $show_bg_instruction Показывать инструкцию на подложке для типа вопроса Тест 5 секунд
 * @property int|null $image_show_time Время показа изображения, секунд для типа вопроса Тест 5 секунд
 * @property string|null $show_image_button_text Текст кнопки «Показать изображение» для типа вопроса Тест 5 секунд
 *
 * @property FoquzQuestionIntermediateBlockSettingSocNetworks $socNetworks
 * @property DiscountPool $pool
 * @property FoquzQuestion $question
 * @property FoquzQuestionIntermediateBlockSettingLang[] $langs
 */
class FoquzQuestionIntermediateBlockSetting extends BaseModel
{
    const SCREEN_TYPE_TEXT = 1;
    const SCREEN_TYPE_START = 2;
    const SCREEN_TYPE_END = 3;
    const SCREEN_TYPE_TEST5S = 4;

    public const TYPES_NAME = [
        self::SCREEN_TYPE_TEXT     => 'Текстовый блок',
        self::SCREEN_TYPE_START    => 'Стартовый экран',
        self::SCREEN_TYPE_END      => 'Конечный экран',
        self::SCREEN_TYPE_TEST5S   => 'Тест 5 секунд',
    ];

    public static function tableName()
    {
        return 'foquz_question_intermediate_block_setting';
    }

    public function rules()
    {
        return [
            [['question_id', 'screen_type', 'pool_id'], 'integer'],
            [[
                'show_question_number', 'scores_button', 'complaint_button', 'unsubscribe_button', 'ready_button',
                'close_widget_button', 'start_over_button', 'agreement', 'show_bg_instruction'], 'boolean'],
            [[
                'text', 'scores_button_text', 'complaint_button_text', 'unsubscribe_button_text', 'poll_button_text',
                'start_over_button_text', 'code', 'ready_button_text', 'close_widget_button_text',
                'external_link', 'logos_backcolor', 'agreement_text'], 'string'],
            [['agreement_text', 'text'], 'filter', 'filter' => [$this, 'filterHTML']],
            [['show_image_button_text'], 'string', 'max' => 255],
            [['image_show_time'], 'integer', 'min' => 5, 'max' => 60]
        ];
    }

    /**
     * Возвращает название типа промежуточного блока
     * @return string
     */
    public function getTypeName(): string
    {
        return self::TYPES_NAME[$this->screen_type] ?? 'Неизвестный тип';
    }

    public function getSocNetworks()
    {
        return $this->hasOne(FoquzQuestionIntermediateBlockSettingSocNetworks::class, ['intermediate_block_id' => 'id']);
    }

    /**
     * Gets query for [[Question]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getQuestion()
    {
        return $this->hasOne(FoquzQuestion::className(), ['id' => 'question_id']);
    }

    public function fields()
    {
        return parent::fields()+[
            'socNetworks' => function($model) {
                return $model->socNetworks;
            },
            'langs' => function() {
                return $this->langs;
            }
        ];
    }

    public function getLangs()
    {
        return $this->hasMany(FoquzQuestionIntermediateBlockSettingLang::class, ['setting_id' => 'id']);
    }

    public function getPool()
    {
        return $this->hasOne(DiscountPool::class, ['id' => 'pool_id']);
    }

    public function getCodeString($answer) : string
    {
        if($this->pool_id !== null && $answer !== null) {

            $discountPoolCode = DiscountPoolCode::find()
                ->where([
                    'pool_id' => $this->pool->id,
                    'answer_id' => $answer->id,
                ])
                ->one();

            return (new DiscountPoolCodeService($this->pool, $discountPoolCode, [
                'poll_id' => $this->question->poll_id,
                'answer_id' => $answer->id,
                'contact_id' => $answer->contact_id,
                'sended_by' => DiscountPoolCode::BY_INTERSCREEN,
            ]))->getCode();
        }

        return '';
    }

    /**
     * Удаляет из HTML вредоносные и невалидные теги
     * @param $value
     * @return string|null
     */
    public function filterHTML($value): ?string
    {
        if (!is_string($value)) {
            return $value;
        }
        return HtmlPurifier::process($value, [
            'HTML.TargetBlank' => true,
        ]);
    }
}