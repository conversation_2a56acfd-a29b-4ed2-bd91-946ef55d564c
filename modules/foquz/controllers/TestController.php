<?php

namespace app\modules\foquz\controllers;

use app\components\RabbitMQComponent;
use app\models\company\Company;
use app\models\User;
use app\modules\foquz\models\FoquzContact;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\notifications\SiteNotification;
use app\modules\foquz\models\Webhook;
use Yii;
use yii\web\Controller;
use yii\web\Response;

class TestController extends Controller
{
    public function beforeAction($action)
    {
        if (!YII_DEBUG) {
            return false;
        }
        Yii::$app->response->format = Response::FORMAT_JSON;
        Yii::$app->request->enableCsrfValidation = false;
        return parent::beforeAction($action);
    }

    public function actionTestMail($type): Response
    {
        if ($type === 'new_answers') {
            $email = \Yii::$app->request->get('email');
            $answerID = \Yii::$app->request->get('answer_id');
            $pollID = \Yii::$app->request->get('poll_id');
            if ($pollID) {
                $answer = FoquzPollAnswer::find()
                    ->where(['foquz_poll_id' => $pollID])
                    ->andWhere(['status' => [FoquzPollAnswer::STATUS_IN_PROGRESS, FoquzPollAnswer::STATUS_DONE]])
                    ->orderBy(['id' => SORT_DESC])
                    ->offset($answerID ? $answerID - 1 : 0)
                    ->one();
            } elseif ($answerID) {
                $answer = FoquzPollAnswer::findOne($answerID);
            } else {
                return $this->asJson(['success' => false, 'message' => 'Не указан ID ответа']);
            }
            //var_dump($answer->answerContentForEmail);die();
            echo \Yii::$app->view->render('@app/mail/new-answers-extended.twig', [
                'answer' => $answer,
                'type' => SiteNotification::TYPE_NEW_ANSWERS_EXTENDED,
            ]);
            die();
            /*$recipient = (new Query())
                ->select('email, site_notification_message_id, site_notification_messages.answer_id')
                ->from('user')
                ->leftJoin('site_notification_message_recipients', 'site_notification_message_recipients.user_id = user.id')
                ->leftJoin('site_notification_messages', 'site_notification_messages.id = site_notification_message_recipients.site_notification_message_id')
                ->orderBy(['site_notification_message_id' => SORT_DESC]);

            if (!$answerID) {
                $recipient->where(['email' => $email, 'site_notification_messages.type' => $type, 'site_notification_messages.sent' => 0]);
            } else {
                $recipient->where(['email' => $email, 'site_notification_messages.type' => $type, 'site_notification_messages.answer_id' => $answerID]);
            }

            $recipient = $recipient->one();

            if (!$recipient) {
                return $this->asJson(['success' => false, 'message' => 'Нет получателей для отправки.']);
            }

            $notification = SiteNotificationMessage::findOne($recipient['site_notification_message_id']);
            if (!$notification) {
                return $this->asJson(['success' => false, 'message' => 'Нет сообщения для отправки.']);
            }

            //print_r(FoquzPollAnswer::findOne($recipient['answer_id'])->answerContentForEmail);die();
            if (!$notification->message && $type === 'new_answers') {
                $message = \Yii::$app->view->render('@app/mail/new-answers-extended.twig', [
                    'answer' => FoquzPollAnswer::findOne($recipient['answer_id']),
                ]);
            } else {
                $message = $notification->message;
            }
            echo $message;die();

            $result = ['email' => $recipient['email'], 'result' => \Yii::$app->mailer->compose()
                ->setFrom(\Yii::$app->params['main_sender'])
                ->setTo($recipient['email'])
                ->setSubject($notification->subject)
                ->setHtmlBody($message)
                ->send()
            ];
            $notification->sent = 1;
            $notification->save();
            return $this->asJson(['success' => true, 'result' => $result]);*/
        } elseif ($type === 'new_feedback_answers') {
            $feedbackID = \Yii::$app->request->get('feedback_id');
            $withAnswers = \Yii::$app->request->get('withAnswers');
            $withAnswers = $withAnswers !== '0';
            if (!$feedbackID) {
                return $this->asJson(['success' => false, 'message' => 'Не указан ID сообщения из обратной связи']);
            }
            $feedback = CompanyFeedback::findOne($feedbackID);
            if (!$feedback) {
                return $this->asJson(['success' => false, 'message' => 'Сообщение из обратной связи не найдено']);
            }
            //print_r($feedback->decodedFiles);die();

            echo \Yii::$app->view->render('@app/mail/new-feedback-answers.twig', [
                'feedback' => $feedback,
                'withAnswers' => $withAnswers,

            ]);
            die();
        }
    }

    public function actionSetUserLimit($limit = 0)
    {
        $company = Company::findOne(1);
        $company->limit_users = $limit;
        $company->save();
    }

    public function actionTestSentry($cfg = false)
    {
        if ($cfg) {
            return $this->asJson(\Yii::$app->components);
        }
        throw new \Exception('Test exception');
    }

    public function actionScore($id)
    {
        $answer = FoquzPollAnswer::findOne($id);
        return $this->asJson(['value' => $answer->minPercentScore]);
    }

    public function actionMaxPoint($id)
    {
        $question = FoquzQuestion::findOne($id);
        if (!$question) {
            return $this->asJson(['success' => false, 'message' => 'Вопрос не найден']);
        }
        if (in_array(Yii::$app->request->get('value'), ['0', '1'])) {
            $question->max_points_calc_recipient = Yii::$app->request->get('value');
            $question->save();
            return $this->asJson([
                'success' => true,
                'message' => 'Значение установлено',
                'value'   => $question->max_points_calc_recipient
            ]);
        }
        return $this->asJson(['success' => false, 'value' => $question->max_points_calc_recipient]);
    }

    public function actionDebugPoints()
    {
        $fapi = FoquzPollAnswerItem::findOne(360504);
        $answer = json_decode($fapi->answer, true) ?? [];
        $matrixSettings = json_decode($fapi->foquzQuestion->matrix_settings, false);
        if (empty($matrixSettings->points) || empty($matrixSettings->donorRows)) {
            Yii::error('Невозможно получить настройки матрицы для расчета баллов для вопроса ID: ' . $fapi->foquz_question_id);
            return 0;
        }
        $donorRows = array_flip($matrixSettings->donorRows);
        echo 'donorRows: ' . print_r($donorRows, true) . '<br>';
        $points = $matrixSettings->points;
        echo 'points: ' . print_r($points, true) . '<br>';
        $multipleChoice = !empty($matrixSettings->multiple_choice);
        echo 'multipleChoice: ' . print_r($multipleChoice, true) . '<br>';
        $selectedIDs = array_filter(array_keys($answer), static function ($id) {
            return is_numeric($id) && $id > 0;
        });
        echo 'selectedIDs: ' . print_r($selectedIDs, true) . '<br>';
        $maxPoint = 0;
        foreach ($donorRows as $rowID => $row) {
            if (!in_array($rowID, $selectedIDs)) {
                continue;
            }
            $rowPoints = $points[$row] ?? [];
            echo 'rowPoints: ' . $rowID . print_r($rowPoints, true) . '<br>';
            if (!$multipleChoice) {
                $maxPoint += max($rowPoints);
            } else {
                $maxPoint += array_sum($rowPoints);
            }
            echo 'maxPoint: ' . $maxPoint . '<br>';
        }

        return $maxPoint;
    }

    public function actionSetWebhook(): Response
    {
        $pollID = Yii::$app->request->post('poll_id');
        $url = Yii::$app->request->post('url');
        $delay = Yii::$app->request->post('delay');

        if (!$pollID && !$url) {
            return $this->asJson(['error' => 'Incorrect data']);
        }

        /** @var Webhook $model */
        $model = Webhook::find()
            ->where(['entity_id' => $pollID, 'entity_type' => Webhook::TYPE_NEW_ANSWER])
            ->one();

        if ($model && empty($url)) {
            if ($model->delete()) {
                return $this->asJson(['success' => true, 'action' => 'delete', 'id' => $model->id]);
            }
            return $this->asJson(['success' => false, 'action' => 'delete', 'errors' => $model->errors]);
        }

        if (!$model && empty($url)) {
            return $this->asJson([
                'success' => false,
                'action'  => 'save',
                'errors'  => ['url' => 'Поле обязательно для заполнения']
            ]);
        }

        if ($delay < 0) {
            return $this->asJson([
                'success' => false,
                'action'  => 'save',
                'errors'  => ['delay' => 'Значение не может быть меньше 0']
            ]);
        }

        if (!$model) {
            $model = new Webhook();
            $model->entity_id = $pollID;
            $model->entity_type = Webhook::TYPE_NEW_ANSWER;
        }

        $model->url = $url;
        if (!empty($delay)) {
            $model->params = ['delay' => (int)$delay];
        } else {
            $model->params = null;
        }

        if ($model->save()) {
            return $this->asJson(['success' => true, 'action' => 'save']);
        }
        return $this->asJson(['success' => false, 'errors' => $model->errors, 'action' => 'save']);
    }

    public function actionRabbit()
    {
        echo Yii::getAlias('@uploads/..');
        die();
        /** @var RabbitMQComponent $rabbit */
        $rabbit = Yii::$app->rabbit;
        $contacts = FoquzContact::find()->where(['company_id' => 1, 'is_deleted' => 0])->limit(10000)->asArray()->all();
        $rabbit->queue('widget.contacts')->type('sync')->push([
            'company_id' => 1,
            'contacts'   => $contacts,
        ]);
        //$rabbit->queue('widget.contacts')->type('upsert')->push(FoquzContact::findOne(1)->attributes);
    }

    public function actionTestPoll()
    {
        $poll = FoquzPoll::findOne(47573);

        return $this->asJson($poll->isAnswerLimit);
    }
}
