<?php

namespace app\modules\foquz\controllers;

use app\models\User;
use app\modules\foquz\helpers\ImageHelper;
use app\modules\foquz\models\EditorFolder;
use app\modules\foquz\models\FoquzComplaint;
use app\modules\foquz\models\FoquzComplaintFile;
use app\modules\foquz\models\FoquzContact;
use app\modules\foquz\models\FoquzPointItem;
use app\modules\foquz\models\FoquzPointTypeRelation;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzPollAnswerItemFile;
use app\modules\foquz\models\FoquzPollDishScoreSearch;
use app\modules\foquz\models\FoquzPollFavorite;
use app\modules\foquz\models\FoquzPollSearch;
use app\modules\foquz\models\FoquzPollStatFilterSettings;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionDetail;
use app\modules\foquz\models\UploadForm;
use app\modules\foquz\models\VideoUploadForm;
use app\modules\foquz\models\FoquzPollStatsLink;
use app\modules\foquz\services\custom\CustomQuotaService;
use webvimark\modules\UserManagement\models\forms\LoginForm;
use yii\web\BadRequestHttpException;
use yii\web\ForbiddenHttpException;
use yii\web\HttpException;
use app\response\Response;
use FFMpeg\Coordinate\TimeCode;
use FFMpeg\FFMpeg;
use Yii;
use yii\db\Expression;
use yii\db\Query;
use yii\filters\AccessControl;
use yii\filters\ContentNegotiator;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\helpers\Url;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\UploadedFile;

/**
 * Class AjaxController
 * @package app\modules\foquz\controllers
 */
class AjaxController extends Controller
{
    public $enableCsrfValidation = false;

    /** {@inheritdoc} */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'rules' => [
                    [
                        'actions' => [
                            'search', 'folders', 'favorite', 'polls', 'folder', 'quick-help-showed', 'mobile-quick-help',
                            'mobile-quick-help-showed', 'user-points', 'points-by-type'
                        ],
                        'allow' => true,
                        'roles' => [
                            'foquz_admin', 'foquz_executor', 'foquz_report_admin', 'editor', 'foquz_superadmin',
                            'foquz_watcher', 'filial_employee',  'wiki_editor'
                        ],
                    ],
                    [
                        'actions' => [
                            'quick-help', 'search-score', 'search-clients', 'search-dish-clients', 'answer-upload-files',
                            'delete-answer-item-file', 'simple-upload-complaint',
                            'delete-complaint-file','add-complaint', 'unsubscribe-email', 'mark-done', 'login'
                        ],
                        'allow' => true,
                    ],
                    [
                        'actions' => [
                            'create-folder', 'delete', 'duplicate', 'archive', 'move', 'restore', 'save-point',
                            'reset-points'
                        ],
                        'allow' => true,
                        'roles' => ['foquz_admin', 'editor'],
                    ],
                ],
            ],
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'create-folder' => ['POST'],
                    'folder-select2' => ['POST'],
                    'delete' => ['POST'],
                    'duplicate' => ['POST'],
                    'restore' => ['POST'],
                    'move' => ['POST'],
                    'favorite' => ['POST'],
                    'add-complaint' => ['POST'],
                    'delete-complaint-file' => ['POST'],
                    'search-score' => ['POST'],
                    'unsubscribe-email' => ['POST'],
                    'mark-done' => ['POST'],
                    'login' => ['POST'],
                ],
            ],
            [
                'class' => ContentNegotiator::class,
                'formats' => [
                    'application/json' => Response::FORMAT_META_JSON,
                ],
            ],
        ];
    }

    public function actionSearch($q)
    {
        $results = [];
        /** @var FoquzPoll[] $polls */
        $pollsQuery = FoquzPoll::find()
            ->where([
                'foquz_poll.is_tmp' => false,
                'foquz_poll.deleted' => 0,
                'foquz_poll.status' => FoquzPoll::STATUS_NEW
            ])
            ->andWhere(['foquz_poll.company_id' => Yii::$app->user->identity->company->id])
            ->andFilterWhere(['like', 'foquz_poll.name', $q])
            ->orderBy(['is_folder' => SORT_DESC,])
            ->limit(5);

        $user = Yii::$app->user->identity;
        if($user && $user->isFilialEmployee() && $user->getUserFilials()->count() > 0) {
            $filials = ArrayHelper::getColumn($user->userFilials, 'filial_id');
            $pollsQuery
                ->leftJoin('foquz_poll_answer FPAF', 'FPAF.foquz_poll_id = foquz_poll.id')
                ->leftJoin('orders', 'orders.id = FPAF.order_id')
                ->andWhere(['in', 'IF(foquz_poll.is_auto, orders.filial_id, FPAF.answer_filial_id)', $filials])
                ->groupBy('foquz_poll.id')
                ->having(new Expression("COUNT(FPAF.id) > 0"));
        }
        if (Yii::$app->user->identity->isEditor() || Yii::$app->user->identity->isWatcher()) {
            $editorFolders = EditorFolder::find()
                ->andWhere(['user_id' => Yii::$app->user->identity->id])
                ->select('folder_id')
                ->column();
            $pollsQuery->andWhere(['foquz_poll.folder_id' => $editorFolders]);
        }

        $polls = $pollsQuery->all();

        foreach ($polls as $poll) {
            if ($poll->is_folder) {
                $results = array_merge($results, FoquzPoll::getPathForSelect2($poll->id));
            } else {
                $results[] = [
                    'type' => 0,
                    'id' => $poll->id,
                    'name' => $poll->name,
                    'category' => 'survey',
                    'status' => $poll->status,
                    'folderName' => ($poll->parentFolder ? $poll->parentFolder->name : null),
                    'level' => 0,
                    'link' => Url::to($poll->getViewUrl()),
                ];
            }
        }

        return $results;
    }

    public function actionFolders()
    {
        return [
            [
                'id' => 0,
                'name' => 'Главная страница',
                'inactive' => Yii::$app->user->identity->isEditor() || Yii::$app->user->identity->isWatcher(),
                'content' => FoquzPoll::getFolderListRecursive(null)
            ]
        ];
    }

    public function actionFavorite()
    {
        $userId = Yii::$app->getUser()->getId();
        $model = FoquzPollFavorite::findOne([
            'foquz_poll_id' => Yii::$app->getRequest()->post('id'),
            'user_id' => $userId,
        ]);

        if (null === $model) {
            (new FoquzPollFavorite([
                'foquz_poll_id' => Yii::$app->getRequest()->post('id'),
                'user_id' => $userId,
            ]))->save();
        } else {
            $model->delete();
        }

        return [];
    }

    public function actionCreateFolder()
    {
        $data = Json::decode(Yii::$app->getRequest()->getRawBody());
        $name = $data['name'];
        $description = $data['description'] ?? '';
        $folderId = (int)$data['location'];

        if (!$folderId) {
            $folderId = null;
        }

        $model = new FoquzPoll([
            'is_folder' => true,
            'folder_id' => $folderId,
            'name' => $name,
            'description' => $description,
            'is_tmp' => false,
        ]);

        if ($model->save()) {
            $user = Yii::$app->user->identity;
            if ($user->isEditor()) {
                $editorFolder = new EditorFolder();
                $editorFolder->user_id = $user->id;
                $editorFolder->folder_id = $model->id;
                $editorFolder->save();
            }
            return FoquzPoll::getFolderRecursive(null, 1);
        }

        return [
            'errors' => $model->getErrors()
        ];
    }

    public function actionPolls()
    {
        $tt = microtime(true);
        $searchModel = new FoquzPollSearch([
            'isFavorite' => true,
            'status' => FoquzPoll::STATUS_NEW,
            'pollType' => 'favorite',
            'rawSql' => false,
        ]);
        $polls = $searchModel->search2(Yii::$app->getRequest()->get());

        $searchModel = new FoquzPollSearch([
            'isFavorite' => false,
            'status' => FoquzPoll::STATUS_NEW,
            'is_folder' => false,
            'withoutTemplates' => true,
            'lastPolls' => true,
            'limit' => 12,
            'pollType' => 'lastPolls',
        ]);
        $lastPolls = $searchModel->search2(Yii::$app->getRequest()->get());

        $searchModel = new FoquzPollSearch([
            'isFavorite' => false,
            'status' => FoquzPoll::STATUS_NEW,
            'is_folder' => true,
            'pollType' => 'folderPolls',
        ]);
        $folderPolls = $searchModel->search2(Yii::$app->getRequest()->get());

        $searchModel = new FoquzPollSearch(['isFavorite' => false, 'status' => FoquzPoll::STATUS_NEW, 'is_folder' => false, 'pollType' => 'all']);
        $allPolls = $searchModel->search2(Yii::$app->getRequest()->get());

        $searchModel = new FoquzPollSearch(['status' => FoquzPoll::STATUS_ARCHIVE, 'pollType' => 'archive']);
        $archivePolls = $searchModel->search2(Yii::$app->getRequest()->get());

        $searchModel = new FoquzPollSearch(['isFavorite' => false, 'is_folder' => false, 'goalPolls' => true, 'status' => FoquzPoll::STATUS_NEW,]);
        $goalPolls = $searchModel->search2(Yii::$app->getRequest()->get());

        return [
            'favorite' => $polls['results'],
            'favoriteLength' => $polls['counter'],

            'lastPolls' => $lastPolls['results'],
            'lastPollsLength' => $lastPolls['counter'],

            'folderPolls' => $folderPolls['results'],
            'folderPollsLength' => $folderPolls['counter'],

            'allPolls' => $allPolls['results'],
            'allPollsLength' => $allPolls['counter'],

            'archivedPolls' => $archivePolls['results'],
            'archivedPollsLength' => $archivePolls['counter'],

            'goalPolls' => $goalPolls['results'],
            'goalPollsLength' => $goalPolls['counter'],

            'totalLength' => $this->getTotalPollsLength(),
        ];
    }

    public function actionFolder($id, $name = '')
    {
        $bread = [
            [
                'id' => 0,
                'label' => 'Опросы'
            ],
        ];

        switch ($id) {
            case 'favorite':
                $searchModel = new FoquzPollSearch([
                    'isFavorite' => true,
                    'status' => FoquzPoll::STATUS_NEW,
                    'limit' => 20,
                    'isPager' => true,
                    'pollType' => 'favorite',
                    'offset' => Yii::$app->getRequest()->get('currentPage', 0),
                ]);
                $polls = $searchModel->search2(Yii::$app->getRequest()->get());
                $name = 'Избранное';
                $bread[] = [
                    'id' => 0,
                    'label' => $name,
                ];
                break;
            case 'lastPolls':
                $searchModel = new FoquzPollSearch([
                    'isFavorite' => false,
                    'status' => FoquzPoll::STATUS_NEW,
                    'is_folder' => false,
                    'lastPolls' => true,
                    'limit' => 20,
                    'isPager' => true,
                    'offset' => Yii::$app->getRequest()->get('currentPage', 0),
                    'pollType' => 'lastPolls',
                ]);
                $polls = $searchModel->search2(Yii::$app->getRequest()->get());
                $name = 'Последние опросы';
                $bread[] = [
                    'id' => 0,
                    'label' => $name,
                ];
                break;
            case 'folders':
                $searchModel = new FoquzPollSearch([
                    'isFavorite' => false,
                    'limit' => 20,
                    'isPager' => true,
                    'offset' => Yii::$app->getRequest()->get('currentPage', 0),
                    'status' => FoquzPoll::STATUS_NEW,
                    'is_folder' => true
                ]);
                $polls = $searchModel->search2(Yii::$app->getRequest()->get());
                $name = 'Папки';
                $bread[] = [
                    'id' => 0,
                    'label' => $name,
                ];
                break;
            case 'allPolls':
                $searchModel = new FoquzPollSearch([
                    'pollType' => 'all',
                    'isFavorite' => false,
                    'limit' => 20,
                    'status' => FoquzPoll::STATUS_NEW,
                    'is_folder' => false,
                    'isPager' => true,
                    'offset' => Yii::$app->getRequest()->get('currentPage', 0),
                ]);
                $polls = $searchModel->search2(Yii::$app->getRequest()->get());
                $name = 'Все опросы';
                $bread[] = [
                    'id' => 0,
                    'label' => $name,
                ];
                break;
            case 'archive':
                $searchModel = new FoquzPollSearch([
                    'pollType' => 'archive',
                    'status' => FoquzPoll::STATUS_ARCHIVE,
                    'limit' => 20,
                    'isPager' => true,
                    'offset' => Yii::$app->getRequest()->get('currentPage', 0),
                ]);
                $polls = $searchModel->search2(Yii::$app->getRequest()->get());
                $name = 'В архиве';
                $bread[] = [
                    'id' => 0,
                    'label' => $name,
                ];
                break;
            case 'goalPolls':
                $searchModel = new FoquzPollSearch([
                    'isFavorite' => false,
                    'limit' => 20,
                    'status' => FoquzPoll::STATUS_NEW,
                    'is_folder' => false,
                    'isPager' => true,
                    'goalPolls' => true,
                    'offset' => Yii::$app->getRequest()->get('currentPage', 0),
                ]);
                $polls = $searchModel->search2(Yii::$app->getRequest()->get());
                $name = 'Опросы с достигнутыми целями';
                $bread[] = [
                    'id' => 0,
                    'label' => $name,
                ];
                break;
            default:
                $model = $this->findModel($id);
                $name = $model->name;
                $id = $model->id;
                $searchModel = new FoquzPollSearch([
                    'folder_id' => $id,
                    'status' => FoquzPoll::STATUS_NEW,
                    'openFolder' => true,
                    'limit' => 20,
                    'isPager' => true,
                    'offset' => Yii::$app->getRequest()->get('currentPage', 0),
                ]);
                $tmpVar = FoquzPoll::createPath($id);
                krsort($tmpVar);
                $bread = array_merge($bread, $tmpVar);


                $polls = $searchModel->search2(Yii::$app->getRequest()->get());
        }

        $lastPage = $polls['pagerCount'] >= 8;

        if ($searchModel->pollType === 'lastPolls') {
            $lastPage = false;
        }

        $archiveSearchModel = new FoquzPollSearch([
            'status' => FoquzPoll::STATUS_ARCHIVE,
            'limit' => 8,
            'isPager' => true,
            'offset' => Yii::$app->getRequest()->get('currentPage', 0),
        ]);
        $archive = $archiveSearchModel->search2(Yii::$app->getRequest()->get());
        $archiveCount = $archive['counter'];

        $allSearchModel = new FoquzPollSearch([
            'isFavorite' => false,
            'limit' => 8,
            'status' => FoquzPoll::STATUS_NEW,
            'is_folder' => false,
            'isPager' => true,
            'offset' => Yii::$app->getRequest()->get('currentPage', 0),
        ]);
        $all = $allSearchModel->search2(Yii::$app->getRequest()->get());
        $allCount = $all['counter'];

        return [
            'folderName' => $name,
            'foldersAndPoll' => $polls,
            'lastPage' => !$lastPage,
            'url' => Url::to(['/foquz/foquz-poll/index', 'id' => $id,]),
            'bread' => $bread,
            'allCount' => $allCount,
            'archiveCount' => $archiveCount,
            'totalLength' => $this->getTotalPollsLength(),
        ];
    }

    public function getTotalPollsLength()
    {
        $totalPollsLength = FoquzPoll::find()->where([
            'foquz_poll.company_id' => Yii::$app->user->identity->company->id,
            'is_tmp' => 0,
            'is_folder' => 0,
            'deleted' => 0,
            'foquz_poll.status' => FoquzPoll::STATUS_NEW
        ]);

        $user = Yii::$app->user->identity;
        if($user && !$user->allFilials && $user->isFilialEmployee()) {
            $filials = ArrayHelper::getColumn($user->userFilials, 'filial_id');
            $totalPollsLength
                ->leftJoin('foquz_poll_answer FPAF', 'FPAF.foquz_poll_id = foquz_poll.id')
                ->leftJoin('orders', 'orders.id = FPAF.order_id')
                ->andWhere(['in', 'IF(foquz_poll.is_auto, orders.filial_id, FPAF.answer_filial_id)', $filials])
                ->groupBy('foquz_poll.id')
                ->having(new Expression("COUNT(FPAF.id) > 0"));
        }
        if (Yii::$app->user->identity->isEditor() || Yii::$app->user->identity->isWatcher()) {
            $totalPollsLength->andWhere([
                'folder_id' => EditorFolder::find()
                    ->select('folder_id')
                    ->where(['user_id' => Yii::$app->user->identity->id])
                    ->column()
            ]);
        }

        return $totalPollsLength->count();
    }

    public function actionDelete($id)
    {
        $company_id = \Yii::$app->user->identity->company->id;


        $model = $this->findModel($id);
        if($model->is_published || $model->company_id!=$company_id) {
            Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
            Yii::$app->response->statusCode=403;
            return [];
        }


        ///print_r(FoquzPoll::getChildIds($model->id)); exit;
        if($model->is_folder && FoquzPoll::getChildIds($model->id)) {
            $this->response->statusCode = 403;
            return ['errors' => ['Папка не пуста']];
        }



        $model->deleted = 1;
        $model->deleted_at = date('Y-m-d H:i:s');
        $model->deleted_by = Yii::$app->user->id;

        foreach($model->folders as $folder) {
            if($folder->is_folder) {
                FoquzPoll::updateAll([
                    'deleted' => true,
                    'deleted_at' => date('Y-m-d H:i:s'),
                    'deleted_by' => Yii::$app->user->id
                ], ['folder_id' => $folder->id]);
            }
            $folder->deleted = 1;
            $folder->deleted_at = date('Y-m-d H:i:s');
            $folder->deleted_by = Yii::$app->user->id;
            $folder->save();
        }

        $model->save();


        $folders = FoquzPoll::find()->where([
            'is_tmp' => 0,
            'is_folder' => 1,
            'company_id' => $company_id
        ])->orderBy('name')->all();
        $items = [];
        foreach($folders as $folder) {
            $items[] = [
                'id' => $folder->id,
                'name' => $folder->name,
                'folder_id' => $folder->folder_id
            ];
        }

        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        return ['items' => $items];
    }

    public function actionArchive($id)
    {
        $model = $this->findModel($id);
        $model->status = FoquzPoll::STATUS_ARCHIVE;
        $model->save();

        return [];
    }

    public function actionDuplicate($id)
    {
        $original = $this->findModel($id);
        $new = new FoquzPoll(['createFirstQuestion' => false]);

        $new->attributes = $original->attributes;
        $new->created_at = time();
        $new->updated_at = time();
        $new->created_by = Yii::$app->user->id;
        $new->updated_by = Yii::$app->user->id;
        $new->key = uniqid('F');
        $new->trigger_time = $original->trigger_time;
        $new->trigger_orders = $original->trigger_orders;
        $new->company_id = Yii::$app->user->identity->company->id;
        $new->is_published = false;
        $new->is_active = 1;
        $new->is_short_link = 0;
        $new->answers_count = 0;
        $new->sent_answers_count = 0;
        $new->opened_answers_count = 0;
        $new->in_progress_answers_count = 0;
        $new->filled_answers_count = 0;
        if($new->save()) {
            if($original->is_auto)
                $original->copyPoints($new->id);

            if($original->pages)
                $original->copyPages($new->id);

            if($original->design)
                $original->copyDesign($new->id);

            if($original->scoresInterpretationRanges) {
                $original->copyScoresInterpretationRanges($new->id);
            }

            $original->copyQuestions(Yii::$app->user->id, $new->id);

            $original->copyChannels($new->id);

            $fav = FoquzPollFavorite::findOne(['foquz_poll_id' => $id, 'user_id' => Yii::$app->user->id]);

            if ($fav) {
                (new FoquzPollFavorite([
                    'user_id' => Yii::$app->user->id,
                    'foquz_poll_id' => $new->id,
                ]))->save();
            }

            return $new->toArray();
        } else {
            return [
                'errors' => $new->getErrors(),
            ];
        }
    }

    public function actionRestore($id)
    {
        $model = $this->findModel($id);
        $model->status = $model::STATUS_NEW;
        $model->save();

        return [];
    }

    public function actionMove()
    {
        $item = Yii::$app->getRequest()->post('item');
        $to = Yii::$app->getRequest()->post('to');
        if($to == 0)
            $to = null;

        FoquzPoll::updateAll([
            'folder_id' => $to,
        ], ['id' => $item]);

        return [];
    }

    public function actionUserPoints()
    {
        $output = array_merge([], FoquzPointItem::find()
            ->alias('FPI')
            ->leftJoin('foquz_question', 'foquz_question.point_id = FPI.id AND foquz_question.is_source = 1')
            ->leftJoin('foquz_question_intermediate_block_setting', 'foquz_question_intermediate_block_setting.question_id = foquz_question.id')
            ->select([
                'FPI.id',
                'FPI.position',
                'FPI.name',
                'question' => 'FPI.description',
                'isSystem' => 'FPI.is_system',
                'screenType' => 'foquz_question_intermediate_block_setting.screen_type',
            ])
            ->where([
                'FPI.company_id' => Yii::$app->params['company']->id,
                'FPI.is_system' => false,
            ])
            ->orderBy(['FPI.is_system' => SORT_DESC, 'FPI.position' => SORT_ASC])
            ->asArray()
            ->all());

        foreach ($output as $k => $systemPoint) {
            $systemPoint['isSystem'] = (bool)$systemPoint['isSystem'];
            $output[$k] = $systemPoint;
        }

        return $output;
    }

    public function actionPointsByType($typeId = null, $sourceId = null)
    {
        if($sourceId === '') $sourceId = null;
        if($typeId === '') $typeId = null;
        $relations = FoquzPointTypeRelation::find()
            ->where(['foquz_order_type_id' => $typeId, 'foquz_order_source_id' => $sourceId])
            ->orWhere(['foquz_order_type_id' => null, 'foquz_order_source_id' => $sourceId])
            ->orWhere(['foquz_order_type_id' => $typeId, 'foquz_order_source_id' => null])
            ->leftJoin('foquz_point_item', 'foquz_point_item.id = foquz_point_type_relation.foquz_point_item_id')
            ->andWhere(['in', 'company_id', [null, Yii::$app->user->identity->company->id]])
            ->all();
        $output = [];
        foreach ($relations as $relation) {
            $output[] = FoquzPointItem::find()
                ->alias('FPI')
                ->select([
                    'FPI.id',
                    'FPI.position',
                    'FPI.name',
                    'question' => 'FPI.description',
                    'isSystem' => 'FPI.is_system',
                ])
                ->where([
                    'FPI.id' => $relation->foquz_point_item_id,
                ])
                ->orderBy(['FPI.is_system' => SORT_DESC, 'FPI.position' => SORT_ASC])
                ->asArray()
                ->one();
        }

        $commonPoints = FoquzPointItem::find()
            ->alias('FPI')
            ->select([
                'FPI.id',
                'FPI.position',
                'FPI.name',
                'question' => 'FPI.description',
                'isSystem' => 'FPI.is_system'
            ])
            ->where([
                'FPI.company_id' => Yii::$app->user->identity->company->id,
                'FPI.is_system' => false
            ])
            ->andWhere(['not in', 'FPI.id', FoquzPointTypeRelation::find()->select('foquz_point_item_id')->distinct()->column()])
            ->orderBy(['FPI.is_system' => SORT_DESC, 'FPI.position' => SORT_ASC])
            ->asArray()
            ->all();

        $output = array_merge($output, $commonPoints);

        foreach ($output as $k => $systemPoint) {
            $systemPoint['isSystem'] = (bool)$systemPoint['isSystem'];
            $output[$k] = $systemPoint;
        }

        return $output;
    }

    public function actionSavePoint($id)
    {
        $model = $this->findModel($id);

        $name = Yii::$app->getRequest()->post('name');
        $point = FoquzPointItem::find()->where(['name' => $name, 'company_id' => [null, Yii::$app->user->identity->company->id]])->one();
        if ($point) {
            return [
                'error' => 'Точка контакта с таким названием уже добавлена в систему'
            ];
        } else {
            $modelForm = new FoquzPointItem(Yii::$app->getRequest()->post());
            $modelForm->foquz_poll_id = $model->id;
            $modelForm->is_active = 1;
            $modelForm->position = (int)FoquzPointItem::find()
                ->where([
                    'company_id' => Yii::$app->user->identity->company->id,
                ])
                ->count();
            if ($modelForm->save()) {
                return $modelForm->toArray();
            }
        }

        return [
            'errors' => $modelForm->getErrors(),
        ];
    }

    public function actionResetPoints($pollId)
    {
        $model = $this->findModel($pollId);

        $points = FoquzPointItem::getTotalPoints($pollId);
        $pointsSelected = FoquzPointItem::getTotalPoints($pollId, true);

        $conditions = FoquzPointItem::getConditions($pollId);

        return [
            'points' => $points,
            'pointsSelected' => $pointsSelected,
            'conditions' => $conditions,
            'model' => $model->toArray(),
        ];
    }

    public function actionAddComplaint($id)
    {
        $foquzAnwer = FoquzPollAnswer::findOne($id);

        if (!$foquzAnwer) {
            throw new NotFoundHttpException('Ответ не найден');
        }

        $model = new FoquzComplaint([
            'foquz_poll_id' => $foquzAnwer->foquz_poll_id,
            'foquz_poll_answer_id' => $id,
        ]);
        $model->text = Yii::$app->getRequest()->post('text');
        $model->save();

        $foquzAnwer->status = FoquzPollAnswer::STATUS_IN_PROGRESS;
        $foquzAnwer->save();

        $foquzAnwer->addProcessingIfNeeded();

        return $model->toArray();
    }

    /**
     * @deprecated
     * @return array
     * @throws NotFoundHttpException
     * @throws \Throwable
     * @throws \yii\db\StaleObjectException
     */
    public function actionDeleteComplaintFile()
    {
        $id = Yii::$app->getRequest()->post('id');
        $model = FoquzComplaintFile::findOne($id);

        if (!$model) {
            throw new NotFoundHttpException('Файл не найден');
        }

        @unlink($model->file_full_path);
        $model->delete();

        return [];
    }

    /**
     * @deprecated (оставлен для старого прохождения в качестве заглушки)
     * @return array
     */
    public function actionDeleteAnswerItemFile()
    {
        return [];
    }
    /**
     * @OA\Post (
     *     path="/foquz/ajax/answer-upload-files",
     *     tags={"Прохождение"},
     *     summary="Загрузка файла для типа вопроса Загрузка файла",
     *     @OA\Parameter(
     *         name="authKey",
     *         description="Ключ анкеты",
     *         in="query",
     *         required=true,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="question_id",
     *         description="ID вопроса",
     *         in="query",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *           @OA\MediaType(
     *               mediaType="multipart/form-data",
     *               encoding="text/plain",
     *               @OA\Schema(
     *                   @OA\Property(
     *                        property="files",
     *                        description="Файлы",
     *                        type="array",
     *                        @OA\Items(
     *                            type="string",
     *                            format="binary"
     *                        ),
     *                   ),
     *               ),
     *           ),
     *       ),
     *       @OA\Response(
     *           response=200,
     *           description="Файлы успешно загружены",
     *           @OA\JsonContent(
     *               @OA\Property(
     *                   property="errors",
     *                   type="array",
     *                   @OA\Items(
     *                       type="string",
     *                       description="Ошибки при загрузке",
     *                       example="Файл 1.jpg имеет недопустимое расширение."
     *                   )
     *               ),
     *               @OA\Property(
     *                   property="files",
     *                   type="array",
     *                   @OA\Items(
     *                       @OA\Property(
     *                           property="id",
     *                           type="integer",
     *                           description="ID файла",
     *                           example=1,
     *                       ),
     *                       @OA\Property(
     *                           property="type",
     *                           type="string",
     *                           description="Тип файла",
     *                           example="picture",
     *                           enum={"picture", "audio", "video"}
     *                       ),
     *                       @OA\Property(
     *                           property="link",
     *                           type="string",
     *                           description="Относительная ссылка на файл",
     *                           example="/uploads/foquz/answer/1/1.jpg"
     *                       ),
     *                       @OA\Property(
     *                           property="image",
     *                           type="string",
     *                           description="Относительная ссылка на изображение (только для видео)",
     *                           example="/uploads/foquz/answer/1/1.jpg"
     *                       ),
     *                       @OA\Property(
     *                           property="origin_name",
     *                           type="string",
     *                           description="Оригинальное имя файла",
     *                           example="1.jpg"
     *                       ),
     *                   ),
     *              ),
     *         ),
     *     ),
     * )
     *
     */
    public function actionAnswerUploadFiles($authKey, $question_id, $withoutCountLimit = false)
    {
        if ($authKey === 'dummy' || $authKey === 'dummyDesign') {
            $dummy = true;
            $basePath = 'uploads/foquz/answer/dummy';
        } else {
            $dummy = false;
            $answerModel = FoquzPollAnswer::findOne(['auth_key' => $authKey]);
            if (!$answerModel) {
                return ['errors' => 'Анкета не найдена'];
            }
            $basePath = "uploads/foquz/answer/{$answerModel->id}/{$question_id}";
        }

        $question = FoquzQuestion::findOne($question_id);

        $fullPath = Yii::getAlias('@app/web/' . $basePath);
        if (!file_exists($fullPath)) {
            if (!mkdir($fullPath, 0777, true) && !is_dir($fullPath)) {
                throw new \RuntimeException(sprintf('Directory "%s" was not created', $fullPath));
            }
        }

        $files = UploadedFile::getInstancesByName('files');

        $errors = [];
        $answerFiles = [];
        $i = 0;

        foreach ($files as $file) {
            if (!$withoutCountLimit && $question && $question->files_length && $i >= $question->files_length) {
                $errors[] = Yii::t(
                        'validation',
                        'Максимальное количество файлов:'
                    ) . ' ' . $question->files_length;
                break;
            }
            $i++;

            $model = new UploadForm();
            $model->company_filesize_limit = $question->poll->company->filesize_limit;
            $model->file = $file;

            if (in_array($model->file->extension, UploadForm::PICTURE_EXTENSIONS)) {
                $model->scenario = UploadForm::SCENARIO_PICTURE;
            } elseif (in_array($model->file->extension, UploadForm::AUDIO_EXTENSIONS)) {
                $model->scenario = UploadForm::SCENARIO_AUDIO;
            } elseif (in_array($model->file->extension, UploadForm::VIDEO_EXTENSIONS)) {
                $model->scenario = UploadForm::SCENARIO_VIDEO;
            } else {
                $errors[] = 'Файл ' . $model->file->name . ' имеет недопустимое расширение.';
                continue;
            }

            $fileName = uniqid() . '.' . $model->file->extension;
            $imageLink = null;

            if ($model->validate() && $model->file->saveAs($fullPath . '/' . $fileName)) {
                $link = '/' . $basePath . '/' . $fileName;

                if ($model->scenario === UploadForm::SCENARIO_VIDEO) {
                    try {
                        $ffmpeg = FFMpeg::create([
                            'ffmpeg.binaries'  => Yii::$app->params['ffmpeg_binaries'],
                            'ffprobe.binaries' => Yii::$app->params['ffprobe_binaries']
                        ]);
                        if (count($ffmpeg->getFFProbe()->streams($fullPath . '/' . $fileName)->videos()) > 0) {
                            $video = $ffmpeg->open($fullPath . '/' . $fileName);
                            $video
                                ->frame(TimeCode::fromSeconds(5))
                                ->save($fullPath . '/' . $fileName . '.jpg');

                            $imageLink = '/' . $basePath . '/' . $fileName . '.jpg';
                        }
                    } catch (\Exception $e) {
                        // do nothing
                    }
                } elseif ($model->scenario === UploadForm::SCENARIO_PICTURE) {
                    $extDotPos = strrpos($fullPath . '/' . $fileName, '.');
                    if ($extDotPos !== false) {
                        $thumbnailPath = substr($fullPath . '/' . $fileName, 0, $extDotPos) . '_preview.webp';
                        if (ImageHelper::createThumbnail($fullPath . '/' . $fileName, $thumbnailPath, null, 200)) {
                            $imageLink = '/' . $basePath . '/' . basename($thumbnailPath);
                        }
                    }
                }

                if ($dummy) {
                    $id = random_int(1, 100);
                } else {
                    $fileModel = new FoquzPollAnswerItemFile([
                        'foquz_poll_answer_id' => $answerModel->id,
                        'foquz_question_id' => $question_id,
                        'file_path' => $link,
                        'image_link' => $imageLink ?? null,
                        'file_full_path' => $fullPath . '/' . $fileName,
                        'origin_name' => $model->file->baseName . '.' . $model->file->extension,
                    ]);
                    $fileModel->save();

                    $id = $fileModel->id;
                }

                $answerFiles[] = [
                    'id' => $id,
                    'type' => $model->scenario,
                    'link' => $link,
                    'image' => $imageLink ?? null,
                    'origin_name' => $model->file->baseName . '.' . $model->file->extension,
                ];
            } else {
                $errors = ArrayHelper::merge($errors, $model->getErrors());
            }
        }

        $errors = array_map(static function($item) {
            return str_replace('МиБ', 'МБ', $item);
        }, $errors);


        return $this->asJson([
            'errors' => $errors,
            'files' => $answerFiles,
        ]);
    }

    public function actionSimpleUploadComplaint($id)
    {
        $model = new UploadForm();

        $basePath = "uploads/foquz/{$id}/complaint";
        $fullPath = Yii::getAlias("@app/web/{$basePath}");

        if (false === file_exists($fullPath)) {
            mkdir($fullPath, 0777, true);
        }



        if (Yii::$app->request->getIsPost()) {
            $model->file = UploadedFile::getInstance($model, 'file');


            if ($model->file && $model->validate()) {


                $fileName = uniqid() . '.' . $model->file->extension;
                if ($res = $model->file->saveAs($fullPath . '/' . $fileName)) {

                    $fileAbsName = '/' . $basePath . '/' . $fileName;
                    $fileModel = new FoquzComplaintFile([
                        'foquz_poll_answer_id' => $id,
                        'file_path' => $fileAbsName,
                        'file_full_path' => $fullPath . '/' . $fileName,
                    ]);
                    $fileModel->save();

                    return [
                        'image' => $fileAbsName,
                        'id' => $fileModel->id,
                    ];
                }
            } else {
                print_r($model->getErrors());
                exit;
            }

        }

        return ['errors' => ['message' => 'wrong params']];
    }

    public function actionSearchScore($questionId = null, $from = null, $to = null)
    {
        $params = Yii::$app->request->get();
        $questionnaires = $params['questionnaires'] ?? null;
        $tags = $params['tags'] ?? [];
        $tagsOperation = $params['tagsOperation'] ?? null;
        $filials = $params['filials'] ?? [];
        $devices = $params['devices'] ?? [];
        $question = FoquzQuestion::findOne($questionId);

        $answersQuery = $question->getDoneAnswers();

        if($from !== null && $to !== null)
            $answersQuery
                ->leftJoin('foquz_poll_answer_item', 'foquz_poll_answer.id = foquz_poll_answer_item.foquz_poll_answer_id')
                ->where([
                    'between',
                    'foquz_poll_answer_item.created_at',
                    date('Y-m-d 00:00:00', strtotime($from)),
                    date('Y-m-d 23:59:59', strtotime($to))
                ]);
        if($questionnaires)
            $answersQuery->andWhere(['foquz_poll_answer.status' => $questionnaires]);
        if(count($tags) > 0 && $tagsOperation) {
            $answersQuery->leftJoin('foquz_contact_tag', 'foquz_contact_tag.contact_id = foquz_poll_answer.contact_id');
            if($tagsOperation == 1) {
                $answersQuery->andWhere(['in', 'foquz_contact_tag.tag_id', $tags]);
            } else {
                $answersQuery
                    ->andWhere(['in', 'tag_id', $tags])
                    ->andHaving("(".(new Query())->select('COUNT(DISTINCT tag_id)')
                            ->from('foquz_contact_tag')
                            ->where('contact_id = foquz_poll_answer.contact_id')
                            ->andWhere(['in', 'foquz_contact_tag.tag_id', $tags])
                            ->createCommand()->rawSql.") =".count($tags)
                    );
            }
        }
        if(count($filials) > 0) {
            $answersQuery
                ->leftJoin('foquz_poll', 'foquz_poll_answer.foquz_poll_id = foquz_poll.id')
                ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id');
            $index = array_search(0, $filials);
            if($index !== false) {
                $filials[$index] = null;
            }
            $answersQuery->andWhere(['in', 'IF(foquz_poll.is_auto, orders.filial_id, foquz_poll_answer.answer_filial_id)', $filials]);
        }
        if(count($devices) > 0) {
            $answersQuery->andWhere(['in', 'foquz_poll_answer.device', $devices]);
        }

        $answers = $answersQuery->select(['foquz_poll_answer.id'])->all();

        $answerIds = ArrayHelper::map(
            $answers, 'id', 'id'
        );
        if (empty($answerIds)) {
            return [];
        }

        $scourDishes = new FoquzPollDishScoreSearch([
            'answer_id' => $answerIds,
            'q' => Yii::$app->getRequest()->get('q'),
            'sortBy' => Yii::$app->getRequest()->get('sort'),
            'page' => Yii::$app->getRequest()->get('page'),
        ]);

        return $scourDishes->search([]);
    }

    public function actionSearchDishClients($id)
    {
        $page = Yii::$app->request->get('page') ?? 1;
        $get = Yii::$app->request->get();
        $questionnaires = $get['questionnaires'] ?? null;
        $tags = $get['tags'] ?? [];
        $tagsOperation = $get['tagsOperation'] ?? null;
        $filials = $get['filials'] ?? [];
        $devices = $get['devices'] ?? [];

        $question = FoquzQuestion::findOne($id);
        $answersQuery = (new Query())
            ->select('foquz_poll_answer.id, foquz_contact.last_name, foquz_contact.first_name, foquz_contact.patronymic, foquz_contact.phone, foquz_contact.email, foquz_poll_answer.updated_at, orders.created_time')
            ->addSelect(new Expression('IF(`orders`.`number` is null or `orders`.`number` = "", `orders`.`id`, `orders`.`number`) as orderId'))
            ->from('foquz_poll_answer')
            ->leftJoin('foquz_poll_answer_item', 'foquz_poll_answer_item.foquz_poll_answer_id = foquz_poll_answer.id')
            ->leftJoin('foquz_contact', 'foquz_contact.id = foquz_poll_answer.contact_id')
            ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
            ->leftJoin('foquz_poll_dish_score', 'foquz_poll_answer.id = foquz_poll_dish_score.answer_id')
            ->where([
                'foquz_question_id' => $question->id,
                'dish_id' => $get['dish_id'],
                'score' => $get['rating']
            ]);
        if(isset($get['from']) && isset($get['to'])) {
            $answersQuery->andWhere(['between', 'foquz_poll_answer.created_at', date('Y-m-d 00:00:00', strtotime($get['from'])), date('Y-m-d 23:59:59', strtotime($get['to']))]);
        }
        if($questionnaires)
            $answersQuery->andWhere(['foquz_poll_answer.status' => $questionnaires]);
        if(count($tags) > 0 && $tagsOperation) {
            $answersQuery->leftJoin('foquz_contact_tag', 'foquz_contact_tag.contact_id = foquz_poll_answer.contact_id');
            if($tagsOperation == 1) {
                $answersQuery->andWhere(['in', 'foquz_contact_tag.tag_id', $tags]);
            } else {
                $answersQuery
                    ->andWhere(['in', 'tag_id', $tags])
                    ->andHaving("(".(new Query())->select('COUNT(DISTINCT tag_id)')
                            ->from('foquz_contact_tag')
                            ->where('contact_id = foquz_poll_answer.contact_id')
                            ->andWhere(['in', 'foquz_contact_tag.tag_id', $tags])
                            ->createCommand()->rawSql.") =".count($tags)
                    );
            }
        }
        if(count($filials) > 0) {
            $answersQuery
                ->leftJoin('foquz_poll', 'foquz_poll_answer.foquz_poll_id = foquz_poll.id');
            $index = array_search(0, $filials);
            if($index !== false) {
                $filials[$index] = null;
            }
            $answersQuery->andWhere(['in', 'IF(foquz_poll.is_auto, orders.filial_id, foquz_poll_answer.answer_filial_id)', $filials]);
        }
        if(count($devices) > 0) {
            $answersQuery->andWhere(['in', 'foquz_poll_answer.device', $devices]);
        }
        if(Yii::$app->request->get('q')) {
            $answersQuery->andWhere(['OR',
                ['like', 'foquz_contact.phone', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.last_name', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.first_name', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.patronymic', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.email', Yii::$app->request->get('q')],
                ['like', 'orders.number', Yii::$app->request->get('q')],
                ['like', 'orders.id', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(foquz_poll_answer.updated_at,"%d.%m.%Y")', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(orders.created_time,"%d.%m.%Y")', Yii::$app->request->get('q')],
            ]);
        }

        $recordsCount = $answersQuery->count();
        $answers = $answersQuery
            ->limit(30)
            ->offset(($page - 1) * 30)
            ->all();
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $data = [];
        foreach($answers as $answer) {
            $data[] = [
                'name' => implode(' ', [$answer['last_name'], $answer['first_name'], $answer['patronymic']]) ?? '',
                'phone' => $answer['phone'] ?? '',
                'email' => $answer['email'] ?? '',
                'passedAt' => date('d.m.Y', strtotime($answer['updated_at'])),
                'orderId' => $answer['orderId'] ?? '',
                'orderCreatedAt' => $answer['created_time'] ? date('d.m.Y', strtotime($answer['created_time'])) : '',
            ];
        }
        return [
            'clients' => $data,
            'lastPage' => $recordsCount <= ($page * 30)
        ];
    }

    public function actionSearchClients($id)
    {

        $question = FoquzQuestion::findOne($id);
        if (!$question) {
            throw new HttpException(403 ,'Доступ запрещён');
        }

        $poll = null; 
        if (Yii::$app->user->isGuest) {
            $link = Yii::$app->request->get("link", "");
            if (strlen($link)<15) throw new HttpException(403 ,'Доступ запрещён');
            $linkData = FoquzPollStatsLink::find()->where(['like', 'link', $link])->one();
            if($linkData === null || $linkData->right_level === FoquzPollStatsLink::RIGHT_BLOCK) {
               throw new HttpException(403 ,'Доступ запрещён');
            }            
            $poll = FoquzPoll::findOne($linkData->foquz_poll_id);
        } else {
            $company =Yii::$app->user->identity->company;  
            $poll = FoquzPoll::find()->where(["company_id"=>$company->id, "id"=>$question->poll_id])->one();
        }

        if (!$poll) {
            throw new HttpException(403 ,'Доступ запрещён');
        }

        $filterSetting = null;
        if (!Yii::$app->user->isGuest && !Yii::$app->request->get('link') && !Yii::$app->request->get('link-key')) {
            $filterSetting = FoquzPollStatFilterSettings::findOne([
                'foquz_poll_id' => $poll->id,
                'user_id' => Yii::$app->user->id
            ])->filter_settings ?? null;
        }

        $page = Yii::$app->request->get('page') ?? 1;
        $get = Yii::$app->request->get();
        $answersQuery = (new Query())
            ->distinct()
            ->select('foquz_poll_answer.id, foquz_contact.last_name, foquz_contact.first_name, foquz_contact.patronymic, foquz_contact.phone, foquz_contact.email, foquz_poll_answer.updated_at, orders.created_time, filials.name as filialName')
            ->addSelect(new Expression('IF(`orders`.`number` is null or `orders`.`number` = "", `orders`.`id`, `orders`.`number`) as orderId'))
            ->from('foquz_poll_answer')
            ->leftJoin('foquz_poll_answer_item', 'foquz_poll_answer_item.foquz_poll_answer_id = foquz_poll_answer.id')
            ->leftJoin('foquz_contact', 'foquz_contact.id = foquz_poll_answer.contact_id')
            ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
            ->leftJoin('foquz_question', 'foquz_poll_answer_item.foquz_question_id = foquz_question.id')
            ->leftJoin('filials', 'filials.id = foquz_poll_answer.answer_filial_id')
            ->where(['foquz_poll_answer_item.foquz_question_id' => $question->id, 'foquz_poll_answer.foquz_poll_id' => $poll->id])
            ->orderBy('foquz_poll_answer.updated_at DESC');

        if ($filterSetting) {
            $answersQuery->andWhere(['foquz_poll_answer.id' => FoquzPollAnswer::filterByAnswer($filterSetting, $question->poll_id)]);
        }

        if ($question->main_question_type === FoquzQuestion::TYPE_VARIANT_STAR) {
            $answersQuery->addSelect('foquz_poll_answer_item.answer');
        } elseif (
            in_array($question->main_question_type, [FoquzQuestion::TYPE_SIMPLE_MATRIX, FoquzQuestion::TYPE_NPS_RATING], true)
        ) {
            $answersQuery->addSelect('foquz_poll_answer_item.detail_item');
            $answersQuery->addSelect('foquz_poll_answer_item.self_variant');
        }

        if (isset($get['skipped']) && $get['skipped']) {
            $answersQuery->andWhere(['foquz_poll_answer_item.skipped' => 1]);
        }

        if (isset($get['field'])) {
            if ($get['field'] === 'answer') {
                if ($question->main_question_type === FoquzQuestion::TYPE_CHOOSE_MEDIA || $question->main_question_type === FoquzQuestion::TYPE_SMILE_RATING) {
                    $answersQuery
                        ->andWhere(['is not', 'answer', null])
                        ->andWhere([
                            'OR',
                            ['answer' => $get['value']],
                            ['like', 'answer', $get['value']]
                        ]);
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_GALLERY_RATING && is_numeric($get['media_id'])) {
                    $answersQuery->andWhere(['NOT', ['answer' => null]]);
                    $answersQuery->andWhere(['=', "json_extract(answer, '$.\"" . $get['media_id'] . "\"')", $get['value']]);
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_VARIANT_STAR) {
                    if (isset($get['invert']) && $get['invert'] === "1" && !empty($get['variant']) && is_numeric($get['variant'])) {
                        $variant = $get['variant'];
                        $answersQuery->andWhere(['NOT LIKE', 'answer->\'$.extra."' . $variant . '"\'', $get['value']]);
                    } elseif (isset($get['invert']) && $get['invert'] === "1") {
                        $answersQuery->andWhere(['not like', 'answer', $get['value']]);
                    } elseif (!empty($get['variant']) && is_numeric($get['variant'])) {
                        $variant = $get['variant'];
                        $value = Yii::$app->db->quoteValue($get['value']);
                        $answersQuery->andWhere(['OR',
                            'answer->\'$.extra."' . $variant . '"\' = ' . $value,
                            'answer->\'$."' . $variant . '"\' = ' . $value
                        ]);
                    } else {
                        $answersQuery->andWhere(['like', 'answer', $get['value']]);
                    }
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_PRIORITY) {
                    /*if ($question->donor) {
                        $answersQuery->andWhere(['NOT', ['answer' => null]]);
                        $answersQuery->andWhere("json_contains(detail_item, '\"" . $get['variant'] . "\"')");
                    }*/
                } else if ($get['value'] === 'null' && $question->main_question_type === FoquzQuestion::TYPE_FORM) {
                    foreach ($question->formFields as $ff) {
                        $answersQuery->andWhere(["json_extract(answer, '$.\"" . $ff->id . "\"')" => '']);
                    }
                } else if ($get['value'] === 'null' && $question->main_question_type === FoquzQuestion::TYPE_TEXT_ANSWER) {
                    $answersQuery->andWhere('(answer IS NULL OR answer = "") AND skipped=0');
                } else {
                    $answersQuery->andWhere($get['value'] === 'null' ? 'answer IS NULL OR answer = ""' : 'answer IS NOT NULL AND answer != ""');
                }
            } elseif ($get['field'] === 'files') {
                $where = $get['value'] === 'withFiles' ? '> 0' : ' = 0';
                $answersQuery->addSelect(new Expression(' COUNT(fpaif.id) AS fileCount'));
                $answersQuery->leftJoin('foquz_poll_answer_item_files fpaif', 'foquz_poll_answer_item.id = fpaif.foquz_poll_answer_item_id');
                $answersQuery->groupBy('foquz_poll_answer_item.id');
                $answersQuery->andHaving('fileCount ' . $where);
            } elseif ($get['field'] === 'formField') {
                $where = "json_extract(answer, '$.\"" . addslashes($get['value']) . "\"') != ''";
                $answersQuery->andWhere(new Expression($where));
            } elseif ($get['field'] === 'smile') {
                $answersQuery->andWhere(['answer' => $get['value']]);
            } elseif ($get['field'] === 'diff') {
                $answersQuery->andWhere(new Expression("json_extract(answer, '$.\"" . addslashes($get['row']) . "\"') = \"" . addslashes($get['value']) . "\" OR json_extract(answer, '$.\"" . addslashes($get['row']) . "\"') = " . addslashes($get['value'])));
            } elseif ($get['field'] === 'rating') {
                if ($question->main_question_type === FoquzQuestion::TYPE_VARIANT_STAR && isset($get['detail_item'])) {
                    $answersQuery->andWhere(['LIKE', 'answer', '"' . $get['detail_item'] . '":"' . $get['rating'] . '"']);
                } else {
                    if (($question->main_question_type === FoquzQuestion::TYPE_NPS_RATING && $question->set_variants) || $question->main_question_type === FoquzQuestion::TYPE_DISTRIBUTION_SCALE) {
                        $where = "answer IS NOT NULL AND (json_extract(answer, '$.\"" . addslashes($get['variant']) . "\"') = \"" . addslashes($get['value']) . "\" OR json_extract(answer, '$.\"" . addslashes($get['variant']) . "\"') = " . addslashes($get['value']) . ")";
                        $answersQuery->andWhere($where);
                    } else {
                        $answersQuery->andWhere(['=', $get['field'], $get['value']]);
                    }
                }
            } elseif ($get['field'] === 'detail_item') {
                if ($question->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX && isset($get['invert']) &&
                    !empty($get['row']) && $get['invert'] === "1" && preg_match('/^[\w\p{Cyrillic}]+$/u', $get['row'])) {
                    $row = $get['row'];
                    $value = Yii::$app->db->quoteValue($get['value']);
                    $answersQuery->andWhere(['OR', 'detail_item->\'$."' . $row . '"\' NOT LIKE ' . $value, ['detail_item' => null]]);
                } elseif (
                    $question->main_question_type === FoquzQuestion::TYPE_NPS_RATING &&
                    isset($get['value'], $get['variant_id']) &&
                    is_numeric($get['value']) && is_numeric($get['variant_id'])
                ) {
                    $answersQuery->andWhere('json_valid(detail_item)');
                    $answersQuery->andWhere(['LIKE', new Expression('detail_item->\'$."' . $get['variant_id'] . '"\''), $get['value']]);
                } elseif (isset($get['invert']) && $get['invert'] === "1") {
                    $answersQuery->andWhere(['OR', ['not like', 'detail_item', $get['value']], ['detail_item' => null]]);
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX && !empty($get['row'])
                        && preg_match('/^[\w\p{Cyrillic}]+$/u', $get['row'])) {
                    $row = $get['row'];
                    $value = Yii::$app->db->quoteValue('%' . $get['value'] . '%');
                    $answersQuery->andWhere('detail_item->\'$."' . $row . '"\' LIKE ' . $value);
                } else {
                    $answersQuery->andWhere(['like', 'detail_item', $get['value']]);
                }
            } elseif ($get['field'] === 'has_answer') {
                switch ($question->main_question_type) {
                    case FoquzQuestion::TYPE_VARIANT_STAR:
                        if ($get['value'] == "1" && !empty($get['variant']) && is_numeric($get['variant'])) {
                            $variant = $get['variant'];
                            $answersQuery->andWhere('answer->\'$.extra."' . $variant . '"\' LIKE \'%answer%\'');
                        } elseif ($get['value'] == "1") {
                            $answersQuery->andWhere(['LIKE', 'answer', 'answer']);
                        } elseif (!empty($get['variant']) && is_numeric($get['variant'])) {
                            $variant = $get['variant'];
                            $answersQuery->andWhere('answer->\'$.extra."' . $variant . '"\' NOT LIKE \'%answer%\'');
                        } else {
                            $answersQuery->andWhere(['NOT LIKE', 'answer', 'answer']);
                        }
                        break;
                    case FoquzQuestion::TYPE_NPS_RATING:
                        if (!empty($get['value'])) {
                            if (!empty($get['variant_id']) && is_numeric($get['variant_id'])) {
                                $answersQuery->andWhere('json_valid(detail_item)');
                                $answersQuery->andWhere(['LIKE', new Expression('detail_item->\'$."' . $get['variant_id'] . '"\''), 'answer']);
                            } else {
                                $answersQuery->andWhere(['LIKE', 'detail_item', 'answer']);
                            }
                        } else if (!empty($get['variant_id']) && is_numeric($get['variant_id'])) {
                            $answersQuery->andWhere('json_valid(detail_item)');
                            $answersQuery->andWhere(['NOT LIKE', new Expression('detail_item->\'$."' . $get['variant_id'] . '"\''), 'answer']);
                        } else {
                            $answersQuery->andWhere(['NOT LIKE', 'detail_item', 'answer']);
                        }
                        break;
                    default:
                        if ($get['value'] == "1") {
                            $answersQuery->andWhere(['LIKE', 'answer', 'answer']);
                        } else {
                            $answersQuery->andWhere(['NOT LIKE', 'answer', 'answer']);
                        }
                }
            } elseif ($get['field'] === 'has_answer_self_variant') {
                if ($get['value'] == "1") {
                    switch ($question->main_question_type) {
                        case FoquzQuestion::TYPE_STAR_RATING:
                        case FoquzQuestion::TYPE_RATING:
                            $answersQuery->andWhere("not (answer = '' or answer is null)");
                            break;
                        case FoquzQuestion::TYPE_VARIANT_STAR:
                            if (!empty($get['variant']) && is_numeric($get['variant'])) {
                                $variant = $get['variant'];
                                $answersQuery->andWhere('answer->\'$.extra."' . $variant . '"\' LIKE \'%self_variant%\'');
                            } else {
                                $answersQuery->andWhere(['LIKE', 'answer', 'self_variant']);
                            }
                            break;
                        case FoquzQuestion::TYPE_NPS_RATING:
                            if (!empty($get['variant_id']) && is_numeric($get['variant_id'])) {
                                $answersQuery->andWhere('json_valid(detail_item)');
                                $answersQuery->andWhere(['LIKE', new Expression('detail_item->\'$."' . $get['variant_id'] . '"\''), 'self_variant']);
                            } else {
                                $answersQuery->andWhere(['LIKE', 'detail_item', 'self_variant']);
                            }
                            break;
                        default:
                            $answersQuery->andWhere("not (self_variant = '' or self_variant is null)");
                    }
                } else {
                    switch ($question->main_question_type) {
                        case FoquzQuestion::TYPE_STAR_RATING:
                        case FoquzQuestion::TYPE_RATING:
                            $answersQuery->andWhere("(answer = '' or answer is null)");
                            break;
                        case FoquzQuestion::TYPE_VARIANT_STAR:
                            if (!empty($get['variant']) && is_numeric($get['variant'])) {
                                $variant = $get['variant'];
                                $answersQuery->andWhere('answer->\'$.extra."' . $variant . '"\' NOT LIKE \'%self_variant%\'');
                            } else {
                                $answersQuery->andWhere(['NOT LIKE', 'answer', 'self_variant']);
                            }
                            break;
                        default:
                            $answersQuery->andWhere("(self_variant = '' or self_variant is null)");
                    }
                }
            } else {
                $answersQuery->andWhere(['like', $get['field'], $get['value']]);
            }
        }
        if(Yii::$app->request->get('q')) {
            $answersQuery->andWhere(['OR',
                ['like', 'foquz_contact.phone', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.last_name', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.first_name', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.patronymic', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.email', Yii::$app->request->get('q')],
                ['like', 'orders.number', Yii::$app->request->get('q')],
                ['like', 'orders.id', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(foquz_poll_answer.updated_at,"%d.%m.%Y")', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(orders.created_time,"%d.%m.%Y")', Yii::$app->request->get('q')],
            ]);
        }
        if(isset($get['from']) && isset($get['to'])) {
            $answersQuery->andWhere(['between', 'foquz_poll_answer_item.created_at', date('Y-m-d 00:00:00', strtotime($get['from'])), date('Y-m-d 23:59:59', strtotime($get['to']))]);
        }

        if(isset($get['questionnaires']))
            $answersQuery->andWhere(['foquz_poll_answer.status' => $get['questionnaires']]);
        if(isset($get['tags']) && count($get['tags']) > 0 && isset($get['tagsOperation'])) {
            $answersQuery->leftJoin('foquz_contact_tag', 'foquz_contact_tag.contact_id = foquz_poll_answer.contact_id');
            if($get['tagsOperation'] == 1) {
                $answersQuery->andWhere(['in', 'foquz_contact_tag.tag_id', $get['tags']]);
            } else {
                $answersQuery
                    ->andHaving("(".(new Query())->select('COUNT(distinct tag_id)')
                            ->from('foquz_contact_tag')
                            ->where('contact_id = foquz_poll_answer.contact_id')
                            ->andWhere(['in', 'tag_id', $get['tags']])
                            ->createCommand()->rawSql.") =".count($get['tags'])
                    );
            }
        }

        if(isset($get['filials']) && count($get['filials']) > 0) {
            $answersQuery
                ->leftJoin('foquz_poll', 'foquz_poll_answer.foquz_poll_id = foquz_poll.id');
            $index = array_search(0, $get['filials']);
            if($index !== false) {
                $get['filials'][$index] = null;
            }
            $answersQuery->andWhere(['in', 'IF(foquz_poll.is_auto, orders.filial_id, foquz_poll_answer.answer_filial_id)', $get['filials']]);
        }

        if(isset($get['devices']) && count($get['devices']) > 0) {
            $answersQuery->andWhere(['in', 'foquz_poll_answer.device', $get['devices']]);
        }

        $recordsCount = $answersQuery->count();
        $answers = $answersQuery
            ->limit(30)
            ->offset(($page - 1) * 30)
            ->all();
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $data = [];

        $detailsWithExtra = [];
        if ($question->main_question_type === FoquzQuestion::TYPE_VARIANT_STAR) {
            $detailsWithExtra = FoquzQuestionDetail::find()->where(['foquz_question_id' => $question->id, 'need_extra' => 1])->all();
        }

        $extraVariants = ArrayHelper::index($question->questionDetails, null, 'extra_question')[1] ?? [];
        $extraVariants = ArrayHelper::map($extraVariants, 'id', 'question');

        foreach($answers as $answer) {
            $item = [
                'name' => trim(implode(' ', [$answer['last_name'], $answer['first_name'], $answer['patronymic']]) ?? ''),
                'phone' => FoquzContact::formatPhone($answer['phone']) ?? '',
                'email' => $answer['email'] ?? '',
                'filialName' => $answer['filialName'],
                'passedAt' => date('d.m.Y', strtotime($answer['updated_at'])),
                'orderId' => $answer['orderId'] ?? '',
                'orderCreatedAt' => $answer['created_time'] ? date('d.m.Y', strtotime($answer['created_time'])) : '',
            ];
            $variantText = [];
            if (isset($answer['answer'])) {
                $answerArray = json_decode($answer['answer'], true);
                if (isset($answerArray['extra'])) {
                    foreach ($answerArray['extra'] as $key => $extraItem) {
                        if (($get['field'] === 'has_answer_self_variant' && !isset($extraItem['self_variant']))
                            || ($get['field'] === 'has_answer' && !isset($extraItem['answer']))
                            || ($get['field'] === 'answer' && !in_array($get['value'], $extraItem))) {
                            continue;
                        }

                        if ($variant = FoquzQuestionDetail::findOne($key)) {
                            $variantText[] = $variant->question;
                        }
                    }
                }
            }
            if (isset($get['field']) && $get['field'] === 'has_answer' && $get['value'] === '0') {
                /** @var FoquzQuestionDetail $detail */
                foreach ($detailsWithExtra as $detail) {
                    if (!isset($answerArray['extra'][$detail->id]['answer'])) {
                        $variantText[] = $detail->question;
                    }
                }
            }
            if (
                $question->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX &&
                !empty($answer['detail_item']) &&
                ($detailArray = json_decode($answer['detail_item'], true)) &&
                is_iterable($detailArray)
            ) {

                foreach ($detailArray as $key => $detailItem) {
                   if (($get['value'] === 'self_variant' && !isset($detailItem['self_variant']))
                       || ($get['value'] === 'answer' && !isset($detailItem['answer']))
                       || ($get['field'] === 'detail_item' && !in_array($get['value'], $detailItem))
                   ) {
                        continue;
                    }
                    $variantText[] = $key;
                }
            } elseif (
                $question->main_question_type === FoquzQuestion::TYPE_NPS_RATING &&
                !empty($answer['detail_item']) &&
                ($detailArray = json_decode($answer['detail_item'], true)) &&
                is_iterable($detailArray)
            ) {
                switch ($question->extra_question_type) {
                    case FoquzQuestion::EXTRA_QUESTION_COMMON:
                        if ($question->variants_element_type !== FoquzQuestion::VARIANT_ELEMENT_TYPE_TEXT) {
                            foreach ($detailArray as $detailID) {
                                $variantText[] = $extraVariants[$detailID] ?? '';
                            }
                            if (isset($answer['self_variant']) && $answer['self_variant'] !== '') {
                                $variantText[] = $answer['self_variant'];
                            }
                        } elseif (isset($answer['self_variant']) && $answer['self_variant'] !== '') {
                            $variantText[] = $answer['self_variant'];
                        }
                        break;
                }
            }

            $item['variantText'] = implode(', ', $variantText);
            $data[] = $item;
        }
        return [
            'clients' => $data,
            'lastPage' => $recordsCount <= ($page * 30)
        ];
    }

    private function findModel($id)
    {
        if (null === ($model = FoquzPoll::findOne($id))) {
            throw new NotFoundHttpException();
        }
        return $model;
    }

    public function actionUnsubscribeEmail()
    {
        $post = Yii::$app->request->post();
        $connection = Yii::$app->getDb();
        //$connection->createCommand('UPDATE client_emails SET is_subscribe = 0 WHERE email=:email', ['email' => $post['email']])->execute();
        if(isset($post['poll_id'])) {
            $poll = FoquzPoll::findOne($post['poll_id']);
            if ($poll) {
                if ($post['phone']) {
                    FoquzContact::updateAll(['is_subscribed' => 0], ['phone' => $post['phone'], 'company_id' => $poll->company_id]);
                } else if ($post['email']) {
                    FoquzContact::updateAll(['is_subscribed' => 0], ['email' => $post['email'], 'company_id' => $poll->company_id]);

                }
            }
        }
        if(isset($post['client_id'])) {
            FoquzContact::updateAll(['is_subscribed' => 0], ['email' => $post['email'], 'id' => $post['client_id']]);
        }
        return true;
    }

    public function actionQuickHelp()
    {
        return [
            'showed' => Yii::$app->user->identity && !Yii::$app->user->identity->isRespondent() ?
                Yii::$app->user->identity->quick_help_showed : true
        ];
    }

    public function actionQuickHelpShowed()
    {
        if(Yii::$app->request->post('showed')) {
            $user = User::findOne(Yii::$app->user->id);
            $user->quick_help_showed = true;
            $user->save(false);
            return ['showed' => true];
        }
        return [];
    }

    public function actionMobileQuickHelp()
    {
        if (!Yii::$app->user->identity || Yii::$app->user->identity->superadmin || Yii::$app->user->identity->isRespondent()) {
            return [
                'showed' => true
            ];
        }
        return [
            'showed' => Yii::$app->user->identity->mobile_quick_help_showed
        ];
    }

    public function actionMobileQuickHelpShowed()
    {
        if(Yii::$app->request->post('showed')) {
            $user = User::findOne(Yii::$app->user->id);
            $user->mobile_quick_help_showed = true;
            $user->save();
            return ['showed' => true];
        }
        return [];
    }

    public function actionMarkDone($id)
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $answer = FoquzPollAnswer::findOne($id);
        if($answer) {
            if ($answer->status!==FoquzPollAnswer::STATUS_IN_PROGRESS)  {
                if ($answer->foquzPoll->getFoquzQuestions()
                    ->where(['<>', 'main_question_type', FoquzQuestion::TYPE_INTERMEDIATE_BLOCK])
                    ->count()==0) {
                    return ;
                }

            }
            $answer->status = FoquzPollAnswer::STATUS_DONE;

            if ($answer->foquzPoll->point_system) {
                /** @var \yii\queue\amqp_interop\Queue $queue */
                $queue = \Yii::$app->rabbit_queue;

                foreach ($answer->foquzAnswer as $item) {
                   if (!$item->points_job_id || !isset(\Yii::$app->rabbit_queue) || $item->points_job_id!=='done') {
                        $item->points = FoquzPollAnswer::calculatePointsForQuestion($item->foquzQuestion, $item);
                        $item->max_points = $item->calculateMaxPoints();
                        $item->save();
                   }
                }

                if (!$answer->points_job_id || !isset(\Yii::$app->rabbit_queue) || $answer->points_job_id!=='done') {
                    $answer->points = $answer->addPoints();
                    $answer->max_points = $answer->calculateMaxPoints();
                    $answer->save();
                }
            }

            $answer->pushWebhookJob();

            if($answer->save()) {
                if (CustomQuotaService::isQuota($answer->foquz_poll_id)) {
                    $quota = new CustomQuotaService($answer);
                    $redirectURL = $quota->checkQuota();
                    if (!empty($redirectURL)) {
                        return  [
                            'redirect_url' => $redirectURL
                        ];
                    }
                }


                $percent = $answer->max_points > 0 ? ($answer->points / $answer->max_points) * 100 : 0;
                if ($percent < 0) {
                    $percent = 0;
                }

                return [
                    'answer' => $answer,
                    'points' => $answer->foquzPoll->point_system ? [
                        'points' => $answer->points,
                        'max' => $answer->max_points,
                        'percent' => $percent,
                    ] : null
                ];
            } else {
                return ['errors' => $answer->errors];
            }
        } else {
            return ['errors' => 'Анкета не найдена'];
        }
    }

    public function actionLogin()
    {
        if (!Yii::$app->user->isGuest)
        {
            return $this->asJson(['success' => true]);
        }
        $model = new LoginForm();
        if ($model->load(Yii::$app->request->post(), '') && $model->login() )
        {
            return $this->asJson(['success' => true]);
        }
        Yii::$app->response->statusCode = 401;
        return $this->asJson(['success' => false, 'error' => implode('. ', $model->getFirstErrors())]);
    }
}
