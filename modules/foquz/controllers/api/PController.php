<?php

namespace app\modules\foquz\controllers\api;

use app\models\User;
use app\modules\foquz\models\FoquzContact;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollAnswerItemFile;
use app\modules\foquz\models\FoquzPollAnswerShowedImage;
use app\modules\foquz\models\FoquzPollLang;
use app\modules\foquz\models\FoquzPollMailingListSend;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\PollLang;
use app\modules\foquz\models\quotes\FoquzPollLinkQuotes;
use app\modules\foquz\services\AnswerService;
use app\modules\foquz\services\quotes\QuoteService;
use OpenApi\Attributes as OA;
use Yii;
use yii\helpers\ArrayHelper;
use yii\web\NotFoundHttpException;
use yii\web\Response;

class PController extends ApiController
{
    public function actionSaveAnswer($authKey, $questionId = null)
    {
        return Yii::$app->runAction('/foquz/default/save-answer', [
            'authKey'    => $authKey,
            'questionId' => $questionId,
        ]);
    }


    public function actionGetTranslate($key = null, $langId = null, $lang = null, $pollId = null)
    {
        $get = Yii::$app->request->get();
        $isPreview = isset($get['preview']);
        if ((empty($pollId) || !$isPreview) && empty($key)) {
            return $this->errorResponse('Поле key обязательно', 400);
        }
        list ($poll) = AnswerService::getInstance($get, $isPreview, $key, $pollId);
        if (!$poll) {
            return $this->errorResponse('Опрос не найден', 404);
        }

        $pollLang = null;
        if ($langId) {
            $pollLang = PollLang::findOne($langId);
        }
        if (!$pollLang && $lang) {
            $pollLang = PollLang::find()->where(["short_code" => $lang])->one();
        }
        if (!$pollLang) {
            $default = FoquzPollLang::find()->where(["foquz_poll_id" => $poll->id, "default" => 1])->one();
            if ($default) {
                $pollLang = PollLang::findOne($default->poll_lang_id);
            }
        }

        if (!$pollLang) {
            throw new NotFoundHttpException('Запись о языке опроса не найдена');
        }

        $translate = Yii::$app->runAction('/foquz/api/poll/get-translate', [
            'id'     => $poll->id,
            'langId' => $pollLang->id
        ]);
        $translate = AnswerService::formatTranslateForProcess($translate);

        return $this->asJson($translate);
    }

    public function actionAnswer($key = null, $langId = null, $lang = null, $pollId = null): Response
    {
        $get = Yii::$app->request->get();
        $isPreview = isset($get['preview']);
        if ((empty($pollId) || !$isPreview) && empty($key)) {
            return $this->errorResponse('Поле key обязательно', 400);
        }

        $params = Yii::$app->request->get("params", Yii::$app->request->post("params", []));
        if (!is_array($params)) {
            $params = [];
        }
        foreach ($params as $keyParam => $value) {
            if (!isset($get[$keyParam])) {
                $get[$keyParam] = $value;
            }
        }

        [$poll, $answer, $answerService] = AnswerService::getInstance($get, $isPreview, $key, $pollId);
        if (!$poll) {
            return $this->errorResponse('Опрос не найден', 404);
        }


        $params = Yii::$app->request->get("params", Yii::$app->request->post("params", []));
        if (!is_array($params)) {
            $params = [];
        }
        foreach ($params as $keyParam => $value) {
            if (!isset($get[$keyParam])) {
                $get[$keyParam] = $value;
            }
        }


        $quote = FoquzPollLinkQuotes::getQuoteByKey((string) $key, $poll->id);
        if ($answer && $quote && !$answer->quote_id) {
            $answer->quote_id = $quote->id;
            $answer->save();
        }

        $authParams = $answerService->getAuthParams();

        $company = $poll->company;
        $hostName = strtolower(\Yii::$app->request->hostName);
        $companyAlias = strtolower($poll->company->alias);
        if (!in_array($hostName, [
            $companyAlias,
            'foquz.ru',
            'localhost',
            'foquzdev.ru',
            'devfoquz.ru',
            'doxswf.ru',
        ], true)) {
            return $this->errorResponse('Анкета не найдена', 404);
        }


        $design = $poll->design;
        $tariffName = $company->tariff->title ?? 'Базовый';

        if (!$design) {
            //  $design = FoquzPollDesign::createDesignFromStandartTemplate($poll->id);
        }

        // индивидуальный макет для заданных опросов
        $customThemes = null;
        if (isset(Yii::$app->params['customThemes'][$poll->id])) {
            $customThemes = Yii::$app->params['customThemes'][$poll->id];
        }

        if (!empty($authParams['needAuth'])) {
            Yii::$app->response->statusCode = 401;
            return $this->asJson([
                'design'        => $design,
                'showFoquzLink' => ($tariffName === 'Базовый' || $poll->show_foquz_link),
                'showAdv'       => ($tariffName === 'Базовый'),
                'auth'          => $authParams,
            ]);
        }

        if (!$answer && !$isPreview) {
            return $this->errorResponse('Анкета не найдена', 404);
        }

        $pollQuestions = $poll->getFoquzQuestions()
            ->with([
                "pointSelected" => function ($q) {
                    $q->with("condition");
                }
            ])
            ->with([
                'intermediateBlock',
                'activeMatrixElements.activeVariants',
                'activeMatrixElements.activeVariants.langs',
                'activeMatrixElements.langs',
                'questionLogic',
                'foquzPollQuestionViewLogics',
                'questionDetails',
                'foquzQuestionEndScreenLogos',
                'foquzQuestionLangs',
                'formFields.langs'
            ]);
        if (!$isPreview) {
            $pollQuestions->andWhere(['is_tmp' => false]);
        }
        $pollQuestions = $pollQuestions->all();

        $pollLang = null;
        if ($langId) {
            $pollLang = PollLang::findOne($langId);
        }
        if (!$pollLang && $lang) {
            $pollLang = PollLang::find()->where(["short_code" => $lang])->one();
        }
        if (!$pollLang) {
            $default = FoquzPollLang::find()->where(["foquz_poll_id" => $poll->id, "default" => 1])->one();
            if ($default) {
                $pollLang = PollLang::findOne($default->poll_lang_id);
            }
        }

        $answerForResponse = [];
        if (!$isPreview) {
            $answerForResponse = ArrayHelper::toArray($answer);
            $answerForResponse = array_filter($answerForResponse, static function ($key) {
                return in_array($key, ['id', 'status', 'auth_key']);
            }, ARRAY_FILTER_USE_KEY);
            $answerForResponse["hasContact"] = !is_null($answer->contact_id);
        }

        $pollForResponse = $poll->attributes;
        $pollForResponse = array_filter($pollForResponse, static function ($key) {
            return in_array($key,
                ['id', 'description', 'title', 'point_system', 'goal_text',
                    'is_published', 'is_answer_limited', 'css_self_type', 'css_self_url', 'css_self_text'
                ]);
        }, ARRAY_FILTER_USE_KEY);
        $pollForResponse["showUserConsentBanner"] = $poll->personal_data ? true : false;

        $pollForResponse['startPage'] = $poll->startPage ?? (object)[];
        $pollForResponse['endPage'] = $poll->endPage ?? (object)[];

        //$pollForResponse['foquzPollLang'] = $poll->getTranslate($pollLang);
        $pollForResponse['displaySetting'] = $poll->displaySetting;
        $pollForResponse['displayPages'] = $poll->displayPages;
        $questions = AnswerService::formatQuestionsForProcess($pollQuestions, $answer, true);
        $pollForResponse['foquzPollLangs'] = AnswerService::formatLanguagesForProcess($poll->foquzPollLangs);

        $answerCount = $poll->in_progress_answers_count + $poll->filled_answers_count;
        $pollForResponse["answerLimitIsOver"] = $poll->is_published && $poll->limit_count && $answerCount >= $poll->limit_count;
        $pollForResponse["testModeLimitIsOver"] = !$poll->is_published && $answerCount >= FoquzPoll::ACCESS_TEST_ANSWERS;

        if (isset(Yii::$app->params['new_title_format_date'])) {
            $pollForResponse['isOldTitleFormat'] = $poll->updated_at < strtotime(Yii::$app->params['new_title_format_date']) ? 1 : 0;
        }

        $translate = null;
        if ($pollLang) {
            $translate = Yii::$app->runAction('/foquz/api/poll/get-translate', [
                'id'     => $poll->id,
                'langId' => $pollLang->id,
                'vue'    => true
            ]);
            if ($translate) {
                $translate = AnswerService::formatTranslateForProcess($translate);
            }

        }

        $isPollPeriodOver = $poll->is_published && (($poll->datetime_start && time() < strtotime($poll->datetime_start)) ||
                ($poll->datetime_end && time() > strtotime($poll->datetime_end)));

        // проверка активности у ссылки на прохождение и лимитов по квоте на ссылку
        $isQuoteLimitsOver = false;
        $isActive = $poll->status === FoquzPoll::STATUS_NEW && !$poll->deleted && $poll->is_active;
        if ($quote && $poll->is_published) {
            $quoteService = new QuoteService($quote);
            $checkQuote = $quoteService->checkLinkQuote();
            if (!$checkQuote['result']) {
                if ($checkQuote['reason'] === FoquzPollLinkQuotes::QUOTE_INACTIVE) {
                    $isActive = false;
                }
                if ($checkQuote['reason'] === FoquzPollLinkQuotes::QUOTE_TIME_IS_OVER) {
                    $isPollPeriodOver = true;
                }
                if ($checkQuote['reason'] === FoquzPollLinkQuotes::QUOTE_LIMIT_IS_OVER) {
                    $isQuoteLimitsOver = true;
                }
            }
        }

        $variables = empty($answer) ? [] : $answer->variables;
        foreach ($params as $key => $param) {
            $variables['URL.' . $key] = $param;
        }

        $now = date("Y-m-d H:i:s");
        $pollPeriodIsOver = (empty($answer))  ? false : $answer->isExpirated();
        // в режиме редактирования добавляем в variables id редактируемого вопроса
        $staffEdit = false;
        if(isset($variables['URL.view'], $variables['URL.edit']) && $variables['URL.edit'] === '1' && is_numeric($variables['URL.view'])) {
            $nonIntermediateQuestions = array_filter($questions, function ($question) {
                return $question['type'] !== FoquzQuestion::TYPE_INTERMEDIATE_BLOCK;
            });
            $nonIntermediateQuestions = array_values($nonIntermediateQuestions);
            $editQuestion = $nonIntermediateQuestions[$variables['URL.view'] - 1] ?? null;
            if ($editQuestion) {
                $variables['editQuestionId'] = $editQuestion['question_id'];
                $staffEdit = $this->isStaffEdit($answer);
            }
        }

        $isResponseTimeExpired = false;
        if (
            $poll->is_published &&
            $poll->expiration_in_minutes &&
            $answer?->created_at &&
            (strtotime($answer->created_at) + $poll->expiration_in_minutes * 60) < time()
        ) {
            $isResponseTimeExpired = true;
        }

        $timer = $this->getTimer($answer, $poll);
        $isAnswersLimitsOver = $company->isAnswersLimitsOver();
        if ($staffEdit || $isPreview) {
            $pollForResponse["answerLimitIsOver"] = false;
            $isActive = !$poll->deleted;
            $isPollPeriodOver = false;
            $isQuoteLimitsOver = false;
            $timer = $this->getTimer($answer, $poll, true);
            $isAnswersLimitsOver = false;
            $isResponseTimeExpired = false;
        }

        return $this->asJson([
            'timer'                     => $timer,
            'isResponseTimeExpired'     => $isResponseTimeExpired,
            'answer'                    => $answerForResponse,
            'poll'                      => $pollForResponse,
            'questions'                 => $questions,
            'design'                    => $design,
            'isActive'                  => $isActive,
            'pollPeriodIsOver'          => $isPollPeriodOver,
            'isAnswersLimitsOver'       => $isAnswersLimitsOver,
            'isQuoteLimitsOver'         => $isQuoteLimitsOver,
            'filesizeLimit'             => $company->filesize_limit,
            'showFoquzLink'             => ($tariffName === 'Базовый' || $poll->show_foquz_link),
            'showFoquzLabel'            => (bool)$company->show_foquz_label,
            'showAdv'                   => ($tariffName === 'Базовый'),
            'allowEditAfterDone'        => $isPreview || $this->allowEditAfterDone($answer),
            'staffEdit'                 => $staffEdit,
            'variables'                 => $variables,
            'auth'                      => $isPreview ? ['needAuth' => false] : $authParams,
            'lang'                      => $translate,
            'customThemes'              => $customThemes,
        ]);
    }

    #[OA\Post(
        path: '/foquz/api/p/change-status',
        summary: 'Изменение статуса у анкеты в прохождении',
        requestBody: new OA\RequestBody(
            content: new OA\MediaType(
                mediaType: 'multipart/form-data',
                schema: new OA\Schema(
                    properties: [
                        new OA\Property(
                            property: 'authKey',
                            description: 'Авторизационный ключ анкеты',
                            type: 'string',
                            example: 'b444ad1ac7e20c4d8748d9f0cc878aa6',
                            nullable: true,
                        ),
                        new OA\Property(
                            property: 'key',
                            description: 'Ключ анкеты (из sending)',
                            type: 'string',
                            example: 'b444ad1ac7e20c4d8748d9f0cc878aa6',
                            nullable: true,
                        ),
                        new OA\Property(
                            property: 'status',
                            description: 'Статус анкеты',
                            type: 'string',
                            enum: [FoquzPollAnswer::STATUS_OPEN, FoquzPollAnswer::STATUS_DONE],
                            example: 'done',
                        ),
                    ],
                ),
            ),
        ),
        tags: ['Прохождение (новое)'],
    )]
    #[OA\Response(
        response: 200,
        description: 'Успешное изменение статуса',
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                properties: [
                    new OA\Property(
                        property: 'success',
                        type: 'boolean',
                        example: true,
                    ),
                    new OA\Property(
                        property: 'error',
                        type: 'string',
                        example: null,
                        nullable: true,
                    ),
                ],
                type: 'object',
            ),
        ),
    )]
    #[OA\Response(
        response: 400,
        description: 'Неверные параметры запроса',
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                properties: [
                    new OA\Property(
                        property: 'success',
                        type: 'boolean',
                        example: false,
                    ),
                    new OA\Property(
                        property: 'error',
                        type: 'string',
                        example: 'authKey is required',
                    ),
                ],
                type: 'object',
            ),
        ),
    )]
    #[OA\Response(
        response: 404,
        description: 'Анкета не найдена',
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                properties: [
                    new OA\Property(
                        property: 'success',
                        type: 'boolean',
                        example: false,
                    ),
                    new OA\Property(
                        property: 'error',
                        type: 'string',
                        example: 'answer not found',
                    ),
                ],
                type: 'object',
            ),
        ),
    )]
    public function actionChangeStatus(): Response
    {
        $authKey = Yii::$app->request->post('authKey');
        $key = Yii::$app->request->post('key');

        if (!$authKey && !$key) {
            return $this->errorResponse('authKey or key is required');
        }

        $status = Yii::$app->request->post('status');
        if (!$status) {
            return $this->errorResponse('status is required');
        }

        if ($key) {
            $answer = $this->findAnswerBySendingKey($key);
        } else {
            $answer = $this->findAnswer($authKey);
        }

        if (!$answer) {
            return $this->errorResponse('Анкета не найдена', 404);
        }

        if (!in_array($status, [FoquzPollAnswer::STATUS_OPEN, FoquzPollAnswer::STATUS_DONE], true)) {
            return $this->errorResponse('status is invalid, must be ' . FoquzPollAnswer::STATUS_OPEN . ' or ' . FoquzPollAnswer::STATUS_DONE);
        }

        if ($answer->status === FoquzPollAnswer::STATUS_DONE && $status !== FoquzPollAnswer::STATUS_DONE) {
            return $this->errorResponse('Невозможно изменить статус с "Завершено" на другой статус');
        }

        if ($status === FoquzPollAnswer::STATUS_DONE) {
            // считать баллы если не успели посчитаться в очереди
            if ($answer->foquzPoll->point_system) {
                foreach ($answer->foquzAnswer as $item) {
                    if (!$item->points_job_id || $item->points_job_id !== 'done') {
                        $item->points = FoquzPollAnswer::calculatePointsForQuestion($item->foquzQuestion, $item);
                        $item->max_points = $item->calculateMaxPoints();
                        $item->save();
                    }
                }
                if (!$answer->points_job_id || $answer->points_job_id !== 'done') {
                    $answer->points = $answer->addPoints();
                    $answer->max_points = $answer->calculateMaxPoints();
                    $answer->save();
                }
            }
        }

        if ($answer->status === FoquzPollAnswer::STATUS_DONE) {
            return $this->asJson(['success' => true, 'error' => null]);
        }

        $answer->status = $status;

        if (!$answer->save()) {
            return $this->errorResponse('Не удалось изменить статус анкеты', 500);
        }

        if ($answer->status == FoquzPollAnswer::STATUS_DONE) {
            $answer->pushWebhookJob();
        }


        return $this->asJson(['success' => true, 'error' => null]);
    }


    public function actionSetupTimer(?string $key = null, ?int $pollId = null)
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $get = Yii::$app->request->get();
        $isPreview = isset($get['preview']);
        if ((empty($pollId) || !$isPreview) && empty($key)) {
            return $this->errorResponse('Поле key обязательно', 400);
        }
        list ($poll, $answer) = AnswerService::getInstance($get, $isPreview, $key, $pollId);
        if (empty($poll) || empty($answer)) {
            return $this->errorResponse('Опрос не найден', 404);
        }

        if (!empty($answer->first_question_showed_at)) {
            return $this->errorResponse('Анкету уже начали заполнять', 403);
        }

        if (!empty($answer->first_question_showed_at)) {
            return $this->errorResponse('Анкету уже начали заполнять', 403);
        }

        $answer->first_question_showed_at = date("Y-m-d H:i:s");
        $answer->save();
        return [
            'status' => true,
            'timer'  => $this->getTimer($answer, $poll)
        ];
    }


    public function actionGetPoints(string $key): Response
    {
        $answer = $this->findAnswer($key);

        if (!$answer) {
            return $this->errorResponse('Анкета не найдена', 404);
        }

        if (!$answer->foquzPoll->point_system) {
            return $this->errorResponse('Для опроса не включены баллы');
        }

        $result = ['questions' => []];

        /** @Todo поправить после слияния. Тут другие очереди */
        /** @var \yii\queue\Queue $queue */
        $queue = \Yii::$app->rabbit_queue ?? null;
        foreach ($answer->foquzAnswer as $item) {
            if (!$item->points_job_id || !isset(\Yii::$app->rabbit_queue) || $item->points_job_id !== 'done') {
                $item->points = FoquzPollAnswer::calculatePointsForQuestion($item->foquzQuestion, $item);
                $item->max_points = $item->calculateMaxPoints();
                $item->save();
            }
            $result['questions'][] = [
                'questionId' => $item->foquz_question_id,
                'points'     => $item->points ?? 0,
                'maxPoints'  => $item->max_points ?? 0
            ];
        }

        if (!$answer->points_job_id || !isset(\Yii::$app->rabbit_queue) || $answer->points_job_id !== 'done') {
            $answer->points = $answer->addPoints();
            $answer->max_points = $answer->calculateMaxPoints();
            $answer->save();
        }
        $result['points'] = (int)$answer->points;
        $result['maxPoints'] = $answer->max_points;

        return $this->asJson($result);
    }

    /**
     * @param $auth_key
     * @return Response
     * @throws NotFoundHttpException
     * @todo в рамках перепроектирования API оптимизировать, столько данных на фронт не нужно
     */
    public function actionGetResults(string $key): Response
    {
        $answer = FoquzPollAnswer::findOne(['auth_key' => $key]);
        if (!$answer) {
            throw new NotFoundHttpException('Ответ не найден');
        }

        $pointPercent = $answer->max_points > 0 ? round(($answer->points / $answer->max_points) * 100) : 0;
        if ($pointPercent < 0) {
            $pointPercent = 0;
        }

        $questions = $answer->collectQuestionsWithAnswerItems();
        $contact = null;
        if (!empty($answer->contact)) {
            $contact = [
                'name'  => $answer->contact->fullName,
                'email' => $answer->contact->email,
                'phone' => FoquzContact::preformatPhone($answer->contact->phone)
            ];
        }

        return $this->asJson([
            'id'              => $answer->id,
            'displaySettings' => $answer->foquzPoll->displaySetting,
            'displayPages'    => $answer->foquzPoll->displayPages,
            'questions'       => $questions,
            'poll'            => [
                'name' => $answer->foquzPoll->name
            ],
            'contact'         => $contact,
            'points'          => $answer->foquzPoll->point_system ? [
                'answer_points' => $answer->points ?? 0,
                'points_max'    => $answer->max_points,
                'percent'       => $pointPercent,
            ] : [],
        ]);
    }

    #[OA\Post(
        path: '/foquz/api/p/unsubscribe',
        summary: 'Отписка от рассылки',
        requestBody: new OA\RequestBody(
            content: new OA\MediaType(
                mediaType: 'multipart/form-data',
                schema: new OA\Schema(
                    properties: [
                        new OA\Property(
                            property: 'authKey',
                            description: 'Ключ анкеты',
                            type: 'string',
                            example: 'b444ad1ac7e20c4d8748d9f0cc878aa6',
                        ),
                    ],
                ),
            ),
        ),
        tags: ['Прохождение (новое)'],
    )]
    #[OA\Response(
        response: 200,
        description: 'Успешная отписка',
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                properties: [
                    new OA\Property(
                        property: 'success',
                        type: 'boolean',
                        example: true,
                    ),
                    new OA\Property(
                        property: 'error',
                        type: 'string',
                        example: null,
                        nullable: true,
                    ),
                ],
                type: 'object',
            ),
        ),
    )]
    #[OA\Response(
        response: 400,
        description: 'Неверные параметры запроса',
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                properties: [
                    new OA\Property(
                        property: 'success',
                        type: 'boolean',
                        example: false,
                    ),
                    new OA\Property(
                        property: 'error',
                        type: 'string',
                        example: 'authKey is required',
                    ),
                ],
                type: 'object',
            ),
        ),
    )]
    #[OA\Response(
        response: 404,
        description: 'Анкета не найдена',
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                properties: [
                    new OA\Property(
                        property: 'success',
                        type: 'boolean',
                        example: false,
                    ),
                    new OA\Property(
                        property: 'error',
                        type: 'string',
                        example: 'Анкета не найдена',
                    ),
                ],
                type: 'object',
            ),
        ),
    )]
    public function actionUnsubscribe(): Response
    {
        $get = Yii::$app->request->get();
        $isPreview = isset($get['preview']);
        if ($isPreview) {
            return $this->asJson(['success' => true, 'error' => null]);
        }

        $authKey = Yii::$app->request->post('authKey');
        if (!$authKey) {
            return $this->errorResponse('authKey is required');
        }

        $answer = $this->findAnswer($authKey);
        if (!$answer) {
            return $this->errorResponse('Анкета не найдена', 404);
        }

        if (!$answer->contact) {
            return $this->errorResponse('Анкета не связана с контактом');
        }

        $answer->contact->is_subscribed = 0;

        if (!$answer->contact->save()) {
            return $this->errorResponse('Не удалось отписать контакт от рассылки');
        }

        return $this->asJson(['success' => true, 'error' => null]);
    }

    #[OA\Post(
        path: '/foquz/api/p/upload-files',
        summary: 'Загрузка файлов к ответу на вопрос',
        requestBody: new OA\RequestBody(
            content: new OA\MediaType(
                mediaType: 'multipart/form-data',
                schema: new OA\Schema(
                    properties: [
                        new OA\Property(
                            property: 'files',
                            description: 'Файлы',
                            type: 'array',
                            items: new OA\Items(
                                type: 'string',
                                format: 'binary',
                            ),
                        ),
                    ],
                ),
            ),
        ),
        tags: ['Прохождение (новое)'],
        parameters: [
            new OA\Parameter(
                name: 'authKey',
                description: 'Ключ анкеты',
                in: 'query',
                required: true,
                example: 'b444ad1ac7e20c4d8748d9f0cc878aa6',
            ),
            new OA\Parameter(
                name: 'question_id',
                description: 'ID вопроса',
                in: 'query',
                required: true,
                example: '1234',
            ),
        ],
    )]
    #[OA\Response(
        response: 200,
        description: 'Файлы успешно загружены или загружены с ошибкой',
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                properties: [
                    new OA\Property(
                        property: 'files',
                        type: 'array',
                        items: new OA\Items(
                            properties: [
                                new OA\Property(
                                    property: 'id',
                                    description: 'ID файла',
                                    type: 'integer',
                                    example: 1234,
                                ),
                                new OA\Property(
                                    property: 'type',
                                    description: 'Тип файла',
                                    type: 'string',
                                    enum: ['picture', 'audio', 'video'],
                                    example: 'picture',
                                ),
                                new OA\Property(
                                    property: 'link',
                                    description: 'Относительная ссылка на файл',
                                    type: 'string',
                                    example: '/uploads/foquz/answer/1/1.jpg',
                                ),
                                new OA\Property(
                                    property: 'image',
                                    description: 'Относительная ссылка на изображение (только для видео)',
                                    type: 'string',
                                    example: '/uploads/foquz/answer/1/1.jpg',
                                ),
                                new OA\Property(
                                    property: 'origin_name',
                                    description: 'Оригинальное имя файла',
                                    type: 'string',
                                    example: '1.jpg',
                                ),
                            ],
                        ),
                    ),
                    new OA\Property(
                        property: 'errors',
                        type: 'array',
                        items: new OA\Items(
                            description: 'Ошибки при загрузке',
                            type: 'string',
                            example: 'Файл 1.jpg имеет недопустимое расширение',
                        ),
                    ),
                ],
                type: 'object',
            ),
        ),
    )]
    public function actionUploadFiles($authKey, $question_id): Response
    {
        $get = Yii::$app->request->get();
        $isPreview = isset($get['preview']);

        if (isset($get['lang'])) {
            Yii::$app->language = $get['lang'];
        }

        return Yii::$app->runAction('/foquz/ajax/answer-upload-files', [
            'authKey'     => $isPreview ? 'dummy' : $authKey,
            'question_id' => $question_id,
        ]);
    }

    #[OA\Post(
        path: '/foquz/api/p/auth',
        summary: 'Авторизация респондента',
        requestBody: new OA\RequestBody(
            content: new OA\MediaType(
                mediaType: 'multipart/form-data',
                schema: new OA\Schema(
                    properties: [
                        new OA\Property(
                            property: 'username',
                            description: 'Имя пользователя',
                            type: 'string',
                            example: 'a.ivanov',
                        ),
                        new OA\Property(
                            property: 'password',
                            description: 'Пароль',
                            type: 'string',
                            example: 'egrgrjlk$egjk',
                        ),
                        new OA\Property(
                            property: 'rememberMe',
                            description: 'Запомнить',
                            type: 'string',
                            enum: ['0', '1'],
                            example: '0',
                        ),
                    ],
                ),
            ),
        ),
        tags: ['Прохождение (новое)'],
    )]
    #[OA\Response(
        response: 200,
        description: 'Успешная авторизация',
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                properties: [
                    new OA\Property(
                        property: 'success',
                        type: 'boolean',
                        example: true,
                    ),
                    new OA\Property(
                        property: 'error',
                        type: 'string',
                        example: null,
                        nullable: true,
                    ),
                ],
                type: 'object',
            ),
        ),
    )]
    #[OA\Response(
        response: 401,
        description: 'Неверные имя пользователя или пароль',
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                properties: [
                    new OA\Property(
                        property: 'success',
                        type: 'boolean',
                        example: false,
                    ),
                    new OA\Property(
                        property: 'error',
                        type: 'string',
                        example: 'Неверный логин или пароль',
                    ),
                ],
                type: 'object',
            ),
        ),
    )]
    public function actionAuth(): Response
    {
        return Yii::$app->runAction('/foquz/ajax/auth');
    }


    #[OA\Post(
        path: '/foquz/api/p/set-image-viewed',
        summary: 'Установить флаг "Изображение показано" для промежуточного блока типа "Тест 5сек"',
        requestBody: new OA\RequestBody(
            content: new OA\MediaType(
                mediaType: 'multipart/form-data',
                schema: new OA\Schema(
                    properties: [
                        new OA\Property(
                            property: 'authKey',
                            description: 'Ключ анкеты',
                            type: 'string',
                            example: 'b444ad1ac7e20c4d8748d9f0cc878aa6',
                        ),
                        new OA\Property(
                            property: 'questionId',
                            description: 'ID промежуточного блока',
                            type: 'integer',
                            example: 135570,
                        ),
                    ],
                ),
            ),
        ),
        tags: ['Прохождение (новое)'],
    )]
    #[OA\Response(
        response: 200,
        description: 'Успешная установка флага',
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                properties: [
                    new OA\Property(
                        property: 'showed',
                        type: 'integer',
                        enum: [0, 1],
                        example: 1,
                    ),
                ],
                type: 'object',
            ),
        ),
    )]
    #[OA\Response(
        response: 404,
        description: 'Анкета не найдена',
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                properties: [
                    new OA\Property(
                        property: 'success',
                        type: 'boolean',
                        example: false,
                    ),
                    new OA\Property(
                        property: 'error',
                        type: 'string',
                        example: 'Анкета не найдена',
                    ),
                ],
                type: 'object',
            ),
        ),
    )]
    /**
     * В промежуточном экране Тест 5 секунд.
     * При клике на кнопку "Показать изображение" сохранять в БД флаг, что респондент посмотрел изображение.
     */
    public function actionSetImageViewed()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $get = Yii::$app->request->get();
        $isPreview = isset($get['preview']);
        if ($isPreview) {
            return ['success' => true, 'error' => null];
        }

        $authKey = Yii::$app->request->post('authKey');
        $questionId = (int)Yii::$app->request->post('questionId');
        if (empty($authKey) || empty($questionId)) {
            return $this->errorResponse('Не заполнены обязательные поля запроса');
        }

        $answer = $this->findAnswer($authKey);
        if (!$answer) {
            return $this->errorResponse('Анкета не найдена', 404);
        }
        try {
            $showedImage = FoquzPollAnswerShowedImage::createOrUpdate($answer->id, $questionId);
            if (!$showedImage->validate()) {
                $this->response->statusCode = 400;
                return $this->asJson(['success' => false, 'errors' => $showedImage->getErrors()]);
            }
            $showedImage->save();
        } catch (\Throwable $e) {
            Yii::error($e->getMessage());
            return $this->errorResponse('Ошибка сохранения записи', 500);
        }

        return $showedImage;
    }

    /**
     * Найти анкету по ключу
     * @param string $authKey
     * @return FoquzPollAnswer|null
     */
    public function findAnswer(string $authKey): ?FoquzPollAnswer
    {
        /** @var FoquzPollAnswer|null $answer */
        $answer = FoquzPollAnswer::find()
            ->where(['auth_key' => $authKey])
            ->one();

        return $answer;
    }

    /**
     * Найти анкету по отправке
     * @param string $key
     * @return FoquzPollAnswer|null
     */
    public function findAnswerBySendingKey(string $key): ?FoquzPollAnswer
    {
        /** @var FoquzPollMailingListSend|null $sending */
        $sending = FoquzPollMailingListSend::find()
            ->where(['key' => $key])
            ->one();

        if (!empty($sending->answer)) {
            return $sending->answer;
        }

        return null;
    }

    /**
     * @param string $key
     * @return Response
     * @throws NotFoundHttpException
     */
    public function actionAnswerVariables(string $key): Response
    {
        $model = FoquzPollAnswer::findOne(['auth_key' => $key]);
        if (!$model) {
            throw new NotFoundHttpException('Анкета не найдена');
        }

        $variables = [];

        foreach ($model->getVariables() as $key => $value) {
            if (!isset($variables[$key])) {
                $variables[$key] = $value;
            }
        }
        return $this->asJson($variables);
    }

    /**
     * Вернуть ответ с ошибкой
     * @param string $message
     * @param int $code
     * @return Response
     */
    public function errorResponse(string $message, int $code = 400): Response
    {
        Yii::$app->response->statusCode = $code;
        return $this->asJson(['success' => false, 'error' => $message]);
    }

    public function allowEditAfterDone(FoquzPollAnswer $model): bool
    {
        return $model->foquzPoll->editing_duration !== null &&
            ($model->foquzPoll->editing_duration === 0 ||
                (strtotime($model->created_at) + $model->foquzPoll->editing_duration * 60 * 60 * 24) > time());
    }

    private function isStaffEdit(FoquzPollAnswer $model): bool
    {
        /** @var User $user */
        if (!Yii::$app->user->isGuest && $user = Yii::$app->user->identity) {
            if ($model->foquzPoll->company_id !== $user->company->id) {
                return false;
            }
            if (
                $user->isAdmin() || $user->isEditor() || ($user->isExecutor() && $user->can_edit_answers) ||
                (
                    $user->isFilialEmployee() &&
                    (
                        empty($user->userFilials) ||
                        (
                            $model->answer_filial_id &&
                            in_array($model->answer_filial_id, ArrayHelper::getColumn($user->userFilials, 'filial_id'))
                        )
                    )
                )
            ) {
                return true;
            }
        }
        return false;
    }


    private function getTimer(?FoquzPollAnswer $answer, FoquzPoll $poll, bool $getDefault = false): array
    {

        $timer = [
            'enabled'     => false,
            'status'      => 'not-started',
            'timeLeft' => null,
            'duration'    => null
        ];
        if ($getDefault) {
            return $timer;
        }
        if ($poll->time_to_pass) {
            $parts = explode(':', $poll->time_to_pass);
            $duration = 0;
            foreach ($parts as $part) {
                $duration = $duration * 60 + intval($part);
            }
            if ($duration > 0) {
                $timer['enabled'] = true;
                $timer['duration'] = $duration;
            }
        }
        if ($timer['enabled'] && !empty($answer)) {
            if ($answer->first_question_showed_at) {
                $timer['timeLeft'] = $timer['duration'] - time() + strtotime($answer->first_question_showed_at);
                if ($timer['timeLeft'] <= 0) {
                    $timer['timeLeft'] = 0;
                    $timer['status'] = 'finished';
                } else {
                    $timer['status'] = 'active';
                }
            } else {
                $timer['timeLeft'] = $timer['duration'];
            }
        }

        return $timer;
    }

}