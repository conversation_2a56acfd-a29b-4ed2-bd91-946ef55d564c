<?php


namespace app\modules\foquz\controllers\api\v1;


use app\models\Filial;
use app\models\vo\Phone;
use app\modules\foquz\controllers\api\ApiController;
use app\modules\foquz\models\FoquzContact;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollShortLink;
use app\modules\foquz\services\api\v1\PollService;
use yii\filters\auth\HttpHeaderAuth;
use yii\filters\auth\QueryParamAuth;
use yii\filters\VerbFilter;
use yii\helpers\Url;
use Yii;
use yii\web\BadRequestHttpException;
use yii\web\ForbiddenHttpException;
use yii\web\NotFoundHttpException;

class PollController extends ApiController
{
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        if (!empty(Yii::$app->request->headers['X-Api-Key'])) {
            $behaviors['authenticator'] = [
                'class'  => HttpHeaderAuth::class,
            ];
        } else {
            $behaviors['authenticator'] = [
                'class' => QueryParamAuth::class,
            ];
        }
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'send-manual' => ['POST'],
                'send-manual-json' => ['POST'],
                'send-manual-many-clients' => ['POST'],
                'list' => ['GET'],
                'send-pachca' => ['POST'],
                'send-pachca-private' => ['POST'],
            ]
        ];
        return $behaviors;
    }

    /**
     * @OA\Get (
     *      path="/foquz/api/v1/poll/list",
     *      tags={"Опросы"},
     *      summary="Список опросов компании",
     *      security={ {"api_key": {}} },
     *      @OA\Parameter(
     *          name="access-token",
     *          in="query",
     *          description="Токен доступа",
     *          required=true,
     *          example="vZGkGCGTTDkxTlv",
     *          @OA\Schema(
     *              type="string",
     *          )
     *      ),
     *      @OA\Parameter(
     *          name="page",
     *          in="query",
     *          description="Номер страницы (по умолчанию 1)",
     *          example=3,
     *          @OA\Schema(
     *              type="integer",
     *          )
     *      ),
     *      @OA\Parameter(
     *          name="perPage",
     *          in="query",
     *          description="Количество опросов на странице (по умолчанию 1000)",
     *          example=50,
     *          @OA\Schema(
     *              type="integer",
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Success",
     *          @OA\JsonContent(
     *              type="object",
     *              @OA\Property(
     *                  property="polls",
     *                  description="Список опросов",
     *                  type="array",
     *                  @OA\Items(oneOf={
     *                      @OA\Schema(
     *                          type="object",
     *                          @OA\Property(
     *                              property="id",
     *                              description="ID опроса",
     *                              type="integer",
     *                              example=1,
     *                          ),
     *                          @OA\Property(
     *                              property="created",
     *                              description="Дата и время создания опроса в формате Y-m-d H:i:s",
     *                              type="string",
     *                              example="2020-01-01 12:00:00",
     *                          ),
     *                          @OA\Property(
     *                              property="author",
     *                              description="Имя пользователя, создавшего опрос",
     *                              type="string",
     *                              example="corpadmin",
     *                          ),
     *                          @OA\Property(
     *                              property="name",
     *                              description="Название опроса",
     *                              type="string",
     *                              example="Опрос посетителей",
     *                          ),
     *                          @OA\Property(
     *                              property="description",
     *                              description="Описание опроса",
     *                              type="string",
     *                              example="Описание опроса посетителей",
     *                          ),
     *                          @OA\Property(
     *                              property="folder",
     *                              description="Расположение папки опроса",
     *                              type="string",
     *                              example="Папка 1 / Подпапка 2 / Подпапка 3",
     *                          ),
     *                          @OA\Property(
     *                              property="isPublished",
     *                              description="Опубликован ли опрос",
     *                              type="boolean",
     *                              example=false,
     *                          ),
     *                          @OA\Property(
     *                              property="link",
     *                              description="Общая (анонимная) ссылка на опрос",
     *                              type="string",
     *                              example="https://mycompany.foquz.ru/p/F123c85f767ade",
     *                          ),
     *                      ),
     *                  }),
     *                ),
     *                @OA\Property(
     *                    property="count",
     *                    description="Общее количество опросов",
     *                    type="integer",
     *                    example=555,
     *                ),
     *                @OA\Property(
     *                    property="page",
     *                    description="Номер страницы",
     *                    type="integer",
     *                    example=1,
     *                ),
     *                @OA\Property(
     *                    property="perPage",
     *                    description="Количество опросов на странице",
     *                    type="integer",
     *                    example=100,
     *                ),
     *            ),
     *        ),
     *        @OA\Response(
     *            response=401,
     *            description="Не авторизован",
     *        ),
     *    ),
     * )
    */
    public function actionList($page=1, $perPage=1000)
    {
        $companyId = \Yii::$app->user->identity->company->id;

        $polls = FoquzPoll::find()->where([
                'is_tmp' => false,
                'is_folder' => 0,
                'deleted' => 0,
                'company_id' =>  $companyId
        ])
        ->andWhere(["<>", 'status', FoquzPoll::STATUS_ARCHIVE]);

        $count = $polls->count();
        $page = intval($page);
        $perPage = intval($perPage);

        $polls = $polls->orderBy('id')->with(['createdBy', 'parentFolder'])->limit($perPage)->offset(($page-1)*$perPage)->all();

        $result = [];
        foreach ($polls as $poll) {
            if (empty($poll->key)) {
                $poll->key = uniqid('F');
                $poll->save();
            }
            $p = [
                "id" => $poll->id,
                "created" => date("Y-m-d H:i:s", $poll->created_at),
                'author' => $poll->createdBy->username ?? null,
                "name" => $poll->name,
                "description" => $poll->goal_text,
                'folder' => FoquzPoll::getFolderName($poll->parentFolder),
                'isPublished' => $poll->is_published ? true : false,
                'link' =>  Url::to(['/foquz/default/anonymous', 'id' => $poll->key], true),
            ];
            $result[] = $p;
        }

        return [
            "polls" => $result,
            "count" => intval($count),
            "page" => $page,
            'perPage' => $perPage
        ];
    }

    /**
     * @OA\Post (
     *     path="/foquz/api/v1/poll/create-contacts",
     *     tags={"Клиенты"},
     *     summary="Пакетное создание/редактирование клиентов",
     *     security={ {"api_key": {}} },
     *     @OA\Parameter(
     *         name="access-token",
     *         in="query",
     *         description="Токен доступа",
     *         required=true,
     *         example="vZGkGCGTTDkxTlv",
     *         @OA\Schema(type="string")
     *     ),
     *    @OA\RequestBody(
     *        @OA\MediaType(
     *            mediaType="multipart/form-data",
     *            encoding="text/plain",
     *            @OA\Schema(
     *                 @OA\Property(
     *                     property="contacts",
     *                     description="Контакт клиента. Помимо указанных полей, можно передать любые другие. Если поле с указанным ключом заведено в качестве дополнительного поля контакта для компании, информация для контакта будет добавлена или обновлена как и для обычного поля. Для контакта обязательно должно быть передано одно из полей - Email или Телефон или Customer. Все остальные поля необязательные.",
     *                     type="array",
     *                     @OA\Items(
     *                         anyOf={@OA\Schema(
     *                             type="array",
     *                             title="Массив из полей клиента",
     *                             @OA\Items(
     *                                 @OA\Property(
     *                                     property="Email",
     *                                     description="Email контакта",
     *                                     type="string",
     *                                     example="<EMAIL>",
     *                                 ),
     *                                 @OA\Property(
     *                                     property="Телефон",
     *                                     description="Телефон контакта",
     *                                     type="string",
     *                                     example="79999999999",
     *                                 ),
     *                                 @OA\Property(
     *                                       property="customer",
     *                                       description="Внешний ID контакта",
     *                                       type="string",
     *                                       example="df26202d-3fd1-4067-8463-337f3754aeb5",
     *                                   ),
     *                                 @OA\Property(
     *                                     property="Фамилия",
     *                                     description="Фамилия контакта",
     *                                     type="string",
     *                                     example="Иванов",
     *                                 ),
     *                                 @OA\Property(
     *                                     property="Имя",
     *                                     description="Имя контакта",
     *                                     type="string",
     *                                     example="Иван",
     *                                 ),
     *                                 @OA\Property(
     *                                     property="Отчество",
     *                                     description="Отчество контакта",
     *                                     type="string",
     *                                     example="Иванович",
     *                                 ),
     *                                 @OA\Property(
     *                                     property="Дата рождения",
     *                                     description="Дата рождения контакта",
     *                                     type="string",
     *                                     example="01.01.1990",
     *                                 ),
     *                             ),
     *                         )},
     *                     ),
     *                 ),
     *            ),
     *        ),
     *    ),
     *    @OA\Response(
     *        response=200,
     *        description="Success",
     *        @OA\MediaType(
     *            mediaType="application/json",
     *            @OA\Schema(
     *                type="array",
     *                @OA\Items(oneOf={
     *                    @OA\Schema(
     *                        type="object",
     *                        @OA\Property(
     *                            property="success",
     *                            description="Результат",
     *                            type="boolean",
     *                            example=true,
     *                            enum={true},
     *                         ),
     *                         @OA\Property(
     *                             property="row",
     *                             description="Номер строки в запросе по порядку (начиная с 0)",
     *                             type="integer",
     *                             example=0,
     *                         ),
     *                         @OA\Property(
     *                             property="result",
     *                             description="Результат запроса",
     *                             type="string",
     *                             example="Контакт обновлен",
     *                             enum={"Контакт добавлен", "Контакт обновлен"},
     *                         ),
     *                     ),
     *                 }),
     *             ),
     *         ),
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Не авторизован",
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Ошибка в запросе",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="array",
     *                 @OA\Items(oneOf={
     *                     @OA\Schema(
     *                         type="object",
     *                         @OA\Property(
     *                             property="success",
     *                             description="Результат отправки",
     *                             type="boolean",
     *                             example=false,
     *                             enum={false},
     *                         ),
     *                         @OA\Property(
     *                             property="errors",
     *                             description="Ошибки",
     *                             type="array",
     *                             example={"pollId":"Опроса с ID=123456 не существует"},
     *                             @OA\Items(oneOf={
     *                                 @OA\Schema(
     *                                     type="object",
     *                                     @OA\AdditionalProperties(
     *                                         type="string",
     *                                         title="Название поля и ошибка",
     *                                     ),
     *                                 ),
     *                             }),
     *                         ),
     *                     ),
     *                 }),
     *             ),
     *         ),
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Ошибка доступа",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="array",
     *                 @OA\Items(oneOf={
     *                     @OA\Schema(
     *                         type="object",
     *                         @OA\Property(
     *                             property="success",
     *                             description="Результат отправки",
     *                             type="boolean",
     *                             example=true,
     *                             enum={false},
     *                         ),
     *                         @OA\Property(
     *                             property="errors",
     *                             description="Ошибки",
     *                             type="array",
     *                             example={"pollId":"Опроса с ID=123456 не существует"},
     *                             @OA\Items(oneOf={
     *                                 @OA\Schema(
     *                                     type="object",
     *                                     @OA\AdditionalProperties(
     *                                         type="string",
     *                                         title="Название поля и ошибка",
     *                                     ),
     *                                 ),
     *                             }),
     *                         ),
     *                     ),
     *                 }),
     *             ),
     *         ),
     *     ),
     * )
     */
    public function actionCreateContacts()
    {
        $companyId = \Yii::$app->user->identity->company->id;
        $contacts = \Yii::$app->request->post('contacts');

        if (!is_array($contacts) || count($contacts)==0) {
            $data = @json_decode(Yii::$app->getRequest()->getRawBody(), true);
            if (is_array($data) && isset($data["contacts"])) $contacts = $data["contacts"];

            if (!is_array($contacts) || count($contacts)==0) {
                 return $this->response(400, [
                    "success"=>false,
                    'errors' => [
                        'contacts' => 'Некорректный массив с контактами'
                    ]
                ]);
            }
        }


        $result = []; $number = 0;
        foreach($contacts as $contactData) {
            $email = isset($contactData['Email']) ? $contactData['Email'] : null;
            $phone = isset($contactData['Телефон']) ? $contactData['Телефон'] : null;

            $r = ["row"=>$number++, 'success'=>false, 'result'=>null, 'errors'=>[]];
            if ($phone) $phone = (string)(new Phone($phone));
            if (!$email && !$phone) {
                $r["errors"][] = "Должен быть указан телефон или Email";
            } else {
                $isCreate = false;
                $contact = FoquzContact::findContact($email, $phone);
                if (!$contact) {
                    $contact = new FoquzContact([
                        'company_id' => \Yii::$app->user->identity->company->id,
                        'email' => $email,
                        'phone' => $phone ? (string)(new Phone($phone)) : null,
                        'created_by' => \Yii::$app->user->id,
                        'updated_by' => \Yii::$app->user->id,
                    ]);
                    if (!$contact->save()) {
                        $r["errors"] = $contact->getErrors();
                    }
                    $isCreate = true;
                }
            }

            if (count($r["errors"])==0) {
                $ret = $contact->fillData($contactData, \Yii::$app->user->identity->company->id, 1);
                $r["success"] = $ret["success"] ? true : false;
                if ($r["success"]) {
                    unset($r["errors"]);
                    $r["result"] = $isCreate ? "Контакт добавлен" : "Контакт обновлен";
                } else {
                    $r["errors"] = $ret["errors"];
                }
            }

            if ($r["success"]) unset($r["errors"]);

            $result[] = $r;
        }
        return $result;
    }

    public function actionSendManualManyClients($id)
    {
        $companyId = \Yii::$app->user->identity->company->id;
        $poll = FoquzPoll::findOne(['id' => $id, 'deleted' => 0]);

        if(!$poll) {
            return $this->response(404, ["success"=>false, 'errors' => ['pollId' => 'Опроса с ID='.$id.' не существует']]);
        }

        if($poll->company_id !== $companyId) {
            return $this->response(403, [
                'errors' => ['pollId' => 'Опрос не принадлежит вашей компании.']
            ]);
        }

        if($poll->is_auto) {
            return $this->response(400, ["success"=>false, 'errors' => ['pollId' => 'Функционал предусмотрен только для ручного типа опроса']]);
        }

        if($poll->getChannels()->where(['active' => true])->count() == 0) {
            return $this->response(400, [
                "success"=>false,
                'errors' => [
                    'pollId' => 'Для опроса с ID='.$id.' не настроены каналы связи'
                ]
            ]);
        }

        $contacts = \Yii::$app->request->post('contacts');
        $onlyLink = \Yii::$app->request->post('onlyLink') ?? false;
        $filial = \Yii::$app->request->post('filial_id') ?? null;
        $filialName = \Yii::$app->request->post('filial') ?? null;
        if($filial) {
            $filialModel = Filial::find()->where(['id' => $filial, 'company_id' => \Yii::$app->user->identity->company->id])->one();
            if(!$filialModel) {
                return $this->response(400, [
                    "success"=>false,
                    'errors' => [
                        'filial_id' => 'Не удалось получить данные указанного филиала'
                    ]
                ]);
            }
        }
        if($filialName) {
            $filialModel = Filial::find()->where(['name' => $filialName, 'company_id' => \Yii::$app->user->identity->company->id])->one();
            if(!$filialModel) {
                return $this->response(400, [
                    "success"=>false,
                    'errors' => [
                        'filial' => 'Не удалось получить данные указанного филиала'
                    ]
                ]);
            }
            $filial =  $filialModel->id;
        }


        $errors = [];
        $sends = [];
        foreach($contacts as $contactData) {
            $email = isset($contactData['Email']) ? $contactData['Email'] : null;
            $phone = isset($contactData['Телефон']) ? $contactData['Телефон'] : null;

            $contact = FoquzContact::getContact($email, $phone);
            if($email && $phone) {
                $channel = $poll->getChannels()->where(['active' => true])->one();
            } elseif($email) {
                $channel = $poll->getChannels()->where(['name' => 'Email', 'active' => true])->one();
                if(!$channel) {
                    $errors[] = [
                        'email' => $email,
                        'phone' => $phone,
                        'error' => 'Для клиента нужно указать телефон, чтобы отправить анкету опроса с ID='.$id
                    ];
                    continue;
                }
            } else {
                $channel = $poll->getChannels()->where(['active' => true])->andWhere(['in', 'name', ['SMS', 'Viber', 'Telegram']])->one();
                if(!$channel) {
                    $errors[] = [
                        'email' => $email,
                        'phone' => $phone,
                        'error' => 'Для клиента нужно указать почту, чтобы отправить анкету опроса с ID='.$id
                    ];
                    continue;
                }
            }

            $customData = $contact->fillData($contactData);

            if($poll->dont_send_if_passed) {
                $answer = FoquzPollAnswer::find()
                    ->where([
                        'foquz_poll_id' => $poll->id,
                        'contact_id' => $contact->id
                    ])->one();
                if($answer) {
                    if($email && $phone) {
                        $errorText = 'Опрос клиенту с номером телефона <b>'.$phone.'</b> и почтой <b>'.$email.'</b> уже был отправлен '.date('d.m.Y', strtotime($answer->created_at));
                    } elseif($email) {
                        $errorText = 'Опрос клиенту с почтой <b>'.$email.'</b> уже был отправлен '.date('d.m.Y', strtotime($answer->created_at));
                    } else {
                        $errorText = 'Опрос клиенту с номером телефона <b>'.$phone.'</b> уже был отправлен '.date('d.m.Y', strtotime($answer->created_at));
                    }
                    $errors[] = $errorText;
                    continue;
                }
            }

            if (!$contact->is_subscribed) {
                $errors[] =  "Клиент отписан от рассылок";
                continue;
            }

            $answer = $channel->apiSend($contact, $customData, $onlyLink, $filial);
            $sends[] = [
                'email' => $email,
                'phone' => $phone,
                'id' => md5($answer['id']),
                'link' => \Yii::$app->params['protocol'].'://'.\Yii::$app->user->identity->company->alias.'/p/'.$answer['key']
            ];
        }

        return $this->response(200, [
            'sends' => $sends,
            'errors' => $errors
        ]);
    }

    public function sendContactsApi($id)
    {
        $result = [];
        $contacts = \Yii::$app->request->post('contacts');
        $onlyLink = \Yii::$app->request->get('onlyLink') ?? false;
        if (!$onlyLink) $onlyLink=\Yii::$app->request->post('onlyLink') ?? false;
        $filial = \Yii::$app->request->post('filial_id') ?? null;
        $filialName = \Yii::$app->request->post('filial') ?? null;
        $companyId = \Yii::$app->user->identity->company->id;
        $poll = FoquzPoll::findOne(['id' => $id, 'deleted' => 0]);

        if(!$poll) {
            return $this->response(404, ["success"=>false, 'errors' => ['pollId' => 'Опроса с ID='.$id.' не существует']]);
        }

        if($poll->company_id !== $companyId) {
            return $this->response(403, [
                'errors' => ['pollId' => 'Опрос не принадлежит вашей компании.']
            ]);
        }

        if($poll->is_auto) {
            return $this->response(400, ["success"=>false, 'errors' => ['pollId' => 'Функционал предусмотрен только для ручного типа опроса']]);
        }

        if($poll->getChannels()->where(['active' => true])->count() == 0) {
            return $this->response(400, [
                "success"=>false,
                'errors' => [
                    'pollId' => 'Для опроса с ID='.$id.' не настроены каналы связи'
                ]
            ]);
        }

        if($filial) {
            $filialModel = Filial::find()->where(['id' => $filial, 'company_id' => \Yii::$app->user->identity->company->id])->one();
            if(!$filialModel) {
                return $this->response(400, [
                    "success"=>false,
                    'errors' => [
                        'filial_id' => 'Не удалось получить данные указанного филиала'
                    ]
                ]);
            }
        }
        if($filialName) {
            $filialModel = Filial::find()->where(['name' => $filialName, 'company_id' => \Yii::$app->user->identity->company->id])->one();
            if(!$filialModel) {
                return $this->response(400, [
                    "success"=>false,
                    'errors' => [
                        'filial_id' => 'Не удалось получить данные указанного филиала'
                    ]
                ]);
            }
            $filial =  $filialModel->id;
        }

        if (is_array($contacts)) {
            $row = 1;
            foreach ($contacts as $contactData) {
                $item = ["row"=>$row++];

                $errors = [];
                $email = isset($contactData['Email']) ? $contactData['Email'] : null;
                $phone = isset($contactData['Телефон']) ? $contactData['Телефон'] : null;
                $customer = isset($contactData['customer']) ? $contactData['customer'] : null;

                $phone = trim($phone);
                if (strlen($phone)==10) {
                    $phone = "7" . $phone;
                }

                if($email || $phone || $customer) {
                    if ($email && $phone) {
                        $foquzContact = FoquzContact::find()->where([
                            'phone' => (string)(new Phone($phone)),
                            'company_id' => \Yii::$app->user->identity->company->id,
                            'is_deleted' => false,
                        ])->one();
                        if (!$foquzContact) {
                            $foquzContact = FoquzContact::find()->where([
                                'email' => $email,
                                'company_id' => \Yii::$app->user->identity->company->id,
                                'is_deleted' => false,
                            ])->one();
                        }
                        $channel = $poll->getChannels()->where(['active' => true])->one();
                    } elseif ($email) {
                        $channel = $poll->getChannels()->where(['name' => 'Email', 'active' => true])->one();
                        if (!$onlyLink && !$channel) {
                            $errors[] = 'Для клиента нужно указать телефон, чтобы отправить анкету опроса с ID=' . $id;
                        }
                        $foquzContact = FoquzContact::find()->where([
                            'email' => $email,
                            'company_id' => \Yii::$app->user->identity->company->id,
                            'is_deleted' => false,
                        ])->one();
                    } elseif ($customer && $onlyLink) {
                        $channel = null;
                        $foquzContact = FoquzContact::find()->where([
                            'company_client_id' => $customer,
                            'company_id' => \Yii::$app->user->identity->company->id,
                            'is_deleted' => false,
                        ])->one();
                    } else {
                        $channel = $poll->getChannels()->where(['active' => true])->andWhere(['in', 'name', ['SMS', 'Viber', 'Telegram']])->one();
                        if (!$onlyLink && !$channel) {
                            $errors[] = 'Для клиента нужно указать почту, чтобы отправить анкету опроса с ID=' . $id;
                        }
                        $foquzContact = FoquzContact::find()->where([
                            'phone' => (string)(new Phone($phone)),
                            'company_id' => \Yii::$app->user->identity->company->id,
                            'is_deleted' => false,
                        ])->one();
                    }

                    if (count($errors) == 0) {
                        if (!$foquzContact) {
                            $foquzContact = new FoquzContact([
                                'company_id' => \Yii::$app->user->identity->company->id,
                                'email' => $email,
                                'phone' => $phone ? (string)(new Phone($phone)) : null,
                                'company_client_id' => $customer,
                                'created_by' => \Yii::$app->user->id,
                                'updated_by' => \Yii::$app->user->id,
                            ]);
                            $foquzContact->save(false);
                        }

                        $customData = $foquzContact->fillData($contactData);

                        if ($poll->dont_send_if_passed) {
                            $answer = FoquzPollAnswer::find()
                                ->where([
                                    'foquz_poll_id' => $poll->id,
                                    'contact_id' => $foquzContact->id
                                ])->one();
                            if ($answer) {
                                if ($email && $phone) {
                                    $errorText = 'Опрос клиенту с номером телефона <b>' . $phone . '</b> и почтой <b>' . $email . '</b> уже был отправлен ' . date('d.m.Y', strtotime($answer->created_at));
                                } elseif ($email) {
                                    $errorText = 'Опрос клиенту с почтой <b>' . $email . '</b> уже был отправлен ' . date('d.m.Y', strtotime($answer->created_at));
                                } else {
                                    $errorText = 'Опрос клиенту с номером телефона <b>' . $phone . '</b> уже был отправлен ' . date('d.m.Y', strtotime($answer->created_at));
                                }
                                $errors[] = $errorText;
                            }
                        }

                        if (count($errors)==0) {
                            if (!$channel) $channel = $poll->getChannels()->one();
                            $answer = $channel->apiSend($foquzContact, $customData, $onlyLink, $filial);
                            $item["link"] = \Yii::$app->params['protocol'] . '://' . \Yii::$app->user->identity->company->alias . '/p/' . $answer['key'];
                            $link = $item["link"];
                            $shortLink = FoquzPollShortLink::find()->where(['link' => $link])->one();
                            if (!$shortLink) {
                                $shortLink = FoquzPollShortLink::create($link);
                            }
                            $item["shortLink"] = Yii::$app->user->identity->company->shortLink . $shortLink->code;
                            $item["id"] = md5($answer['id']);
                            $item["success"] = true;
                        }
                    }
                } else {
                    $errors[] = "Одно из полей Еmail или Телефон обязательны для заполнения";
                }

                if (count($errors)>0) {
                    $item["success"] = false;
                    $item["errors"] = $errors;

                }

                $result[] = $item;
            }

        }
        return $this->response(200, $result);
        return $result;
    }


    public function actionSendManualJson($id)
    {
        $data = @json_decode(Yii::$app->getRequest()->getRawBody(), true);
        if (!is_array($data)) $data = [];
        $companyId = \Yii::$app->user->identity->company->id;
        $poll = FoquzPoll::findOne(['id' => $id, 'deleted' => 0]);

        if(!$poll) {
            return $this->response(404, ["success"=>false, 'errors' => ['pollId' => 'Опроса с ID='.$id.' не существует']]);
        }

        if($poll->company_id !== $companyId) {
            return $this->response(403, [
                'errors' => ['pollId' => 'Опрос не принадлежит вашей компании.']
            ]);
        }

        if($poll->is_auto) {
            return $this->response(400, ["success"=>false, 'errors' => ['pollId' => 'Функционал предусмотрен только для ручного типа опроса']]);
        }

        $onlyLink = isset($data['onlyLink']) ?  $data['onlyLink'] : false;


        if($poll->getChannels()->where(['active' => true])->count() == 0 && !$onlyLink) {
            return $this->response(400, [
                "success"=>false,
                'errors' => [
                    'pollId' => 'Для опроса с ID='.$id.' не настроены каналы связи'
                ]
            ]);
        }

        $contactData = isset($data['contact'])  ? $data['contact'] : [];

        $filial = isset($data['filial_id']) ?  $data['filial_id'] : null;

        $filial = \Yii::$app->request->post('filial_id') ?? null;
        $filialName = \Yii::$app->request->post('filial') ?? null;



        if($filial) {
            $filialModel = Filial::find()->where(['id' => $filial, 'company_id' => \Yii::$app->user->identity->company->id])->one();
            if(!$filialModel) {
                return $this->response(400, [
                    "success"=>false,
                    'errors' => [
                        'filial_id' => 'Не удалось получить данные указанного филиала'
                    ]
                ]);
            }
        }
        if($filialName) {
            $filialModel = Filial::find()->where(['name' => $filialName, 'company_id' => \Yii::$app->user->identity->company->id])->one();
            if(!$filialModel) {
                return $this->response(400, [
                    "success"=>false,
                    'errors' => [
                        'filial' => 'Не удалось получить данные указанного филиала'
                    ]
                ]);
            }
            $filial =  $filialModel->id;
        }

        $email = isset($contactData['Email']) ? $contactData['Email'] : null;
        $phone = isset($contactData['Телефон']) ? $contactData['Телефон'] : null;

        $phone = trim($phone);
        if (strlen($phone)==10) {
            $phone = "7" . $phone;
        }



        if ($email || $phone) {
            if ($email && $phone) {
                $foquzContact = FoquzContact::find()->where([
                    'phone' => (string)(new Phone($phone)),
                    'company_id' => \Yii::$app->user->identity->company->id,
                ])->one();
                if (!$foquzContact) {
                    $foquzContact = FoquzContact::find()->where([
                        'email' => $email,
                        'company_id' => \Yii::$app->user->identity->company->id,
                    ])->one();
                }
                $channel = $poll->getChannels()->where(['active' => true])->one();
            } elseif ($email) {
                $channel = $poll->getChannels()->where(['name' => 'Email', 'active' => true])->one();
                if (!$channel && !$onlyLink) {
                    return $this->response(400, ["success" => false, 'errors' => ['channel' => 'Для клиента нужно указать телефон, чтобы отправить анкету опроса с ID=' . $id]]);
                }
                $foquzContact = FoquzContact::find()->where([
                    'email' => $email,
                    'company_id' => \Yii::$app->user->identity->company->id,
                ])->one();
            } else {
                $channel = $poll->getChannels()->where(['active' => true])->andWhere(['in', 'name', ['SMS', 'Viber', 'Telegram']])->one();
                if (!$channel && !$onlyLink) {
                    return $this->response(400, ["success" => false, 'errors' => ['channel' => 'Для клиента нужно указать почту, чтобы отправить анкету опроса с ID=' . $id]]);
                }
                $foquzContact = FoquzContact::find()->where([
                    'phone' => (string)(new Phone($phone)),
                    'company_id' => \Yii::$app->user->identity->company->id,
                ])->one();
            }

            if(!$foquzContact) {
                $foquzContact = new FoquzContact([
                    'company_id' => \Yii::$app->user->identity->company->id,
                    'email' => $email,
                    'phone' => $phone ? (string)(new Phone($phone)) : null,
                    'created_by' => \Yii::$app->user->id,
                    'updated_by' => \Yii::$app->user->id,
                    'is_subscribed' => 1,
                ]);
                $foquzContact->save();
            }

            $customData = $foquzContact->fillData($contactData);

            if($poll->dont_send_if_passed) {
                $answer = FoquzPollAnswer::find()
                    ->where([
                        'foquz_poll_id' => $poll->id,
                        'contact_id' => $foquzContact->id
                    ])->one();
                if($answer) {
                    if($email && $phone) {
                        $errorText = 'Опрос клиенту с номером телефона <b>'.$phone.'</b> и почтой <b>'.$email.'</b> уже был отправлен '.date('d.m.Y', strtotime($answer->created_at));
                    } elseif($email) {
                        $errorText = 'Опрос клиенту с почтой <b>'.$email.'</b> уже был отправлен '.date('d.m.Y', strtotime($answer->created_at));
                    } else {
                        $errorText = 'Опрос клиенту с номером телефона <b>'.$phone.'</b> уже был отправлен '.date('d.m.Y', strtotime($answer->created_at));
                    }
                    return $this->response(400, ['errors' => ['contact' => $errorText]]);
                }
            }

            if (!$foquzContact->is_subscribed) {
                return $this->response(400, ['errors' => ['contact' => "Клиент отписан от рассылок"]]);
            }

            $answer = $channel->apiSend($foquzContact, $customData, $onlyLink, $filial);

            $link = \Yii::$app->params['protocol'].'://'.\Yii::$app->user->identity->company->alias.'/p/'.$answer['key'];
            $shortLink = FoquzPollShortLink::find()->where(['link' => $link])->one();
            if(!$shortLink) {
                $shortLink = FoquzPollShortLink::create($link);
            }
            $shortLink = Yii::$app->user->identity->company->shortLink.$shortLink->code;

            return $this->response(200, [
                'success' => true,
                'id' => md5($answer['id']),
                'link' => $link,
                'shortLink' => $shortLink,
            ]);
        }

        return $this->response(400, ["success"=>false, 'errors' => ['contact' => 'Для клиента нужно указать телефон или почту, чтобы отправить анкету опроса с ID='.$id]]);
    }


    /**
     * @OA\Post (
     *     path="/foquz/api/v1/poll/send-manual",
     *     tags={"Опросы"},
     *     summary="Отправка ручного опроса указанному клиенту",
     *     security={ {"api_key": {}} },
     *     @OA\Parameter(
     *         name="access-token",
     *         in="query",
     *         description="Токен доступа",
     *         required=true,
     *         example="vZGkGCGTTDkxTlv",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="id",
     *         in="query",
     *         description="ID опроса. Получить можно из URL опроса, перейдя в раздел “Настройки” в сервисе (/foquz/foquz-poll/settings?id=1812)",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *    @OA\RequestBody(
     *        @OA\MediaType(
     *            mediaType="multipart/form-data",
     *            encoding="text/plain",
     *            @OA\Schema(
     *                 @OA\Property(
     *                     property="contacts",
     *                     description="Контакт клиента. Помимо указанных полей, можно передать любые другие. Если поле с указанным ключом заведено в качестве дополнительного поля контакта для компании, информация для контакта будет добавлена или обновлена как и для обычного поля. Если поле с указанным ключом не заведено в качестве дополнительного поля контакта для компании, поле с информацией будет сохранено в свойстах анкеты (поле customFields) и в дальнейшем может быть получено с использованием метода **foquz/api/v1/answers**. Для контакта обязательно должно быть передано одно из полей - Email или Телефон или customer. Все остальные поля необязательные.",
     *                     type="array",
     *                     @OA\Items(
     *                         anyOf={@OA\Schema(
     *                             type="array",
     *                             title="Массив из полей клиента",
     *                             @OA\Items(
     *                                 @OA\Property(
     *                                     property="Email",
     *                                     description="Email контакта",
     *                                     type="string",
     *                                     example="<EMAIL>",
     *                                 ),
     *                                 @OA\Property(
     *                                     property="Телефон",
     *                                     description="Телефон контакта",
     *                                     type="string",
     *                                     example="79999999999",
     *                                 ),
     *                                 @OA\Property(
     *                                      property="customer",
     *                                      description="Внешний ID контакта",
     *                                      type="string",
     *                                      example="df26202d-3fd1-4067-8463-337f3754aeb5",
     *                                  ),
     *                                 @OA\Property(
     *                                     property="Фамилия",
     *                                     description="Фамилия контакта",
     *                                     type="string",
     *                                     example="Иванов",
     *                                 ),
     *                                 @OA\Property(
     *                                     property="Имя",
     *                                     description="Имя контакта",
     *                                     type="string",
     *                                     example="Иван",
     *                                 ),
     *                                 @OA\Property(
     *                                     property="Отчество",
     *                                     description="Отчество контакта",
     *                                     type="string",
     *                                     example="Иванович",
     *                                 ),
     *                                 @OA\Property(
     *                                     property="Дата рождения",
     *                                     description="Дата рождения контакта",
     *                                     type="string",
     *                                     example="01.01.1990",
     *                                 ),
     *                             ),
     *                         )},
     *                     ),
     *                 ),
     *                 @OA\Property(
     *                     property="onlyLink",
     *                     description="Если передано 1, то ссылка на анкету не будет отправляться, а будет возвращена в ответе сервера",
     *                     type="integer",
     *                     default=0,
     *                     example=1,
     *                     enum={0,1},
     *                 ),
     *                 @OA\Property(
     *                     property="filial",
     *                     description="Название филиала из справочника компании (должно совпадать с названием в справочнике). Если филиал передан, то будет создана анкета, связанная с филиалом",
     *                     type="string",
     *                     example="Щербинка",
     *                 ),
     *            ),
     *        ),
     *    ),
     *    @OA\Response(
     *        response=200,
     *        description="Success",
     *        @OA\MediaType(
     *            mediaType="application/json",
     *            @OA\Schema(
     *                type="array",
     *                @OA\Items(oneOf={
     *                    @OA\Schema(
     *                        type="object",
     *                        @OA\Property(
     *                            property="success",
     *                            description="Результат отправки",
     *                            type="boolean",
     *                            example=true,
     *                            enum={true},
     *                         ),
     *                         @OA\Property(
     *                             property="row",
     *                             description="Номер строки в запросе по порядку (начиная с 1)",
     *                             type="integer",
     *                             example=1,
     *                         ),
     *                         @OA\Property(
     *                             property="id",
     *                             description="Уникальный ID анкеты",
     *                             type="string",
     *                             example="053ac8b9303a0c42cfe51573537ca67c",
     *                         ),
     *                         @OA\Property(
     *                             property="link",
     *                             description="Ссылка для прохождения анкеты",
     *                             type="string",
     *                             example="https://foquz.ru/p/7c78473522cf66e728aacb47e248794e",
     *                         ),
     *                         @OA\Property(
     *                             property="shortLink",
     *                             description="Короткая ссылка для прохождения анкеты",
     *                             type="string",
     *                             example="https://4qz.ru/qgvuap8",
     *                         ),
     *                     ),
     *                 }),
     *             ),
     *         ),
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Не авторизован",
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Ошибка в запросе",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="array",
     *                 @OA\Items(oneOf={
     *                     @OA\Schema(
     *                         type="object",
     *                         @OA\Property(
     *                             property="success",
     *                             description="Результат отправки",
     *                             type="boolean",
     *                             example=true,
     *                             enum={false},
     *                         ),
     *                         @OA\Property(
     *                             property="errors",
     *                             description="Ошибки",
     *                             type="array",
     *                             example={"pollId":"Опроса с ID=123456 не существует"},
     *                             @OA\Items(oneOf={
     *                                 @OA\Schema(
     *                                     type="object",
     *                                     @OA\AdditionalProperties(
     *                                         type="string",
     *                                         title="Название поля и ошибка",
     *                                     ),
     *                                 ),
     *                             }),
     *                         ),
     *                     ),
     *                 }),
     *             ),
     *         ),
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Ошибка доступа",
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 type="array",
     *                 @OA\Items(oneOf={
     *                     @OA\Schema(
     *                         type="object",
     *                         @OA\Property(
     *                             property="success",
     *                             description="Результат отправки",
     *                             type="boolean",
     *                             example=true,
     *                             enum={false},
     *                         ),
     *                         @OA\Property(
     *                             property="errors",
     *                             description="Ошибки",
     *                             type="array",
     *                             example={"pollId":"Опроса с ID=123456 не существует"},
     *                             @OA\Items(oneOf={
     *                                 @OA\Schema(
     *                                     type="object",
     *                                     @OA\AdditionalProperties(
     *                                         type="string",
     *                                         title="Название поля и ошибка",
     *                                     ),
     *                                 ),
     *                             }),
     *                         ),
     *                     ),
     *                 }),
     *             ),
     *         ),
     *     ),
     * )
     */
    public function actionSendManual($id)
    {
        $companyId = \Yii::$app->user->identity->company->id;
        $contacts = \Yii::$app->request->post('contacts');
        if ($contacts) {
            return $this->sendContactsApi($id);
        }


        $poll = FoquzPoll::findOne(['id' => $id, 'deleted' => 0]);

        if(!$poll) {
            return $this->response(404, ["success"=>false, 'errors' => ['pollId' => 'Опроса с ID='.$id.' не существует']]);
        }

        if($poll->company_id !== $companyId) {
            return $this->response(403, [
                'errors' => ['pollId' => 'Опрос не принадлежит вашей компании.']
            ]);
        }

        if($poll->is_auto) {
            return $this->response(400, ["success"=>false, 'errors' => ['pollId' => 'Функционал предусмотрен только для ручного типа опроса']]);
        }

        $onlyLink = \Yii::$app->request->post('onlyLink') ?? false;
        //if (!$onlyLink) die("ghj");

        if($poll->getChannels()->where(['active' => true])->count() == 0 && !$onlyLink) {
            return $this->response(400, [
                "success"=>false,
                'errors' => [
                    'pollId' => 'Для опроса с ID='.$id.' не настроены каналы связи'
                ]
            ]);
        }


        $contactData = \Yii::$app->request->post('contact');
        $filial = \Yii::$app->request->post('filial_id') ?? null;
        $filialName = \Yii::$app->request->post('filial') ?? null;
        if($filial) {
            $filialModel = Filial::find()->where(['id' => $filial, 'company_id' => \Yii::$app->user->identity->company->id])->one();
            if(!$filialModel) {
                return $this->response(400, [
                    "success"=>false,
                    'errors' => [
                        'filial_id' => 'Не удалось получить данные указанного филиала'
                    ]
                ]);
            }
        }
        if($filialName) {
            $filialModel = Filial::find()->where(['name' => $filialName, 'company_id' => Yii::$app->user->identity->company->id])->one();
            if(!$filialModel) {
                return $this->response(400, [
                    "success"=>false,
                    'errors' => [
                        'filial' => 'Не удалось получить данные указанного филиала'
                    ]
                ]);
            }
            $filial =  $filialModel->id;
        }

        $email = isset($contactData['Email']) ? $contactData['Email'] : null;
        $phone = isset($contactData['Телефон']) ? $contactData['Телефон'] : null;

        $phone = trim($phone);
        if (strlen($phone)==10) {
            $phone = "7" . $phone;
        }



        if($email || $phone) {
            if($email && $phone) {
                $foquzContact = FoquzContact::find()->where([
                    'phone' => (string)(new Phone($phone)),
                    'company_id' => \Yii::$app->user->identity->company->id,
                    'is_deleted' => false,
                ])->one();
                if (!$foquzContact) {
                    $foquzContact = FoquzContact::find()->where([
                        'email' => $email,
                        'company_id' => \Yii::$app->user->identity->company->id,
                        'is_deleted' => false,
                    ])->one();
                }
                $channel = $poll->getChannels()->where(['active' => true])->one();
            } elseif($email) {
                $channel = $poll->getChannels()->where(['name' => 'Email', 'active' => true])->one();
                if(!$channel && !$onlyLink) {
                    return $this->response(400, ["success"=>false, 'errors' => ['channel' => 'Для клиента нужно указать телефон, чтобы отправить анкету опроса с ID='.$id]]);
                }
                $foquzContact = FoquzContact::find()->where([
                    'email' => $email,
                    'company_id' => \Yii::$app->user->identity->company->id,
                    'is_deleted' => false,
                ])->one();
            } else {
                $channel = $poll->getChannels()->where(['active' => true])->andWhere(['in', 'name', ['SMS', 'Viber', 'Telegram']])->one();
                if(!$channel && !$onlyLink) {
                    return $this->response(400, ["success"=>false, 'errors' => ['channel' => 'Для клиента нужно указать почту, чтобы отправить анкету опроса с ID='.$id]]);
                }
                $foquzContact = FoquzContact::find()->where([
                    'phone' => (string)(new Phone($phone)),
                    'company_id' => \Yii::$app->user->identity->company->id,
                    'is_deleted' => false,
                ])->one();
            }

            if(!$foquzContact) {
                $foquzContact = new FoquzContact([
                    'company_id' => \Yii::$app->user->identity->company->id,
                    'email' => $email,
                    'phone' => $phone ? (string)(new Phone($phone)) : null,
                    'created_by' => \Yii::$app->user->id,
                    'updated_by' => \Yii::$app->user->id,
                    'is_subscribed' => 1,
                ]);
                $foquzContact->save();
            }

            $customData = $foquzContact->fillData($contactData);

            if($poll->dont_send_if_passed) {

                $answer = FoquzPollAnswer::find()
                    ->where([
                        'foquz_poll_id' => $poll->id,
                        'contact_id' => $foquzContact->id
                    ])->one();

                //print_r($answer); exit;
                if($answer) {

                    if($email && $phone) {
                        $errorText = 'Опрос клиенту с номером телефона <b>'.$phone.'</b> и почтой <b>'.$email.'</b> уже был отправлен '.date('d.m.Y', strtotime($answer->created_at));
                    } elseif($email) {
                        $errorText = 'Опрос клиенту с почтой <b>'.$email.'</b> уже был отправлен '.date('d.m.Y', strtotime($answer->created_at));
                    } else {
                        $errorText = 'Опрос клиенту с номером телефона <b>'.$phone.'</b> уже был отправлен '.date('d.m.Y', strtotime($answer->created_at));
                    }
                    return $this->response(400, ['errors' => ['contact' => $errorText]]);
                }
            }

            if (!$foquzContact->is_subscribed) {
                return $this->response(400, ['errors' => ['contact' => "Клиент отписан от рассылок"]]);
            }


            if (!$channel) {
                $channel = $poll->getChannels()->one();
            }

            $answer = $channel->apiSend($foquzContact, $customData, $onlyLink, $filial);
            
            $link = \Yii::$app->params['protocol'].'://'.\Yii::$app->user->identity->company->alias.'/p/'.$answer['key'];
            $shortLink = FoquzPollShortLink::find()->where(['link' => $link])->one();
            if(!$shortLink) {
                $shortLink = FoquzPollShortLink::create($link);
            }
            $shortLink = Yii::$app->user->identity->company->shortLink.$shortLink->code;

            return $this->response(200, [
                'success' => true,
                'id' => md5($answer['id']),
                'link' => $link,
                'shortLink' => $shortLink,
            ]);
        }

        return $this->response(400, ["success"=>false, 'errors' => ['contact' => 'Для клиента нужно указать телефон или почту, чтобы отправить анкету опроса с ID='.$id]]);
    }

    public function actionSendPachca()
    {
        try {
            (new PollService(Yii::$app->request))->sendPachca();
        } catch (NotFoundHttpException $e) {
            return $this->response(404, ['success' => false, 'error' => $e->getMessage()]);
        } catch (ForbiddenHttpException $e) {
            return $this->response(403, ['success' => false, 'error' => $e->getMessage()]);
        } catch (BadRequestHttpException $e) {
            return $this->response(400, ['success' => false, 'error' => $e->getMessage()]);
        } catch (\Exception $e) {
            return $this->response(500, ['success' => false, 'error' => $e->getMessage()]);
        }

        return $this->response(200, ['success' => true, 'errors' => []]);

    }

    public function actionSendPachcaPrivate()
    {
        try {
            (new PollService(Yii::$app->request))->sendPachca(true);
        } catch (NotFoundHttpException $e) {
            return $this->response(404, ['success' => false, 'error' => $e->getMessage()]);
        } catch (ForbiddenHttpException $e) {
            return $this->response(403, ['success' => false, 'error' => $e->getMessage()]);
        } catch (BadRequestHttpException $e) {
            return $this->response(400, ['success' => false, 'error' => $e->getMessage()]);
        } catch (\Exception $e) {
            return $this->response(500, ['success' => false, 'error' => $e->getMessage()]);
        }

        return $this->response(200, ['success' => true, 'errors' => []]);

    }
}