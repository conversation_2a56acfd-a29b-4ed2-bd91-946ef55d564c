<?php

namespace app\modules\foquz\controllers\api\v1;
/**
 * @OA\OpenApi (
 *    @OA\Server(
 *        url="https://foquz.ru/",
 *        description="Production Server"
 *    ),
 *    @OA\Components(
 *        @OA\SecurityScheme(
 *            securityScheme="api_key",
 *            type="apiKey",
 *            in="query",
 *            name="access-token",
 *        ),
 *        @OA\Schema(
 *            schema="dictionary",
 *            type="object",
 *            @OA\Property(
 *                property="title",
 *                description="Название категории",
 *                nullable=false,
 *                type="string",
 *                example="Категория 1",
 *            ),
 *            @OA\Property(
 *                property="categories",
 *                description="Категории",
 *                nullable=false,
 *                type="object",
 *                ref="#/components/schemas/dictionary",
 *            ),
 *            @OA\Property(
 *                property="items",
 *                description="Выбранные элементы",
 *                nullable=false,
 *                type="object",
 *                @OA\Property(
 *                    property="title",
 *                    description="Название выбранного элемента",
 *                    nullable=false,
 *                    type="string",
 *                    example="Элемент 1",
 *                ),
 *            ),
 *        ),
 *        @OA\Schema(
 *            schema="Answer",
 *            type="object",
 *            @OA\Property(
 *                property="id",
 *                type="string",
 *                description="Уникальный идентификатор анкеты",
 *                example="c188e1f3868deb2120ca2d5a7001a257",
 *            ),
 *            @OA\Property(
 *                property="key",
 *                type="string",
 *                description="Уникальный ключ анкеты, который используется в URL уникальной ссылки на опрос",
 *                example="ba39f794528ef3ad05f746c8f8f2ace9",
 *            ),
 *            @OA\Property(
 *                property="poll_id",
 *                type="integer",
 *                description="ID опроса",
 *                example=12345,
 *            ),
 *            @OA\Property(
 *                property="poll_name",
 *                type="string",
 *                description="Название опроса",
 *                example="Опрос удовлетворённости клиентов",
 *            ),
 *            @OA\Property(
 *                property="filial_name",
 *                type="string",
 *                nullable=true,
 *                description="Название филиала, с которым связана анкета",
 *                example="Филиал Рязань",
 *            ),
 *            @OA\Property(
 *                property="poll_type",
 *                type="string",
 *                description="Тип опроса",
 *                example="Ручной",
 *                enum={"Ручной", "Автоматический"},
 *            ),
 *            @OA\Property(
 *                property="answer_date",
 *                type="string",
 *                description="Дата и время прохождения опроса (в формате DD.MM.YYYY hh:mm)",
 *                example="10.10.2022 11:55",
 *            ),
 *            @OA\Property(
 *                property="sent_date",
 *                type="string",
 *                description="Дата и время отправки опроса (в формате DD.MM.YYYY hh:mm)",
 *                example="01.10.2022 10:15",
 *            ),
 *            @OA\Property(
 *                property="ip",
 *                type="string",
 *                description="IP адрес респондента, проходившего опрос",
 *                example="************",
 *            ),
 *            @OA\Property(
 *                property="os",
 *                type="string",
 *                description="Операционная система респондента, проходившего опрос",
 *                example="Windows",
 *            ),
 *            @OA\Property(
 *                property="browser",
 *                type="string",
 *                description="Названия браузера, в котором проходили опрос",
 *                example="Opera *********",
 *            ),
 *            @OA\Property(
 *                property="device_type",
 *                type="string",
 *                description="Тип устройства, с которого проходили опрос (desktop - компьютер, mobile - мобильное устройство, tablet - планшет)",
 *                example="desktop",
 *                enum={"desktop", "mobile", "tablet"},
 *            ),
 *            @OA\Property(
 *                property="fio",
 *                type="string",
 *                nullable=true,
 *                description="ФИО клиента, который проходил опрос (если опрос анонимный - null)",
 *                example="Иванов Иван Иванович",
 *            ),
 *            @OA\Property(
 *                property="phone",
 *                type="string",
 *                nullable=true,
 *                description="Телефон клиента, который проходил опрос (если опрос анонимный - null)",
 *                example="+7 (*************",
 *            ),
 *            @OA\Property(
 *                 property="customer",
 *                 type="string",
 *                 nullable=true,
 *                 description="Внешний ID контакта (если опрос анонимный - null)",
 *                 example="df26202d-3fd1-4067-8463-337f3754aeb5",
 *             ),
 *            @OA\Property(
 *                property="email",
 *                type="string",
 *                nullable=true,
 *                description="Email клиента, который проходил опрос (если опрос анонимный - null)",
 *                example="<EMAIL>",
 *            ),
 *            @OA\Property(
 *                property="custom_fields",
 *                type="object",
 *                nullable=false,
 *                description="Произвольные поля-свойства анкет, переданные по API при создании анкеты",
 *                example={"var1": "value1", "var2": "value2"},
 *            ),
 *            @OA\Property(
 *                property="status",
 *                type="string",
 *                description="Статус прохождения опроса (new - анкета создана, email-open - клиент открыл письмо с ссылкой на опрос, open - клиент перешёл по ссылке на опрос, in-progress - клиент проходит опрос, done - клиент завершил опрос)",
 *                enum={"new", "email-open", "open", "in-progress", "done"},
 *                example="done",
 *            ),
 *            @OA\Property(
 *                property="processing_status",
 *                type="string",
 *                description="Статус обработки опроса",
 *                nullable=true,
 *                enum={"Новая", "В процессе", "Обрабатывается исполнителем", "Обработана", "Отложена"},
 *                example="В процессе",
 *            ),
 *            @OA\Property(
 *                property="language",
 *                type="string",
 *                description="Язык прохождения опроса",
 *                example="Русский",
 *            ),
 *            @OA\Property(
 *                property="user_agreement",
 *                type="boolean",
 *                description="Наличие согласия респондента на обработку персональных данных (true - согласие получено, false - согласие не получено)",
 *                example="true",
 *            ),
 *            @OA\Property(
 *                property="points",
 *                type="object",
 *                description="Баллы за опрос",
 *                @OA\Property(
 *                    property="value",
 *                    type="integer",
 *                    description="Количество баллов",
 *                    example=10,
 *                    nullable=true,
 *                ),
 *                @OA\Property(
 *                    property="max",
 *                    type="integer",
 *                    description="Максимальное количество баллов за ответ",
 *                    example=20,
 *                    nullable=true,
 *                ),
 *            ),
 *            @OA\Property(
 *                property="answers",
 *                type="array",
 *                description="Ответы пользователей",
 *                @OA\Items(oneOf={
 *                    @OA\Schema(
 *                        type="object",
 *                        title="Тип вопроса: Рейтинг",
 *                        @OA\Property(
 *                            property="service_name",
 *                            type="string",
 *                            nullable=true,
 *                            description="Служебное название вопроса",
 *                            example="Вопрос типа Рейтинг",
 *                        ),
 *                        @OA\Property(
 *                            property="type",
 *                            type="string",
 *                            description="Название типа вопроса",
 *                            example="Рейтинг",
 *                        ),
 *                        @OA\Property(
 *                            property="question_name",
 *                            type="string",
 *                            nullable=true,
 *                            description="Название вопроса",
 *                            example="Название вопроса",
 *                        ),
 *                        @OA\Property(
 *                            property="question_text",
 *                            type="string",
 *                            description="Текст вопроса",
 *                            example="Текст вопроса",
 *                        ),
 *                        @OA\Property(
 *                            property="extra_description",
 *                            type="string",
 *                            nullable=true,
 *                            description="Дополнительное описание вопроса",
 *                            example="Дополнительное описание",
 *                        ),
 *                        @OA\Property(
 *                            property="answer",
 *                            type="object",
 *                            nullable=true,
 *                            description="Оценка и ответ на уточняющий вопрос (если он задавался). В случае отсутствия ответа, возвращается **null**.",
 *                            @OA\Property(
 *                                property="value",
 *                                type="integer",
 *                                description="Оценка",
 *                                example=5,
 *                                nullable=true,
 *                            ),
 *                            @OA\Property(
 *                                property="clarifying_question",
 *                                type="object",
 *                                description="Уточняющий вопрос",
 *                                nullable=false,
 *                                @OA\Property(
 *                                    property="value",
 *                                    type="string",
 *                                    description="Ответ на уточняющий вопрос",
 *                                    example="Текст ответа",
 *                                    nullable=true,
 *                                ),
 *                            ),
 *                        ),
 *                        @OA\Property(
 *                            property="comment",
 *                            type="string",
 *                            description="Комментарий респондента",
 *                            nullable=true,
 *                            example="Текст комментария",
 *                        ),
 *                        @OA\Property(
 *                            property="dictionary_link",
 *                            type="object",
 *                            description="Элемент справочника, связанный с вопросом",
 *                            nullable=true,
 *                            @OA\Property(
 *                                property="id",
 *                                type="integer",
 *                                description="ID элемента справочника",
 *                                example=12345,
 *                            ),
 *                            @OA\Property(
 *                                property="title",
 *                                type="string",
 *                                description="Название элемента справочника",
 *                                example="Элемент справочника",
 *                            ),
 *                        ),
 *                        @OA\Property(
 *                            property="points",
 *                            type="object",
 *                            description="Баллы за вопрос",
 *                            nullable=true,
 *                            @OA\Property(
 *                                property="value",
 *                                type="integer",
 *                                description="Количество баллов",
 *                                example=10,
 *                                nullable=true,
 *                            ),
 *                            @OA\Property(
 *                                property="max",
 *                                type="integer",
 *                                description="Максимальное количество баллов за вопрос",
 *                                example=20,
 *                                nullable=true,
 *                            ),
 *                        ),
 *                        @OA\Property(
 *                            property="answer_tags",
 *                            type="array",
 *                            description="Теги анкеты",
 *                            @OA\Items(allOf={
 *                                @OA\Schema(
 *                                    type="string",
 *                                    description="тег",
 *                                    example="тег1",
 *                                 ),
 *                             }),
 *                        ),
 *                    ),
 *                    @OA\Schema(
 *                        type="object",
 *                        title="Тип вопроса: Звездный рейтинг",
 *                        @OA\Property(
 *                            property="service_name",
 *                            type="string",
 *                            nullable=true,
 *                            description="Служебное название вопроса",
 *                            example="Вопрос типа Звездный рейтинг",
 *                        ),
 *                        @OA\Property(
 *                            property="type",
 *                            type="string",
 *                            description="Название типа вопроса",
 *                            example="Звездный рейтинг",
 *                        ),
 *                        @OA\Property(
 *                            property="question_name",
 *                            type="string",
 *                            nullable=true,
 *                            description="Название вопроса",
 *                            example="Название вопроса",
 *                        ),
 *                        @OA\Property(
 *                            property="question_text",
 *                            type="string",
 *                            description="Текст вопроса",
 *                            example="Текст вопроса",
 *                        ),
 *                        @OA\Property(
 *                            property="extra_description",
 *                            type="string",
 *                            nullable=true,
 *                            description="Дополнительное описание вопроса",
 *                            example="Дополнительное описание",
 *                        ),
 *                        @OA\Property(
 *                            property="answer",
 *                            type="object",
 *                            description="Оценка и ответ на уточняющий вопрос (если он задавался). В случае отсутствия ответа, возвращается **null**.",
 *                            nullable=true,
 *                            @OA\Property(
 *                                property="value",
 *                                type="integer",
 *                                description="Оценка",
 *                                example=5,
 *                                nullable=true,
 *                            ),
 *                            @OA\Property(
 *                                property="clarifying_question",
 *                                type="object",
 *                                description="Уточняющий вопрос",
 *                                nullable=false,
 *                                @OA\Property(
 *                                    property="value",
 *                                    type="string",
 *                                    description="Ответ на уточняющий вопрос",
 *                                    example="Текст ответа",
 *                                    nullable=true,
 *                                ),
 *                            ),
 *                         ),
 *                        @OA\Property(
 *                            property="comment",
 *                            type="string",
 *                            description="Комментарий респондента",
 *                            nullable=true,
 *                            example="Текст комментария",
 *                        ),
 *                        @OA\Property(
 *                            property="dictionary_link",
 *                            type="object",
 *                            description="Элемент справочника, связанный с вопросом",
 *                            nullable=true,
 *                            @OA\Property(
 *                                property="id",
 *                                type="integer",
 *                                description="ID элемента справочника",
 *                                example=12345,
 *                            ),
 *                            @OA\Property(
 *                                property="title",
 *                                type="string",
 *                                description="Название элемента справочника",
 *                                example="Элемент справочника",
 *                            ),
 *                        ),
 *                        @OA\Property(
 *                            property="points",
 *                            type="object",
 *                            description="Баллы за вопрос",
 *                            nullable=true,
 *                            @OA\Property(
 *                                property="value",
 *                                type="integer",
 *                                description="Количество баллов",
 *                                example=10,
 *                                nullable=true,
 *                            ),
 *                            @OA\Property(
 *                                property="max",
 *                                type="integer",
 *                                description="Максимальное количество баллов за вопрос",
 *                                example=20,
 *                                nullable=true,
 *                            ),
 *                        ),
 *                    ),
 *                    @OA\Schema(
 *                        type="object",
 *                        title="Тип вопроса: Варианты ответов",
 *                        @OA\Property(
 *                            property="service_name",
 *                            type="string",
 *                            nullable=true,
 *                            description="Служебное название вопроса",
 *                            example="Вопрос типа Варианты ответов",
 *                        ),
 *                        @OA\Property(
 *                            property="type",
 *                            type="string",
 *                            description="Название типа вопроса",
 *                            example="Варианты ответов",
 *                        ),
 *                        @OA\Property(
 *                            property="question_name",
 *                            type="string",
 *                            nullable=true,
 *                            description="Название вопроса",
 *                            example="Название вопроса",
 *                        ),
 *                        @OA\Property(
 *                            property="question_text",
 *                            type="string",
 *                            description="Текст вопроса",
 *                            example="Текст вопроса",
 *                        ),
 *                        @OA\Property(
 *                            property="extra_description",
 *                            type="string",
 *                            nullable=true,
 *                            description="Дополнительное описание вопроса",
 *                            example="Дополнительное описание",
 *                        ),
 *                        @OA\Property(
 *                            property="answer",
 *                            type="array",
 *                            description="Выбранные варианты ответов. В случае отсутствия ответа, возвращается **null**.",
 *                            nullable=true,
 *                            @OA\Items(oneOf={
 *                                @OA\Schema(
 *                                    type="object",
 *                                    @OA\Property(
 *                                        property="title",
 *                                        type="string",
 *                                        description="Название варианта ответа",
 *                                        example="Вариант ответа",
 *                                    ),
 *                                    @OA\Property(
 *                                        property="dictionary_link",
 *                                        type="object",
 *                                        description="Элемент справочника, связанный с вариантом ответа",
 *                                        nullable=true,
 *                                        @OA\Property(
 *                                            property="id",
 *                                            type="integer",
 *                                            description="ID элемента справочника",
 *                                            example=12345,
 *                                        ),
 *                                        @OA\Property(
 *                                            property="title",
 *                                            type="string",
 *                                            description="Название элемента справочника",
 *                                            example="Элемент справочника",
 *                                        ),
 *                                    ),
 *                                    @OA\Property(
 *                                        property="file",
 *                                        type="object",
 *                                        description="Файл, связанный с вариантом ответа",
 *                                        nullable=true,
 *                                        @OA\Property(
 *                                            property="title",
 *                                            type="string",
 *                                            description="Название файла",
 *                                            example="Название файла",
 *                                        ),
 *                                        @OA\Property(
 *                                            property="url",
 *                                            type="string",
 *                                            description="Ссылка на файл",
 *                                            example="https://foquz.ru/uploads/file.jpg",
 *                                        ),
 *                                    ),
 *                                ),
 *                            }),
 *                        ),
 *                        @OA\Property(
 *                            property="comment",
 *                            type="string",
 *                            description="Комментарий респондента",
 *                            nullable=true,
 *                            example="Текст комментария",
 *                        ),
 *                        @OA\Property(
 *                            property="dictionary_link",
 *                            type="object",
 *                            description="Элемент справочника, связанный с вопросом",
 *                            nullable=true,
 *                            @OA\Property(
 *                                property="id",
 *                                type="integer",
 *                                description="ID элемента справочника",
 *                                example=12345,
 *                            ),
 *                            @OA\Property(
 *                                property="title",
 *                                type="string",
 *                                description="Название элемента справочника",
 *                                example="Элемент справочника",
 *                            ),
 *                        ),
 *                        @OA\Property(
 *                            property="points",
 *                            type="object",
 *                            description="Баллы за вопрос",
 *                            nullable=true,
 *                            @OA\Property(
 *                                property="value",
 *                                type="integer",
 *                                description="Количество баллов",
 *                                example=10,
 *                                nullable=true,
 *                            ),
 *                            @OA\Property(
 *                                property="max",
 *                                type="integer",
 *                                description="Максимальное количество баллов за вопрос",
 *                                example=20,
 *                                nullable=true,
 *                            ),
 *                        ),
 *                    ),
 *                    @OA\Schema(
 *                        type="object",
 *                        title="Тип вопроса: Текстовый ответ",
 *                        @OA\Property(
 *                            property="service_name",
 *                            type="string",
 *                            nullable=true,
 *                            description="Служебное название вопроса",
 *                            example="Вопрос типа Текстовый ответ",
 *                        ),
 *                        @OA\Property(
 *                            property="type",
 *                            type="string",
 *                            description="Название типа вопроса",
 *                            example="Текстовый ответ",
 *                        ),
 *                        @OA\Property(
 *                            property="question_name",
 *                            type="string",
 *                            nullable=true,
 *                            description="Название вопроса",
 *                            example="Название вопроса",
 *                        ),
 *                        @OA\Property(
 *                            property="question_text",
 *                            type="string",
 *                            description="Текст вопроса",
 *                            example="Текст вопроса",
 *                        ),
 *                        @OA\Property(
 *                            property="extra_description",
 *                            type="string",
 *                            nullable=true,
 *                            description="Дополнительное описание вопроса",
 *                            example="Дополнительное описание",
 *                        ),
 *                        @OA\Property(
 *                            property="answer",
 *                            type="object",
 *                            description="Текст ответа. В случае отсутствия ответа, возвращается **null**.",
 *                            nullable=true,
 *                            @OA\Property(
 *                                property="value",
 *                                anyOf={
 *                                    @OA\Schema(
 *                                        type="string",
 *                                        description="Ответ респондента (для всех типов полей, кроме ФИО)",
 *                                        example="Ответ",
 *                                    ),
 *                                    @OA\Schema(
 *                                        type="object",
 *                                        description="Ответ респондента (для поля типа ФИО)",
 *                                        @OA\Property(
 *                                            property="last_name",
 *                                            type="string",
 *                                            description="Фамилия",
 *                                            example="Иванов",
 *                                        ),
 *                                        @OA\Property(
 *                                            property="first_name",
 *                                            type="string",
 *                                            description="Имя",
 *                                            example="Иван",
 *                                        ),
 *                                        @OA\Property(
 *                                            property="patronymic",
 *                                            type="string",
 *                                            description="Отчество",
 *                                            example="Иванович",
 *                                        ),
 *                                    ),
 *                                },
 *                            ),
 *                        ),
 *                        @OA\Property(
 *                            property="comment",
 *                            type="string",
 *                            description="Комментарий респондента (для этого типа вопроса всегда null)",
 *                            nullable=true,
 *                            example=null,
 *                        ),
 *                        @OA\Property(
 *                            property="dictionary_link",
 *                            type="object",
 *                            description="Элемент справочника, связанный с вопросом",
 *                            nullable=true,
 *                            @OA\Property(
 *                                property="id",
 *                                type="integer",
 *                                description="ID элемента справочника",
 *                                example=12345,
 *                            ),
 *                            @OA\Property(
 *                                property="title",
 *                                type="string",
 *                                description="Название элемента справочника",
 *                                example="Элемент справочника",
 *                            ),
 *                        ),
 *                        @OA\Property(
 *                            property="points",
 *                            type="object",
 *                            description="Баллы за вопрос",
 *                            nullable=true,
 *                            @OA\Property(
 *                                property="value",
 *                                type="integer",
 *                                description="Количество баллов",
 *                                example=10,
 *                                nullable=true,
 *                            ),
 *                            @OA\Property(
 *                                property="max",
 *                                type="integer",
 *                                description="Максимальное количество баллов за вопрос",
 *                                example=20,
 *                                nullable=true,
 *                            ),
 *                        ),
 *                    ),
 *                    @OA\Schema(
 *                        type="object",
 *                        title="Тип вопроса: Дата/время",
 *                        @OA\Property(
 *                            property="service_name",
 *                            type="string",
 *                            nullable=true,
 *                            description="Служебное название вопроса",
 *                            example="Вопрос типа Дата/время",
 *                        ),
 *                        @OA\Property(
 *                            property="type",
 *                            type="string",
 *                            description="Название типа вопроса",
 *                            example="Дата/время",
 *                        ),
 *                        @OA\Property(
 *                            property="question_name",
 *                            type="string",
 *                            nullable=true,
 *                            description="Название вопроса",
 *                            example="Название вопроса",
 *                        ),
 *                        @OA\Property(
 *                            property="question_text",
 *                            type="string",
 *                            description="Текст вопроса",
 *                            example="Текст вопроса",
 *                        ),
 *                        @OA\Property(
 *                            property="extra_description",
 *                            type="string",
 *                            nullable=true,
 *                            description="Дополнительное описание вопроса",
 *                            example="Дополнительное описание",
 *                        ),
 *                        @OA\Property(
 *                            property="answer",
 *                            type="string",
 *                            description="Выбранные дата и время. В случае отсутствия ответа, возвращается **null**.",
 *                            nullable=true,
 *                            oneOf={
 *                                @OA\Schema(
 *                                    type="string",
 *                                    example="01.01.2022",
 *                                    description="Только дата (в формате DD.MM.YYYY)",
 *                                ),
 *                                @OA\Schema(
 *                                    type="string",
 *                                    example="01.01",
 *                                    description="Только дата (день и месяц) (в формате DD.MM)",
 *                                ),
 *                                @OA\Schema(
 *                                    type="string",
 *                                    example="15:30",
 *                                    description="Только время (в формате hh:mm)",
 *                                ),
 *                                @OA\Schema(
 *                                    type="string",
 *                                    example="31.12.2023 15:00",
 *                                    description="Дата и время (в формате DD.MM.YYYY hh:mm)",
 *                                ),
 *                                @OA\Schema(
 *                                    type="string",
 *                                    example="31.12 15:00",
 *                                    description="Дата и время (день и месяц) (в формате DD.MM hh:mm)",
 *                                ),
 *                            }
 *                        ),
 *                        @OA\Property(
 *                            property="comment",
 *                            type="string",
 *                            description="Комментарий респондента (для этого типа вопроса всегда null)",
 *                            nullable=true,
 *                            example=null,
 *                        ),
 *                        @OA\Property(
 *                            property="dictionary_link",
 *                            type="object",
 *                            description="Элемент справочника, связанный с вопросом",
 *                            nullable=true,
 *                            @OA\Property(
 *                                property="id",
 *                                type="integer",
 *                                description="ID элемента справочника",
 *                                example=12345,
 *                            ),
 *                            @OA\Property(
 *                                property="title",
 *                                type="string",
 *                                description="Название элемента справочника",
 *                                example="Элемент справочника",
 *                            ),
 *                        ),
 *                        @OA\Property(
 *                            property="points",
 *                            type="object",
 *                            description="Баллы за вопрос",
 *                            nullable=true,
 *                            @OA\Property(
 *                                property="value",
 *                                type="integer",
 *                                description="Количество баллов",
 *                                example=10,
 *                                nullable=true,
 *                            ),
 *                            @OA\Property(
 *                                property="max",
 *                                type="integer",
 *                                description="Максимальное количество баллов за вопрос",
 *                                example=20,
 *                                nullable=true,
 *                            ),
 *                        ),
 *                    ),
 *                    @OA\Schema(
 *                        type="object",
 *                        title="Тип вопроса: Адрес",
 *                        @OA\Property(
 *                            property="service_name",
 *                            type="string",
 *                            nullable=true,
 *                            description="Служебное название вопроса",
 *                            example="Вопрос типа Адрес",
 *                        ),
 *                        @OA\Property(
 *                            property="type",
 *                            type="string",
 *                            description="Название типа вопроса",
 *                            example="Адрес",
 *                        ),
 *                        @OA\Property(
 *                            property="question_name",
 *                            type="string",
 *                            nullable=true,
 *                            description="Название вопроса",
 *                            example="Название вопроса",
 *                        ),
 *                        @OA\Property(
 *                            property="question_text",
 *                            type="string",
 *                            description="Текст вопроса",
 *                            example="Текст вопроса",
 *                        ),
 *                        @OA\Property(
 *                            property="extra_description",
 *                            type="string",
 *                            nullable=true,
 *                            description="Дополнительное описание вопроса",
 *                            example="Дополнительное описание",
 *                        ),
 *                        @OA\Property(
 *                            property="answer",
 *                            type="string",
 *                            description="Адрес. В случае отсутствия ответа, возвращается **null**.",
 *                            nullable=true,
 *                            example="Москва Город, Город Москва, Улица Адмирала Руднева, 4",
 *                        ),
 *                        @OA\Property(
 *                             property="comment",
 *                             type="string",
 *                             description="Комментарий респондента",
 *                             nullable=true,
 *                             example="Текст комментария",
 *                         ),
 *                        @OA\Property(
 *                            property="dictionary_link",
 *                            type="object",
 *                            description="Элемент справочника, связанный с вопросом",
 *                            nullable=true,
 *                            @OA\Property(
 *                                property="id",
 *                                type="integer",
 *                                description="ID элемента справочника",
 *                                example=12345,
 *                            ),
 *                            @OA\Property(
 *                                property="title",
 *                                type="string",
 *                                description="Название элемента справочника",
 *                                example="Элемент справочника",
 *                            ),
 *                        ),
 *                        @OA\Property(
 *                            property="points",
 *                            type="object",
 *                            description="Баллы за вопрос",
 *                            nullable=true,
 *                            @OA\Property(
 *                                property="value",
 *                                type="integer",
 *                                description="Количество баллов",
 *                                example=10,
 *                                nullable=true,
 *                            ),
 *                            @OA\Property(
 *                                property="max",
 *                                type="integer",
 *                                description="Максимальное количество баллов за вопрос",
 *                                example=20,
 *                                nullable=true,
 *                            ),
 *                        ),
 *                    ),
 *                    @OA\Schema(
 *                         type="object",
 *                         title="Тип вопроса: Загрузка файла",
 *                         @OA\Property(
 *                             property="service_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Служебное название вопроса",
 *                             example="Вопрос типа Загрузка файла",
 *                         ),
 *                         @OA\Property(
 *                             property="type",
 *                             type="string",
 *                             description="Название типа вопроса",
 *                             example="Загрузка файла",
 *                         ),
 *                         @OA\Property(
 *                             property="question_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Название вопроса",
 *                             example="Название вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="question_text",
 *                             type="string",
 *                             description="Текст вопроса",
 *                             example="Текст вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="extra_description",
 *                             type="string",
 *                             nullable=true,
 *                             description="Дополнительное описание вопроса",
 *                             example="Дополнительное описание",
 *                         ),
 *                         @OA\Property(
 *                             property="answer",
 *                             type="object",
 *                             description="Загруженные файлы. В случае отсутствия ответа, возвращается **null**.",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="count",
 *                                 type="integer",
 *                                 description="Количество загруженных файлов",
 *                                 example=2,
 *                             ),
 *                             @OA\Property(
 *                                 property="items",
 *                                 type="array",
 *                                 description="Список загруженных файлов",
 *                                 @OA\Items(
 *                                     type="object",
 *                                     @OA\Property(
 *                                         property="name",
 *                                         type="string",
 *                                         description="Имя файла",
 *                                         example="file1.jpg",
 *                                     ),
 *                                     @OA\Property(
 *                                         property="size",
 *                                         type="float",
 *                                         description="Размер файла (в килобайтах)",
 *                                         example=1024.51,
 *                                     ),
 *                                     @OA\Property(
 *                                         property="url",
 *                                         type="string",
 *                                         description="Ссылка на файл",
 *                                         example="https://foquz.ru/uploads/foquz/answer/207593/64edd01e30ae4.jpeg",
 *                                     ),
 *                                 ),
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="comment",
 *                             type="string",
 *                             description="Комментарий респондента (для этого типа вопроса всегда null)",
 *                             nullable=true,
 *                             example=null,
 *                         ),
 *                         @OA\Property(
 *                             property="dictionary_link",
 *                             type="object",
 *                             description="Элемент справочника, связанный с вопросом",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="id",
 *                                 type="integer",
 *                                 description="ID элемента справочника",
 *                                 example=12345,
 *                             ),
 *                             @OA\Property(
 *                                 property="title",
 *                                 type="string",
 *                                 description="Название элемента справочника",
 *                                 example="Элемент справочника",
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="points",
 *                             type="object",
 *                             description="Баллы за вопрос",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="value",
 *                                 type="integer",
 *                                 description="Количество баллов",
 *                                 example=10,
 *                                 nullable=true,
 *                             ),
 *                             @OA\Property(
 *                                 property="max",
 *                                 type="integer",
 *                                 description="Максимальное количество баллов за вопрос",
 *                                 example=20,
 *                                 nullable=true,
 *                             ),
 *                         ),
 *                     ),
 *                     @OA\Schema(
 *                         type="object",
 *                         title="Тип вопроса: Шкала (Стандартная)",
 *                         @OA\Property(
 *                             property="service_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Служебное название вопроса",
 *                             example="Вопрос типа Шкала",
 *                         ),
 *                         @OA\Property(
 *                             property="type",
 *                             type="string",
 *                             description="Название типа вопроса",
 *                             example="Шкала",
 *                         ),
 *                         @OA\Property(
 *                             property="question_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Название вопроса",
 *                             example="Название вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="question_text",
 *                             type="string",
 *                             description="Текст вопроса",
 *                             example="Текст вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="extra_description",
 *                             type="string",
 *                             nullable=true,
 *                             description="Дополнительное описание вопроса",
 *                             example="Дополнительное описание",
 *                         ),
 *                         @OA\Property(
 *                             property="answer",
 *                             type="object",
 *                             description="Ответ респондента. В случае отсутствия ответа, возвращается **null**.",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="value",
 *                                 type="integer",
 *                                 description="Оценка респондента",
 *                                 example=5,
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="comment",
 *                             type="string",
 *                             description="Комментарий респондента",
 *                             nullable=true,
 *                             example="Текст комментария",
 *                         ),
 *                         @OA\Property(
 *                             property="dictionary_link",
 *                             type="object",
 *                             description="Элемент справочника, связанный с вопросом",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="id",
 *                                 type="integer",
 *                                 description="ID элемента справочника",
 *                                 example=12345,
 *                             ),
 *                             @OA\Property(
 *                                 property="title",
 *                                 type="string",
 *                                 description="Название элемента справочника",
 *                                 example="Элемент справочника",
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="points",
 *                             type="object",
 *                             description="Баллы за вопрос",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="value",
 *                                 type="integer",
 *                                 description="Количество баллов",
 *                                 example=10,
 *                                 nullable=true,
 *                             ),
 *                             @OA\Property(
 *                                 property="max",
 *                                 type="integer",
 *                                 description="Максимальное количество баллов за вопрос",
 *                                 example=20,
 *                                 nullable=true,
 *                             ),
 *                         ),
 *                     ),
 *                     @OA\Schema(
 *                         type="object",
 *                         title="Тип вопроса: Шкала (для вариантов)",
 *                         @OA\Property(
 *                             property="service_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Служебное название вопроса",
 *                             example="Вопрос типа Шкала",
 *                         ),
 *                         @OA\Property(
 *                             property="type",
 *                             type="string",
 *                             description="Название типа вопроса",
 *                             example="Шкала",
 *                         ),
 *                         @OA\Property(
 *                             property="question_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Название вопроса",
 *                             example="Название вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="question_text",
 *                             type="string",
 *                             description="Текст вопроса",
 *                             example="Текст вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="extra_description",
 *                             type="string",
 *                             nullable=true,
 *                             description="Дополнительное описание вопроса",
 *                             example="Дополнительное описание",
 *                         ),
 *                         @OA\Property(
 *                             property="answer",
 *                             type="array",
 *                             description="Оценки респондента. В случае отсутствия ответа, возвращается **null**.",
 *                             nullable=true,
 *                             @OA\Items(oneOf={
 *                                 @OA\Schema(
 *                                     type="object",
 *                                     @OA\Property(
 *                                         property="name",
 *                                         type="string",
 *                                         description="Название варианта",
 *                                         example="Вариант 1",
 *                                      ),
 *                                     @OA\Property(
 *                                         property="value",
 *                                         type="integer",
 *                                         description="Оценка респондента",
 *                                         example=5,
 *                                      ),
 *                                 ),
 *                             }),
 *                         ),
 *                         @OA\Property(
 *                             property="comment",
 *                             type="string",
 *                             description="Комментарий респондента",
 *                             nullable=true,
 *                             example="Текст комментария",
 *                         ),
 *                         @OA\Property(
 *                             property="dictionary_link",
 *                             type="object",
 *                             description="Элемент справочника, связанный с вопросом",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="id",
 *                                 type="integer",
 *                                 description="ID элемента справочника",
 *                                 example=12345,
 *                             ),
 *                             @OA\Property(
 *                                 property="title",
 *                                 type="string",
 *                                 description="Название элемента справочника",
 *                                 example="Элемент справочника",
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="points",
 *                             type="object",
 *                             description="Баллы за вопрос",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="value",
 *                                 type="integer",
 *                                 description="Количество баллов",
 *                                 example=10,
 *                                 nullable=true,
 *                             ),
 *                             @OA\Property(
 *                                 property="max",
 *                                 type="integer",
 *                                 description="Максимальное количество баллов за вопрос",
 *                                 example=20,
 *                                 nullable=true,
 *                             ),
 *                         ),
 *                     ),
 *                     @OA\Schema(
 *                         type="object",
 *                         title="Тип вопроса: Смайл рейтинг",
 *                         @OA\Property(
 *                             property="service_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Служебное название вопроса",
 *                             example="Вопрос типа Смайл рейтинг",
 *                         ),
 *                         @OA\Property(
 *                             property="type",
 *                             type="string",
 *                             description="Название типа вопроса",
 *                             example="Смайл рейтинг, робот",
 *                             enum={
 *                                 "Смайл рейтинг, сердце",
 *                                 "Смайл рейтинг, лайк",
 *                                 "Смайл рейтинг, лицо",
 *                                 "Смайл рейтинг, лицо (жёлтый фон)",
 *                                 "Смайл рейтинг, робот",
 *                                 "Смайл рейтинг, эмодзи",
 *                                 "Смайл рейтинг, погода",
 *                                 "Смайл рейтинг, кастом",
 *                             },
 *                         ),
 *                         @OA\Property(
 *                             property="question_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Название вопроса",
 *                             example="Название вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="question_text",
 *                             type="string",
 *                             description="Текст вопроса",
 *                             example="Текст вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="extra_description",
 *                             type="string",
 *                             nullable=true,
 *                             description="Дополнительное описание вопроса",
 *                             example="Дополнительное описание",
 *                         ),
 *                         @OA\Property(
 *                             property="answer",
 *                             type="object",
 *                             description="Оценка респондента. В случае отсутствия ответа, возвращается **null**.",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="value",
 *                                 type="integer",
 *                                 description="Оценка респондента",
 *                                 example=5,
 *                                 nullable=true,
 *                             ),
 *                             @OA\Property(
 *                                 property="max",
 *                                 type="integer",
 *                                 description="Максимальная оценка",
 *                                 example=10,
 *                                 nullable=true,
 *                             ),
 *                             @OA\Property(
 *                                 property="mark",
 *                                 type="string",
 *                                 description="Метка смайла",
 *                                 example="smile",
 *                                 nullable=true,
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="comment",
 *                             type="string",
 *                             description="Комментарий респондента",
 *                             nullable=true,
 *                             example="Текст комментария",
 *                         ),
 *                         @OA\Property(
 *                             property="dictionary_link",
 *                             type="object",
 *                             description="Элемент справочника, связанный с вопросом",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="id",
 *                                 type="integer",
 *                                 description="ID элемента справочника",
 *                                 example=12345,
 *                             ),
 *                             @OA\Property(
 *                                 property="title",
 *                                 type="string",
 *                                 description="Название элемента справочника",
 *                                 example="Элемент справочника",
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="points",
 *                             type="object",
 *                             description="Баллы за вопрос",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="value",
 *                                 type="integer",
 *                                 description="Количество баллов",
 *                                 example=10,
 *                                 nullable=true,
 *                             ),
 *                             @OA\Property(
 *                                 property="max",
 *                                 type="integer",
 *                                 description="Максимальное количество баллов за вопрос",
 *                                 example=20,
 *                                 nullable=true,
 *                             ),
 *                         ),
 *                     ),
 *                     @OA\Schema(
 *                         type="object",
 *                         title="Тип вопроса: Рейтинг NPS (cтандартный)",
 *                         @OA\Property(
 *                             property="service_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Служебное название вопроса",
 *                             example="Вопрос типа Рейтинг NPS",
 *                         ),
 *                         @OA\Property(
 *                             property="type",
 *                             type="string",
 *                             description="Название типа вопроса",
 *                             example="Рейтинг NPS",
 *                         ),
 *                         @OA\Property(
 *                             property="question_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Название вопроса",
 *                             example="Название вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="question_text",
 *                             type="string",
 *                             description="Текст вопроса",
 *                             example="Текст вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="extra_description",
 *                             type="string",
 *                             nullable=true,
 *                             description="Дополнительное описание вопроса",
 *                             example="Дополнительное описание",
 *                         ),
 *                         @OA\Property(
 *                             property="answer",
 *                             type="object",
 *                             description="Оценка респондента. В случае отсутствия ответа, возвращается **null**.",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="value",
 *                                 type="integer",
 *                                 description="Оценка респондента",
 *                                 example=5,
 *                                 nullable=true,
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="comment",
 *                             type="string",
 *                             description="Комментарий респондента",
 *                             nullable=true,
 *                             example="Текст комментария",
 *                         ),
 *                         @OA\Property(
 *                             property="dictionary_link",
 *                             type="object",
 *                             description="Элемент справочника, связанный с вопросом",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="id",
 *                                 type="integer",
 *                                 description="ID элемента справочника",
 *                                 example=12345,
 *                             ),
 *                             @OA\Property(
 *                                 property="title",
 *                                 type="string",
 *                                 description="Название элемента справочника",
 *                                 example="Элемент справочника",
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="points",
 *                             type="object",
 *                             description="Баллы за вопрос",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="value",
 *                                 type="integer",
 *                                 description="Количество баллов",
 *                                 example=10,
 *                                 nullable=true,
 *                             ),
 *                             @OA\Property(
 *                                 property="max",
 *                                 type="integer",
 *                                 description="Максимальное количество баллов за вопрос",
 *                                 example=20,
 *                                 nullable=true,
 *                             ),
 *                         ),
 *                     ),
 *                     @OA\Schema(
 *                         type="object",
 *                         title="Тип вопроса: Рейтинг NPS (для вариантов)",
 *                         @OA\Property(
 *                             property="service_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Служебное название вопроса",
 *                             example="Вопрос типа Рейтинг NPS",
 *                         ),
 *                         @OA\Property(
 *                             property="type",
 *                             type="string",
 *                             description="Название типа вопроса",
 *                             example="Рейтинг NPS",
 *                         ),
 *                         @OA\Property(
 *                             property="question_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Название вопроса",
 *                             example="Название вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="question_text",
 *                             type="string",
 *                             description="Текст вопроса",
 *                             example="Текст вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="extra_description",
 *                             type="string",
 *                             nullable=true,
 *                             description="Дополнительное описание вопроса",
 *                             example="Дополнительное описание",
 *                         ),
 *                         @OA\Property(
 *                             property="answer",
 *                             type="array",
 *                             description="Оценки респондента. В случае отсутствия ответа, возвращается **null**.",
 *                             nullable=true,
 *                             @OA\Items(oneOf={
 *                                 @OA\Schema(
 *                                     type="object",
 *                                     @OA\Property(
 *                                         property="name",
 *                                         type="string",
 *                                         description="Название варианта",
 *                                         example="Вариант 1",
 *                                     ),
 *                                     @OA\Property(
 *                                         property="value",
 *                                         type="string",
 *                                         description="Оценка респондента",
 *                                         example="5",
 *                                     ),
 *                                     @OA\Property(
 *                                         property="dictionary_link",
 *                                         type="object",
 *                                         description="Элемент справочника, связанный с вариантом ответа",
 *                                         nullable=true,
 *                                         @OA\Property(
 *                                             property="id",
 *                                             type="integer",
 *                                             description="ID элемента справочника",
 *                                             example=12345,
 *                                         ),
 *                                         @OA\Property(
 *                                             property="title",
 *                                             type="string",
 *                                             description="Название элемента справочника",
 *                                             example="Элемент справочника",
 *                                         ),
 *                                     ),
 *                                 ),
 *                             }),
 *                         ),
 *                         @OA\Property(
 *                             property="comment",
 *                             type="string",
 *                             description="Комментарий респондента",
 *                             nullable=true,
 *                             example="Текст комментария",
 *                         ),
 *                         @OA\Property(
 *                             property="dictionary_link",
 *                             type="object",
 *                             description="Элемент справочника, связанный с вопросом",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="id",
 *                                 type="integer",
 *                                 description="ID элемента справочника",
 *                                 example=12345,
 *                             ),
 *                             @OA\Property(
 *                                 property="title",
 *                                 type="string",
 *                                 description="Название элемента справочника",
 *                                 example="Элемент справочника",
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="points",
 *                             type="object",
 *                             description="Баллы за вопрос",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="value",
 *                                 type="integer",
 *                                 description="Количество баллов",
 *                                 example=10,
 *                                 nullable=true,
 *                             ),
 *                             @OA\Property(
 *                                 property="max",
 *                                 type="integer",
 *                                 description="Максимальное количество баллов за вопрос",
 *                                 example=20,
 *                                 nullable=true,
 *                             ),
 *                         ),
 *                     ),
 *                     @OA\Schema(
 *                         type="object",
 *                         title="Тип вопроса: Простая матрица",
 *                         @OA\Property(
 *                             property="service_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Служебное название вопроса",
 *                             example="Вопрос типа Простая матрица",
 *                         ),
 *                         @OA\Property(
 *                             property="type",
 *                             type="string",
 *                             description="Название типа вопроса",
 *                             example="Простая матрица",
 *                         ),
 *                         @OA\Property(
 *                             property="question_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Название вопроса",
 *                             example="Название вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="question_text",
 *                             type="string",
 *                             description="Текст вопроса",
 *                             example="Текст вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="extra_description",
 *                             type="string",
 *                             nullable=true,
 *                             description="Дополнительное описание вопроса",
 *                             example="Дополнительное описание",
 *                         ),
 *                         @OA\Property(
 *                             property="answer",
 *                             type="array",
 *                             description="Выбранные столбцы для каждой строки. В случае отсутствия ответа, возвращается **null**.",
 *                             nullable=true,
 *                             @OA\Items(oneOf={
 *                                 @OA\Schema(
 *                                     type="object",
 *                                     @OA\Property(
 *                                         property="title",
 *                                         type="string",
 *                                         description="Название строки",
 *                                         example="Строка 1",
 *                                     ),
 *                                     @OA\Property(
 *                                         property="values",
 *                                         type="array",
 *                                         description="Выбранные значения в столбцах",
 *                                         @OA\Items(
 *                                             type="string",
 *                                             description="Название столбца",
 *                                             example="Столбец 1",
 *                                         ),
 *                                     ),
 *                                     @OA\Property(
 *                                         property="clarifying_question",
 *                                         type="object",
 *                                         description="Уточняющий вопрос",
 *                                         nullable=false,
 *                                         @OA\Property(
 *                                             property="value",
 *                                             type="string",
 *                                             description="Ответ на уточняющий вопрос",
 *                                             example="Ответ на уточняющий вопрос",
 *                                         ),
 *                                     ),
 *                                     @OA\Property(
 *                                         property="dictionary_link",
 *                                         type="object",
 *                                         description="Элемент справочника, связанный со строкой",
 *                                         nullable=true,
 *                                         @OA\Property(
 *                                             property="id",
 *                                             type="integer",
 *                                             description="ID элемента справочника",
 *                                             example=12345,
 *                                         ),
 *                                         @OA\Property(
 *                                             property="title",
 *                                             type="string",
 *                                             description="Название элемента справочника",
 *                                             example="Элемент справочника",
 *                                         ),
 *                                     ),
 *                                 ),
 *                             }),
 *                         ),
 *                         @OA\Property(
 *                             property="comment",
 *                             type="string",
 *                             description="Комментарий респондента",
 *                             nullable=true,
 *                             example="Текст комментария",
 *                         ),
 *                         @OA\Property(
 *                             property="dictionary_link",
 *                             type="object",
 *                             description="Элемент справочника, связанный с вопросом",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="id",
 *                                 type="integer",
 *                                 description="ID элемента справочника",
 *                                 example=12345,
 *                             ),
 *                             @OA\Property(
 *                                 property="title",
 *                                 type="string",
 *                                 description="Название элемента справочника",
 *                                 example="Элемент справочника",
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="points",
 *                             type="object",
 *                             description="Баллы за вопрос",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="value",
 *                                 type="integer",
 *                                 description="Количество баллов",
 *                                 example=10,
 *                                 nullable=true,
 *                             ),
 *                             @OA\Property(
 *                                 property="max",
 *                                 type="integer",
 *                                 description="Максимальное количество баллов за вопрос",
 *                                 example=20,
 *                                 nullable=true,
 *                             ),
 *                         ),
 *                     ),
 *                     @OA\Schema(
 *                         type="object",
 *                         title="Тип вопроса: Семантический дифференциал",
 *                         @OA\Property(
 *                             property="service_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Служебное название вопроса",
 *                             example="Вопрос типа Семантический дифференциал",
 *                         ),
 *                         @OA\Property(
 *                             property="type",
 *                             type="string",
 *                             description="Название типа вопроса",
 *                             example="Семантический дифференциал",
 *                         ),
 *                         @OA\Property(
 *                             property="question_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Название вопроса",
 *                             example="Название вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="question_text",
 *                             type="string",
 *                             description="Текст вопроса",
 *                             example="Текст вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="extra_description",
 *                             type="string",
 *                             nullable=true,
 *                             description="Дополнительное описание вопроса",
 *                             example="Дополнительное описание",
 *                         ),
 *                         @OA\Property(
 *                             property="answer",
 *                             type="array",
 *                             description="Оценка респондента для каждого ряда дифференциала. В случае отсутствия ответа, возвращается **null**.",
 *                             nullable=true,
 *                             @OA\Items(oneOf={
 *                                 @OA\Schema(
 *                                     type="object",
 *                                     @OA\Property(
 *                                         property="start_label",
 *                                         type="string",
 *                                         description="Метка начальной точки",
 *                                         example="Неприятный",
 *                                         nullable=true,
 *                                     ),
 *                                     @OA\Property(
 *                                         property="end_label",
 *                                         type="string",
 *                                         description="Метка конечной точки",
 *                                         example="Приятный",
 *                                         nullable=true,
 *                                     ),
 *                                     @OA\Property(
 *                                         property="value",
 *                                         type="integer",
 *                                         description="Оценка респондента",
 *                                         example=5,
 *                                         nullable=true,
 *                                     ),
 *                                 ),
 *                             }),
 *                         ),
 *                         @OA\Property(
 *                             property="comment",
 *                             type="string",
 *                             description="Комментарий респондента",
 *                             nullable=true,
 *                             example="Текст комментария",
 *                         ),
 *                         @OA\Property(
 *                             property="dictionary_link",
 *                             type="object",
 *                             description="Элемент справочника, связанный с вопросом",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="id",
 *                                 type="integer",
 *                                 description="ID элемента справочника",
 *                                 example=12345,
 *                             ),
 *                             @OA\Property(
 *                                 property="title",
 *                                 type="string",
 *                                 description="Название элемента справочника",
 *                                 example="Элемент справочника",
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="points",
 *                             type="object",
 *                             description="Баллы за вопрос",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="value",
 *                                 type="integer",
 *                                 description="Количество баллов",
 *                                 example=10,
 *                                 nullable=true,
 *                             ),
 *                             @OA\Property(
 *                                 property="max",
 *                                 type="integer",
 *                                 description="Максимальное количество баллов за вопрос",
 *                                 example=20,
 *                                 nullable=true,
 *                             ),
 *                         ),
 *                     ),
 *                     @OA\Schema(
 *                         type="object",
 *                         title="Тип вопроса: Выбор изображения/видео",
 *                         @OA\Property(
 *                             property="service_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Служебное название вопроса",
 *                             example="Вопрос типа Выбор изображения/видео",
 *                         ),
 *                         @OA\Property(
 *                             property="type",
 *                             type="string",
 *                             description="Название типа вопроса",
 *                             example="Выбор изображения/видео",
 *                         ),
 *                         @OA\Property(
 *                             property="question_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Название вопроса",
 *                             example="Название вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="question_text",
 *                             type="string",
 *                             description="Текст вопроса",
 *                             example="Текст вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="extra_description",
 *                             type="string",
 *                             nullable=true,
 *                             description="Дополнительное описание вопроса",
 *                             example="Дополнительное описание",
 *                         ),
 *                         @OA\Property(
 *                             property="answer",
 *                             type="array",
 *                             description="Выбранные изображения/видео. В случае отсутствия ответа, возвращается **null**.",
 *                             nullable=true,
 *                             @OA\Items(oneOf={
 *                                 @OA\Schema(
 *                                     type="object",
 *                                     description="Выбранное изображение/видео",
 *                                     @OA\Property(
 *                                         property="title",
 *                                         type="string",
 *                                         description="Название изображения/видео, выбранного пользователем. В случае отсутствия названия - номер выбранного изображения/видео",
 *                                         example="Изображение 1",
 *                                     ),
 *                                     @OA\Property(
 *                                         property="url",
 *                                         type="string",
 *                                         description="Ссылка на изображение/видео",
 *                                         example="https://foquz.ru/uploads/foquz/86103/64e7a7b46204d.jpeg",
 *                                     ),
 *                                 ),
 *                             }),
 *                         ),
 *                         @OA\Property(
 *                             property="comment",
 *                             type="string",
 *                             description="Комментарий респондента",
 *                             nullable=true,
 *                             example="Текст комментария",
 *                         ),
 *                         @OA\Property(
 *                             property="dictionary_link",
 *                             type="object",
 *                             description="Элемент справочника, связанный с вопросом",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="id",
 *                                 type="integer",
 *                                 description="ID элемента справочника",
 *                                 example=12345,
 *                             ),
 *                             @OA\Property(
 *                                 property="title",
 *                                 type="string",
 *                                 description="Название элемента справочника",
 *                                 example="Элемент справочника",
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="points",
 *                             type="object",
 *                             description="Баллы за вопрос",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="value",
 *                                 type="integer",
 *                                 description="Количество баллов",
 *                                 example=10,
 *                                 nullable=true,
 *                             ),
 *                             @OA\Property(
 *                                 property="max",
 *                                 type="integer",
 *                                 description="Максимальное количество баллов за вопрос",
 *                                 example=20,
 *                                 nullable=true,
 *                             ),
 *                         ),
 *                     ),
 *                     @OA\Schema(
 *                         type="object",
 *                         title="Тип вопроса: 3D матрица",
 *                         @OA\Property(
 *                             property="service_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Служебное название вопроса",
 *                             example="Вопрос типа 3D матрицао",
 *                         ),
 *                         @OA\Property(
 *                             property="type",
 *                             type="string",
 *                             description="Название типа вопроса",
 *                             example="3D матрица",
 *                         ),
 *                         @OA\Property(
 *                             property="question_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Название вопроса",
 *                             example="Название вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="question_text",
 *                             type="string",
 *                             description="Текст вопроса",
 *                             example="Текст вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="extra_description",
 *                             type="string",
 *                             nullable=true,
 *                             description="Дополнительное описание вопроса",
 *                             example="Дополнительное описание",
 *                         ),
 *                         @OA\Property(
 *                             property="answer",
 *                             type="array",
 *                             description="Выбранные варианты в матрице. В случае отсутствия ответа, возвращается **null**.",
 *                             nullable=true,
 *                             @OA\Items(oneOf={
 *                                 @OA\Schema(
 *                                     type="object",
 *                                     description="Ответ для строки матрицы",
 *                                     @OA\Property(
 *                                         property="title",
 *                                         type="string",
 *                                         description="Наименование строки",
 *                                         example="Строка 1",
 *                                     ),
 *                                     @OA\Property(
 *                                         property="values",
 *                                         type="array",
 *                                         description="Выбранные значения для каждого столбца",
 *                                         @OA\Items(oneOf={
 *                                             @OA\Schema(
 *                                                 type="object",
 *                                                 description="Ответ для столбца матрицы",
 *                                                 @OA\Property(
 *                                                     property="title",
 *                                                     type="string",
 *                                                     description="Наименование столбца",
 *                                                     example="Столбец 1",
 *                                                 ),
 *                                                 @OA\Property(
 *                                                     property="values",
 *                                                     type="array",
 *                                                     description="Выбранные ответы",
 *                                                     @OA\Items(oneOf={
 *                                                         @OA\Schema(
 *                                                             type="string",
 *                                                             description="Выбранный ответ",
 *                                                             example="Вариант 1",
 *                                                          ),
 *                                                      }),
 *                                                 ),
 *                                             ),
 *                                         }),
 *                                     ),
 *                                 ),
 *                             }),
 *                         ),
 *                         @OA\Property(
 *                             property="comment",
 *                             type="string",
 *                             description="Комментарий респондента",
 *                             nullable=true,
 *                             example="Текст комментария",
 *                         ),
 *                         @OA\Property(
 *                             property="dictionary_link",
 *                             type="object",
 *                             description="Элемент справочника, связанный с вопросом",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="id",
 *                                 type="integer",
 *                                 description="ID элемента справочника",
 *                                 example=12345,
 *                             ),
 *                             @OA\Property(
 *                                 property="title",
 *                                 type="string",
 *                                 description="Название элемента справочника",
 *                                 example="Элемент справочника",
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="points",
 *                             type="object",
 *                             description="Баллы за вопрос",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="value",
 *                                 type="integer",
 *                                 description="Количество баллов",
 *                                 example=10,
 *                                 nullable=true,
 *                             ),
 *                             @OA\Property(
 *                                 property="max",
 *                                 type="integer",
 *                                 description="Максимальное количество баллов за вопрос",
 *                                 example=20,
 *                                 nullable=true,
 *                             ),
 *                         ),
 *                     ),
 *                     @OA\Schema(
 *                         type="object",
 *                         title="Тип вопроса: Анкета",
 *                         @OA\Property(
 *                             property="service_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Служебное название вопроса",
 *                             example="Вопрос типа Анкета",
 *                         ),
 *                         @OA\Property(
 *                             property="type",
 *                             type="string",
 *                             description="Название типа вопроса",
 *                             example="Анкета",
 *                         ),
 *                         @OA\Property(
 *                             property="question_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Название вопроса",
 *                             example="Название вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="question_text",
 *                             type="string",
 *                             description="Текст вопроса",
 *                             example="Текст вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="extra_description",
 *                             type="string",
 *                             nullable=true,
 *                             description="Дополнительное описание вопроса",
 *                             example="Дополнительное описание",
 *                         ),
 *                         @OA\Property(
 *                             property="answer",
 *                             type="array",
 *                             description="Ответы респондента на каждое поле анкеты. В случае отсутствия ответа, возвращается **null**.",
 *                             nullable=true,
 *                             @OA\Items(oneOf={
 *                                 @OA\Schema(
 *                                     type="object",
 *                                     @OA\Property(
 *                                         property="title",
 *                                         type="string",
 *                                         description="Название поля анкеты",
 *                                         example="Поле 1",
 *                                     ),
 *                                     @OA\Property(
 *                                         property="value",
 *                                         anyOf={
 *                                             @OA\Schema(
 *                                                 type="string",
 *                                                 description="Ответ респондента (для всех типов полей, кроме ФИО)",
 *                                                 example="Ответ",
 *                                             ),
 *                                             @OA\Schema(
 *                                                 type="object",
 *                                                 description="Ответ респондента (для поля типа ФИО)",
 *                                                 @OA\Property(
 *                                                     property="last_name",
 *                                                     type="string",
 *                                                     description="Фамилия",
 *                                                     example="Иванов",
 *                                                 ),
 *                                                 @OA\Property(
 *                                                     property="first_name",
 *                                                     type="string",
 *                                                     description="Имя",
 *                                                     example="Иван",
 *                                                 ),
 *                                                 @OA\Property(
 *                                                     property="patronymic",
 *                                                     type="string",
 *                                                     description="Отчество",
 *                                                     example="Иванович",
 *                                                 ),
 *                                             ),
 *                                         },
 *                                     ),
 *                                 ),
 *                             }),
 *                         ),
 *                         @OA\Property(
 *                             property="comment",
 *                             type="string",
 *                             description="Комментарий респондента (для этого типа вопроса всегда null)",
 *                             nullable=true,
 *                             example=null,
 *                         ),
 *                         @OA\Property(
 *                             property="dictionary_link",
 *                             type="object",
 *                             description="Элемент справочника, связанный с вопросом",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="id",
 *                                 type="integer",
 *                                 description="ID элемента справочника",
 *                                 example=12345,
 *                             ),
 *                             @OA\Property(
 *                                 property="title",
 *                                 type="string",
 *                                 description="Название элемента справочника",
 *                                 example="Элемент справочника",
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="points",
 *                             type="object",
 *                             description="Баллы за вопрос",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="value",
 *                                 type="integer",
 *                                 description="Количество баллов",
 *                                 example=10,
 *                                 nullable=true,
 *                             ),
 *                             @OA\Property(
 *                                 property="max",
 *                                 type="integer",
 *                                 description="Максимальное количество баллов за вопрос",
 *                                 example=20,
 *                                 nullable=true,
 *                             ),
 *                         ),
 *                     ),
 *                     @OA\Schema(
 *                         type="object",
 *                         title="Тип вопроса: Приоритет",
 *                         @OA\Property(
 *                             property="service_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Служебное название вопроса",
 *                             example="Вопрос типа Приоритет",
 *                         ),
 *                         @OA\Property(
 *                             property="type",
 *                             type="string",
 *                             description="Название типа вопроса",
 *                             example="Приоритет",
 *                         ),
 *                         @OA\Property(
 *                             property="question_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Название вопроса",
 *                             example="Название вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="question_text",
 *                             type="string",
 *                             description="Текст вопроса",
 *                             example="Текст вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="extra_description",
 *                             type="string",
 *                             nullable=true,
 *                             description="Дополнительное описание вопроса",
 *                             example="Дополнительное описание",
 *                         ),
 *                         @OA\Property(
 *                             property="answer",
 *                             type="array",
 *                             description="Варианты ответа в порядке, выбранном пользователем. В случае отсутствия ответа, возвращается **null**.",
 *                             nullable=true,
 *                             @OA\Items(oneOf={
 *                                 @OA\Schema(
 *                                     type="object",
 *                                     description="Ответ респондента",
 *                                     @OA\Property(
 *                                         property="title",
 *                                         type="string",
 *                                         description="Название варианта",
 *                                         example="Вариант 1",
 *                                     ),
 *                                     @OA\Property(
 *                                         property="dictionary_link",
 *                                         type="object",
 *                                         description="Элемент справочника, связанный с вариантом ответа",
 *                                         nullable=true,
 *                                         @OA\Property(
 *                                             property="id",
 *                                             type="integer",
 *                                             description="ID элемента справочника",
 *                                             example=12345,
 *                                         ),
 *                                         @OA\Property(
 *                                             property="title",
 *                                             type="string",
 *                                             description="Название элемента справочника",
 *                                             example="Элемент справочника",
 *                                         ),
 *                                     ),
 *                                 ),
 *                             }),
 *                         ),
 *                         @OA\Property(
 *                             property="comment",
 *                             type="string",
 *                             description="Комментарий респондента",
 *                             nullable=true,
 *                             example="Текст комментария",
 *                         ),
 *                         @OA\Property(
 *                             property="dictionary_link",
 *                             type="object",
 *                             description="Элемент справочника, связанный с вопросом",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="id",
 *                                 type="integer",
 *                                 description="ID элемента справочника",
 *                                 example=12345,
 *                             ),
 *                             @OA\Property(
 *                                 property="title",
 *                                 type="string",
 *                                 description="Название элемента справочника",
 *                                 example="Элемент справочника",
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="points",
 *                             type="object",
 *                             description="Баллы за вопрос",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="value",
 *                                 type="integer",
 *                                 description="Количество баллов",
 *                                 example=10,
 *                                 nullable=true,
 *                             ),
 *                             @OA\Property(
 *                                 property="max",
 *                                 type="integer",
 *                                 description="Максимальное количество баллов за вопрос",
 *                                 example=20,
 *                                 nullable=true,
 *                             ),
 *                         ),
 *                     ),
 *                     @OA\Schema(
 *                         type="object",
 *                         title="Тип вопроса: Рейтинг фото/видео галереи",
 *                         @OA\Property(
 *                             property="service_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Служебное название вопроса",
 *                             example="Вопрос типа Рейтинг фото/видео галереи",
 *                         ),
 *                         @OA\Property(
 *                             property="type",
 *                             type="string",
 *                             description="Название типа вопроса",
 *                             example="Рейтинг фото/видео галереи",
 *                         ),
 *                         @OA\Property(
 *                             property="question_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Название вопроса",
 *                             example="Название вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="question_text",
 *                             type="string",
 *                             description="Текст вопроса",
 *                             example="Текст вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="extra_description",
 *                             type="string",
 *                             nullable=true,
 *                             description="Дополнительное описание вопроса",
 *                             example="Дополнительное описание",
 *                         ),
 *                         @OA\Property(
 *                             property="answer",
 *                             type="array",
 *                             description="Оценки респондент. В случае отсутствия ответа, возвращается **null**.",
 *                             nullable=true,
 *                             @OA\Items(oneOf={
 *                                 @OA\Schema(
 *                                     type="object",
 *                                     description="Оценка респондента",
 *                                     @OA\Property(
 *                                         property="title",
 *                                         type="string",
 *                                         description="Название изображения/видео, оцененного пользователем. В случае отсутствия названия - номер изображения/видео",
 *                                         example="Изображение 1",
 *                                     ),
 *                                     @OA\Property(
 *                                         property="url",
 *                                         type="string",
 *                                         description="Ссылка на изображение/видео",
 *                                         example="https://foquz.ru/uploads/foquz/86103/64e7a7b46204d.jpeg",
 *                                     ),
 *                                     @OA\Property(
 *                                         property="value",
 *                                         type="integer",
 *                                         description="Оценка респондента",
 *                                         example=4,
 *                                     ),
 *                                 ),
 *                             }),
 *                         ),
 *                         @OA\Property(
 *                             property="comment",
 *                             type="string",
 *                             description="Комментарий респондента",
 *                             nullable=true,
 *                             example="Текст комментария",
 *                         ),
 *                         @OA\Property(
 *                             property="dictionary_link",
 *                             type="object",
 *                             description="Элемент справочника, связанный с вопросом",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="id",
 *                                 type="integer",
 *                                 description="ID элемента справочника",
 *                                 example=12345,
 *                             ),
 *                             @OA\Property(
 *                                 property="title",
 *                                 type="string",
 *                                 description="Название элемента справочника",
 *                                 example="Элемент справочника",
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="points",
 *                             type="object",
 *                             description="Баллы за вопрос",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="value",
 *                                 type="integer",
 *                                 description="Количество баллов",
 *                                 example=10,
 *                                 nullable=true,
 *                             ),
 *                             @OA\Property(
 *                                 property="max",
 *                                 type="integer",
 *                                 description="Максимальное количество баллов за вопрос",
 *                                 example=20,
 *                                 nullable=true,
 *                             ),
 *                         ),
 *                     ),
 *                     @OA\Schema(
 *                         type="object",
 *                         title="Тип вопроса: Выбор филиала",
 *                         @OA\Property(
 *                             property="service_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Служебное название вопроса",
 *                             example="Вопрос типа Выбор филиала",
 *                         ),
 *                         @OA\Property(
 *                             property="type",
 *                             type="string",
 *                             description="Название типа вопроса",
 *                             example="Выбор филиала",
 *                         ),
 *                         @OA\Property(
 *                             property="question_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Название вопроса",
 *                             example="Название вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="question_text",
 *                             type="string",
 *                             description="Текст вопроса",
 *                             example="Текст вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="extra_description",
 *                             type="string",
 *                             nullable=true,
 *                             description="Дополнительное описание вопроса",
 *                             example="Дополнительное описание",
 *                         ),
 *                         @OA\Property(
 *                             property="answer",
 *                             type="object",
 *                             description="Выбранный филиал. В случае отсутствия ответа, возвращается **null**.",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="title",
 *                                 type="string",
 *                                 description="Название филиала",
 *                                 example="Филиал 1",
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="comment",
 *                             type="string",
 *                             description="Комментарий респондента",
 *                             nullable=true,
 *                             example="Текст комментария",
 *                         ),
 *                         @OA\Property(
 *                             property="dictionary_link",
 *                             type="object",
 *                             description="Элемент справочника, связанный с вопросом",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="id",
 *                                 type="integer",
 *                                 description="ID элемента справочника",
 *                                 example=12345,
 *                             ),
 *                             @OA\Property(
 *                                 property="title",
 *                                 type="string",
 *                                 description="Название элемента справочника",
 *                                 example="Элемент справочника",
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="points",
 *                             type="object",
 *                             description="Баллы за вопрос",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="value",
 *                                 type="integer",
 *                                 description="Количество баллов",
 *                                 example=10,
 *                                 nullable=true,
 *                             ),
 *                             @OA\Property(
 *                                 property="max",
 *                                 type="integer",
 *                                 description="Максимальное количество баллов за вопрос",
 *                                 example=20,
 *                                 nullable=true,
 *                             ),
 *                         ),
 *                     ),
 *                     @OA\Schema(
 *                         type="object",
 *                         title="Тип вопроса: Звёздный рейтинг для вариантов",
 *                         @OA\Property(
 *                             property="service_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Служебное название вопроса",
 *                             example="Вопрос типа Звёздный рейтинг вариантов",
 *                         ),
 *                         @OA\Property(
 *                             property="type",
 *                             type="string",
 *                             description="Название типа вопроса",
 *                             example="Звёздный рейтинг вариантов",
 *                         ),
 *                         @OA\Property(
 *                             property="question_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Название вопроса",
 *                             example="Название вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="question_text",
 *                             type="string",
 *                             description="Текст вопроса",
 *                             example="Текст вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="extra_description",
 *                             type="string",
 *                             nullable=true,
 *                             description="Дополнительное описание вопроса",
 *                             example="Дополнительное описание",
 *                         ),
 *                         @OA\Property(
 *                             property="answer",
 *                             type="object",
 *                             description="Оценка респондента для каждого варианта. В случае отсутствия ответа, возвращается **null**.",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="title",
 *                                 type="string",
 *                                 description="Название варианта",
 *                                 example="Вариант 1",
 *                             ),
 *                             @OA\Property(
 *                                 property="value",
 *                                 type="integer",
 *                                 description="Оценка респондента",
 *                                 example=4,
 *                             ),
 *                             @OA\Property(
 *                                 property="clarifying_question",
 *                                 type="object",
 *                                 description="Уточняющий вопрос",
 *                                 nullable=false,
 *                                 @OA\Property(
 *                                     property="value",
 *                                     type="string",
 *                                     description="Ответ на уточняющий вопрос",
 *                                     example="Ответ на уточняющий вопрос",
 *                                 ),
 *                             ),
 *                             @OA\Property(
 *                                 property="dictionary_link",
 *                                 type="object",
 *                                 description="Элемент справочника, связанный с вопросом",
 *                                 nullable=true,
 *                                 @OA\Property(
 *                                     property="id",
 *                                     type="integer",
 *                                     description="ID элемента справочника",
 *                                     example=12345,
 *                                 ),
 *                                 @OA\Property(
 *                                     property="title",
 *                                     type="string",
 *                                     description="Название элемента справочника",
 *                                     example="Элемент справочника",
 *                                 ),
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="comment",
 *                             type="string",
 *                             description="Комментарий респондента",
 *                             nullable=true,
 *                             example="Текст комментария",
 *                         ),
 *                         @OA\Property(
 *                             property="dictionary_link",
 *                             type="object",
 *                             description="Элемент справочника, связанный с вопросом",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="id",
 *                                 type="integer",
 *                                 description="ID элемента справочника",
 *                                 example=12345,
 *                             ),
 *                             @OA\Property(
 *                                 property="title",
 *                                 type="string",
 *                                 description="Название элемента справочника",
 *                                 example="Элемент справочника",
 *                             ),
 *                         ),
 *                         @OA\Property(
 *                             property="points",
 *                             type="object",
 *                             description="Баллы за вопрос",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="value",
 *                                 type="integer",
 *                                 description="Количество баллов",
 *                                 example=10,
 *                                 nullable=true,
 *                             ),
 *                             @OA\Property(
 *                                 property="max",
 *                                 type="integer",
 *                                 description="Максимальное количество баллов за вопрос",
 *                                 example=20,
 *                                 nullable=true,
 *                             ),
 *                         ),
 *                     ),
 *                     @OA\Schema(
 *                         type="object",
 *                         title="Тип вопроса: Классификатор",
 *                         @OA\Property(
 *                             property="service_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Служебное название вопроса",
 *                             example="Вопрос типа Классификатор",
 *                         ),
 *                         @OA\Property(
 *                             property="type",
 *                             type="string",
 *                             description="Название типа вопроса",
 *                             example="Классификатор",
 *                         ),
 *                         @OA\Property(
 *                             property="question_name",
 *                             type="string",
 *                             nullable=true,
 *                             description="Название вопроса",
 *                             example="Название вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="question_text",
 *                             type="string",
 *                             description="Текст вопроса",
 *                             example="Текст вопроса",
 *                         ),
 *                         @OA\Property(
 *                             property="extra_description",
 *                             type="string",
 *                             nullable=true,
 *                             description="Дополнительное описание вопроса",
 *                             example="Дополнительное описание",
 *                         ),
 *                         @OA\Property(
 *                             property="answer",
 *                             type="object",
 *                             description="Элементы справочника, выбранные респондетом. В случае отсутствия ответа, возвращается **null**.",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="categories",
 *                                 description="Категории",
 *                                 nullable=false,
 *                                 type="object",
 *                                 ref="#/components/schemas/dictionary",
 *                             ),
 *                             @OA\Property(
 *                                 property="items",
 *                                 description="Выбранные элементы",
 *                                 nullable=false,
 *                                 type="object",
 *                                 @OA\Property(
 *                                     property="title",
 *                                     description="Название выбранного элемента",
 *                                     nullable=false,
 *                                     type="string",
 *                                     example="Элемент 1",
 *                                 ),
 *                             ),
 *                             @OA\Property(
 *                                 property="points",
 *                                 type="object",
 *                                  description="Баллы за вопрос",
 *                                  nullable=true,
 *                                  @OA\Property(
 *                                      property="value",
 *                                      type="integer",
 *                                      description="Количество баллов",
 *                                      example=10,
 *                                      nullable=true,
 *                                  ),
 *                                  @OA\Property(
 *                                      property="max",
 *                                      type="integer",
 *                                      description="Максимальное количество баллов за вопрос",
 *                                      example=20,
 *                                      nullable=true,
 *                                  ),
 *                              ),
 *                         ),
 *                         @OA\Property(
 *                             property="comment",
 *                             type="string",
 *                             description="Комментарий респондента",
 *                             nullable=true,
 *                             example="Текст комментария",
 *                         ),
 *                         @OA\Property(
 *                             property="dictionary_link",
 *                             type="object",
 *                             description="Элемент справочника, связанный с вопросом",
 *                             nullable=true,
 *                             @OA\Property(
 *                                 property="id",
 *                                 type="integer",
 *                                 description="ID элемента справочника",
 *                                 example=12345,
 *                             ),
 *                             @OA\Property(
 *                                 property="title",
 *                                 type="string",
 *                                 description="Название элемента справочника",
 *                                 example="Элемент справочника",
 *                             ),
 *                         ),
 *                     ),
 *                }),
 *            ),
 *        ),
 *    ),
 * ),
 * @OA\Webhook(
 *     webhook="newAnswer",
 *     @OA\Post(
 *         tags={"Вебхуки"},
 *         summary="Новый ответ на опрос",
 *         description="Вебхук нужен для того, чтобы получать информацию о новом ответе на опрос в стороннем сервисе. Вебхук — это POST запрос на указанный url, в теле которого находится JSON-объект с информацией о новом ответе на опрос. Для настойки вебхука необходимо обратиться в службу технической поддержки.",
 *         @OA\RequestBody(
 *             description="Анкета респондента в формате json",
 *             @OA\MediaType(
 *                 mediaType="application/json",
 *                 @OA\Schema(ref="#/components/schemas/Answer")
 *             )
 *         ),
 *         @OA\Response(
 *             response=200,
 *             description="Success",
 *         )
 *     )
 * ),
 * @OA\Info(
 *     title="Foquz API",
 *     description="
 # О REST API Foquz
Клиенты онлайн-платформы Foquz могут использовать API для интеграции со сторонними сервисами. Интеграция позволяет сторонним сервисам создавать и редактировать информацию о клиентах, отправлять опросы, а также получать ответы клиентов с помощью HTTP-запросов. Обратите внимание, что добавление полей в тело запросов может происходить в любой момент. Изменение или удаление полей происходит после предварительного уведомления.
 # Авторизация
Для авторизации запросов стороннего сервиса используется токен доступа. Токен доступа можно получить в разделе Настройки -> Общие настройки -> Интеграция. Для авторизации необходимо передать токен доступа в качестве query-параметра **access-token**.
",
 *     version="1.0"
 * )
 */
class DefaultController
{

}