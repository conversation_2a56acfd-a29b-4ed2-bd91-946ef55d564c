<?php

namespace app\modules\foquz\controllers\api;

use app\components\SendsayService;
use app\models\company\CompanyStaff;
use app\modules\foquz\models\FilialEmployeeSettings;
use app\models\User;
use app\modules\foquz\models\FilialEmployeeUserFilial;
use app\modules\foquz\models\FoquzContact;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollStatsLink;
use app\modules\foquz\models\notifications\SiteNotification;
use app\modules\foquz\models\UserChangeEmail;
use app\modules\foquz\services\notifications\cache\NotificationsCacheService;
use app\modules\foquz\services\notifications\NotificationsDailyService;
use webvimark\modules\UserManagement\models\rbacDB\Role;
use Yii;
use yii\filters\auth\QueryParamAuth;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\validators\EmailValidator;
use yii\web\BadRequestHttpException;
use yii\web\ForbiddenHttpException;
use yii\web\NotFoundHttpException;
use yii\web\UploadedFile;

class UserController extends ApiController
{
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['authenticator'] = [
            'class' => QueryParamAuth::class,
            'except' => ['index'],
        ];
        $behaviors['verbs'] = [
            'class' => VerbFilter::className(),
            'actions' => [
                'index' => ['GET'],
                'view' => ['GET'],
                'update-avatar' => ['POST'],
                'update-name' => ['PUT'],
                'update-password' => ['PUT'],
                'update-email' => ['PUT'],
                'activate-email-code' => ['POST'],
                'update-notifications' => ['PUT']
            ]
        ];
        return $behaviors;
    }

    public function actionIndex($activity = null, $role = null)
    {
        $company_id = $this->auth();

        $items = [];
        $query = User::find()
            ->select('user.*')
            ->addSelect(new \yii\db\Expression('IF(`user`.`name`= "" or `user`.`name` is null, `user`.`username`, `user`.`name`) as ifName'))
            ->leftJoin('company_staff', 'company_staff.user_id = user.id')
            ->where([
                'company_id' => $company_id
            ])
            ->andFilterWhere(['status' => $activity])
            ->orderBy(['ifName' => SORT_ASC]);

        if ($role !== null) {
            $query->leftJoin('auth_assignment aa', 'aa.user_id = user.id');
            $query->andWhere(['item_name' => $role]);
        }

        $users = $query->all();

        foreach($users as $user) {
            $items[] = [
                'id' => $user->id,
                'name' => $user->name == '' ? $user->username : $user->name,
                'avatar' => $user->getThumbUploadUrl('avatar', 'preview'),
            ];
        }

        return $this->response(200, ['items' => $items]);
    }

    /**
     * Return user data
     *
     * @return array
     */
    public function actionView($id = null)
    {
        $user = $id ? User::findOne($id) : \Yii::$app->user->identity;

        if (!$user) {
            throw new NotFoundHttpException('User not found');
        }

        return $user;
    }

    public function actionUpdateAvatar()
    {
        $user = Yii::$app->user->identity;
        $user->scenario = 'updateUser';

        if (Yii::$app->request->getIsPost()) {
            try {
                if (!$user->validate(['avatar'])) {
                    throw new BadRequestHttpException($user->getFirstError('avatar'));
                }
                $user->save(false);
            } catch (\Exception $e) {
                return $this->response(400, [
                    'errors' => $e->getMessage()
                ]);
            }
        }

        return $this->response(200, [
            'photo' => $user->getThumbUploadUrl('avatar', 'preview')
        ]);
    }

    public function actionUpdateName()
    {
        $request = array_merge($_REQUEST, \Yii::$app->request->getBodyParams());
        $user = \Yii::$app->user->identity;
        $user->scenario = 'updateUser';

        if($request['name'] === '') $request['name'] = null;
        $user->name = htmlspecialchars($request['name']);
        $user->save();
        return $this->response(200, [
            'name' => $user->name,
        ]);
    }

    public function actionCreate()
    {
        $post = \Yii::$app->request->post();
        $company = \Yii::$app->user->identity->company;

        $model = new User(['scenario' => 'newUser']);
        if($post['name'] === '') $post['name'] = null;
        $post['name'] = htmlspecialchars($post['name']);
        if(isset($post['phone']) && $post['phone'] !== '') $post['phone'] = FoquzContact::preformatPhone($post['phone']);
        if(isset($_FILES['avatar'])) {
            $post['avatar'] = UploadedFile::getInstanceByName('avatar');
        }
        $post['language_id'] = $company->language->id;
        $post["superadmin"] = 0;
        if ($post["role"]=="foquz_superadmin") $post["role"]="foquz_admin";

        if(!$model->load(['User' => $post])) {
            return $this->response(400, ['errors' => $model->errors]);
        }

        if (
            !Yii::$app->user->identity->superadmin && $post['role'] !== 'foquz_respondent' &&
            (int) $model->status === User::STATUS_ACTIVE && Yii::$app->user->identity->company->userLimit &&
            Yii::$app->user->identity->company->userLimit <= Yii::$app->user->identity->company->activeUsersCount
        ) {
            $title = '';
            if (!empty(Yii::$app->user->identity->company->currentTariff->tariff->title)) {
                $title = ' «' . Yii::$app->user->identity->company->currentTariff->tariff->title . '»';
            }
            return $this->response(400, ['limit' => true, 'error' => 'Вы достигли максимального количества активных пользователей на текущем тарифе' . $title]);
        }

        if ($model->save()) {
            User::assignRole($model->id, $post['role']);
            if($post['role'] === 'filial_employee' && isset($post['filials'])) {
                $insert = [];
                foreach($post['filials'] as $filialId) {
                    $insert[] = [$model->id, $filialId];
                }
                \Yii::$app->db->createCommand()->batchInsert(
                    'filial_employee_user_filial',
                    ['user_id', 'filial_id'],
                    $insert
                )->execute();
            }
            if($post['role'] === 'filial_employee') {
                $employeeSettings = new FilialEmployeeSettings([
                    'user_id' => $model->id,
                    'can_google_review_answer' => $post['googleReviewAnswer'] ?? false,
                    'can_process_answer' => $post['answerProcessing'] ?? false,
                ]);
                if (!$employeeSettings->save()) {
                    //print_r($employeeSettings->getErrors());
                    //exit;
                }
            }

            (new CompanyStaff([
                'user_id' => $model->id,
                'company_id' => \Yii::$app->user->identity->company->id
            ]))->save();
            if(($post['notify'] ?? null) and $model->email and $model->status == "1") {
                $model->notify($post['password']);
            }
            return $model->collectArrayForUsersPage();
        } else {
            return $this->response(400, ['errors' => $model->errors]);
        }
        
    }

    public function actionRole()
    {
        $userId = \Yii::$app->user->identity->getId();
        $roles = Role::getUserRoles($userId);
        return count($roles)===1 ? ['role' => array_values($roles)[0]->name] : ['role' => null];
    }

    public function actionRoles()
    {
        $roles = [];
        foreach (Role::getAvailableRoles(true) as $role) {
            if ($role['is_foquz']) {
                if (!Yii::$app->user->isSuperAdmin && in_array($role->name, ['foquz_superadmin', 'wiki_editor'])) {
                    break;
                }
                $roles[] = [
                    'name' => $role->name,
                    'description' => $role->description
                ];
            }
        }
        ArrayHelper::multisort($roles, 'description');

        return $roles;
    }

    public function actionUpdate($id)
    {
        //throw new ForbiddenHttpException();

        $post = \Yii::$app->request->post();
        $post["superadmin"] = 0;

        /** @var User $model */
        $model = User::findOne($id);

        if (!$model) {
            $this->response(400, [
                'errors' => ['user' => 'Пользователь не найден']
            ]);
        }

        if (Yii::$app->user->isGuest || Yii::$app->user->identity->company->id !== $model->company->id) {
            return $this->response(403);
        }

        $model->scenario = 'updateUser';
        if(isset($post['avatar']) && $post['avatar'] === 'null') {
            $post['avatar'] = null;
        }
        if(isset($_FILES['avatar'])) {
            $post['avatar'] = UploadedFile::getInstanceByName('avatar');
        }
        if(isset($post['name']) && $post['name'] === '') $post['name'] = null;
        if(isset($post['phone']) && $post['phone'] !== '') $post['phone'] = FoquzContact::preformatPhone($post['phone']);
                $post["superadmin"] = 0;


        if (str_contains($post['username'], '@')) {
            $validator = new EmailValidator();
            if (!$validator->validate($post['username'], $error)) {
                $msg = Html::encode('Значение «' . $post['username'] . '» не является правильным email адресом.');
                return $this->response(400, ['errors' => ['username' => $msg]]);
            }

            if (empty($post['email'])) {
                $post['email'] = $post['username'];
            } else {
                if ($post['email'] !== $post['username']) {
                    return $this->response(400, ['errors' => ['username' => Yii::t('main', 'У пользователя может быть только один Email')]]);
                }
            }
        }

        if(!$model->load(['User' => $post])) {
            return $this->response(400, ['errors' => $model->errors]);
        }

        if (
            !Yii::$app->user->identity->superadmin && $post['role'] !== 'foquz_respondent' &&
            (int) $model->status === User::STATUS_ACTIVE && (int) $model->oldAttributes['status'] !== User::STATUS_ACTIVE &&
            Yii::$app->user->identity->company->userLimit &&
            Yii::$app->user->identity->company->userLimit <= Yii::$app->user->identity->company->activeUsersCount
        ) {
            $title = '';
            if (!empty(Yii::$app->user->identity->company->currentTariff->tariff->title)) {
                $title = ' «' . Yii::$app->user->identity->company->currentTariff->tariff->title . '»';
            }
            return $this->response(400, ['limit' => true, 'error' => 'Вы достигли максимального количества активных пользователей на текущем тарифе' . $title]);
        }

        if ($post['avatar'] === null) {
            User::updateAll(['avatar' => null], ['id' => $model->id]);
        }

        if ($model->save()) {
                    if ($post["role"]=="foquz_superadmin") $post["role"]="foquz_admin";

            $model->refresh();
            $oldAssignments = array_keys(Role::getUserRoles($id));
            $newAssignments = [$post['role']];
            $toRevoke = array_diff($oldAssignments, $newAssignments);
            foreach ($toRevoke as $role)
            {
                User::revokeRole($model->id, $role);
            }
            User::assignRole($model->id, $post['role']);
            if(in_array('filial_employee', $oldAssignments) || $post['role'] === 'filial_employee') {
                FilialEmployeeUserFilial::deleteAll(['user_id' => $model->id]);
            }

            if($post['role'] === 'filial_employee') {
                if (isset($post['filials'])) {
                    $insert = [];
                    foreach($post['filials'] as $filialId) {
                        $insert[] = [$model->id, $filialId];
                    }
                    \Yii::$app->db->createCommand()->batchInsert(
                        'filial_employee_user_filial',
                        ['user_id', 'filial_id'],
                        $insert
                    )->execute();
                }
                $employeeSettings = $model->filialEmployeeSettings ?? new FilialEmployeeSettings([
                    'user_id' => $model->id,
                ]);

                $employeeSettings->can_google_review_answer = $post['googleReviewAnswer'] ?? false;
                $employeeSettings->can_process_answer = $post['answerProcessing'] ?? false;
                $employeeSettings->save();
            }

            //это только суперадмину, этот контроллер внутри компании 
            /*CompanyStaff::deleteAll(['user_id' => $model->id]);
            (new CompanyStaff([
                'user_id' => $model->id,
                'company_id' => \Yii::$app->user->identity->company->id,
            ]))->save();*/
            return $model->collectArrayForUsersPage();
        } else {
            return $this->response(400, ['errors' => $model->errors]);
        }
    }

    public function actionDelete($id)
    {
        $user = User::findOne($id);
       // $user->delete();
        return $this->response(204, []);
    }

    public function actionUpdatePassword()
    {
        $request = array_merge($_REQUEST, \Yii::$app->request->getBodyParams());
        $user = \Yii::$app->user->identity;
        $user->scenario = 'updateUser';

        if($user->validatePassword($request['currentPassword'])) {
            $user->password = $request['newPassword'];
            if($user->save()) {
                return [];
            }
            // IT NEVER WORKS
//            else {
//                return $this->response(400, [
//                    'errors' => $user->errors
//                ]);
//            }
        }
        if ($user->getFirstError('password')) {
            return $this->response(400, [
                'errors' => ['newPassword' => $user->getFirstError('password')]
            ]);
        }
        return $this->response(400, [
            'errors' => ['currentPassword' => 'Старый пароль указан неверно']
        ]);
    }

    public function actionSetEmail()
    {
        $request = array_merge($_REQUEST, \Yii::$app->request->getBodyParams());
        $user = \Yii::$app->user->identity;
        $user->scenario = 'updateUser';
        $email = $request['newEmail'] ?? $request['email'] ?? null;

        if ($email) {
            if (!User::find()->where(['email' => $email])->exists()) {
                $this->createChangeEmailModel(htmlspecialchars($request['email']));
            } else {
                return $this->response(400, [
                    'errors' => ['email' => 'Пользователь с данным email уже добавлен в систему']
                ]);
            }
        } else {
            return $this->response(400, [
                'errors' => ['email' => 'Неверно переданы данные']
            ]);
        }
    }

    public function actionUpdateEmail()
    {
        $request = array_merge($_REQUEST, \Yii::$app->request->getBodyParams());
        $user = \Yii::$app->user->identity;
        $user->scenario = 'updateUser';

        if(isset($request['currentEmail']) && isset($request['newEmail'])) {
            if(!User::find()->where(['email' => $request['newEmail']])->exists()) {
                return $this->createChangeEmailModel($request['newEmail'], $request['currentEmail']);
            } else {
                return $this->response(400, [
                    'errors' => ['email' => 'Пользователь с данным email уже добавлен в систему']
                ]);
            }
        }

        return $this->response(400, [
            'errors' => ['email' => 'Неверно переданы данные']
        ]);
    }

    public function actionActivateEmailCode()
    {
        $user = \Yii::$app->user->identity;

        $request = array_merge($_REQUEST, \Yii::$app->request->getBodyParams());

        if(empty($request['code'])) {
            return $this->response(400, [
                'errors' => ['code' => 'Неверно указан код']
            ]);
        }

        $changeEmailModel = UserChangeEmail::find()
            ->where(['code' => $request['code']])
            ->andWhere(['user_id' => $user->id])
            ->andWhere(['is_used' => false])
            ->andWhere(['>', 'available_to', date('Y-m-d H:i:s')])
            ->one();

        if(!$changeEmailModel) {
            return $this->response(400, [
                'errors' => [
                    'code' => 'Код не найден или не действителен'
                ]
            ]);
        }

        $user->scenario = 'updateUser';
        $user->email = $changeEmailModel->new_email;

        if ($changeEmailModel->old_email === $user->username) {
            $user->username = $changeEmailModel->new_email;
        }

        if($user->save()) {
            if ($changeEmailModel->old_email) {
                $s = new SendsayService(\Yii::$app->params['sendsay']['login'], \Yii::$app->params['sendsay']['password']);
                $s->sendEmail(
                    \Yii::$app->params['main_sender_email'],
                    "Опросы Foquz.ru",
                    $changeEmailModel->old_email,
                    'Для вашего аккаунта в личном кабинете сервиса Foquz была изменена почта',
                    "Новый email: " . $changeEmailModel->new_email . "." . PHP_EOL . "В случае, если это сделали не вы, то обратитесь в службу поддержки."
                );
                /*\Yii::$app->mailer->compose()
                    ->setFrom(\Yii::$app->params['main_sender'])
                    ->setTo($changeEmailModel->old_email)
                    ->setSubject('Для вашего аккаунта в личном кабинете сервиса Foquz была изменена почта')
                    ->setTextBody("Новый email: ".$changeEmailModel->new_email.".".PHP_EOL."В случае, если это сделали не вы, то обратитесь в службу поддержки.")
                    ->send();*/
            }

            $changeEmailModel->is_used = true;
            $changeEmailModel->save();

            return $this->response(200, []);
        } else {
            return $this->response(400, ['errors' => $user->errors]);
        }
    }

    private function createChangeEmailModel($newEmail, $oldEmail = null)
    {
        $changeEmailModel = new UserChangeEmail();
        $changeEmailModel->user_id = \Yii::$app->user->identity->id;
        $changeEmailModel->old_email = $oldEmail;
        $changeEmailModel->new_email = $newEmail;
        $changeEmailModel->code = $changeEmailModel->generateCode();
        $changeEmailModel->available_to = date('Y-m-d H:i:s', strtotime('+30 minutes'));
        if($changeEmailModel->save()) {
            return $this->response(200, [
                'available_to' => $changeEmailModel->available_to,
            ]);
        } else {
            return $this->response(400, [
                'errors' => $changeEmailModel->errors
            ]);
        }
    }

    public function actionUpdateNotifications()
    {
        /** @var User $user */
        $user = \Yii::$app->user->identity;
        $params = \Yii::$app->request->getBodyParams()['notifications'];
        $errors = [];
        foreach($params as $k => $paramsArray) {
            if (isset($paramsArray['custom_param']) && is_array($paramsArray['custom_param'])) {
                $paramsArray['custom_param'] = json_encode($paramsArray['custom_param'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            }
            $notification = SiteNotification::findOne([
                'type' => $paramsArray['type'],
                'user_id' => $user->id
            ]);
            if(!$notification) {
                $notification = new SiteNotification([
                    'type' => $paramsArray['type'],
                    'user_id' => $user->id
                ]);
            }
            $notification->load($paramsArray, '');
            if($notification->save()) {
                $notification->refreshPolls($paramsArray['polls'] ?? null);
            } else {
                $errors[] = [
                    'index' => $k,
                    'errors' => $notification->errors
                ];
            }
        }

        $service = new NotificationsCacheService();
        $service->saveUserNotifications($user);

        if (!empty(\Yii::$app->redis)) {
            //Меняем, пересоздаем задачу на ежедневный отчет
            $serviceDaily = new NotificationsDailyService($user, \Yii::$app->redis);
            $serviceDaily->updateJobToday();
        }

        if(count($errors) > 0) {
            return $this->response(400, ['errors' => $errors]);
        }
        return $this->response(200, ['notifications' => $user->notifications]);
    }

    public function actionApiToken()
    {
        return Yii::$app->user->identity->access_token;
    }

    public function actionUpdateApiToken()
    {
        $user = User::findOne(Yii::$app->user->identity->id);
        if ($user) {
            $user->access_token = Yii::$app->security->generateRandomString(15);
            if ($user->save(false)) {
                return $user->access_token;
            }
        }

        return false;
    }

    /**
     * @return int
     * @throws ForbiddenHttpException
     */
    public function auth(): int
    {
        $accessToken = Yii::$app->request->get('access-token');
        $linkKey = Yii::$app->request->get('link-key');
        if (is_string($accessToken) && !empty($accessToken)) {
            Yii::$app->user->loginByAccessToken($accessToken, QueryParamAuth::class);
            if (!Yii::$app->user->isGuest && !empty(Yii::$app->user->identity->company->id)) {
                return Yii::$app->user->identity->company->id;
            }
        } elseif (is_string($linkKey) && strlen($linkKey) === 32) {
            $poll_id = FoquzPollStatsLink::find()->select('foquz_poll_id')
                ->where(['show_answers' => true, 'right_level' => FoquzPollStatsLink::RIGHT_READ])
                ->andWhere(['like', 'link', '/stats/' . $linkKey])
                ->scalar();
            if ($poll_id && $poll = FoquzPoll::findOne(['id' => $poll_id, 'deleted' => false])
            ) {
                return $poll->company_id;
            }
        }
        throw new ForbiddenHttpException('Доступ запрещен');
    }
}