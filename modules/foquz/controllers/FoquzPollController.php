<?php

namespace app\modules\foquz\controllers;

use app\components\crypt\JsonCrypt;
use app\helpers\ReportHelper;
use app\models\company\CompanyRequestProcessingSettings;
use app\models\DictionaryElement;
use app\models\Filial;
use app\models\Order;
use app\models\OrderDishes;
use app\models\User;
use app\models\Worker;
use app\modules\foquz\models\Channel;
use app\modules\foquz\models\channels\ChannelDiscountBlock;
use app\modules\foquz\models\channels\ChannelDiscountBlockDiscount;
use app\modules\foquz\models\channels\ChannelProductBlock;
use app\modules\foquz\models\channels\ChannelProductBlockProduct;
use app\modules\foquz\models\channels\GlobalEmailSettings;
use app\modules\foquz\models\channels\GlobalPushSettings;
use app\modules\foquz\models\channels\GlobalSmsSettings;
use app\modules\foquz\models\channels\GlobalTelegramSettings;
use app\modules\foquz\models\channels\GlobalViberSettings;
use app\modules\foquz\models\channels\RepeatDiscountBlock;
use app\modules\foquz\models\channels\RepeatDiscountBlockDiscount;
use app\modules\foquz\models\channels\RepeatProductBlock;
use app\modules\foquz\models\channels\RepeatProductBlockProduct;
use app\modules\foquz\models\DiscountPool;
use app\modules\foquz\models\FoquzAutoPollForm;
use app\modules\foquz\models\FoquzComplaintFile;
use app\modules\foquz\models\FoquzContact;
use app\modules\foquz\models\FoquzOrderType;
use app\modules\foquz\models\FoquzPointCondition;
use app\modules\foquz\models\FoquzPointItem;
use app\modules\foquz\models\FoquzPointSelected;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollAnswerHiddenQuestion;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzPollAnswerItemFile;
use app\modules\foquz\models\FoquzPollDesign;
use app\modules\foquz\models\FoquzPollDesignTemplate;
use app\modules\foquz\models\FoquzPollDishScore;
use app\modules\foquz\models\FoquzPollFavorite;
use app\modules\foquz\models\FoquzPollKey;
use app\modules\foquz\models\FoquzPollMailingList;
use app\modules\foquz\models\FoquzPollMailingListSearch;
use app\modules\foquz\models\FoquzPollMailingListSend;
use app\modules\foquz\models\FoquzPollPage;
use app\modules\foquz\models\FoquzPollPageSocialNetworksOptions;
use app\modules\foquz\models\FoquzPollStatFilterSettings;
use app\modules\foquz\models\FoquzPollStatsLink;
use app\modules\foquz\models\FoquzPollStatWidgetKey;
use app\modules\foquz\models\FoquzPollWidget;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionDetail;
use app\modules\foquz\models\FoquzQuestionFile;
use app\modules\foquz\models\FoquzQuestionMatrixElement;
use app\modules\foquz\models\FoquzQuestionSmile;
use app\modules\foquz\models\notifications\NotificationScript;
use app\modules\foquz\models\polls\builders\Poll2ArrayBuilder;
use app\modules\foquz\models\processing\FoquzPollAnswerProcessing;
use app\modules\foquz\models\RecipientQuestionDetail;
use app\modules\foquz\models\Repeat;
use app\modules\foquz\models\Sender;
use app\modules\foquz\models\SenderName;
use app\modules\foquz\models\site\SiteCategory;
use app\modules\foquz\models\UploadForm;
use app\modules\foquz\services\answers\stat\AnswerStatService;
use app\modules\foquz\services\mailings\cache\MailingsAfterPollCache;
use Da\QrCode\Action\QrCodeAction;
use Da\QrCode\QrCode;
use Dompdf\Dompdf;
use RuntimeException;
use Yii;
use yii\data\ActiveDataProvider;
use yii\db\Exception;
use yii\db\Expression;
use yii\db\Query;
use yii\filters\AccessControl;
use yii\filters\ContentNegotiator;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use yii\helpers\BaseInflector;
use yii\helpers\Json;
use yii\helpers\Url;
use yii\web\BadRequestHttpException;
use yii\web\Controller;
use yii\web\ForbiddenHttpException;
use yii\web\HttpException;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use yii\web\UploadedFile;

/**
 * FoquzPollController implements the CRUD actions for FoquzPoll model.
 */
class FoquzPollController extends Controller
{
    public $enableCsrfValidation = false;

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class'        => AccessControl::class,
                'rules'        => [
                    [
                        'actions'       => [
                            'get-files',
                            'get-nps-answers',
                            'get-variants',
                            'get-star-rating-answers',
                            'unsubscribe',
                            'stats-link',
                            'stats-filter',
                            'get-answers',
                            'get-star-variant-answers',
                            'get-simple-matrix-answers',
                            'get-card-sorting-closed-answers',
                            'get-scale-answers',
                            'get-addresses',
                            'get-priorities',
                            'get-gallery-answers',
                            'get-profiles',
                            'get-differential-answers',
                            'get-smiles-answers',
                            'get-choose-media',
                            'get-3d-matrix-answers',
                            'stat-widget',
                            'widget-stats',
                            'answers-link',
                            'qr',
                        ],
                        'allow'         => true,
                        'matchCallback' => function () {
                            //есть проверки внутри экшенов
                            if (in_array($this->action->id, [
                                'stats-link',
                                'stats-filter',
                                'stat-widget',
                                'widget-stats',
                                'answers-link',
                                'qr',
                                'unsubscribe'
                            ])) {
                                return true;
                            }

                            //возможно где то  перегнул
                            // @todo надо проверить каждый экшен
                            $linkKey = Yii::$app->request->get('link');
                            $id = Yii::$app->request->get('id');
                            if (empty($id)) {
                                $id = Yii::$app->request->post('id');
                            }
                            if (empty($id)) {
                                throw  new NotFoundHttpException('Вопрос не найден');
                            }
                            $question = FoquzQuestion::findOne($id);
                            if (empty($question)) {
                                throw  new NotFoundHttpException('Вопрос не найден');
                            }

                            if (!empty($linkKey)) {
                                $service = AnswerStatService::getInstanceByLink($linkKey);
                                if ($question->poll_id !== $service->getPoll()->id) {
                                    throw new ForbiddenHttpException('Доступ запрещен');
                                }
                                return true;
                            }

                            if (Yii::$app->user->isGuest || Yii::$app->user->identity->isExecutor()) {
                                throw new ForbiddenHttpException('Доступ запрещен');
                            }

                            AnswerStatService::getInstanceByPollId($question->poll_id,
                                Yii::$app->user->identity->getId());
                        }

                    ],
                    [
                        'allow' => false,
                        'roles' => ['foquz_respondent'],
                    ],
                    [
                        'allow'   => false,
                        'actions' => ['mailings'],
                        'roles'   => ['filial_employee'],
                    ],
                    [
                        'allow'   => false,
                        'actions' => [
                            'update-questions-positions',
                            'download-json',
                            'save-point',
                            'update-vhannel',
                            'restore',
                            'change-status',
                            'duplicate',
                            'create',
                            'update',
                            'delete',
                            'update-design',
                            'update-design-background-image',
                            'update-design-logo-image',
                            'update-design-cover-image',
                            'delete-image',
                            'delete-channels',
                            'activate',
                            'update-channel',
                            'setposition',
                            'setpositions',
                            'save-point',
                            'save-point-page',
                            'save-inter-screen',
                            'reset-inter-screen',
                            'reset-common',
                            'update-processing',
                            'update-questions-positions',
                            'favorite',
                            'download-json'

                        ],
                        'roles'   => ['foquz_watcher', 'foquz_respondent'],
                    ],
                    [
                        'allow'   => true,
                        'actions' => ['index'],
                        'roles'   => [
                            'foquz_admin',
                            'foquz_executor',
                            'foquz_report_admin',
                            'editor',
                            'foquz_superadmin',
                            'foquz_watcher',
                            'filial_employee',
                            'wiki_editor'
                        ],
                    ],
                    [
                        'allow'   => true,
                        'actions' => ['print-answer'],
                        'roles'   => ['foquz_admin', 'foquz_executor', 'editor', 'foquz_watcher', 'filial_employee'],
                    ],
                    [
                        'allow'         => true,
                        'matchCallback' => function () {
                            return isset(\Yii::$app->user->identity) && !\Yii::$app->user->identity->superadmin && !Yii::$app->user->identity->isExecutor();
                        }
                    ],

                ],
                'denyCallback' => function ($rule, $action) {
                    if (isset(\Yii::$app->user->identity) && \Yii::$app->user->identity->isExecutor()) {
                        return $this->redirect('/foquz/answers');
                    } elseif (isset(\Yii::$app->user->identity)) {
                        throw new ForbiddenHttpException();
                    } else {
                        return $this->redirect('/user-management/auth/login');
                    }
                }
            ],
            'verbs'  => [
                'class'   => VerbFilter::className(),
                'actions' => [
                    'delete'        => ['POST'],
                    //'restore' => ['POST'],
                    'duplicate'     => ['POST'],
                    'change-status' => ['POST'],
                    'save-point'    => ['POST'],
                    'compensations' => ['GET'],
                    'reviews'       => ['GET'],
                    'stats-filter'  => ['POST', 'GET']
                ],
            ],
            [
                'class'   => ContentNegotiator::class,
                'only'    => [
                    'favorite',
                    'delete-image',
                    'delete',
                    //'restore',
                    'duplicate',
                    'change-status',
                    'save-inter-screen',
                    'reset-inter-screen',
                    'reset-common',
                    'save-point',
                    'save-point-page',
                    'points-by-type',
                    'stats-filter'
                ],
                'formats' => [
                    'application/json' => \app\response\Response::FORMAT_META_JSON,
                ],
            ],
        ];
    }

    public function actions()
    {
        return [
            'qr' => [
                'class'     => QrCodeAction::className(),
                'text'      => Yii::$app->getRequest()->getUrl(),
                'param'     => 'v',
                'component' => 'qr'
            ]
        ];
    }

    public function actionIpAnswers()
    {
        $id = Yii::$app->getRequest()->get('id');
        $page = Yii::$app->getRequest()->get('page') ?? 1;
        $ip_address = Yii::$app->getRequest()->get('ip_address');
        $answersQuery = FoquzPollAnswer::find()
            ->where(['ip_address' => $ip_address])
            ->orderBy(['id' => SORT_DESC]);
        $totalCount = $answersQuery->count();
        $answers_array = $answersQuery->limit(30)->offset(($page - 1) * 30)->all();
        Yii::$app->getResponse()->format = Response::FORMAT_JSON;
        $poll_questions_list = [];
        foreach ($answers_array as $item) {
            if (!isset($poll_questions_list[$item['foquz_poll_id']])) {
                $poll_questions_list[$item['foquz_poll_id']] = ArrayHelper::toArray(FoquzQuestion::find()->where(['poll_id' => $item['foquz_poll_id']])->all(),
                    [
                        'app\modules\foquz\models\FoquzQuestion' => [
                            'id',
                            'name'                       => function ($question) {
                                return $question->service_name;
                            },
                            'shortName'                  => function ($question) {
                                if ($question->service_name != '') {
                                    return $question->service_name;
                                }
                                if ($question->name != '') {
                                    return $question->name;
                                }
                                return $question->description;
                            },
                            'type'                       => 'rating_type',
                            'mediaType'                  => function ($question) {
                                return $question->type == 'text' ? 0 : ($question->type == 'image' ? 1 : 2);
                            },
                            'text'                       => 'text',
                            'clarifyingQuestion'         => 'detail_question',
                            'clarifyingQuestionVariants' => function ($question) {
                                return isset($question->questionDetails[0]) ? ArrayHelper::getColumn($question->questionDetails,
                                    function ($element) {
                                        return $element->question;
                                    }) : null;
                            },
                            'variants'                   => function ($question) {
                                return isset($question->questionDetails[0]) ? ArrayHelper::getColumn($question->questionDetails,
                                    function ($element) {
                                        return $element->question;
                                    }) : null;
                            },
                            'dishes'                     => function ($question) {
                                return $question->service_name == 'Товар' && $question->is_system;
                            },
                        ],
                    ]);
            }
        }


        $GLOBALS['poll_questions_list'] = $poll_questions_list;

        $data = ArrayHelper::toArray($answers_array, [
            'app\modules\foquz\models\FoquzPollAnswer' => [
                'id',
                'createdAt'           => function ($answer) {
                    return date("d.m.Y H:i", $answer->foquzPoll->created_at);
                },
                'ip_address',
                'status'              => function ($answer) {
                    return $answer->getStatusId();
                },
                'passedAt'            => function ($answer) {
                    return date("d.m.Y H:i",
                        strtotime(isset($answer->foquzAnswer[0]) ? $answer->foquzAnswer[0]->created_at : $answer->updated_at));
                },
                //'updated_at'=>'lastStatusChangedAt',
                'isProcessed'         => function ($answer) {
                    return true;
                },
                'surveyName'          => function ($answer) {
                    return $answer->foquzPoll->name;
                },
                'complaint'           => function ($answer) {
                    if (!$answer->complaint) {
                        return null;
                    }

                    return [
                        'text'      => ArrayHelper::getValue($answer, 'complaint.text'),
                        'photoUrls' => FoquzComplaintFile::find()->select(['file_path'])->where(['foquz_poll_answer_id' => $answer->id])->column(),
                    ];
                },
                // the key name in array result => property name
                // 'createTime' => 'created_at',
                // the key name in array result => anonymous function
                'lastStatusChangedAt' => 'updated_at',
                'answers'             => function ($answer) {

                    return ArrayHelper::toArray($answer->getRatedAnswers(), [
                        'app\modules\foquz\models\FoquzPollAnswerItem' => [
                            'question' => function ($item) {
                                return (object)['name' => $item->foquzQuestion->service_name];
                            },
                            'rating',
                            'answered' => function () {
                                return true;
                            },
                            'comment'  => 'self_variant'
                        ]
                    ]);
                },
                'questions'           => function ($answer) {

                    return FoquzQuestion::questionsListPoll($GLOBALS['poll_questions_list'][$answer->foquz_poll_id],
                        $answer);
                },
            ],
        ]);


        return [
            'data'     => $data,
            'total'    => $totalCount,
            'lastPage' => $totalCount <= ($page * 30)
        ];
    }


    public function actionAnswer($id)
    {
        $model = $this->findModel($id);

        $advantageSuppliersExcelEnabled = false;
        if (
            ((YII_DEBUG && $model->company_id === 1) || ($model->company_id === 749)) &&
            FoquzQuestion::find()->where([
                'poll_id'            => $model->id,
                'main_question_type' => FoquzQuestion::TYPE_3D_MATRIX,
                'is_deleted'         => 0
            ])->exists()
        ) {
            $advantageSuppliersExcelEnabled = true;
        }

        return $this->render('answer', [
            'poll'                           => $model,
            'advantageSuppliersExcelEnabled' => $advantageSuppliersExcelEnabled,
        ]);
    }

    public function actionPrintAnswer($id)
    {
        $selectedElements = Yii::$app->getRequest()->get('elements', []);
        /** @var FoquzPollAnswer $answer */
        $answer = FoquzPollAnswer::findOne($id);
        if (!$answer) {
            throw new NotFoundHttpException();
        }
        if ($answer->foquzPoll->company_id !== Yii::$app->user->identity->company->id) {
            throw new ForbiddenHttpException();
        }
        $answers = $answer->answers;
        if (!empty($selectedElements) && !in_array('all', $selectedElements, true)) {
            foreach ($answers['questions'] ?? [] as $key => $question) {
                if (!in_array((string)$question['dictionary_element_id'], $selectedElements, true) &&
                    ($question['dictionary_element_id'] || !in_array('null', $selectedElements, true))
                ) {
                    unset($answers['questions'][$key]);
                    if ($answer->foquzPoll->point_system) {
                        $answers['points']['answer_points'] -= $question['answer']['points'];
                        $answers['points']['points_max'] -= $question['answer']['max_points'];
                    }
                }
            }
            $answers['questions'] = array_values($answers['questions']);
            if ($answer->foquzPoll->point_system) {
                $answers['points']['percent'] = $answers['points']['points_max'] ? (($answers['points']['answer_points'] / $answers['points']['points_max']) * 100) : 0;
            }
        }

        $hiddenQuestions = FoquzPollAnswerHiddenQuestion::find()
            ->select('question_id')
            ->where(['answer_item_id' => ArrayHelper::getColumn($answer->foquzAnswer, 'id')])
            ->column();
        $hiddenQuestionsStrings = [];
        foreach ($hiddenQuestions as $hd) {
            $hiddenQuestionsStrings[] = (string) $hd;
        }

        $this->layout = "report-print";
        return $this->render('print-answer', [
            'answers'         => $answers,
            'hiddenQuestions' => $hiddenQuestionsStrings,
        ]);
    }

    public function actionCharts($id)
    {
        $model = $this->findModel($id);
        return $this->render('charts', ['poll' => $model]);
    }

    /**
     * Lists all FoquzPoll models.
     * @return mixed
     */
    public function actionIndex()
    {
        $id = Yii::$app->request->get('id');
        if (Yii::$app->user->identity->superadmin) {
            return Yii::$app->response->redirect(['/foquz/company']);
        }

        if (Yii::$app->user->identity->isExecutor()) {
            return Yii::$app->response->redirect(['/foquz/answers']);
        }

        $company = Yii::$app->user->identity->company->id;

        $user = Yii::$app->user->identity;
        $filials = [];

        if ($user->isFilialEmployee() && $user->getUserFilials()->count() > 0) {
            $filials = ArrayHelper::getColumn($user->userFilials, 'filial_id');
        }

        $data = FoquzPoll::find()
            ->select([
                'sent_answers_count'        => new Expression('SUM(foquz_poll.sent_answers_count)'),
                'opened_answers_count'      => new Expression('SUM(foquz_poll.opened_answers_count)'),
                'in_progress_answers_count' => new Expression('SUM(foquz_poll.in_progress_answers_count)'),
                'filled_answers_count'      => new Expression('SUM(foquz_poll.filled_answers_count)'),
            ])
            ->where(['foquz_poll.company_id' => $company, 'deleted' => 0]);

        if (count($filials) > 0) {
            $data->select(["status" => "foquz_poll_answer.status", "c" => "count(*)"])
                ->leftJoin('foquz_poll_answer', 'foquz_poll_answer.foquz_poll_id=foquz_poll.id')
                ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
                ->andWhere("foquz_poll_answer.id is not null")
                ->andWhere([
                    'in',
                    'IF(foquz_poll.is_auto, orders.filial_id, foquz_poll_answer.answer_filial_id)',
                    $filials
                ])
                ->groupBy("status");
        }

        if ($user->isEditor() || $user->isWatcher()) {
            $editorFoldersIds = FoquzPoll::getEditorFolderIdsAll($user->id);
            if (!empty($id) && $id !== 'archive' && $id !== 'allPolls' && !in_array($id,
                    $editorFoldersIds['folders'])) {
                throw new ForbiddenHttpException();
            }
            $conditionArray = ['foquz_poll.folder_id' => $editorFoldersIds['folders']];

//            $conditionArray = ['foquz_poll.folder_id' => EditorFolder::find()->select('folder_id')->where(['user_id' => $user->id])->column()];
            $data->andWhere($conditionArray);
        }

        $data = $data->asArray()->all();

        if (count($filials) > 0) {
            $res = [];
            foreach ($data as $row) {
                $res[$row['status']] = $row['c'];
            }
            $filled = $res[FoquzPollAnswer::STATUS_DONE] ?? 0;
            $processed = $filled + ($res[FoquzPollAnswer::STATUS_IN_PROGRESS] ?? 0);
            $opened = $processed + ($res[FoquzPollAnswer::STATUS_OPEN] ?? 0);
            $sended = $opened + ($res[FoquzPollAnswer::STATUS_EMAIL_OPEN] ?? 0) + ($res[FoquzPollAnswer::STATUS_NEW] ?? 0);
        } else {
            $filled = $data[0]['filled_answers_count'] ?? 0;
            $processed = $filled + ($data[0]['in_progress_answers_count'] ?? 0);
            $opened = $processed + ($data[0]['opened_answers_count'] ?? 0);
            $sended = $opened + ($data[0]['sent_answers_count'] ?? 0);
        }

        $goals = FoquzPoll::goalsCount();
        $folders = FoquzPoll::find()->where([
            'company_id' => $company,
            'is_folder'  => true,
            'is_tmp'     => false,
            'folder_id'  => null
        ]);

        $pollsCount = FoquzPoll::find()->where([
            'company_id' => $company,
            'is_folder'  => false,
            'is_tmp'     => false,
            'status'     => FoquzPoll::STATUS_NEW
        ]);

        if ($user->isEditor() || $user->isWatcher()) {
            $editorFoldersIds = FoquzPoll::getEditorFolderIdsAll($user->id);
            //$conditionArray = ['foquz_poll.folder_id' => EditorFolder::find()->select('folder_id')->where(['user_id' => $user->id])->column()];
            $conditionArray = ['foquz_poll.folder_id' => $editorFoldersIds['folders']];
            $pollsCount->andWhere($conditionArray);
            $folders->andWhere([
                'or',
                ['foquz_poll.id' => $editorFoldersIds['folders']],
                ['foquz_poll.id' => $editorFoldersIds['parents']]
            ]);
        }

        return $this->render('index2', [
            'sended'    => $sended, //$company=="35" ? 303 : $sendedQuery->count(),
            'opened'    => $opened, //$openedQuery->count(),
            'processed' => $processed, //$processedQuery->count(),
            'filled'    => $filled,// $filledQuery->count(),
            'goals'     => $goals,
            'folders'   => $folders->all(),
            'allPolls'  => $pollsCount->count(),
        ]);
    }

    public function actionFavorite()
    {
        $userId = Yii::$app->getUser()->getId();
        $model = FoquzPollFavorite::findOne([
            'foquz_poll_id' => Yii::$app->getRequest()->post('id'),
            'user_id'       => $userId,
        ]);

        if (null === $model) {
            (new FoquzPollFavorite([
                'foquz_poll_id' => Yii::$app->getRequest()->post('id'),
                'user_id'       => $userId,
            ]))->save();
        } else {
            $model->delete();
        }

        return [];
    }


    /**
     * @param int $id
     * @param int $status
     * @return array
     */
    public function actionChangeStatus(int $id, int $status = FoquzPoll::STATUS_ARCHIVE)
    {
        $model = $this->findModel($id);
        $model->status = $status;
        $model->save();

        return [
            'html' => $this->renderPartial((ArrayHelper::getValue(Yii::$app->getRequest()->post('FoquzPollSearch'),
                'viewType', 'grid') === 'grid' ? '_item' : '_item_tr'), [
                'model' => $model,
                'dNone' => '',
            ])
        ];
    }

    public function actionDuplicate($id)
    {
        $model = $this->findModel($id);
        $newModel = new FoquzPoll(['createFirstQuestion' => false]);

        $newModel->setAttributes($model->getAttributes());
        $newModel->save();

        foreach ($model->foquzQuestions as $foquzQuestion) {
            $newQuestion = new FoquzQuestion($foquzQuestion->getAttributes([
                'name',
                'description',
                'text',
                'rating_type',
                'detail_question',
                'is_self_answer',
                'type',
            ]));
            $newQuestion->setAttributes(['poll_id' => $newModel->id]);
            if ($newQuestion->save()) {
                foreach ($foquzQuestion->questionDetails as $foquzQuestionDetail) {
                    $newDetail = new FoquzQuestionDetail(['foquz_question_id' => $newQuestion->id]);
                    $newDetail->question = $foquzQuestionDetail->question;
                    $newDetail->save();
                }
                foreach ($foquzQuestion->questionFiles as $questionFile) {
                    $newFile = new FoquzQuestionFile(['question_id' => $newQuestion->id]);
                    $newFile->setAttributes($questionFile->getAttributes([
                        'file_path',
                        'file_full_path',
                        'file_text',
                        'type',
                        'duration',
                    ]));
                    $newFile->save();
                }
            }
        }

        return [
            'html' => $this->renderPartial((ArrayHelper::getValue(Yii::$app->getRequest()->post('FoquzPollSearch'),
                'viewType', 'grid') === 'grid' ? '_item' : '_item_tr'), [
                'model' => $this->findModel($newModel->id),
            ]),
        ];
    }

    public function actionUnsubscribe($key)
    {
        $this->layout = '@app/modules/foquz/views/layouts/answer';
        /** @var FoquzPollMailingListSend|null $s */
        $s = FoquzPollMailingListSend::find()->where(["key" => $key])->one();
        $contact = null;
        $answerId = null;
        $email = null;
        $clientID = null;

        if ($s) {
            $s->status = 2;
            $s->save();
            $answerId = $s->answer_id;
        }

        if (!$answerId) {
            $answer = FoquzPollAnswer::find()->where(["auth_key" => $key])->one();
            if ($answer) {
                $answerId = $answer->id;
            }
        }

        if ($answerId) {
            $model = FoquzPollAnswer::findOne(["id" => $answerId]);

            if ($model) {
                $poll = FoquzPoll::findOne($model->foquz_poll_id);
                $id = $poll->id;
                if ($model->contact_id) {
                    $contact = FoquzContact::findOne($model->contact_id);
                    // print_r($contact); exit;
                }
                if (!empty($contact->email)) {
                    $email = $contact->email;
                    $clientID = $contact->id;
                }
            }
        }
        if (!$email && !empty($s->contact->foquzContact->email)) {
            $email = $s->contact->foquzContact->email;
            $clientID = $s->contact->foquzContact->id;
        }
        if (!$email) {
            $pollKey = FoquzPollKey::findOne(['key' => $key]);
            if (!empty($pollKey->clientEmail->email)) {
                $email = $pollKey->clientEmail->email;
                $clientID = $pollKey->client_id;
            }
        }

        if (!$email) {
            throw new RuntimeException('Email not found');
        }

        return $this->render('unsubscribe', [
            'email'  => $email,
            'client' => $clientID,
        ]);
    }

    public function actionSender($id)
    {
        $model = $this->findModel($id);
        $hasContacts = FoquzContact::find()->where(['company_id' => $model->company_id])->exists();

        return $this->render('sender', [
            'model'       => $model,
            'hasContacts' => $hasContacts,
        ]);
    }

    public function actionCheckCaptcha($token)
    {
        $ch = curl_init();
        $args = http_build_query([
            "secret" => Yii::$app->params['smart_captcha_server_key'],
            "token"  => $token,
            "ip"     => $_SERVER['REMOTE_ADDR'], // Нужно передать IP-адрес пользователя.
            // Способ получения IP-адреса пользователя зависит от вашего прокси.
        ]);
        curl_setopt($ch, CURLOPT_URL, "https://captcha-api.yandex.ru/validate?$args");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 1);

        $server_output = curl_exec($ch);
        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpcode !== 200) {
            echo "Allow access due to an error: code=$httpcode; message=$server_output\n";
            return true;
        }
        $resp = json_decode($server_output);

        return $resp->status === "ok";
    }

    public function actionDownloadQrCode($fileLink, string $name)
    {
        $qrCode = (new QrCode($fileLink))
            ->useEncoding('UTF-8')
            ->setLogoWidth(60)
            ->setSize(300)
            ->setMargin(5);

        $filePath = Yii::getAlias('@app/runtime/QR-code-' . BaseInflector::slug($name) . '.png');
        $qrCode->writeFile($filePath);

        return Yii::$app->getResponse()->sendFile($filePath);
    }

    public function actionAutoPollInfo()
    {
        return $this->render('auto-poll-info');
    }

    /**
     * Creates a new FoquzPoll model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @param bool $isAuto
     * @return mixed
     */

    public function actionCreate($isAuto = false, $folder_id = null, $pointSystem = false)
    {
        if ($isAuto and !Yii::$app->user->identity->company->auto_poll_enabled) {
            return $this->redirect(['foquz-poll/auto-poll-info', 'from' => 'poll']);
        }
        $model = new FoquzPoll([
            'name'            => ($isAuto ? 'Автоматический опрос' : 'Мой тестовый опрос'),
            'is_auto'         => (boolean)$isAuto,
            'is_tmp'          => true,
            'folder_id'       => $folder_id,
            'point_system'    => $pointSystem,
            'time_to_restart' => 15,
            'show_foquz_link' => in_array(Yii::$app->user->identity->company->tariff->title, ['Бизнес', 'Простой']),
        ]);

        if ($model->save()) {
            foreach (FoquzPollPage::getTypes() as $type => $value) {
                (new FoquzPollPage([
                    'type'          => $type,
                    'foquz_poll_id' => $model->id,
                    'name'          => '<strong>Заголовок</strong>',
                    'description'   => 'Текст раздела',
                    'enabled'       => false
                ]))->save();
            }
            if ($model->is_auto) {
                return $this->redirect(['launch', 'id' => $model->id]);
            } else {
                return $this->redirect(['/foquz/foquz-question/create', 'pollId' => $model->id]);
            }
        }
    }

    /**
     * Updates an existing FoquzPoll model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $post = Yii::$app->request->post();
        $post['FoquzPoll']['is_tmp'] = 0;

        if ($model->load($post) && $model->save()) {
            Yii::$app->getResponse()->format = Response::FORMAT_JSON;
            return ['success' => true, 'title' => $model->name];
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing FoquzPoll model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $model = $this->findModel($id);
        $model->is_tmp = true;
        $model->save();

        return [

        ];
    }

    public function actionUpdateDesign($pollId)
    {
        $poll = $this->findModel($pollId);
        $post = Yii::$app->request->post();
        if ($poll->design->load(['FoquzPollDesign' => $post]) && $poll->design->save()) {
            if ($poll->is_template) {
                $poll->is_template = false;
                $poll->save();
            }
            Yii::$app->response->format = Response::FORMAT_JSON;
            return $poll->design->toArray();
        }
        return json_encode($poll->design->errors, JSON_UNESCAPED_UNICODE);
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionUpdateDesignBackgroundImage($designId, $mobile = 0)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $designModel = FoquzPollDesign::findOne($designId);
        if (!$designModel) {
            throw new NotFoundHttpException();
        }
        $model = new UploadForm();
        $basePath = $designModel->getUploadDirForBackgrounds();
        $fullPath = Yii::getAlias("@app/web/{$basePath}");
        if (false === file_exists($fullPath)) {
            mkdir($fullPath, 0777, true);
        }

        if (Yii::$app->request->getIsPost()) {
            $model->file = UploadedFile::getInstance($model, 'file');
            if ($model->file && $model->validate()) {
                $fileName = time() . '.' . $model->file->extension;
                if ($res = $model->file->saveAs($fullPath . '/' . $fileName)) {

                    $fileAbsName = '/' . $basePath . '/' . $fileName;
                    if ($mobile) {
                        $designModel->mobile_background_image = $fileAbsName;
                    } else {
                        $designModel->background_image = $fileAbsName;
                    }
                    $designModel->save();

                    return [
                        'image' => $fileAbsName,
                    ];
                }
            } else {
                return [
                    'errors' => $model->errors
                ];
            }
        }
        return [];
    }

    public function actionUpdateDesignLogoImage($designId)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $designModel = FoquzPollDesign::findOne($designId);
        $model = new UploadForm();
        $basePath = $designModel->getUploadDirForLogos();
        $fullPath = Yii::getAlias("@app/web/{$basePath}");
        if (false === file_exists($fullPath)) {
            mkdir($fullPath, 0777, true);
        }

        if (Yii::$app->request->getIsPost()) {
            $model->file = UploadedFile::getInstance($model, 'file');
            if ($model->file && $model->validate()) {
                $fileName = time() . '.' . $model->file->extension;
                if ($res = $model->file->saveAs($fullPath . '/' . $fileName)) {

                    $fileAbsName = '/' . $basePath . '/' . $fileName;
                    $designModel->logo_image = $fileAbsName;
                    $designModel->save();

                    return [
                        'image' => $fileAbsName,
                    ];
                }
            }
        }
        return [];
    }

    public function actionUpdateDesignCoverImage($designId)
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $designModel = FoquzPollDesign::findOne($designId);
        $model = new UploadForm();
        $model->setScenario($model::SCENARIO_SMALL_PICTURE);
        $basePath = $designModel->getUploadDirForCovers();

        try {
            $fullPath = Yii::getAlias("@app/web/{$basePath}");
            if (false === file_exists($fullPath)) {
                mkdir($fullPath, 0777, true);
            }

            if (Yii::$app->request->getIsPost()) {
                $model->file = UploadedFile::getInstance($model, 'file');
                if ($model->file && $model->validate()) {
                    $fileName = time() . '.' . $model->file->extension;
                    if ($model->file->saveAs($fullPath . '/' . $fileName)) {
                        $fileAbsName = '/' . $basePath . '/' . $fileName;

                        $designModel->cover_image = $fileAbsName;

                        $designModel->save();

                        return [
                            'image' => $fileAbsName,
                        ];
                    }
                } else {
                    Yii::$app->response->setStatusCode(400);
                    return [
                        'errors' => $model->errors
                    ];
                }
            }
        } catch (\Throwable $e) {
            Yii::$app->response->setStatusCode(400);
            return [
                'errors' => $e->getMessage()
            ];
        }
        return [];
    }

    public function actionCustomDesign($id)
    {
        $model = $this->findModel($id);

        $designModel = $model->design ? $model->design->toArray() : FoquzPollDesign::createDesignFromStandartTemplate($id);

        $templates = [];

        $templatesDb = FoquzPollDesignTemplate::find()
            ->where(['company_id' => null])
            ->orWhere(['company_id' => Yii::$app->user->identity->company->id])
            ->orderBy('company_id ASC, id DESC')
            ->all();

        foreach ($templatesDb as $template) {
            $templates[] = $template->toArray();
        }

        return $this->render('custom-design', [
            'design'    => $designModel,
            'templates' => $templates,
            'poll'      => $model,
            'hasLogic'  => $model->hasLogic(),
        ]);
    }

    public function actionDeleteImage(int $id, string $fieldName)
    {
        $poll = $this->findModel($id);
        $design = $poll->design;
        $image = $design->image;

        if ($image) {
            $image->$fieldName = null;

            if (!$image->save()) {
                return [
                    'errors' => $design->getErrors(),
                ];
            }
        }


        return [
            'imageUrl' => ($fieldName === 'logo_image' ? $design->getLogo() : $design->getBackground()),
        ];
    }

    public function actionAutoSettings(int $id)
    {
        $activeTab = 'nav-common-tab';
        $model = $this->findModel($id);

        $points = FoquzPointItem::getTotalPoints($id);
        $pointsSelected = FoquzPointItem::getTotalPoints($id, true);

        $conditions = FoquzPointItem::getConditions($id);

        $form = new FoquzAutoPollForm(['poll' => $model, 'scenario' => FoquzAutoPollForm::SCENARIO_AUTO_POLL]);
        $form->triggerDays = 0;

        if ($form->load(Yii::$app->getRequest()->post()) && $form->validate() && $form->handle()) {
            Yii::$app->response->format = Response::FORMAT_JSON;

            $model->refresh();
            return [
                'poll'      => $model,
                'activeTab' => 'nav-inter-screens-tab',
                'message'   => 'Данные успешно сохранились'
            ];
        }

        $channels = Channel::find()->where(['poll_id' => $id])->orderBy(['position' => SORT_ASC])->all();
        if (!count($channels)) {
            $this->addChannels($id);
            $channels = Channel::find()->where(['poll_id' => $id])->orderBy(['position' => SORT_ASC])->all();
        }
        $icons = [];
        foreach ($channels as $index => $channel) {
            $icons[$channel->id] = "settings__" . strtolower($channel->name) . "-icon";
        }

        $senders = Sender::getSenderValues();
        $sender_names = SenderName::getSenderNameValues();

        $notificationScripts = NotificationScript::find()
            ->where([
                'company_id' => Yii::$app->user->identity->company->id,
                'deleted'    => false,
            ])
            ->orderBy('name')
            ->all();

        $pools = DiscountPool::find()
            ->select('discount_pool.id, title')
            ->where([
                'company_id' => Yii::$app->user->identity->company->id,
            ])
            ->orderBy('title')
            ->all();

        return $this->render('auto-settings', [
            'model'               => $model,
            'modelForm'           => $form,
            'activeTab'           => $activeTab,
            'points'              => $points,
            'pointsSelected'      => $pointsSelected,
            'conditions'          => $conditions,
            'channels'            => $channels,
            'icons'               => $icons,
            'senders'             => $senders,
            'sender_names'        => $sender_names,
            'pools'               => $pools,
            'notificationScripts' => $notificationScripts
        ]);
    }

    public function actionSettings(int $id)
    {
        $model = $this->findModel($id);

        //Проверка на существование таба
        $tab = Yii::$app->request->get('tab');

        $tabs = [
            'main',
            'fillingForm',
            'sendForm',
            'processingAnswers',
            'other',
        ];

        if ($tab === 'processingAnswers') {
            $processingEnabled = (bool)CompanyRequestProcessingSettings::find()
                ->select('request_processing_enabled')
                ->where(['company_id' => $model->company_id])
                ->scalar();

            if (!$processingEnabled) {
                throw new NotFoundHttpException();
            }
        } elseif ($tab !== null && !in_array($tab, $tabs, true)) {
            throw new NotFoundHttpException();
        }

        $activeTab = 'nav-common-tab';
        $form = new FoquzAutoPollForm(['poll' => $model]);
        if ($form->load(Yii::$app->getRequest()->post()) && $form->validate() && $form->handle()) {
            Yii::$app
                ->getSession()
                ->setFlash('Данные успешно сохранились');

            $activeTab = 'nav-inter-screens-tab';
            //return $this->refresh();
        }
        $points = FoquzPointItem::getTotalPoints($id);
        $pointsSelected = FoquzPointItem::getTotalPoints($id, true);
        $conditions = FoquzPointItem::getConditions($id);

        $channels = Channel::find()->where(['poll_id' => $id])->orderBy(['position' => SORT_ASC])->all();
        if (!count($channels)) {
            $this->addChannels($id);
            $channels = Channel::find()->where(['poll_id' => $id])->orderBy(['position' => SORT_ASC])->all();
        }
        $icons = [];
        foreach ($channels as $index => $channel) {
            $icons[$channel->id] = "settings__" . strtolower($channel->name) . "-icon";
        }

        $senders = Sender::getSenderValues();
        $sender_names = SenderName::getSenderNameValues();

        $notificationScripts = NotificationScript::find()
            ->joinWith(['channels.repeats', 'channels.pool'])
            ->where([
                'notification_scripts.company_id' => Yii::$app->user->identity->company->id,
                'deleted'                         => false,
            ])
            ->orderBy('name')
            ->all();

        $pools = DiscountPool::find()
            ->select('discount_pool.id, title')
            ->where([
                'company_id' => Yii::$app->user->identity->company->id,
            ])
            ->orderBy('title')
            ->all();

        if ($model->stop_sending) {
            if ($model->stop_sending === 'double') {
                $stopSendingCondition = 'double';
            } else {
                $stopSendingCondition = 'period';
            }
        } else {
            $stopSendingCondition = null;
        }
        if ($model->stop_sending && $model->stop_sending !== 'double') {
            $stopSendingPeriod = $model->stop_sending;
        } else {
            $stopSendingPeriod = null;
        }

        $model->hasDictionaryLinks = $model->hasDictionaryLinks();
        $model->dictionarySelectDisabled = $model->dictionarySelectDisabled();

        return $this->render('settings', [
            'model'                => $model,
            'channels'             => $channels,
            'activeTab'            => $activeTab,
            'points'               => $points,
            'pointsSelected'       => $pointsSelected,
            'modelForm'            => $form,
            'icons'                => $icons,
            'sender_names'         => $sender_names,
            'senders'              => $senders,
            'conditions'           => $conditions,
            'pools'                => $pools,
            'notificationScripts'  => $notificationScripts,
            'stopSendingCondition' => $stopSendingCondition,
            'stopSendingPeriod'    => $stopSendingPeriod,
        ]);
    }

    public function actionLoadChannel($id)
    {
        $model = Channel::findOne($id);
        $data = $model->attributes;
        if ($model->code !== null) {
            $data['promocodeName'] = $model->code;
            $data['promocodeIsPool'] = 0;
        }
        if ($model->pool_id !== null) {
            $data['promocodePoolId'] = $model->pool_id;
            $data['promocodePoolName'] = $model->pool->title;
            $data['promocodeIsPool'] = 1;
        }
        $data['product_blocks'] = $model->getProductBlocks()->with('products')->with('categories')->asArray()->all();
        foreach ($data['product_blocks'] as $i => $pb) {
            if (isset($pb['products']) && count($pb['products']) > 0) {
                foreach ($pb['products'] as $ip => $product) {
                    $data['product_blocks'][$i]['products'][$ip]['categoryName'] = SiteCategory::find([
                        'id'          => $product['categoryId'],
                        'endpoint_id' => $product['endpoint_id']
                    ])->one()->name;
                }
            }
            if (isset($pb['categories']) && count($pb['categories']) > 0) {
                foreach ($pb['categories'] as $ic => $category) {
                    $data['product_blocks'][$i]['categories'][$ic]['products'] = SiteCategory::findOne($category['id'])->getProducts()->asArray()->all();
                }
            }
        }
        $data['discount_blocks'] = $model->getDiscountBlocks()->with('discounts')->asArray()->all();
        $data['repeats'] = [];
        //$data['sender']=mt_rand();
        //$data['image']='https://sun1-19.userapi.com/c855236/v855236651/156019/32YbvdmtdJk.jpg';
        foreach ($model->repeats as $repeat) {
            $repeatArray = [
                'message'   => $repeat->text,
                'imageUrl'  => $repeat->image,
                'delayTime' => $repeat->delay,
                'delayDays' => $repeat->delay_days,
                'link'      => $repeat->link,
                'payload'   => $repeat->payload,
            ];
            if ($repeat->code !== null) {
                $repeatArray['promocodeName'] = $repeat->code;
                $repeatArray['promocodeIsPool'] = 0;
            }
            if ($repeat->pool_id !== null) {
                $repeatArray['promocodePoolId'] = $repeat->pool_id;
                $repeatArray['promocodePoolName'] = $repeat->pool->title;
                $repeatArray['promocodeIsPool'] = 1;
            }
            $repeatArray['product_blocks'] = $repeat->getProductBlocks()->with('products')->with('categories')->asArray()->all();
            foreach ($repeatArray['product_blocks'] as $i => $pb) {
                if (isset($pb['products']) && count($pb['products']) > 0) {
                    foreach ($pb['products'] as $ip => $product) {
                        $data['product_blocks'][$i]['products'][$ip]['categoryName'] = SiteCategory::find([
                            'id'          => $product['categoryId'],
                            'endpoint_id' => $product['endpoint_id']
                        ])->one()->name;
                    }
                }
                if (isset($pb['categories']) && count($pb['categories']) > 0) {
                    foreach ($pb['categories'] as $ic => $category) {
                        $repeatArray['product_blocks'][$i]['categories'][$ic]['products'] = SiteCategory::findOne($category['id'])->getProducts()->asArray()->all();
                    }
                }
            }
            $repeatArray['discount_blocks'] = $repeat->getDiscountBlocks()->with('discounts')->asArray()->all();
            $data['repeats'][] = $repeatArray;
        }

        return json_encode($data, JSON_UNESCAPED_UNICODE);
    }

    /**
     * @return Response
     * @throws BadRequestHttpException
     * @throws \Throwable
     * @throws \yii\db\StaleObjectException
     */
    public function actionDeleteChannels(): Response
    {
        $mailing_id = (int)Yii::$app->request->get('mailing_id');
        if ($mailing_id) {
            $mailing = FoquzPollMailingList::findOne($mailing_id);
            if (!$mailing) {
                throw new BadRequestHttpException('Неверный ID рассылки!');
            }
            return $this->asJson([
                'success' => (bool)Channel::deleteAll(['mailing_id' => $mailing_id])
            ]);
        }

        throw new BadRequestHttpException('Не переданы обязательные параметры!');
    }

    public function actionLoadChannels($id)
    {
        $mailingID = Yii::$app->request->get('mailing_id');
        $model = $this->findModel($id);
        $subQuery = Channel::find()->select(['id', 'MAX(mailing_id)'])->where(['poll_id' => $id])->groupBy([
            'name',
            'passed_trigger'
        ]);
        if ($mailingID) {
            $subQuery->andWhere(['OR', ['mailing_id' => $mailingID], ['mailing_id' => null]]);
        } else {
            $subQuery->andWhere(['mailing_id' => null]);
        }
        $subQuery = $subQuery->column();
        $channels = Channel::find()->where(['id' => $subQuery])->orderBy(['position' => SORT_ASC])->all();
        $data = [];
        $order = [];
        $passedTriggers = [];
        $data['is_auto'] = $model->is_auto;
        foreach ($channels as $channel) {
            $data1 = $channel->attributes;
            $data1['text'] = $data1['text'] ?? '';
            $data1['product_blocks'] = $channel->getProductBlocks()->with('products')->with('categories')->asArray()->all();
            foreach ($data1['product_blocks'] as $i => $pb) {
                if (isset($pb['products']) && count($pb['products']) > 0) {
                    foreach ($pb['products'] as $ip => $product) {
                        $data1['product_blocks'][$i]['products'][$ip]['categoryName'] = SiteCategory::find([
                            'id'          => $product['categoryId'],
                            'endpoint_id' => $product['endpoint_id']
                        ])->one()->name;
                    }
                }
                if (isset($pb['categories']) && count($pb['categories']) > 0) {
                    foreach ($pb['categories'] as $ic => $category) {
                        $data1['product_blocks'][$i]['categories'][$ic]['products'] = SiteCategory::findOne($category['id'])->getProducts()->asArray()->all();
                    }
                }
            }
            $data1['discount_blocks'] = $channel->getDiscountBlocks()->with('discounts')->asArray()->all();
            if (!$data1['passed_trigger']) {
                $order[] = ['channel' => strtolower($channel->name)];
            }
            $data1['repeats'] = [];
            if ($channel->code !== null) {
                $data1['promocodeName'] = $channel->code;
                $data1['promocodeIsPool'] = 0;
            }
            if ($channel->pool_id !== null) {
                $data1['promocodePoolId'] = $channel->pool_id;
                $data1['promocodePoolName'] = $channel->pool->title;
                $data1['promocodeIsPool'] = 1;
            }
            //$data['sender']=mt_rand();
            foreach ($channel->repeats as $repeat) {
                $repeat_data = $repeat->attributes;
                $repeatArray = [
                    'message'         => $repeat_data['text'],
                    'imageUrl'        => $repeat_data['image'],
                    'delayTime'       => $repeat_data['delay'],
                    'delayDays'       => $repeat_data['delay_days'],
                    'link'            => $repeat_data['link'],
                    'payload'         => $repeat_data['payload'],
                    'product_blocks'  => $repeat->getProductBlocks()->with('products')->with('categories')->asArray()->all(),
                    'discount_blocks' => $repeat->getDiscountBlocks()->with('discounts')->asArray()->all()
                ];
                foreach ($repeatArray['product_blocks'] as $i => $pb) {
                    if (isset($pb['products']) && count($pb['products']) > 0) {
                        foreach ($pb['products'] as $ip => $product) {
                            $data['product_blocks'][$i]['products'][$ip]['categoryName'] = SiteCategory::find([
                                'id'          => $product['categoryId'],
                                'endpoint_id' => $product['endpoint_id']
                            ])->one()->name;
                        }
                    }
                    if (isset($pb['categories']) && count($pb['categories']) > 0) {
                        foreach ($pb['categories'] as $ic => $category) {
                            $repeatArray['product_blocks'][$i]['categories'][$ic]['products'] = SiteCategory::findOne($category['id'])->getProducts()->asArray()->all();
                        }
                    }
                }
                if ($repeat->code !== null) {
                    $repeatArray['promocodeName'] = $repeat->code;
                    $repeatArray['promocodeIsPool'] = 0;
                }
                if ($repeat->pool_id !== null) {
                    $repeatArray['promocodePoolId'] = $repeat->pool_id;
                    $repeatArray['promocodePoolName'] = $repeat->pool->title;
                    $repeatArray['promocodeIsPool'] = 1;
                }
                $data1['repeats'][] = $repeatArray;
            }
            if ($data1['passed_trigger'] !== null) {
                $passedTriggers[$data1['name']] = $data1;
            } else {
                $data[$data1['name']] = $data1;
            }
        }
        if ($order) {
            $data['order'] = $order;
        }
        $data['passedTriggers'] = $passedTriggers;

        return json_encode($data, JSON_UNESCAPED_UNICODE);
    }

    /**
     * @return bool[]
     * @deprecated Метод не используется фронтом
     */
    public function actionCanActivateChannel()
    {
        $channelId = Yii::$app->getRequest()->get('channel_id');
        $channel = Channel::findOne($channelId);
        Yii::$app->response->format = Response::FORMAT_JSON;
        return ['result' => $channel->sender && $channel->text];

    }

    public function actionActivate()
    {
        $channelId = Yii::$app->getRequest()->get('channel_id');
        $mailingId = Yii::$app->getRequest()->get('mailing_id');
        $channel = Channel::findOne($channelId);
        Yii::$app->response->format = Response::FORMAT_JSON;
        if (!$channel) {
            throw new BadRequestHttpException('Неверный ID канала рассылки');
        }
        if ($mailingId && $channel->passed_trigger === null) {
            $mailing = FoquzPollMailingList::findOne($mailingId);
            if (!$mailing) {
                throw new BadRequestHttpException('Неверный ID рассылки');
            }
            if (!$channel->mailing_id) {
                Channel::createChannelsForMailing($mailing->foquz_poll_id, $mailingId);
                $channel = Channel::find()->where([
                    'name'       => $channel->name,
                    'mailing_id' => $mailingId,
                    'poll_id'    => $mailing->foquz_poll_id
                ])->one();
            }
        }
        $channel->active = $channel->active ? 0 : 1;
        $channel->save();
        return $channel->active;
    }

    public function base64ToImage($output_file, $base64_string)
    {
        $file = fopen($output_file, "wb");

        $data = explode(',', $base64_string);


        fwrite($file, base64_decode($data[1]));
        fclose($file);

        return $output_file;
    }

    public function actionUpdateChannel(): Response
    {
        $id = (int)Yii::$app->request->get('id');
        $mailing_id = (int)Yii::$app->request->get('mailing_id');
        $name = Yii::$app->request->get('name');
        if ($mailing_id && $name) {
            $mailing = FoquzPollMailingList::find()
                ->joinWith('foquzPoll')
                ->where([
                    'foquz_poll_mailing_list.id' => $mailing_id,
                    'foquz_poll.company_id'      => Yii::$app->user->identity->company->id
                ])
                ->one();
            if (!$mailing) {
                throw new BadRequestHttpException('Неверный ID рассылки');
            }
            if ($channel = Channel::find()->where([
                'name'       => $name,
                'mailing_id' => $mailing_id,
                'poll_id'    => $mailing->foquz_poll_id
            ])->one()) {
                $model = $channel;
            } else {
                Channel::createChannelsForMailing($mailing->foquz_poll_id, $mailing_id);
                $model = Channel::find()->where([
                    'name'       => $name,
                    'mailing_id' => $mailing_id,
                    'poll_id'    => $mailing->foquz_poll_id
                ])->one();
            }
        } elseif ($id) {
            $model = Channel::findOne($id);
        } else {
            throw new BadRequestHttpException('Не переданы обязательные параметры');
        }

        if ($model) {
            $post = Yii::$app->request->post('data');
            $model->delay = $post['delayTime'];
            $model->delay_days = $post['delayDays'];
            $model->sender = $post['sender'];
            $model->sender_name = Yii::$app->request->post('data')['senderName'] ?? '';
            $model->subject = Yii::$app->request->post('data')['subject'] ?? '';
            $model->text = $post['message'];
            $model->code = isset($post['promocodeName']) && $post['promocodeName'] != '' ? $post['promocodeName'] : null;
            $model->pool_id = $post['promocodePoolId'] ?? null;
            $model->sender_id = $post['sender_id'] ?? null;
            $model->updated_at = date('Y-m-d H:i:s');
            $model->active = $post['active'];
            $model->processing_time_in_minutes = $post['processing_time_in_minutes'] ?? null;
            $model->link = $post['link'] ?? null;
            $model->payload = $post['payload'] ?? null;
            $model->passed_trigger = $post['passed_trigger'] ?? null;
            if (isset($post['image'])) {
                if ($post['image'] === 'delete') {
                    if ($model->image != '') {
                        @unlink('uploads/channel_image_' . $id . '.jpg');
                    }
                    $model->image = '';
                } else {
                    if (strpos($post['image'], '/uploads') !== false) {
                        $model->image = $post['image'];
                    } else {
                        $this->base64ToImage('uploads/channel_image_' . $id . '.jpg', $post['image']);
                        $model->image = '/uploads/channel_image_' . $id . '.jpg';
                    }
                }
            }

            if ($model->validate()) {
                $model->save();


                ChannelDiscountBlock::deleteAll(['channel_id' => $model->id]);

                if (isset($post['discount_block'])) {
                    $dbIds = [];
                    foreach ($post['discount_block'] as $discountBlock) {
                        if ($discountBlock['id'] == 0) {
                            $dbModel = new ChannelDiscountBlock();
                        } else {
                            $dbModel = ChannelDiscountBlock::findOne($discountBlock['id']);
                        }
                        $dbModel->channel_id = $model->id;
                        $dbModel->count = $discountBlock['count'];
                        $dbModel->save();
                        $dbIds[] = $dbModel->id;
                        foreach ($discountBlock['discounts'] as $discountId) {
                            $cdbdModel = new ChannelDiscountBlockDiscount([
                                'channel_db_id'    => $dbModel->id,
                                'site_discount_id' => $discountId
                            ]);
                            $cdbdModel->save();
                        }
                    }
                    ChannelDiscountBlock::deleteAll(['AND', ['channel_id' => $model->id], ['NOT IN', 'id', $dbIds]]);
                } else {
                    ChannelDiscountBlock::deleteAll(['channel_id' => $model->id]);
                }
                if (isset($post['product_block'])) {
                    $pbIds = [];
                    foreach ($post['product_block'] as $productBlock) {
                        if ($productBlock['id'] == 0) {
                            $pbModel = new ChannelProductBlock();
                        } else {
                            $pbModel = ChannelProductBlock::findOne($productBlock['id']);
                        }
                        $pbModel->channel_id = $model->id;
                        $pbModel->count = $productBlock['count'];
                        $pbModel->save();
                        $pbIds[] = $pbModel->id;
                        if (isset($productBlock['products'])) {
                            foreach ($productBlock['products'] as $productId) {
                                $cpbpModel = new ChannelProductBlockProduct([
                                    'channel_pb_id'   => $pbModel->id,
                                    'site_product_id' => $productId
                                ]);
                                $cpbpModel->save();
                            }
                        }
                        if (isset($productBlock['categories'])) {
                            foreach ($productBlock['categories'] as $categoryId) {
                                $cpbpModel = new ChannelProductBlockProduct([
                                    'channel_pb_id'    => $pbModel->id,
                                    'site_category_id' => $categoryId
                                ]);
                                $cpbpModel->save();
                            }
                        }
                    }
                    ChannelProductBlock::deleteAll(['AND', ['channel_id' => $model->id], ['NOT IN', 'id', $pbIds]]);
                } else {
                    ChannelProductBlock::deleteAll(['channel_id' => $model->id]);
                }
                $model->foquzPoll->is_tmp = false;
                $model->foquzPoll->is_template = false;
                $model->foquzPoll->save();
            } else {
                print_r($model->getErrors());
            }
            Repeat::deleteAll(['channel_id' => $id]);
            if (isset($_POST['repeats'])) {
                foreach ($_POST['repeats'] as $item) {
                    if (isset($item['message']) && $item['message'] != '') {
                        $repeat = new Repeat();
                        $repeat->text = $item['message'];
                        $repeat->delay = $item['delayTime'];
                        $repeat->delay_days = $item['delayDays'];
                        $repeat->channel_id = $id;
                        $repeat->code = isset($item['promocodeName']) && $item['promocodeName'] != '' ? $item['promocodeName'] : null;
                        $repeat->pool_id = $item['promocodePoolId'] ?? null;
                        $repeat->link = $item['link'] ?? null;
                        $repeat->payload = $item['payload'] ?? null;
                        if ($repeat->validate()) {
                            $repeat->save();
                            if (isset($item['image']) && $item['image']) {
                                if ($item['image'] == 'delete') {
                                    $repeat->image = '';
                                } elseif (strlen($item['image']) > 10) {
                                    if (strpos($item['image'], '/uploads') !== false) {
                                        $repeat->image = $item['image'];
                                    } else {
                                        $this->base64ToImage('uploads/repeat_image_' . $repeat->id . '.jpg',
                                            $item['image']);
                                        $repeat->image = '/uploads/repeat_image_' . $repeat->id . '.jpg';
                                    }

                                }
                                $repeat->save();
                            }

                            if (isset($item['discount_block'])) {
                                $dbIds = [];
                                foreach ($item['discount_block'] as $discountBlock) {
                                    if ($discountBlock['id'] == 0) {
                                        $dbModel = new RepeatDiscountBlock();
                                    } else {
                                        $dbModel = RepeatDiscountBlock::findOne($discountBlock['id']);
                                    }
                                    $dbModel->repeat_id = $repeat->id;
                                    $dbModel->count = $discountBlock['count'];
                                    $dbModel->save();
                                    $dbIds[] = $dbModel->id;
                                    foreach ($discountBlock['discounts'] as $discountId) {
                                        $rdbdModel = new RepeatDiscountBlockDiscount([
                                            'repeat_db_id'     => $dbModel->id,
                                            'site_discount_id' => $discountId
                                        ]);
                                        $rdbdModel->save();
                                    }
                                }
                                RepeatDiscountBlock::deleteAll([
                                    'AND',
                                    ['repeat_id' => $repeat->id],
                                    ['NOT IN', 'id', $dbIds]
                                ]);
                            } else {
                                RepeatDiscountBlock::deleteAll(['repeat_id' => $repeat->id]);
                            }

                            if (isset($item['product_block'])) {
                                $pbIds = [];
                                foreach ($item['product_block'] as $productBlock) {
                                    if ($productBlock['id'] == 0) {
                                        $pbModel = new RepeatProductBlock();
                                    } else {
                                        $pbModel = RepeatProductBlock::findOne($productBlock['id']);
                                    }
                                    $pbModel->repeat_id = $repeat->id;
                                    $pbModel->count = $productBlock['count'];
                                    $pbModel->save();
                                    $pbIds[] = $pbModel->id;
                                    if (isset($productBlock['products'])) {
                                        foreach ($productBlock['products'] as $productId) {
                                            $rpbpModel = new RepeatProductBlockProduct([
                                                'repeat_pb_id'    => $pbModel->id,
                                                'site_product_id' => $productId
                                            ]);
                                            $rpbpModel->save();
                                        }
                                    }
                                    if (isset($productBlock['categories'])) {
                                        foreach ($productBlock['categories'] as $categoryId) {
                                            $rpbpModel = new RepeatProductBlockProduct([
                                                'repeat_pb_id'     => $pbModel->id,
                                                'site_category_id' => $categoryId
                                            ]);
                                            $rpbpModel->save();
                                        }
                                    }
                                }
                                RepeatDiscountBlock::deleteAll([
                                    'AND',
                                    ['repeat_id' => $repeat->id],
                                    ['NOT IN', 'id', $pbIds]
                                ]);
                            } else {
                                RepeatDiscountBlock::deleteAll(['repeat_id' => $repeat->id]);
                            }
                        } else {
                            return $this->asJson(['success' => false, 'errors' => $repeat->getErrors()]);
                            //print_r($repeat->getErrors());
                        }
                    }
                }
            }
            $model->foquzPoll->refreshPromocodeUsage();
            return $this->asJson(['success' => true, 'id' => $model->id]);
        }

        if (isset(\Yii::$app->redis)) {
            (new MailingsAfterPollCache(\Yii::$app->redis))->updateActivePoll($model->poll_id);
        }
        return $this->asJson(['success' => false, 'errors' => 'Некорректный запрос']);
    }

    /**
     * Старый метод, используется для сортировки каналов рассылок после опроса
     * @return void
     */
    public function actionSetposition()
    {
        if (isset($_GET['channel_id']) && isset($_GET['position'])) {
            $id = intval($_GET['channel_id']);
            $position = intval($_GET['position']);
            $model = Channel::findOne($id);
            $model->position = $position;
            $model->save();
            exit;
        }
    }

    /**
     * Сортирует каналы приглашений к опросу
     * @return void
     */
    public function actionSetPositions(): Response
    {
        $pollID = Yii::$app->request->get('poll_id');
        $mailingID = Yii::$app->request->get('mailing_id');
        $positions = Yii::$app->request->post('positions', []);
        if (is_array($positions)) {
            $positions = array_values($positions);
        } else {
            $positions = [];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;
        if (!$pollID || empty($positions)) {
            throw new BadRequestHttpException('Не переданы обязательные параметры (poll_id, positions)');
        }
        if (FoquzPoll::find()->where(['id' => $pollID])->exists() === false) {
            throw new BadRequestHttpException('Передан некорректный poll_id');
        }
        if ($mailingID && FoquzPollMailingList::find()->where(['id' => $mailingID])->exists() === false) {
            throw new BadRequestHttpException('Передан некорректный mailing_id');
        }
        if ($mailingID) {
            Channel::createChannelsForMailing($pollID, $mailingID);
        }
        $channels = Channel::find()->where([
            'poll_id'        => $pollID,
            'mailing_id'     => $mailingID,
            'passed_trigger' => null
        ])->all();
        if (empty($channels)) {
            throw new BadRequestHttpException('Переданы некорректные параметры (poll_id, mailing_id)');
        }
        /** @var Channel $channel */
        foreach ($channels as $channel) {
            $index = array_search($channel->name, $positions, true);
            $channel->position = $index === false ? null : ($index + 1);
            $channel->save();
        }
        return $this->asJson(['success' => true]);
    }

    public function actionSavePoint($id)
    {
        $model = $this->findModel($id);

        $modelForm = new FoquzPointItem(Yii::$app->getRequest()->post());
        $modelForm->foquz_poll_id = $model->id;
        $modelForm->is_active = 1;

        if ($modelForm->save()) {
            return $modelForm->toArray();
        }

        return [
            'errors' => $modelForm->getErrors(),
        ];
    }

    public function actionSavePointPage($id)
    {

        $model = $this->findModel($id);
        $show_links = false;

        $common = Yii::$app->getRequest()->post('common', []) ?: [];
        $conditions = Yii::$app->getRequest()->post('conditions', []);

        if (!empty($conditions)) {
            $conditions = Json::decode($conditions);
        }

        $max_position = FoquzQuestion::find()->where(["poll_id" => $model->id])->max("position");
        $max_position = $max_position ? $max_position + 1 : 1;

        //список вопросов
        $questions = [];
        foreach (ArrayHelper::getValue($common, 'selected', []) as $k => $item) {
            $itemId = $item["id"];
            if (!isset($questions[$itemId])) {
                $questions[$itemId] = [
                    "isSystem"   => $item['isSystem'] == "true",
                    "name"       => $item['name'],
                    "conditions" => [],
                ];
            }
            $questions[$itemId]["conditions"][] = ["isCommon" => true];
        }
        foreach ($conditions as $indCond => $condition) {
            foreach ($condition['selectedContactPointsModels'] as $item) {
                $itemId = $item["id"];
                if (!isset($questions[$itemId])) {
                    $questions[$itemId] = [
                        "isSystem"   => $item['isSystem'] == "true",
                        "name"       => $item['name'],
                        "conditions" => [],
                    ];
                }
                $questions[$itemId]["conditions"][] = [
                    "isCommon"     => false,
                    "show"         => $condition["show"],
                    "index"        => $indCond,
                    "orderType"    => $condition["orderType"],
                    "orderSource"  => $condition["orderSource"],
                    "talkOperator" => $condition["talkOperator"],
                ];
            }
        }

        if (count(ArrayHelper::getValue($common, 'selected', [])) === 0) {
            $rows = FoquzQuestion::deleteAll([
                'poll_id'   => $id,
                'is_source' => 0,
            ]);

            FoquzQuestion::updateAll(
                ["poll_id" => null],
                [
                    'poll_id'   => $id,
                    'is_source' => 1,

                ]
            );
        }

        $rows = FoquzQuestion::deleteAll([
            "and",
            ['poll_id' => $id],
            ['is_source' => 0],
            ["or", ["not in", "point_id", array_keys($questions)], "point_id is null"]
        ]);

        FoquzQuestion::updateAll(
            ["poll_id" => null],
            [
                "and",
                ['poll_id' => $id],
                ['is_source' => 1],
                ["or", ["not in", "point_id", array_keys($questions)], "point_id is null"]
            ]
        );

        FoquzPointSelected::deleteAll(['foquz_poll_id' => $id]);
        FoquzPointCondition::deleteAll(['foquz_poll_id' => $id]);

        $createdConditions = [];

        foreach ($questions as $pointId => $question) {
            $point = FoquzPointItem::findOne($pointId);
            if ($point) {
                $isCondition = false;
                foreach ($question["conditions"] as $condition) {
                    $conditionId = null;

                    if (!$condition["isCommon"]) {
                        $indCond = $condition["index"];
                        if (isset($createdConditions[$indCond])) {
                            $conditionId = $createdConditions[$indCond];
                        } else {
                            $conditionModel = new FoquzPointCondition();
                            $conditionModel->foquz_poll_id = $id;
                            $conditionModel->order_type_id = $condition["orderType"];
                            $conditionModel->order_source = $condition["orderSource"];
                            $conditionModel->talk_operator = $condition["talkOperator"];
                            if ($conditionModel->save()) {
                                $conditionId = $conditionModel->id;
                                $createdConditions[$indCond] = $conditionId;
                            }

                        }
                    }

                    (new FoquzPointSelected([
                        'foquz_poll_id'       => $id,
                        'foquz_point_item_id' => $pointId,
                        'condition_id'        => $conditionId,
                    ]))->save();
                }

                $questionModel = FoquzQuestion::find()->where([
                    'poll_id'  => $id,
                    "point_id" => $point->id
                ])->one();
                if ($questionModel === null) {
                    FoquzQuestion::createQuestionFromPoint($model->id, $point, $question, $isCondition,
                        $max_position++);
                }
                $show_links = true;

            }
        }

        return $show_links ? [
            'question_link' => Url::to($model->getViewUrl(true))
        ] : [];
    }

    public function actionSaveInterScreen(int $id)
    {
        $model = $this->findModel($id);
        $pages = Yii::$app->getRequest()->post('pages', []);
        $error = false;

        if (empty($model->startPage)) {
            foreach (FoquzPollPage::getTypes() as $type => $value) {
                (new FoquzPollPage([
                    'type'          => $type,
                    'foquz_poll_id' => $model->id,
                    'description'   => 'Текст раздела',
                    'enabled'       => false
                ]))->save();
            }
            $model = $this->findModel($id);
        }
        $start = $model->startPage;
        $end = $model->endPage;

        foreach ($pages as $pageKey => $pageVal) {
            if ($pageKey === 'start') {
                $start->setAttributes($pageVal);
                if ($start->save()) {
                    $start->fillPromocodes($pageKey, $pageVal);
                    if (isset($pageVal['socNetworks'])) {
                        $socOptions = $start->socNetworksOptions ?? new FoquzPollPageSocialNetworksOptions(['foquz_poll_page_id' => $start->id]);
                        $socOptions->social_networks_enabled = $pageVal['socNetworks']['social_networks_enabled'] ?? false;
                        $socOptions->social_networks = json_encode($pageVal['socNetworks']['social_networks']);
                        $socOptions->form = $pageVal['socNetworks']['form'] ?? null;
                        $socOptions->style = $pageVal['socNetworks']['style'] ?? null;
                        $socOptions->substrate = $pageVal['socNetworks']['substrate'] ?? false;
                        $socOptions->total_counter = $pageVal['socNetworks']['total_counter'] ?? false;
                        $socOptions->location_for_total_counter = $pageVal['socNetworks']['location_for_total_counter'] ?? null;
                        $socOptions->for_each_counter = $pageVal['socNetworks']['for_each_counter'] ?? false;
                        $socOptions->location_for_each_counter = $pageVal['socNetworks']['location_for_each_counter'] ?? null;
                        $socOptions->size = $pageVal['socNetworks']['size'] ?? null;
                        $socOptions->save();
                    }
                } else {
                    $error = true;
                }

            } else {
                $end->setAttributes($pageVal);
                if ($end->save()) {
                    $end->fillPromocodes($pageKey, $pageVal);
                    if (isset($pageVal['socNetworks'])) {
                        $socOptions = $end->socNetworksOptions ?? new FoquzPollPageSocialNetworksOptions(['foquz_poll_page_id' => $end->id]);
                        $socOptions->social_networks_enabled = $pageVal['socNetworks']['social_networks_enabled'] ?? false;
                        $socOptions->social_networks = json_encode($pageVal['socNetworks']['social_networks']);
                        $socOptions->form = $pageVal['socNetworks']['form'] ?? null;
                        $socOptions->style = $pageVal['socNetworks']['style'] ?? null;
                        $socOptions->substrate = $pageVal['socNetworks']['substrate'] ?? false;
                        $socOptions->total_counter = $pageVal['socNetworks']['total_counter'] ?? false;
                        $socOptions->location_for_total_counter = $pageVal['socNetworks']['location_for_total_counter'] ?? null;
                        $socOptions->for_each_counter = $pageVal['socNetworks']['for_each_counter'] ?? false;
                        $socOptions->location_for_each_counter = $pageVal['socNetworks']['location_for_each_counter'] ?? null;
                        $socOptions->size = $pageVal['socNetworks']['size'] ?? null;
                        $socOptions->save();
                    }
                } else {
                    $error = true;
                }
            }
        }

        if (!$error) {
            if ($model->is_tmp) {
                $model->is_tmp = false;
                $model->save();
            }
            if ($model->is_template) {
                $model->is_template = false;
                $model->save();
            }
            return [];
        } else {
            return [
                'errors' => ArrayHelper::merge($end->getErrors(), $start->getErrors()),
            ];
        }
    }

    public function actionResetInterScreen(int $id)
    {
        $model = $this->findModel($id);
        $start = $model->startPage;
        $end = $model->endPage;

        return [
            'start' => $start,
            'end'   => $end,
        ];
    }

    public function actionResetCommon(int $id)
    {
        $model = $this->findModel($id);
        $form = new FoquzAutoPollForm(['poll' => $model, 'scenario' => FoquzAutoPollForm::SCENARIO_AUTO_POLL]);

        return [
            'dateRange'       => $form->dateRange,
            'goals_count'     => $model->goals_count,
            'end_of_question' => $model->end_of_question,
            'trigger'         => $model->trigger,
            'triggerTime'     => $form->triggerTime,
            'goal_text'       => $model->goal_text,
        ];
    }

    public function actionPagePreview($id, $type)
    {
        $this->layout = '@app/modules/foquz/views/layouts/answer.php';
        $model = $this->findModel($id);

        switch ($type) {
            case 'end':
                $page = $model->endPage;
                break;
            default:
                $page = $model->startPage;
                break;
        }

        return $this->render('_page_preview', [
            'model' => $model,
            'page'  => $page,
        ]);
    }

    public function actionMailings(int $id): string
    {

        $model = $this->findModel($id);

        if ($model->is_auto) {
            throw new NotFoundHttpException();
        }

        $userModels = User::find()
            ->select('user.*')
            ->addSelect(new \yii\db\Expression('IF(`user`.`name`= "" or `user`.`name` is null, `user`.`username`, `user`.`name`) as ifName'))
            ->leftJoin('company_staff', 'company_staff.user_id = user.id')
            ->where([
                'company_id' => $model->company_id,
            ])
            ->orderBy(['ifName' => SORT_ASC])
            ->all();

        $users = [];

        foreach ($userModels as $user) {
            $users[] = [
                'id'     => $user->id,
                'name'   => $user->name == '' ? $user->username : $user->name,
                'avatar' => $user->getThumbUploadUrl('avatar', 'preview'),
            ];
        }

        $mailings = FoquzPollMailingListSearch::searchList($model->id)
            ->asArray()->all();

        return $this->render('mailings', [
            'poll'         => $model,
            'channels'     => $model->channels,
            'users'        => $users,
            'mailings'     => $mailings,
            'clientsCount' => FoquzContact::find()->where(['company_id' => Yii::$app->user->identity->company->id])->count('id')
        ]);
    }

    public function actionWidgets(int $id): string
    {
        $model = $this->findModel($id);

        if ($model->is_auto) {
            throw new ForbiddenHttpException('Виджеты недоступны для автоматических опросов');
        }

        $hasWidgets = FoquzPollWidget::find()->where(['poll_id' => $model->id])->exists();

        return $this->render('widgets', [
            'model'      => $model,
            'hasWidgets' => $hasWidgets,
        ]);
    }

    public function actionGetAddresses(int $id)
    {
        $answers = [];
        $page = Yii::$app->request->get('page') ?? 1;
        $question = FoquzQuestion::findOne($id);

        if (!$question) {
            throw new NotFoundHttpException('Вопрос не найден');
        }

        $query = (new \yii\db\Query())
            ->select('foquz_contact.last_name, foquz_contact.first_name, foquz_contact.patronymic, foquz_contact.phone, foquz_contact.email, foquz_poll_answer.updated_at, orders.created_time, foquz_poll_answer_item.answer')
            ->addSelect(new Expression('IF(`orders`.`number` is null or `orders`.`number` = "", `orders`.`id`, `orders`.`number`) as orderId'))
            ->from('foquz_poll_answer')
            ->leftJoin('foquz_poll_answer_item', 'foquz_poll_answer_item.foquz_poll_answer_id = foquz_poll_answer.id')
            ->leftJoin('foquz_contact', 'foquz_contact.id = foquz_poll_answer.contact_id')
            ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
            ->where(['foquz_question_id' => $id])
            ->andWhere(['!=', 'answer', ''])
            ->orderBy('foquz_poll_answer.updated_at desc');
        if (Yii::$app->request->get('q')) {
            $query->andWhere([
                'OR',
                ['like', 'answer', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.phone', Yii::$app->request->get('q')],
                [
                    'like',
                    "TRIM(CONCAT(IFNULL(last_name, ''), ' ', IFNULL(first_name, ''), ' ', IFNULL(patronymic, '')))",
                    Yii::$app->request->get('q')
                ],
                ['like', 'foquz_contact.email', Yii::$app->request->get('q')],
                ['like', 'orders.number', Yii::$app->request->get('q')],
                ['like', 'orders.id', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(foquz_poll_answer.updated_at,"%d.%m.%Y")', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(orders.created_time,"%d.%m.%Y")', Yii::$app->request->get('q')],
            ]);
        }

        $query = $this->addAdditionalFiltersToModals($query, Yii::$app->request->get());

        if ($filterSetting = $this->getFilterSettings($question->poll_id)) {
            $query->andWhere([
                'foquz_poll_answer.id' => FoquzPollAnswer::filterByAnswer($filterSetting, $question->poll_id)
            ]);
        }

        $recordsCount = $query->count();

        $answersItemsDb = $query
            ->limit(30)
            ->offset(($page - 1) * 30)
            ->all();
        foreach ($answersItemsDb as $answerItem) {
            $answers[] = [
                'name'           => implode(' ',
                    [$answerItem['last_name'], $answerItem['first_name'], $answerItem['patronymic']]),
                'phone'          => FoquzContact::formatPhone($answerItem['phone']) ?? '',
                'email'          => $answerItem['email'] ?? '',
                'passedAt'       => date('d.m.Y', strtotime($answerItem['updated_at'])),
                'orderId'        => $answerItem['orderId'] ?? '',
                'orderCreatedAt' => $answerItem['created_time'] ? date('d.m.Y',
                    strtotime($answerItem['created_time'])) : '',
                'address'        => $answerItem['answer']
            ];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            'answers'  => $answers,
            'lastPage' => $recordsCount <= ($page * 30)
        ];
    }

    public function actionGetFiles(int $id)
    {
        $question = FoquzQuestion::findOne($id);
        if (!$question) {
            throw new HttpException(403, 'Доступ запрещён');
        }

        $poll = null;
        if (Yii::$app->user->isGuest) {
            $link = Yii::$app->request->get("link", "");
            if (strlen($link) < 15) {
                throw new HttpException(403, 'Доступ запрещён');
            }
            $linkData = FoquzPollStatsLink::find()->where(['like', 'link', $link])->one();
            if ($linkData === null || $linkData->right_level === FoquzPollStatsLink::RIGHT_BLOCK) {
                throw new HttpException(403, 'Доступ запрещён');
            }
            $poll = FoquzPoll::findOne($linkData->foquz_poll_id);
        } else {
            $company = Yii::$app->user->identity->company;
            $poll = FoquzPoll::find()->where(["company_id" => $company->id, "id" => $question->poll_id])->one();
        }

        if (!$poll) {
            throw new HttpException(403, 'Доступ запрещён');
        }

        $answers = [];
        $page = Yii::$app->request->get('page') ?? 1;
        //$question = FoquzQuestion::findOne($id);
        if ($question->poll_id !== $poll->id) {
            throw new HttpException(403, 'Доступ запрещён');
        }


        $answersItemsDbQuery = $question->getQuestionAnswerItems()
            ->leftJoin('foquz_poll_answer', 'foquz_poll_answer_item.foquz_poll_answer_id = foquz_poll_answer.id')
            ->orderBy('created_at desc');
        if (Yii::$app->request->get('q')) {
            $q = Yii::$app->request->get('q');
            if ($question->poll->is_auto) {
                $answersItemsDbQuery->leftJoin('foquz_contact', 'foquz_contact.id = foquz_poll_answer.contact_id')
                    ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id');
                $answersItemsDbQuery->andWhere([
                    'OR',
                    ['like', 'foquz_contact.phone', $q],
                    ['like', 'foquz_contact.email', $q],
                    ['like', 'foquz_poll_answer.order_id', $q]
                ]);
            } else {
                $answersItemsDbQuery->andWhere([
                    'like',
                    'DATE_FORMAT(`foquz_poll_answer`.updated_at,"%d.%m.%Y %H:%i")',
                    '%' . Yii::$app->getRequest()->get('q') . '%',
                    false
                ]);
            }
        }

        $answersItemsDbQuery = $this->addAdditionalFiltersToModals($answersItemsDbQuery, Yii::$app->request->get());
        $answersItemsDbQuery
            ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id');

        if ($filterSetting = $this->getFilterSettings($poll->id)) {
            $answersItemsDbQuery->andWhere([
                'foquz_poll_answer.id' => FoquzPollAnswer::filterByAnswer($filterSetting, $poll->id)
            ]);
        }

        $recordsCount = $answersItemsDbQuery->count();
        $answersItemsDb = $answersItemsDbQuery
            ->limit(72)
            ->offset(($page - 1) * 72)
            ->all();
        foreach ($answersItemsDb as $answerItem) {
            $files = [];
            /** @var FoquzPollAnswerItemFile $file */
            foreach ($answerItem->answerItemFiles as $file) {
                $files[] = [
                    'type'    => $file->type,
                    'url'     => $file->file_path,
                    'preview' => $file->image_link ?? $file->file_path
                ];
            }
            if (count($files) > 0) {
                $answers[] = [
                    'files'          => $files,
                    'name'           => $answerItem->foquzPollAnswer->contact ? implode(' ', [
                        $answerItem->foquzPollAnswer->contact->last_name,
                        $answerItem->foquzPollAnswer->contact->first_name,
                        $answerItem->foquzPollAnswer->contact->patronymic
                    ]) : '',
                    'phone'          => $answerItem->foquzPollAnswer->contact ? FoquzContact::formatPhone($answerItem->foquzPollAnswer->contact->phone) : '',
                    'email'          => $answerItem->foquzPollAnswer->contact ? $answerItem->foquzPollAnswer->contact->email : '',
                    'passedAt'       => date('d.m.Y', strtotime($answerItem->foquzPollAnswer->updated_at)),
                    'orderId'        => $answerItem->foquzPollAnswer->order->id ?? '',
                    'orderCreatedAt' => isset($answerItem->foquzPollAnswer->order) ? date('d.m.Y',
                        strtotime($answerItem->foquzPollAnswer->order->created_time)) : '',
                    'comment'        => $answerItem->answer,
                    'firstInfo'      => $question->poll->is_auto ? ($answerItem->foquzPollAnswer->contact->phone == '' ? $answerItem->foquzPollAnswer->contact->email : FoquzContact::formatPhone($answerItem->foquzPollAnswer->contact->phone)) : date('d.m.Y H:i',
                        strtotime($answerItem->foquzPollAnswer->updated_at)),
                    'secondInfo'     => $question->poll->is_auto ? '#' . $answerItem->foquzPollAnswer->order_id : '',
                ];
            }
        }
        Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            'answers'  => $answers,
            'lastPage' => $recordsCount <= ($page * 30)
        ];
    }

    public function actionGetProfiles(int $id)
    {
        $answers = [];
        $page = Yii::$app->request->get('page') ?? 1;
        $question = FoquzQuestion::findOne($id);
        if (!$question) {
            throw new NotFoundHttpException('Вопрос не найден');
        }
        $query = (new \yii\db\Query())
            ->select('foquz_contact.last_name, foquz_contact.first_name, foquz_contact.patronymic, foquz_contact.phone, foquz_contact.email, foquz_poll_answer.updated_at, orders.created_time, foquz_poll_answer_item.answer, filials.name filialName')
            ->addSelect(new Expression('IF(`orders`.`number` is null or `orders`.`number` = "", `orders`.`id`, `orders`.`number`) as orderId'))
            ->from('foquz_poll_answer')
            ->leftJoin('foquz_poll_answer_item', 'foquz_poll_answer_item.foquz_poll_answer_id = foquz_poll_answer.id')
            ->leftJoin('foquz_contact', 'foquz_contact.id = foquz_poll_answer.contact_id')
            ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
            ->leftJoin('filials', 'filials.id = foquz_poll_answer.answer_filial_id')
            ->where(['foquz_question_id' => $id])
            ->andWhere(['!=', 'answer', ''])
            ->orderBy('foquz_poll_answer_item.created_at desc');
        if (Yii::$app->request->get('q')) {
            $query->andWhere([
                'OR',
                ['like', 'answer', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.phone', Yii::$app->request->get('q')],
                [
                    'like',
                    "TRIM(CONCAT(IFNULL(last_name, ''), ' ', IFNULL(first_name, ''), ' ', IFNULL(patronymic, '')))",
                    Yii::$app->request->get('q')
                ],
                ['like', 'foquz_contact.email', Yii::$app->request->get('q')],
                ['like', 'orders.number', Yii::$app->request->get('q')],
                ['like', 'orders.id', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(foquz_poll_answer.updated_at,"%d.%m.%Y")', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(orders.created_time,"%d.%m.%Y")', Yii::$app->request->get('q')],
            ]);
        }

        $query = $this->addAdditionalFiltersToModals($query, Yii::$app->request->get());

        if ($filterSetting = $this->getFilterSettings($question->poll_id)) {
            $query->andWhere([
                'foquz_poll_answer.id' => FoquzPollAnswer::filterByAnswer($filterSetting, $question->poll_id)
            ]);
        }

        $recordsCount = $query->count();

        $answersItemsDb = $query
            ->limit(30)
            ->offset(($page - 1) * 30)
            ->all();

        $propertiesDb = $question->getFormFields()->select('id, name')->all();

        foreach ($answersItemsDb as $answerItem) {
            $stProperties = [];
            $dataJson = json_decode($answerItem['answer']);
            if (is_object($dataJson)) {
                $notEmptyValues = array_filter(ArrayHelper::toArray($dataJson), static function ($value) {
                    return $value !== '';
                });
                if (count($notEmptyValues) === 0) {
                    continue;
                }
            }
            foreach ($propertiesDb as $prop) {
                $id = $prop->id;
                $stProperties[] = isset($dataJson->$id) ? ($dataJson->$id === '' ? null : $dataJson->$id) : null;
            }
            $answers[] = [
                'name'           => implode(' ',
                        [$answerItem['last_name'], $answerItem['first_name'], $answerItem['patronymic']]) ?? '',
                'phone'          => FoquzContact::formatPhone($answerItem['phone']) ?? '',
                'email'          => $answerItem['email'] ?? '',
                'passedAt'       => date('d.m.Y', strtotime($answerItem['updated_at'])),
                'orderId'        => $answerItem['orderId'] ?? '',
                'orderCreatedAt' => $answerItem['created_time'] ? date('d.m.Y',
                    strtotime($answerItem['created_time'])) : '',
                'properties'     => $stProperties,
                'filialName'     => $answerItem['filialName'],
            ];
        }
        $properties = ArrayHelper::getColumn($propertiesDb, 'name');

        Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            'properties' => $properties,
            'profiles'   => $answers,
            'lastPage'   => $recordsCount <= ($page * 30)
        ];
    }

    public function actionGetPriorities(int $id)
    {
        $answers = [];
        $page = Yii::$app->request->get('page') ?? 1;
        $question = FoquzQuestion::findOne($id);

        $query = (new \yii\db\Query())
            ->select('foquz_contact.last_name, foquz_contact.first_name, foquz_contact.patronymic, foquz_contact.phone, foquz_contact.email, foquz_poll_answer.updated_at, orders.created_time, foquz_poll_answer_item.foquz_poll_answer_id, foquz_poll_answer_item.answer, foquz_poll_answer_item.detail_item, foquz_poll_answer_item.points, foquz_poll_answer_item.self_variant')
            ->addSelect(new Expression('IF(`orders`.`number` is null or `orders`.`number` = "", `orders`.`id`, `orders`.`number`) as orderId'))
            ->from('foquz_poll_answer')
            ->leftJoin('foquz_poll_answer_item', 'foquz_poll_answer_item.foquz_poll_answer_id = foquz_poll_answer.id')
            ->leftJoin('foquz_contact', 'foquz_contact.id = foquz_poll_answer.contact_id')
            ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
            ->where(['foquz_question_id' => $id])
            ->orderBy('foquz_poll_answer_item.created_at desc');
        if (Yii::$app->request->get('q')) {
            $query->andWhere([
                'OR',
                ['like', 'answer', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.phone', Yii::$app->request->get('q')],
                [
                    'like',
                    "TRIM(CONCAT(IFNULL(last_name, ''), ' ', IFNULL(first_name, ''), ' ', IFNULL(patronymic, '')))",
                    Yii::$app->request->get('q')
                ],
                ['like', 'foquz_contact.email', Yii::$app->request->get('q')],
            ]);
        }
        $points = Yii::$app->request->get('points');
        if ($points && is_array($points)) {
            $query->andWhere(['between', 'foquz_poll_answer_item.points', $points[0], $points[1]]);
        }
        $query = $this->addAdditionalFiltersToModals($query, Yii::$app->request->get());

        if ($filterSetting = $this->getFilterSettings($question->poll_id)) {
            $query->andWhere([
                'foquz_poll_answer.id' => FoquzPollAnswer::filterByAnswer($filterSetting, $question->poll_id)
            ]);
        }

        $recordsCount = $query->count();

        $answersItemsDb = $query
            ->limit(30)
            ->offset(($page - 1) * 30)
            ->all();
        foreach ($answersItemsDb as $answerItem) {
            $variants = [];
            if ($question->getMainDonor()) {
                $detailVariants = json_decode($answerItem['detail_item']);
                foreach ($detailVariants as $variantId) {
                    if ($variantId == '-1') {
                        $donorAnswer = FoquzPollAnswerItem::findOne([
                            'foquz_poll_answer_id' => $answerItem['foquz_poll_answer_id'],
                            'foquz_question_id'    => $question->getMainDonor()->id,
                        ]);
                        $variants[] = [
                            'id'      => -1,
                            'variant' => $donorAnswer->self_variant ?? null,
                        ];
                    } elseif ($question->getMainDonor()->main_question_type !== FoquzQuestion::TYPE_DICTIONARY && $variant = FoquzQuestionDetail::findOne($variantId)) {
                        $variants[] = [
                            'id'      => $variantId,
                            'variant' => $variant->question
                        ];
                    } elseif ($question->getMainDonor()->main_question_type === FoquzQuestion::TYPE_DICTIONARY && $variant = DictionaryElement::findOne($variantId)) {
                        $variants[] = [
                            'id'      => $variantId,
                            'variant' => $variant->fullPath
                        ];
                    }
                }
            } elseif ($answerItem['detail_item']) {
                $details = json_decode($answerItem['detail_item']);
                foreach ($details as $detail) {
                    $detailItem = FoquzQuestionDetail::findOne($detail);
                    if ($detailItem) {
                        $variants[] = [
                            'id'      => $detailItem->id,
                            'variant' => $detailItem->question
                        ];
                    }
                }
            } else {
                $variants = json_decode($answerItem['answer']);
            }
            $answers[] = [
                'name'           => implode(' ',
                        [$answerItem['last_name'], $answerItem['first_name'], $answerItem['patronymic']]) ?? '',
                'phone'          => FoquzContact::formatPhone($answerItem['phone']) ?? '',
                'email'          => $answerItem['email'] ?? '',
                'passedAt'       => date('d.m.Y', strtotime($answerItem['updated_at'])),
                'orderId'        => $answerItem['orderId'] ?? '',
                'orderCreatedAt' => $answerItem['created_time'] ? date('d.m.Y',
                    strtotime($answerItem['created_time'])) : '',
                'answer'         => [
                    'variants' => $variants
                ],
                'points'         => $answerItem['points'],
                'comment'        => $answerItem['self_variant'],
            ];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            'answers'  => $answers,
            'lastPage' => $recordsCount <= ($page * 30)
        ];
    }

    public function actionGetVariants(int $id)
    {
        $question = FoquzQuestion::findOne($id);
        if (!$question) {
            throw new HttpException(403, 'Доступ запрещён');
        }
        $poll = null;
        if (Yii::$app->user->isGuest) {
            $link = Yii::$app->request->get("link", "");
            if (strlen($link) < 15) {
                throw new HttpException(403, 'Доступ запрещён');
            }
            $linkData = FoquzPollStatsLink::find()->where(['like', 'link', $link])->one();
            if ($linkData === null || $linkData->right_level === FoquzPollStatsLink::RIGHT_BLOCK) {
                throw new HttpException(403, 'Доступ запрещён');
            }
            $poll = FoquzPoll::findOne($linkData->foquz_poll_id);
        } else {
            $company = Yii::$app->user->identity->company;
            $poll = FoquzPoll::find()->where(["company_id" => $company->id, "id" => $question->poll_id])->one();
        }
        if (!$poll) {
            throw new HttpException(403, 'Доступ запрещён');
        }

        $answers = [];
        $page = Yii::$app->request->get('page') ?? 1;
        /** @var FoquzQuestion $question */
        $question = FoquzQuestion::findOne($id);
        if ($question->poll_id !== $poll->id) {
            throw new HttpException(403, 'Доступ запрещён');
        }
        if (!$question) {
            throw new NotFoundHttpException('Question not found');
        }

        $query = (new \yii\db\Query())
            ->select('foquz_contact.last_name, foquz_contact.first_name, foquz_contact.patronymic, foquz_contact.phone, foquz_contact.email, foquz_poll_answer.updated_at, orders.created_time, foquz_poll_answer_item.foquz_poll_answer_id, foquz_poll_answer_item.answer, foquz_poll_answer_item.detail_item, foquz_poll_answer_item.self_variant, foquz_poll_answer_item.points, foquz_poll_answer_item.skipped, filials.name filialName')
            ->addSelect(new Expression('IF(`orders`.`number` is null or `orders`.`number` = "", `orders`.`id`, `orders`.`number`) as orderId'))
            ->from('foquz_poll_answer')
            ->leftJoin('foquz_poll_answer_item', 'foquz_poll_answer_item.foquz_poll_answer_id = foquz_poll_answer.id')
            ->leftJoin('foquz_contact', 'foquz_contact.id = foquz_poll_answer.contact_id')
            ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
            ->leftJoin('filials', 'filials.id = foquz_poll_answer.answer_filial_id')
            ->where([
                'AND',
                ['foquz_question_id' => $id],
                ['OR', ['IS NOT', 'foquz_poll_answer_item.detail_item', null], ['foquz_poll_answer_item.skipped' => 1]]
            ])
            ->orderBy('foquz_poll_answer_item.created_at desc');
        if (Yii::$app->request->get('q')) {
            $query->andWhere([
                'OR',
                ['like', 'foquz_contact.phone', Yii::$app->request->get('q')],
                [
                    'like',
                    "TRIM(CONCAT(IFNULL(last_name, ''), ' ', IFNULL(first_name, ''), ' ', IFNULL(patronymic, '')))",
                    Yii::$app->request->get('q')
                ],
                ['like', 'foquz_contact.email', Yii::$app->request->get('q')],
            ]);
        }
        $points = Yii::$app->request->get('points');
        if ($points && is_array($points)) {
            $query->andWhere(['between', 'foquz_poll_answer_item.points', $points[0], $points[1]]);
        }
        $query = $this->addAdditionalFiltersToModals($query, Yii::$app->request->get());

        if ($filterSetting = $this->getFilterSettings($poll->id)) {
            $query->andWhere(['foquz_poll_answer.id' => FoquzPollAnswer::filterByAnswer($filterSetting, $poll->id)]);
        }

        $recordsCount = $query->count();

        $answersItemsDb = $query
            ->limit(30)
            ->offset(($page - 1) * 30)
            ->all();
        foreach ($answersItemsDb as $answerItem) {
            $variants = json_decode($answerItem['detail_item']);
            $deletedVariants = json_decode($question->deleted_detail_question) ?? [];
            if (is_string($variants)) {
                $variants = json_decode($variants);
            }
            $variantsArray = [];
            if ($question->main_question_type === FoquzQuestion::TYPE_FILIAL && $variants) {
                $filial = Filial::findOne($variants[0]);
                $variantsArray[] = [
                    'id'         => $variants[0],
                    'variant'    => $filial ? $filial->name : null,
                    'is_deleted' => in_array($variants[0], $deletedVariants) ? 1 : 0
                ];
            } elseif ($question->main_question_type === FoquzQuestion::TYPE_DICTIONARY && $variants) {
                foreach ($variants as $variant) {
                    $element = DictionaryElement::findOne($variant);
                    $variantsArray[] = [
                        'id'      => $variant,
                        'variant' => $element->title ?? null,
                    ];
                }
            } else {
                if (is_array($variants)) {
                    foreach ($variants as $variantId) {
                        if ($variantId == '-1') {
                            $donorAnswer = FoquzPollAnswerItem::findOne([
                                'foquz_poll_answer_id' => $answerItem['foquz_poll_answer_id'],
                                'foquz_question_id'    => $question->getMainDonor()->id,
                            ]);
                            $variantsArray[] = [
                                'id'      => -1,
                                'variant' => $donorAnswer->self_variant ?? null,
                            ];
                        } elseif ($question->donor && $question->getMainDonor()->main_question_type !== FoquzQuestion::TYPE_DICTIONARY && $variant = FoquzQuestionDetail::findOne($variantId)) {
                            $variantsArray[] = [
                                'id'          => $variantId,
                                'variant'     => $variant->question,
                                'is_deleted'  => $variant->is_deleted ?? null,
                                'file_id'     => $variant->file->id ?? null,
                                'file_url'    => $variant->file->fileUrl ?? null,
                                'preview_url' => $variant->file->previewUrl ?? null,
                            ];
                        } elseif ($question->donor && $question->getMainDonor()->main_question_type === FoquzQuestion::TYPE_DICTIONARY && $variant = DictionaryElement::findOne($variantId)) {
                            $variantsArray[] = [
                                'id'      => $variantId,
                                'variant' => $variant->fullPath
                            ];
                        } else {
                            $variant = FoquzQuestionDetail::findOne($variantId);
                            if ($variant) {
                                $variantsArray[] = [
                                    'id'          => $variantId,
                                    'variant'     => $variant->question,
                                    'is_deleted'  => $variant->is_deleted ?? null,
                                    'file_id'     => $variant->file->id ?? null,
                                    'file_url'    => $variant->file->fileUrl ?? null,
                                    'preview_url' => $variant->file->previewUrl ?? null,
                                ];
                            }
                        }
                    }
                }
                if ($answerItem['self_variant'] !== null && $answerItem['self_variant'] !== '') {
                    $variantsArray[] = [
                        'id'      => 'self',
                        'variant' => $answerItem['self_variant']
                    ];
                }
                $answerItem['max_points'] = null;
                if ($poll->point_system) {
                    $answerItem['without_points'] = false;
                    $detailsWithoutPoints = FoquzQuestionDetail::find()
                        ->select('id')
                        ->where(['foquz_question_id' => $question->id])
                        ->andWhere(['without_points' => true])
                        ->column();
                    if (!empty($detailsWithoutPoints)) {
                        $answerDetails = $answerItem['detail_item'];
                        if (is_string($answerDetails)) {
                            $answerDetails = trim($answerDetails, '"');
                            $answerDetails = str_replace('\"', '"', $answerDetails);
                            $answerDetails = json_decode($answerDetails, true) ?? [];
                        }
                        if (!empty($answerDetails) && count(array_diff($answerDetails, $detailsWithoutPoints)) === 0) {
                            $answerItem['without_points'] = true;
                        }
                    }
                }
            }
            $answer = [
                'name'           => implode(' ',
                        [$answerItem['last_name'], $answerItem['first_name'], $answerItem['patronymic']]) ?? '',
                'phone'          => FoquzContact::formatPhone($answerItem['phone']) ?? '',
                'email'          => $answerItem['email'] ?? '',
                'passedAt'       => date('d.m.Y', strtotime($answerItem['updated_at'])),
                'orderId'        => $answerItem['orderId'] ?? '',
                'orderCreatedAt' => $answerItem['created_time'] ? date('d.m.Y',
                    strtotime($answerItem['created_time'])) : '',
                'answer'         => $variantsArray,
                'comment'        => $answerItem['answer'] ?? null,
                'points'         => $answerItem['points'],
                'without_points' => $answerItem['without_points'] ?? null,
                'skipped'        => (int)$answerItem['skipped'],
                'filialName'     => $answerItem['filialName'],
            ];
            if (in_array($question->main_question_type, [FoquzQuestion::TYPE_FILIAL, FoquzQuestion::TYPE_DICTIONARY])) {
                $answer['comment'] = $answerItem['self_variant'];
            }
            $answers[] = $answer;
        }
        Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            'answers'  => $answers,
            'lastPage' => $recordsCount <= ($page * 30)
        ];
    }

    public function actionGetChooseMedia(int $id)
    {
        $answers = [];
        $page = Yii::$app->request->get('page') ?? 1;
        $question = FoquzQuestion::findOne($id);
        if (!$question) {
            throw new NotFoundHttpException('Вопрос не найден');
        }

        $query = (new \yii\db\Query())
            ->select('foquz_contact.last_name, foquz_contact.first_name, foquz_contact.patronymic, foquz_contact.phone, foquz_contact.email, foquz_poll_answer.updated_at, orders.created_time, foquz_poll_answer_item.answer, foquz_poll_answer_item.self_variant, foquz_poll_answer_item.points, foquz_poll_answer_item.skipped')
            ->addSelect(new Expression('IF(`orders`.`number` is null or `orders`.`number` = "", `orders`.`id`, `orders`.`number`) as orderId'))
            ->from('foquz_poll_answer')
            ->leftJoin('foquz_poll_answer_item', 'foquz_poll_answer_item.foquz_poll_answer_id = foquz_poll_answer.id')
            ->leftJoin('foquz_contact', 'foquz_contact.id = foquz_poll_answer.contact_id')
            ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
            ->where(['foquz_question_id' => $id])
            ->andWhere([
                'OR',
                ['AND', ['!=', 'answer', ''], ['!=', 'answer', '[]']],
                ['foquz_poll_answer_item.skipped' => 1]
            ])
            ->orderBy('foquz_poll_answer_item.created_at desc');
        if (Yii::$app->request->get('q')) {
            $query->andWhere([
                'OR',
                ['like', 'answer', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.phone', Yii::$app->request->get('q')],
                [
                    'like',
                    "TRIM(CONCAT(IFNULL(last_name, ''), ' ', IFNULL(first_name, ''), ' ', IFNULL(patronymic, '')))",
                    Yii::$app->request->get('q')
                ],
                ['like', 'foquz_contact.email', Yii::$app->request->get('q')],
                ['like', 'self_variant', Yii::$app->request->get('q')],
            ]);
        }
        $points = Yii::$app->request->get('points');
        if ($points && is_array($points)) {
            $query->andWhere(['between', 'foquz_poll_answer_item.points', $points[0], $points[1]]);
        }

        $query = $this->addAdditionalFiltersToModals($query, Yii::$app->request->get());

        if ($filterSetting = $this->getFilterSettings($question->poll_id)) {
            $query->andWhere([
                'foquz_poll_answer.id' => FoquzPollAnswer::filterByAnswer($filterSetting, $question->poll_id)
            ]);
        }
        //exit;

        $recordsCount = $query->count();

        $answersItemsDb = $query
            ->limit(30)
            ->offset(($page - 1) * 30)
            ->all();
        foreach ($answersItemsDb as $answerItem) {
            $chooseAnswers = json_decode($answerItem['answer']);
            $chooseIds = $chooseAnswers;
            if ($chooseAnswers) {
                foreach ($chooseAnswers as $k => $chooseAnswer) {
                    $foquzQuestionMedia = FoquzQuestionFile::findOne($chooseAnswer);
                    $chooseAnswers[$k] = ($foquzQuestionMedia->type === 'image' ? 'Изображение ' : 'Видео ') . $foquzQuestionMedia->position;
                }
            }
            $answers[] = [
                'name'           => implode(' ',
                        [$answerItem['last_name'], $answerItem['first_name'], $answerItem['patronymic']]) ?? '',
                'phone'          => FoquzContact::formatPhone($answerItem['phone']) ?? '',
                'email'          => $answerItem['email'] ?? '',
                'passedAt'       => date('d.m.Y', strtotime($answerItem['updated_at'])),
                'orderId'        => $answerItem['orderId'] ?? '',
                'orderCreatedAt' => $answerItem['created_time'] ? date('d.m.Y',
                    strtotime($answerItem['created_time'])) : '',
                'answer'         => $chooseAnswers,
                'ids'            => $chooseIds,
                'comment'        => $answerItem['self_variant'],
                'points'         => $answerItem['points'],
                'skipped'        => (int)$answerItem['skipped'],
            ];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            'answers'  => $answers,
            'lastPage' => $recordsCount <= ($page * 30)
        ];
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionGetStarVariantAnswers(int $id): array
    {
        $question = FoquzQuestion::findOne($id);
        if (!$question) {
            throw new HttpException(403, 'Доступ запрещён');
        }
        $poll = null;
        if (Yii::$app->user->isGuest) {
            $link = Yii::$app->request->get("link", "");
            if (strlen($link) < 15) {
                throw new HttpException(403, 'Доступ запрещён');
            }
            $linkData = FoquzPollStatsLink::find()->where(['like', 'link', $link])->one();
            if ($linkData === null || $linkData->right_level === FoquzPollStatsLink::RIGHT_BLOCK) {
                throw new HttpException(403, 'Доступ запрещён');
            }
            $poll = FoquzPoll::findOne($linkData->foquz_poll_id);
        } else {
            $company = Yii::$app->user->identity->company;
            $poll = FoquzPoll::find()->where(["company_id" => $company->id, "id" => $question->poll_id])->one();
        }
        if (!$poll) {
            throw new HttpException(403, 'Доступ запрещён');
        }
        //$id = $poll->id;

        $answers = $this->getAnswers($id);
        foreach ($answers['answers'] as $answerKey => &$answer) {
            if ($answer['answer']) {
                $notEmptyValues = array_filter($answer['answer'], static function ($value) {
                    return (int)$value !== 0;
                });
                if (count($notEmptyValues) === 0) {
                    unset($answers['answers'][$answerKey]);
                    continue;
                }
                $array = $extra = [];
                if (isset($answer['answer']['extra'])) {
                    $extra = $answer['answer']['extra'];
                    unset($answer['answer']['extra']);
                }
                foreach ($answer['answer'] as $detailId => $value) {
                    if ($detailId == '-1') {
                        $question = FoquzQuestion::findOne($id);
                        if ($question && $question->donor && $question->donor !== $question->id) {
                            $donor = $question->getMainDonor();
                            if ($donor) {
                                $array[] = [
                                    'id'    => -1,
                                    'name'  => $donor->self_variant_text ?: 'Свой вариант',
                                    'value' => $value,
                                ];
                            }
                        }
                    } elseif (!$question->donor || $question->getMainDonor()->main_question_type !== FoquzQuestion::TYPE_DICTIONARY) {
                        $questionDetail = FoquzQuestionDetail::findOne($detailId);
                        if (!$questionDetail) {
                            throw new NotFoundHttpException('Foquz detail not found');
                        }
                        if ($questionDetail->extra_question) {
                            continue;
                        }

                        if ($value === 'null') {
                            $value = 'Респондент отказался от оценки';
                        } elseif ($value === '0') {
                            $value = 'Пропуск оценки';
                        }
                        $array[] = [
                            'id'         => $questionDetail->id,
                            'name'       => $questionDetail->question,
                            'value'      => $value,
                            'is_deleted' => $questionDetail->is_deleted,
                        ];
                    } else {
                        $dictionaryElement = DictionaryElement::findOne($detailId);
                        if (!$dictionaryElement) {
                            throw new NotFoundHttpException('Dictionary element not found');
                        }

                        if ($value === 'null') {
                            $value = 'Респондент отказался от оценки';
                        } elseif ($value === '0') {
                            $value = 'Пропуск оценки';
                        }
                        $array[] = [
                            'id'    => $dictionaryElement->id,
                            'name'  => $dictionaryElement->fullPath,
                            'value' => $value,
                        ];
                    }
                }
                if ($extra) {
                    foreach ($array as $key => $item) {
                        if (isset($extra[$item['id']])) {
                            foreach ($extra[$item['id']] as $extraKey => $extraItem) {
                                if ($extraKey === 'self_variant') {
                                    $array[$key]['extra']['self_variant'] = $extraItem;
                                } elseif ($extraKey === 'answer') {
                                    $array[$key]['extra']['answer'] = $extraItem;
                                } else {
                                    $array[$key]['extra'][] = FoquzQuestionDetail::findOne($extraItem)->question;
                                }
                            }
                        }
                    }
                }
                $answer['answer'] = $array;
            }
        }
        $answers['answers'] = array_values($answers['answers']);
        return $answers;
    }

    public function actionGetGalleryAnswers(int $id): array
    {
        return $this->getAnswers($id);
    }

    private function getAnswers(int $id): array
    {
        $question = FoquzQuestion::findOne($id);
        if (!$question) {
            throw new HttpException(403, 'Доступ запрещён');
        }

        $poll = null;
        if (Yii::$app->user->isGuest) {
            $link = Yii::$app->request->get("link", "");
            if (strlen($link) < 15) {
                throw new HttpException(403, 'Доступ запрещён');
            }
            $linkData = FoquzPollStatsLink::find()->where(['like', 'link', $link])->one();
            if ($linkData === null || $linkData->right_level === FoquzPollStatsLink::RIGHT_BLOCK) {
                throw new HttpException(403, 'Доступ запрещён');
            }
            $poll = FoquzPoll::findOne($linkData->foquz_poll_id);
        } else {
            $company = Yii::$app->user->identity->company;
            $poll = FoquzPoll::find()->where(["company_id" => $company->id, "id" => $question->poll_id])->one();
        }

        if (!$poll) {
            throw new HttpException(403, 'Доступ запрещён');
        }

        $answers = [];
        $page = Yii::$app->request->get('page') ?? 1;
        //$question = FoquzQuestion::findOne($id);

        $query = (new \yii\db\Query())
            ->select('foquz_contact.last_name, foquz_contact.first_name, foquz_contact.patronymic, foquz_contact.phone, foquz_contact.email, foquz_poll_answer.updated_at, orders.created_time, foquz_poll_answer_item.foquz_poll_answer_id, foquz_poll_answer_item.answer, foquz_poll_answer_item.self_variant, foquz_poll_answer_item.skipped')
            ->addSelect(new Expression('IF(`orders`.`number` is null or `orders`.`number` = "", `orders`.`id`, `orders`.`number`) as orderId'))
            ->from('foquz_poll_answer')
            ->leftJoin('foquz_poll_answer_item', 'foquz_poll_answer_item.foquz_poll_answer_id = foquz_poll_answer.id')
            ->leftJoin('foquz_contact', 'foquz_contact.id = foquz_poll_answer.contact_id')
            ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
            ->where(['foquz_question_id' => $id, 'foquz_poll_id' => $poll->id])
            ->andWhere([
                'OR',
                ['AND', ['!=', 'answer', ''], ['!=', 'answer', '[]']],
                ['foquz_poll_answer_item.skipped' => 1]
            ])
            ->orderBy('foquz_poll_answer_item.created_at desc');
        if (Yii::$app->request->get('q')) {
            $query->andWhere([
                'OR',
                ['like', 'foquz_contact.phone', Yii::$app->request->get('q')],
                [
                    'like',
                    "TRIM(CONCAT(IFNULL(last_name, ''), ' ', IFNULL(first_name, ''), ' ', IFNULL(patronymic, '')))",
                    Yii::$app->request->get('q')
                ],
                ['like', 'foquz_contact.email', Yii::$app->request->get('q')],
                ['like', 'foquz_poll_answer_item.self_variant', Yii::$app->request->get('q')],
            ]);
        }

        $query = $this->addAdditionalFiltersToModals($query, Yii::$app->request->get());
        if ($filterSetting = $this->getFilterSettings($poll->id)) {
            $query->andWhere(['foquz_poll_answer.id' => FoquzPollAnswer::filterByAnswer($filterSetting, $poll->id)]);
        }
        $recordsCount = $query->count();

        $answersItemsDb = $query
            ->limit(30)
            ->offset(($page - 1) * 30)
            ->all();
        foreach ($answersItemsDb as $answerItem) {
            $chooseAnswers = json_decode($answerItem['answer'], true);
            $selfAnswerValue = $answerItem['self_variant'];
            if ($question->donor && $question->donor !== $question->id) {
                $details = [];
                $recipientDetails = RecipientQuestionDetail::find()->where(['recipient_id' => $id])->orderBy('position')->all();
                /** @var RecipientQuestionDetail $recipientDetail */
                foreach ($recipientDetails as $recipientDetail) {
                    if ($recipientDetail->question_detail_id === null && array_key_exists('-1', $chooseAnswers)) {
                        $details['-1'] = $chooseAnswers['-1'];
                    } else {
                        if (array_key_exists($recipientDetail->question_detail_id ?: $recipientDetail->dictionary_element_id,
                            $chooseAnswers)) {
                            $details[$recipientDetail->question_detail_id ?: $recipientDetail->dictionary_element_id] = $chooseAnswers[$recipientDetail->question_detail_id ?: $recipientDetail->dictionary_element_id];
                        }
                    }
                }
                if (!empty($chooseAnswers['extra'])) {
                    $details['extra'] = $chooseAnswers['extra'];
                }
                $chooseAnswers = $details;
                $donorAnswer = FoquzPollAnswerItem::findOne([
                    'foquz_poll_answer_id' => $answerItem['foquz_poll_answer_id'],
                    'foquz_question_id'    => $question->getMainDonor()->id,
                ]);
                if ($donorAnswer) {
                    $selfAnswerValue = $donorAnswer->self_variant;
                }
            }
            $answers[] = [
                'name'            => trim(implode(' ',
                    [$answerItem['last_name'], $answerItem['first_name'], $answerItem['patronymic']]) ?? ''),
                'phone'           => FoquzContact::formatPhone($answerItem['phone']) ?? '',
                'email'           => $answerItem['email'] ?? '',
                'passedAt'        => date('d.m.Y', strtotime($answerItem['updated_at'])),
                'orderId'         => $answerItem['orderId'] ?? '',
                'orderCreatedAt'  => $answerItem['created_time'] ? date('d.m.Y',
                    strtotime($answerItem['created_time'])) : '',
                'answer'          => $chooseAnswers,
                'media'           => $question->grabMediaContent(),
                'comment'         => $answerItem['self_variant'],
                'selfAnswerValue' => $selfAnswerValue,
                'skipped'         => (int)$answerItem['skipped'],
            ];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            'answers'  => $answers,
            'lastPage' => $recordsCount <= ($page * 30)
        ];
    }

    private function getUserDataWithDecodedAnswer($id, $page)
    {
        $answers = [];
        $question = FoquzQuestion::findOne($id);
        if (!$question) {
            throw new NotFoundHttpException('Question not found');
        }

        $query = (new \yii\db\Query())
            ->select('foquz_contact.last_name, foquz_contact.first_name, foquz_contact.patronymic, foquz_contact.phone, foquz_contact.email, foquz_poll_answer.updated_at, orders.created_time, foquz_poll_answer_item.foquz_poll_answer_id, foquz_poll_answer_item.answer, foquz_poll_answer_item.detail_item, foquz_poll_answer_item.self_variant, foquz_poll_answer_item.points, foquz_poll_answer_item.skipped')
            ->addSelect(new Expression('IF(`orders`.`number` is null or `orders`.`number` = "", `orders`.`id`, `orders`.`number`) as orderId'))
            ->from('foquz_poll_answer')
            ->leftJoin('foquz_poll_answer_item', 'foquz_poll_answer_item.foquz_poll_answer_id = foquz_poll_answer.id')
            ->leftJoin('foquz_contact', 'foquz_contact.id = foquz_poll_answer.contact_id')
            ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
            ->where(['foquz_question_id' => $id])
            ->andWhere([
                'OR',
                ['and', ['<>', 'answer', '[]'], ['<>', 'answer', '""'], ['!=', 'answer', '']],
                ['foquz_poll_answer_item.skipped' => 1]
            ])
            ->orderBy('foquz_poll_answer_item.created_at desc');
        if (Yii::$app->request->get('q')) {
            $query->andWhere([
                'OR',
                ['like', 'foquz_poll_answer_item.self_variant', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.phone', Yii::$app->request->get('q')],
                [
                    'like',
                    "TRIM(CONCAT(IFNULL(last_name, ''), ' ', IFNULL(first_name, ''), ' ', IFNULL(patronymic, '')))",
                    Yii::$app->request->get('q')
                ],
                ['like', 'foquz_contact.email', Yii::$app->request->get('q')],
                ['like', 'orders.number', Yii::$app->request->get('q')],
                ['like', 'orders.id', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(foquz_poll_answer.updated_at,"%d.%m.%Y")', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(orders.created_time,"%d.%m.%Y")', Yii::$app->request->get('q')],
            ]);
        }

        $points = Yii::$app->request->get('points');
        if ($points && is_array($points)) {
            $query->andWhere(['between', 'foquz_poll_answer_item.points', $points[0], $points[1]]);
        }

        if ($filials = Yii::$app->request->get('filials')) {
            $query->andWhere(['foquz_poll_answer.answer_filial_id' => $filials]);
        }
        if ($filialCategories = Yii::$app->request->get('filialCategories')) {
            $query->andWhere(['foquz_poll_answer.answer_filial_id' => Filial::find()->select('id')->where(['category_id' => $filialCategories])]);
        }
        //$query = $this->addAdditionalFiltersToModals($query, Yii::$app->request->get());

        if ($filterSetting = $this->getFilterSettings($question->poll_id)) {
            $query->andWhere([
                'foquz_poll_answer.id' => FoquzPollAnswer::filterByAnswer($filterSetting, $question->poll_id)
            ]);
        }

        $matrixRowsDonorID = [];
        $matrixColumnsDonorID = [];
        if ($question->main_question_type === FoquzQuestion::TYPE_3D_MATRIX && $question->donor_rows) {
            $matrixRows = FoquzQuestionMatrixElement::find()
                ->where(['foquz_question_id' => $question->id])
                ->andWhere(['type_id' => FoquzQuestionMatrixElement::TYPE_ROW])
                ->all();
            if ($question->rowsDonor && $question->rowsDonor->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                $matrixRowsDonorID = ArrayHelper::map($matrixRows, 'id', 'donor_dictionary_element_id');
            } else {
                $matrixRowsDonorID = ArrayHelper::map($matrixRows, 'id', 'donor_variant_id');
            }
            $matrixRowsDonorID = array_map(static function ($value) {
                return $value ?? '-1';
            }, $matrixRowsDonorID);
        }
        if ($question->main_question_type === FoquzQuestion::TYPE_3D_MATRIX && $question->donor_columns) {
            $matrixColumns = FoquzQuestionMatrixElement::find()
                ->where(['foquz_question_id' => $question->id])
                ->andWhere(['type_id' => FoquzQuestionMatrixElement::TYPE_COLUMN])
                ->all();
            if ($question->columnsDonor && $question->columnsDonor->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                $matrixColumnsDonorID = ArrayHelper::map($matrixColumns, 'id', 'donor_dictionary_element_id');
            } else {
                $matrixColumnsDonorID = ArrayHelper::map($matrixColumns, 'id', 'donor_variant_id');
            }
            $matrixColumnsDonorID = array_map(static function ($value) {
                return $value ?? '-1';
            }, $matrixColumnsDonorID);
        }

        $recordsCount = $query->count();

        $answersItemsDb = $query
            ->limit(30)
            ->offset(($page - 1) * 30)
            ->all();
        foreach ($answersItemsDb as $answerItem) {
            if ($answerItem['answer'] === 'null') {
                continue;
            }
            $detailAnswers = [];
            $selfAnswerValue = '';
            $extraQuestion = null;
            $detailVariants = json_decode($answerItem['answer']);
            $detailArray = json_decode($answerItem['detail_item'], true) ?? [];
            if ($detailVariants) {
                if ($question->getMainDonor()) {
                    $donorAnswer = FoquzPollAnswerItem::findOne([
                        'foquz_poll_answer_id' => $answerItem['foquz_poll_answer_id'],
                        'foquz_question_id'    => $question->getMainDonor()->id,
                    ]);
                    if ($donorAnswer) {
                        $selfAnswerValue = $donorAnswer->self_variant;
                    }
                    $selectedIDs = $donorAnswer->detail_item;
                    if (is_string($selectedIDs)) {
                        $selectedIDs = json_decode($selectedIDs, true);
                    }
                    if (!empty($selfAnswerValue)) {
                        $selectedIDs[] = '-1';
                    }
                }
                if ($question->donor_rows) {
                    $donorAnswer = FoquzPollAnswerItem::findOne([
                        'foquz_poll_answer_id' => $answerItem['foquz_poll_answer_id'],
                        'foquz_question_id'    => $question->donor_rows,
                    ]);
                    if ($donorAnswer) {
                        $selfAnswerValue = $donorAnswer->self_variant;
                        $selectedIDs = $donorAnswer->detail_item;
                        if (is_string($selectedIDs)) {
                            $selectedIDs = json_decode($selectedIDs, true);
                        }
                        if (!empty($selfAnswerValue)) {
                            $selectedIDs[] = '-1';
                        }
                        $recipientRows = array_filter($matrixRowsDonorID, static function ($value) use ($selectedIDs) {
                            return in_array($value, $selectedIDs);
                        });
                        $recipientRows = array_keys($recipientRows);
                    }
                }
                if ($question->donor_columns) {
                    $donorAnswer = FoquzPollAnswerItem::findOne([
                        'foquz_poll_answer_id' => $answerItem['foquz_poll_answer_id'],
                        'foquz_question_id'    => $question->donor_columns,
                    ]);
                    if ($donorAnswer) {
                        $selfAnswerValue = $donorAnswer->self_variant;
                        $selectedIDs = $donorAnswer->detail_item;
                        if (is_string($selectedIDs)) {
                            $selectedIDs = json_decode($selectedIDs, true);
                        }
                        if (!empty($selfAnswerValue)) {
                            $selectedIDs[] = '-1';
                        }
                        $recipientColumns = array_filter($matrixColumnsDonorID,
                            static function ($value) use ($selectedIDs) {
                                return in_array($value, $selectedIDs);
                            });
                        $recipientColumns = array_keys($recipientColumns);
                    }
                }
                foreach ($detailVariants as $variantId => $value) {
                    if ($question->donor && isset($selectedIDs) &&
                        (
                            ($question->donor_chosen && !in_array($variantId, $selectedIDs)) ||
                            (!$question->donor_chosen && in_array($variantId, $selectedIDs))
                        )
                    ) {
                        continue;
                    }
                    if ($value === null || $value === 'null') {
                        $value = 'Респондент отказался от оценки';
                    }
                    $detailAnswers[$variantId] = $value;
                    if (isset($detailArray[$variantId])) {
                        $detailAnswers['extra'][$variantId] = $detailArray[$variantId];
                    }
                }
                if ($question->donor) {
                    $detailAnswers['extra'] = $detailArray;
                }
            }
            if (!empty($answerItem['detail_item']) && ($detailArray = json_decode($answerItem['detail_item'], true)) &&
                is_iterable($detailArray)) {
                foreach ($detailArray as $key => $detailItem) {
                    foreach ($detailItem as $value) {
                        if (preg_match('/^\d+$/',
                                $value) && $extraQuestionDetail = FoquzQuestionDetail::findOne($value)) {
                            $extraQuestion[$key][] = $extraQuestionDetail->question;
                        }
                    }
                }
            }

            $answers[] = [
                'name'             => implode(' ',
                        [$answerItem['last_name'], $answerItem['first_name'], $answerItem['patronymic']]) ?? '',
                'phone'            => FoquzContact::formatPhone($answerItem['phone']) ?? '',
                'email'            => $answerItem['email'] ?? '',
                'passedAt'         => date('d.m.Y', strtotime($answerItem['updated_at'])),
                'orderId'          => $answerItem['orderId'] ?? '',
                'orderCreatedAt'   => $answerItem['created_time'] ? date('d.m.Y',
                    strtotime($answerItem['created_time'])) : '',
                'answer'           => $detailAnswers,
                'recipientRows'    => $recipientRows ?? null,
                'recipientColumns' => $recipientColumns ?? null,
                'extraQuestion'    => $extraQuestion,
                'comment'          => $answerItem['self_variant'],
                'points'           => $answerItem['points'],
                'selfAnswerValue'  => $selfAnswerValue,
                'skipped'          => (int)$answerItem['skipped'],
            ];
        }

        return [
            $answers,
            $recordsCount
        ];
    }

    private static function getQuestionByParams($id)
    {
        $question = FoquzQuestion::findOne($id);
        if (!$question) {
            throw new HttpException(403, 'Доступ запрещён');
        }
        $poll = null;
        if (Yii::$app->user->isGuest) {
            $link = Yii::$app->request->get("link", "");
            if (strlen($link) < 15) {
                throw new HttpException(403, 'Доступ запрещён');
            }
            $linkData = FoquzPollStatsLink::find()->where(['like', 'link', $link])->one();
            if ($linkData === null || $linkData->right_level === FoquzPollStatsLink::RIGHT_BLOCK) {
                throw new HttpException(403, 'Доступ запрещён');
            }
            $poll = FoquzPoll::findOne($linkData->foquz_poll_id);
        } else {
            $company = Yii::$app->user->identity->company;
            $poll = FoquzPoll::find()->where(["company_id" => $company->id, "id" => $question->poll_id])->one();
        }

        if (!$poll) {
            throw new HttpException(403, 'Доступ запрещён');
        }

        if ($question->poll_id !== $poll->id) {
            throw new HttpException(403, 'Доступ запрещён');
        }

        return $question->id;
    }

    public function actionGetSimpleMatrixAnswers(int $id)
    {
        $id = self::getQuestionByParams($id);
        $page = Yii::$app->request->get('page') ?? 1;
        Yii::$app->response->format = Response::FORMAT_JSON;

        list($answers, $recordsCount) = $this->getUserDataWithDecodedAnswer($id, $page);

        return [
            'answers'  => $answers,
            'lastPage' => $recordsCount <= ($page * 30)
        ];
    }

    public function actionGet3dMatrixAnswers(int $id)
    {
        $page = Yii::$app->request->get('page') ?? 1;
        Yii::$app->response->format = Response::FORMAT_JSON;

        list($answers, $recordsCount) = $this->getUserDataWithDecodedAnswer($id, $page);

        return [
            'answers'  => $answers,
            'lastPage' => $recordsCount <= ($page * 30)
        ];
    }

    public function actionGetDifferentialAnswers(int $id)
    {
        $page = Yii::$app->request->get('page') ?? 1;
        Yii::$app->response->format = Response::FORMAT_JSON;

        list($answers, $recordsCount) = $this->getUserDataWithDecodedAnswer($id, $page);

        return [
            'answers'  => $answers,
            'lastPage' => $recordsCount <= ($page * 30)
        ];
    }

    public function actionGetStarRatingAnswers(int $id)
    {
        $question = FoquzQuestion::findOne($id);
        if (!$question) {
            throw new HttpException(403, 'Доступ запрещён');
        }

        $poll = null;
        if (Yii::$app->user->isGuest) {
            $link = Yii::$app->request->get("link", "");
            if (strlen($link) < 15) {
                throw new HttpException(403, 'Доступ запрещён');
            }
            $linkData = FoquzPollStatsLink::find()->where(['like', 'link', $link])->one();
            if ($linkData === null || $linkData->right_level === FoquzPollStatsLink::RIGHT_BLOCK) {
                throw new HttpException(403, 'Доступ запрещён');
            }
            $poll = FoquzPoll::findOne($linkData->foquz_poll_id);
        } else {
            $company = Yii::$app->user->identity->company;
            $poll = FoquzPoll::find()->where(["company_id" => $company->id, "id" => $question->poll_id])->one();
        }

        if (!$poll) {
            throw new HttpException(403, 'Доступ запрещён');
        }

        if ($question->poll_id !== $poll->id) {
            throw new HttpException(403, 'Доступ запрещён');
        }

        $answers = [];
        $page = Yii::$app->request->get('page') ?? 1;

        $query = (new \yii\db\Query())
            ->select('foquz_poll_answer_item.id, foquz_contact.last_name, foquz_contact.first_name, foquz_contact.patronymic, foquz_contact.phone, foquz_contact.email, foquz_poll_answer.updated_at, orders.created_time, foquz_poll_answer_item.rating')
            ->addSelect(new Expression('IF(`orders`.`number` is null or `orders`.`number` = "", `orders`.`id`, `orders`.`number`) as orderId'))
            ->from('foquz_poll_answer')
            ->leftJoin('foquz_poll_answer_item', 'foquz_poll_answer_item.foquz_poll_answer_id = foquz_poll_answer.id')
            ->leftJoin('foquz_contact', 'foquz_contact.id = foquz_poll_answer.contact_id')
            ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
            ->where(['foquz_question_id' => $id])
            ->andWhere([
                'OR',
                ['AND', ['IS NOT', 'answer', null], ['!=', 'answer', ''], ['!=', 'answer', '[]']],
                ['OR', ['!=', 'rating', ''], ['skipped' => 1]],
            ])
            ->orderBy('foquz_poll_answer_item.created_at desc');

        $user = Yii::$app->user->identity ?? null;

        if ($user && $user->isFilialEmployee() && !$user->allFilials) {
            $filialsUser = ArrayHelper::getColumn($user->userFilials, 'filial_id');
            $query->andWhere(["answer_filial_id" => $filialsUser]);
        }


        if (Yii::$app->request->get('q')) {
            $query->andWhere([
                'OR',
                ['like', 'rating', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.phone', Yii::$app->request->get('q')],
                [
                    'like',
                    "TRIM(CONCAT(IFNULL(last_name, ''), ' ', IFNULL(first_name, ''), ' ', IFNULL(patronymic, '')))",
                    Yii::$app->request->get('q')
                ],
                ['like', 'foquz_contact.email', Yii::$app->request->get('q')],
                ['like', 'orders.number', Yii::$app->request->get('q')],
                ['like', 'orders.id', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(foquz_poll_answer.updated_at,"%d.%m.%Y")', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(orders.created_time,"%d.%m.%Y")', Yii::$app->request->get('q')],
            ]);
        }
        if (Yii::$app->request->get('rating')) {
            $query->andWhere(['in', 'rating', Yii::$app->request->get('rating')]);
        }
        if (Yii::$app->request->get('from') && Yii::$app->request->get('to')) {
            $query->andWhere([
                'between',
                'foquz_poll_answer_item.created_at',
                date('Y-m-d 00:00:00', strtotime(Yii::$app->request->get('from'))),
                date('Y-m-d 23:59:59', strtotime(Yii::$app->request->get('to')))
            ]);
        }
        $query = $this->addAdditionalFiltersToModals($query, Yii::$app->request->get());
        if ($filterSetting = $this->getFilterSettings($poll->id)) {
            $query->andWhere(['foquz_poll_answer.id' => FoquzPollAnswer::filterByAnswer($filterSetting, $poll->id)]);
        }
        $recordsCount = $query->count();

        $answersItemsDb = $query
            ->limit(30)
            ->offset(($page - 1) * 30)
            ->all();
        foreach ($answersItemsDb as $answerItem) {
            $answerItemModel = FoquzPollAnswerItem::findOne($answerItem['id']);
            if (is_array(json_decode($answerItemModel->detail_item)) && count(json_decode($answerItemModel->detail_item)) > 0) {
                $answer = [];
                foreach (json_decode($answerItemModel->detail_item) as $detailId) {
                    $detailModel = FoquzQuestionDetail::findOne($detailId);
                    if ($detailModel) {
                        $answer[] = $detailModel->question;
                    }
                }
                if ($answerItemModel->self_variant != '') {
                    $answer[] = $answerItemModel->self_variant;
                }
            } else {
                $answer = $answerItemModel->self_variant != '' ? $answerItemModel->self_variant : $answerItemModel->answer;
            }
            $answers[] = [
                'name'           => implode(' ',
                        [$answerItem['last_name'], $answerItem['first_name'], $answerItem['patronymic']]) ?? '',
                'phone'          => FoquzContact::formatPhone($answerItem['phone']) ?? '',
                'email'          => $answerItem['email'] ?? '',
                'passedAt'       => date('d.m.Y', strtotime($answerItem['updated_at'])),
                'orderId'        => $answerItem['orderId'] ?? '',
                'orderCreatedAt' => $answerItem['created_time'] ? date('d.m.Y',
                    strtotime($answerItem['created_time'])) : '',
                'rating'         => $answerItem['rating'],
                'answer'         => $answer,
                'skipped'        => $answerItemModel->skipped,
            ];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            'answers'  => $answers,
            'lastPage' => $recordsCount <= ($page * 30)
        ];
    }

    public function actionGetSmilesAnswers(int $id)
    {
        $answers = [];
        $page = Yii::$app->request->get('page') ?? 1;
        $question = FoquzQuestion::findOne($id);
        if (!$question) {
            throw new NotFoundHttpException('Вопрос не найден');
        }

        $query = (new \yii\db\Query())
            ->select('foquz_contact.last_name, foquz_contact.first_name, foquz_contact.patronymic, 
            foquz_contact.phone, foquz_contact.email, foquz_poll_answer.updated_at, orders.created_time, foquz_poll_answer_item.answer, 
            foquz_poll_answer_item.self_variant, foquz_poll_answer_item.skipped, foquz_poll_answer_item.detail_item')
            ->addSelect(new Expression('IF(`orders`.`number` is null or `orders`.`number` = "", `orders`.`id`, `orders`.`number`) as orderId'))
            ->from('foquz_poll_answer')
            ->leftJoin('foquz_poll_answer_item', 'foquz_poll_answer_item.foquz_poll_answer_id = foquz_poll_answer.id')
            ->leftJoin('foquz_contact', 'foquz_contact.id = foquz_poll_answer.contact_id')
            ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
            ->where(['foquz_question_id' => $id])
            ->andWhere([
                'OR',
                ['AND', ['!=', 'answer', ''], ['!=', 'answer', '[]']],
                ['foquz_poll_answer_item.skipped' => 1]
            ])
            ->orderBy('foquz_poll_answer_item.created_at desc');
        if (Yii::$app->request->get('q')) {
            $query->andWhere([
                'OR',
                ['like', 'foquz_poll_answer_item.self_variant', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.phone', Yii::$app->request->get('q')],
                [
                    'like',
                    "TRIM(CONCAT(IFNULL(last_name, ''), ' ', IFNULL(first_name, ''), ' ', IFNULL(patronymic, '')))",
                    Yii::$app->request->get('q')
                ],
                ['like', 'foquz_contact.email', Yii::$app->request->get('q')],
                ['like', 'orders.number', Yii::$app->request->get('q')],
                ['like', 'orders.id', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(foquz_poll_answer.updated_at,"%d.%m.%Y")', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(orders.created_time,"%d.%m.%Y")', Yii::$app->request->get('q')],
            ]);
        }
        if (Yii::$app->request->get('from') && Yii::$app->request->get('to')) {
            $query->andWhere([
                'between',
                'foquz_poll_answer_item.created_at',
                date('Y-m-d 00:00:00', strtotime(Yii::$app->request->get('from'))),
                date('Y-m-d 23:59:59', strtotime(Yii::$app->request->get('to')))
            ]);
        }
        $query = $this->addAdditionalFiltersToModals($query, Yii::$app->request->get());

        if ($filterSetting = $this->getFilterSettings($question->poll_id)) {
            $query->andWhere([
                'foquz_poll_answer.id' => FoquzPollAnswer::filterByAnswer($filterSetting, $question->poll_id)
            ]);
        }

        $recordsCount = $query->count();

        $answersItemsDb = $query
            ->limit(30)
            ->offset(($page - 1) * 30)
            ->all();
        foreach ($answersItemsDb as $answerItem) {
            $chooseAnswers = $answerItem['answer'];
            $selected = is_array($answerItem["detail_item"]) ? $answerItem["detail_item"] : json_decode($answerItem["detail_item"], true);
            $selectedIds = [];
            if (is_array($selected)) {
                foreach ($selected as $selectedKey => $selectedItem) {
                    if ($selectedKey === 'self_variant') { // один ответ на УВ
                        $selectedIds[] = [
                            'selfVariant' => $selectedItem,
                            'file_url' => isset($question->selfVariantFile['file_url']) ? $question->selfVariantFile['file_url'] : null,
                        ];
                    } else if ($selectedKey === 'text_answer') {
                        $selectedIds['comment'] = $selectedItem;
                    } else { // несколько ответов на УВ
                        if (is_array($selectedItem) && isset($selectedItem['self_variant'])) {
                            $selectedIds[] =[
                                'selfVariant' => $selectedItem['self_variant'],
                                'file_url' => isset($question->selfVariantFile['file_url']) ? $question->selfVariantFile['file_url'] : null,
                            ];
                        } else {
                            $detailModel = FoquzQuestionDetail::findOne($selectedItem);
                            if ($detailModel) {
                                $selectedIds[] = [
                                    'question' => $detailModel->question,
                                    'file_url' => $detailModel->file?->fileUrl,
                                ];
                            }
                        }
                    }
                }
            }
            $answers[] = [
                'name'           => implode(' ',
                        [$answerItem['last_name'], $answerItem['first_name'], $answerItem['patronymic']]) ?? '',
                'phone'          => FoquzContact::formatPhone($answerItem['phone']) ?? '',
                'email'          => $answerItem['email'] ?? '',
                'passedAt'       => date('d.m.Y', strtotime($answerItem['updated_at'])),
                'orderId'        => $answerItem['orderId'] ?? '',
                'orderCreatedAt' => $answerItem['created_time'] ? date('d.m.Y',
                    strtotime($answerItem['created_time'])) : '',
                'answer'         => $chooseAnswers,
                'smile'          => FoquzQuestionSmile::findOne($chooseAnswers),
                'comment'        => $answerItem['self_variant'],
                'skipped'        => (int)$answerItem['skipped'],
                'selectedIds'    => $selectedIds,
            ];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            'answers'  => $answers,
            'lastPage' => $recordsCount <= ($page * 30)
        ];
    }

    public function actionGetCardSortingClosedAnswers(int $id)
    {
        $id = self::getQuestionByParams($id);
        $page = Yii::$app->request->get('page') ?? 1;
        Yii::$app->response->format = Response::FORMAT_JSON;

        list($answers, $recordsCount) = $this->getUserDataWithDecodedAnswer($id, $page);

        return [
            'answers'  => $answers,
            'lastPage' => $recordsCount <= ($page * 30)
        ];
    }

    public function actionGetAnswers(int $id)
    {
        $question = FoquzQuestion::findOne($id);
        if (!$question) {
            throw new HttpException(403, 'Доступ запрещён');
        }

        $poll = null;
        if (Yii::$app->user->isGuest) {
            $link = Yii::$app->request->get("link", "");
            if (strlen($link) < 15) {
                throw new HttpException(403, 'Доступ запрещён');
            }
            $linkData = FoquzPollStatsLink::find()->where(['like', 'link', $link])->one();
            if ($linkData === null || $linkData->right_level === FoquzPollStatsLink::RIGHT_BLOCK) {
                throw new HttpException(403, 'Доступ запрещён');
            }
            $poll = FoquzPoll::findOne($linkData->foquz_poll_id);
        } else {
            $company = Yii::$app->user->identity->company;
            $poll = FoquzPoll::find()->where(["company_id" => $company->id, "id" => $question->poll_id])->one();
        }

        if (!$poll) {
            throw new HttpException(403, 'Доступ запрещён');
        }

        $answers = [];
        $page = Yii::$app->request->get('page') ?? 1;
        if ($question->poll_id !== $poll->id) {
            throw new HttpException(403, 'Доступ запрещён');
        }

        $query = (new \yii\db\Query())
            ->select('foquz_contact.last_name, foquz_contact.first_name, foquz_contact.patronymic, foquz_contact.phone, foquz_contact.email, foquz_poll_answer.updated_at, orders.created_time, foquz_poll_answer_item.answer, foquz_poll_answer_item.points, foquz_poll_answer_item.self_variant, foquz_poll_answer_item.skipped')
            ->addSelect(new Expression('IF(`orders`.`number` is null or `orders`.`number` = "", `orders`.`id`, `orders`.`number`) as orderId'))
            ->from('foquz_poll_answer')
            ->leftJoin('foquz_poll_answer_item', 'foquz_poll_answer_item.foquz_poll_answer_id = foquz_poll_answer.id')
            ->leftJoin('foquz_contact', 'foquz_contact.id = foquz_poll_answer.contact_id')
            ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
            ->where(['foquz_question_id' => $id])
            ->orderBy('foquz_poll_answer_item.created_at desc');
        if ($question->main_question_type !== FoquzQuestion::TYPE_TEXT_ANSWER) {
            $query->andWhere(['!=', 'answer', '']);
        }
        if (Yii::$app->request->get('q')) {
            $query->andWhere([
                'OR',
                ['like', 'answer', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.phone', Yii::$app->request->get('q')],
                [
                    'like',
                    "TRIM(CONCAT(IFNULL(last_name, ''), ' ', IFNULL(first_name, ''), ' ', IFNULL(patronymic, '')))",
                    Yii::$app->request->get('q')
                ],
                ['like', 'foquz_contact.email', Yii::$app->request->get('q')],
                ['like', 'orders.number', Yii::$app->request->get('q')],
                ['like', 'orders.id', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(foquz_poll_answer.updated_at,"%d.%m.%Y")', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(orders.created_time,"%d.%m.%Y")', Yii::$app->request->get('q')],
            ]);
        }
        if (Yii::$app->request->get('from') && Yii::$app->request->get('to')) {
            $query->andWhere([
                'between',
                'foquz_poll_answer_item.created_at',
                date('Y-m-d 00:00:00', strtotime(Yii::$app->request->get('from'))),
                date('Y-m-d 23:59:59', strtotime(Yii::$app->request->get('to')))
            ]);
        }
        $points = Yii::$app->request->get('points');
        if ($points && is_array($points)) {
            $query->andWhere(['between', 'foquz_poll_answer_item.points', $points[0], $points[1]]);
        }
        $query = $this->addAdditionalFiltersToModals($query, Yii::$app->request->get());
        if ($filterSetting = $this->getFilterSettings($poll->id)) {
            $query->andWhere(['foquz_poll_answer.id' => FoquzPollAnswer::filterByAnswer($filterSetting, $poll->id)]);
        }
        $recordsCount = $query->count();

        $answersItemsDb = $query
            ->limit(30)
            ->offset(($page - 1) * 30)
            ->all();
        foreach ($answersItemsDb as $answerItem) {
            if ($question->main_question_type === FoquzQuestion::TYPE_DATE && empty(trim($this->collectDateAnswer($answerItem['answer'])))) {
                continue;
            }
            $answers[] = [
                'name'           => implode(' ',
                        [$answerItem['last_name'], $answerItem['first_name'], $answerItem['patronymic']]) ?? '',
                'phone'          => FoquzContact::formatPhone($answerItem['phone']) ?? '',
                'email'          => $answerItem['email'] ?? '',
                'passedAt'       => date('d.m.Y', strtotime($answerItem['updated_at'])),
                'orderId'        => $answerItem['orderId'] ?? '',
                'orderCreatedAt' => $answerItem['created_time'] ? date('d.m.Y',
                    strtotime($answerItem['created_time'])) : '',
                'answer'         => ($question->main_question_type === FoquzQuestion::TYPE_DATE) ? $this->collectDateAnswer($answerItem['answer']) : ($question->mask === 5 ? json_decode($answerItem['answer']) : $answerItem['answer']),
                'points'         => $answerItem['points'],
                'comment'   => $answerItem['self_variant'],
                'skipped' => $answerItem['skipped']
            ];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            'answers'  => $answers,
            'lastPage' => $recordsCount <= ($page * 30)
        ];
    }

    protected function collectDateAnswer($answer)
    {
        $answer = json_decode($answer);
        $date = $answer->date ?? '';
        $time = str_replace(' ', '', $answer->time) ?? '';
        return implode(' ', [$date, $time]);
    }

    public function actionGetComments(int $id)
    {
        $answers = [];
        $page = Yii::$app->request->get('page') ?? 1;
        $query = (new \yii\db\Query())
            ->select('foquz_contact.last_name, foquz_contact.first_name, foquz_contact.patronymic, foquz_contact.phone, foquz_contact.email, foquz_poll_answer.updated_at, orders.created_time')
            ->addSelect(new Expression('IF(foquz_poll_answer_item.self_variant = "" OR foquz_poll_answer_item.self_variant IS NULL, foquz_poll_answer_item.answer, foquz_poll_answer_item.self_variant) as comment, filials.name as filialName'))
            ->addSelect(new Expression('IF(`orders`.`number` is null or `orders`.`number` = "", `orders`.`id`, `orders`.`number`) as orderId'))
            ->from('foquz_poll_answer')
            ->leftJoin('foquz_poll_answer_item', 'foquz_poll_answer_item.foquz_poll_answer_id = foquz_poll_answer.id')
            ->leftJoin('foquz_contact', 'foquz_contact.id = foquz_poll_answer.contact_id')
            ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
            ->leftJoin('filials', 'filials.id = foquz_poll_answer.answer_filial_id')
            ->where(['foquz_question_id' => $id])
            ->andWhere(new Expression('(is_self_variant = 1 AND self_variant != "") OR answer IS NOT NULL'))
            ->orderBy('foquz_poll_answer_item.created_at desc');
        if (Yii::$app->request->get('q')) {
            $query->andWhere([
                'OR',
                ['like', 'self_variant', Yii::$app->request->get('q')],
                ['like', 'answer', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.phone', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.last_name', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.first_name', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.patronymic', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.email', Yii::$app->request->get('q')],
                ['like', 'orders.number', Yii::$app->request->get('q')],
                ['like', 'orders.id', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(foquz_poll_answer.updated_at,"%d.%m.%Y")', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(orders.created_time,"%d.%m.%Y")', Yii::$app->request->get('q')],
            ]);
        }

        $query = $this->addAdditionalFiltersToModals($query, Yii::$app->request->get());

        $recordsCount = $query->count();

        $answersItemsDb = $query
            ->limit(30)
            ->offset(($page - 1) * 30)
            ->all();
        foreach ($answersItemsDb as $answerItem) {
            $answers[] = [
                'name'           => implode(' ',
                        [$answerItem['last_name'], $answerItem['first_name'], $answerItem['patronymic']]) ?? '',
                'phone'          => FoquzContact::formatPhone($answerItem['phone']) ?? '',
                'email'          => $answerItem['email'] ?? '',
                'filialName'     => $answerItem['filialName'],
                'passedAt'       => date('d.m.Y', strtotime($answerItem['updated_at'])),
                'orderId'        => $answerItem['orderId'] ?? '',
                'orderCreatedAt' => $answerItem['created_time'] ? date('d.m.Y',
                    strtotime($answerItem['created_time'])) : '',
                'comment'        => $answerItem['comment']
            ];
        }
        Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            'comments' => $answers,
            'lastPage' => $recordsCount <= ($page * 30)
        ];
    }

    public function actionGetNpsAnswers(int $id)
    {
        $question = FoquzQuestion::findOne($id);
        if (!$question) {
            throw new HttpException(403, 'Доступ запрещён');
        }
        if (Yii::$app->user->isGuest) {
            $link = Yii::$app->request->get("link", "");
            if (strlen($link) < 15) {
                throw new HttpException(403, 'Доступ запрещён');
            }
            $linkData = FoquzPollStatsLink::find()->where(['like', 'link', $link])->one();
            if ($linkData === null || $linkData->right_level === FoquzPollStatsLink::RIGHT_BLOCK) {
                throw new HttpException(403, 'Доступ запрещён');
            }
            $poll = FoquzPoll::findOne($linkData->foquz_poll_id);
        } else {
            $company = Yii::$app->user->identity->company;
            $poll = FoquzPoll::find()->where(["company_id" => $company->id, "id" => $question->poll_id])->one();
        }
        if (!$poll) {
            throw new HttpException(403, 'Доступ запрещён');
        }
        //$id = $poll->id;

        $answers = [];
        $page = Yii::$app->request->get('page') ?? 1;
        $query = (new \yii\db\Query())
            ->select('foquz_contact.last_name, foquz_contact.first_name, foquz_contact.patronymic, foquz_contact.phone, foquz_contact.email, foquz_poll_answer.updated_at, orders.created_time, foquz_poll_answer_item.foquz_poll_answer_id, foquz_poll_answer_item.rating, foquz_poll_answer_item.skipped, filials.name filialName')
            ->addSelect([
                'foquz_poll_answer_item.answer',
                'foquz_poll_answer_item.detail_item',
                'foquz_poll_answer_item.self_variant AS comment'
            ])
            ->addSelect(new Expression('IF(`orders`.`number` is null or `orders`.`number` = "", `orders`.`id`, `orders`.`number`) as orderId'))
            ->from('foquz_poll_answer')
            ->leftJoin('foquz_poll_answer_item', 'foquz_poll_answer_item.foquz_poll_answer_id = foquz_poll_answer.id')
            ->leftJoin('foquz_contact', 'foquz_contact.id = foquz_poll_answer.contact_id')
            ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
            ->leftJoin('filials', 'filials.id = foquz_poll_answer.answer_filial_id')
            ->where(['foquz_question_id' => $id])
            ->andWhere(['foquz_poll_answer.foquz_poll_id' => $poll->id])
            ->orderBy('foquz_poll_answer_item.created_at desc');
        if (Yii::$app->request->get('q')) {
            $query->andWhere([
                'OR',
                ['like', 'answer', Yii::$app->request->get('q')],
                ['like', 'self_variant', Yii::$app->request->get('q')],
                ['like', 'rating', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.phone', Yii::$app->request->get('q')],
                [
                    'like',
                    "TRIM(CONCAT(IFNULL(last_name, ''), ' ', IFNULL(first_name, ''), ' ', IFNULL(patronymic, '')))",
                    Yii::$app->request->get('q')
                ],
                ['like', 'foquz_contact.email', Yii::$app->request->get('q')],
                ['like', 'orders.number', Yii::$app->request->get('q')],
                ['like', 'orders.id', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(foquz_poll_answer.updated_at,"%d.%m.%Y")', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(orders.created_time,"%d.%m.%Y")', Yii::$app->request->get('q')],
                [
                    'like',
                    new Expression("(CASE WHEN rating >= 0 AND rating <= 6 THEN 'Критик' WHEN rating >= 7 AND rating < 9 THEN 'Нейтрал' ELSE 'Промоутер' END)"),
                    Yii::$app->request->get('q')
                ]
            ]);
        }
        if (Yii::$app->request->get('from') && Yii::$app->request->get('to')) {
            $query->andWhere([
                'between',
                'foquz_poll_answer_item.created_at',
                date('Y-m-d 00:00:00', strtotime(Yii::$app->request->get('from'))),
                date('Y-m-d 23:59:59', strtotime(Yii::$app->request->get('to')))
            ]);
        }

        $query = $this->addAdditionalFiltersToModals($query, Yii::$app->request->get());
        if ($filterSetting = $this->getFilterSettings($poll->id)) {
            $query->andWhere(['foquz_poll_answer.id' => FoquzPollAnswer::filterByAnswer($filterSetting, $poll->id)]);
        }
        $recordsCount = $query->count();
        $answersItemsDb = $query
            ->limit(30)
            ->offset(($page - 1) * 30)
            ->all();

        /** @var FoquzQuestionDetail[] $extraVariants */
        $extraVariants = ArrayHelper::index($question->questionDetails, null, 'extra_question')[1] ?? [];
        $extraVariants = ArrayHelper::index($extraVariants, 'id');

        foreach ($answersItemsDb as $answerItemKey => $answerItem) {
            $chooseAnswers = json_decode($answerItem['answer'], true);
            $detailItem = json_decode($answerItem['detail_item'], true);

            if ($answerItem['answer'] && $chooseAnswers) {
                $notEmptyAnswers = array_filter($chooseAnswers, static function ($value) {
                    return (int)$value !== -1;
                });
                if (count($notEmptyAnswers) === 0) {
                    continue;
                }
            } elseif ((int)$answerItem['rating'] === -1) {
                continue;
            }

            $selfAnswerValue = $answerItem['comment'];
            if ($question->donor) {
                $donorAnswer = FoquzPollAnswerItem::findOne([
                    'foquz_poll_answer_id' => $answerItem['foquz_poll_answer_id'],
                    'foquz_question_id'    => $question->getMainDonor()->id,
                ]);
                if ($donorAnswer) {
                    $selfAnswerValue = $donorAnswer->self_variant;
                }
            }

            $answers[$answerItemKey] = [
                'name'            => implode(' ',
                        [$answerItem['last_name'], $answerItem['first_name'], $answerItem['patronymic']]) ?? '',
                'phone'           => FoquzContact::formatPhone($answerItem['phone']) ?? '',
                'email'           => $answerItem['email'] ?? '',
                'passedAt'        => date('d.m.Y', strtotime($answerItem['updated_at'])),
                'orderId'         => $answerItem['orderId'] ?? '',
                'orderCreatedAt'  => $answerItem['created_time'] ? date('d.m.Y',
                    strtotime($answerItem['created_time'])) : '',
                'rating'          => $answerItem['rating'],
                'comment'         => !json_decode($answerItem['comment']) ? $answerItem['comment'] : '',
                'skipped'         => (int)$answerItem['skipped'],
                'selfAnswerValue' => $selfAnswerValue,
                'filialName'      => $answerItem['filialName'],
            ];

            if ($chooseAnswers) {
                $array = [];
                $extraAnswers = [];
                foreach ($chooseAnswers as $answerId => $chooseAnswer) {
                    if ($answerId != -1 && (!$question->donor || $question->getMainDonor()->main_question_type !== FoquzQuestion::TYPE_DICTIONARY) && $questionDetail = FoquzQuestionDetail::findOne($answerId)) {
                        $array[] = [
                            'id'         => $questionDetail->id,
                            'name'       => $questionDetail->question,
                            'value'      => $chooseAnswer,
                            'is_deleted' => $questionDetail->is_deleted,
                        ];
                    } elseif ($answerId != -1 && $question->getMainDonor()->main_question_type === FoquzQuestion::TYPE_DICTIONARY && $questionDetail = DictionaryElement::findOne($answerId)) {
                        $array[] = [
                            'id'         => $questionDetail->id,
                            'name'       => $questionDetail->fullPath,
                            'value'      => $chooseAnswer,
                            'is_deleted' => in_array($questionDetail->id,
                                json_decode($question->getMainDonor()->deleted_detail_question) ?? []),
                        ];
                    } elseif ($answerId == -1) {
                        if ($question->donor) {
                            $array[] = [
                                'id'         => -1,
                                'name'       => $question->getMainDonor()->self_variant_text ?: 'Свой вариант',
                                'value'      => $chooseAnswer,
                                'is_deleted' => !$question->getMainDonor()->is_self_answer,
                            ];
                        } else {
                            $array[] = [
                                'id'         => -1,
                                'name'       => $question->self_variant_text ?: 'Свой вариант',
                                'value'      => $chooseAnswer,
                                'is_deleted' => !$question->is_self_answer,
                            ];
                        }
                    }
                }

                switch ($question->extra_question_type) {
                    case FoquzQuestion::EXTRA_QUESTION_COMMON:
                        if (
                            $question->variants_element_type !== FoquzQuestion::VARIANT_ELEMENT_TYPE_TEXT &&
                            is_array($detailItem)
                        ) {
                            foreach ($detailItem as $detailItemId) {
                                if (isset($extraVariants[$detailItemId])) {
                                    $extraAnswers[] = $detailItemId; // $extraVariants[$detailItemId]->question;
                                }
                            }
                            if ($question->is_self_answer && isset($answerItem['comment']) && $answerItem['comment'] !== '') {
                                $extraAnswers[] = $answerItem['comment'];
                            }
                        } elseif (isset($answerItem['comment']) && $answerItem['comment'] !== '') {
                            $extraAnswers[] = $answerItem['comment'];
                        }
                        $answers[$answerItemKey]['extra_answer'] = $extraAnswers; //implode(', ', $extraAnswers);
                        break;
                    case FoquzQuestion::EXTRA_QUESTION_COMMON_FOR_EACH:
                    case FoquzQuestion::EXTRA_QUESTION_DIFFERENT_EACH:
                        if (is_array($detailItem)) {
                            foreach ($detailItem as $npsVariantId => $variantDetailItem) {
                                if (!is_array($variantDetailItem)) {
                                    continue;
                                }
                                $variantExtraAnswer = [];
                                foreach ($variantDetailItem as $key => $detailValue) {
                                    if (isset($extraVariants[$detailValue])) {
                                        $variantExtraAnswer[] = $detailValue; //$extraVariants[$detailValue]->question;
                                    } elseif (in_array($key, ['self_variant', 'answer']) && $detailValue !== '') {
                                        $variantExtraAnswer[] = $detailValue;
                                    }
                                }
                                $extraAnswers[$npsVariantId] = $variantExtraAnswer; // implode(', ', $variantExtraAnswer);
                            }
                        }
                        $answers[$answerItemKey]['extra_answer'] = $extraAnswers;
                        break;
                }

                $answers[$answerItemKey]['answer'] = $array;
            }
        }
        Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            'answers'  => array_values($answers),
            'lastPage' => $recordsCount <= ($page * 30)
        ];
    }

    public function actionGetScaleAnswers(int $id)
    {
        $id = self::getQuestionByParams($id);

        $foquzQuestion = FoquzQuestion::findOne($id);
        if (!$foquzQuestion) {
            throw new NotFoundHttpException('Вопрос не найден');
        }

        $answers = [
            'scaleRatingSetting' => $foquzQuestion->scaleRatingSetting,
            'set_variants'       => $foquzQuestion->set_variants
        ];

        $page = Yii::$app->request->get('page') ?? 1;
        $query = (new \yii\db\Query())
            ->select([
                'foquz_contact.last_name',
                'foquz_contact.first_name',
                'foquz_contact.patronymic',
                'foquz_contact.phone',
                'foquz_contact.email',
                'foquz_poll_answer.updated_at',
                'orders.created_time',
                'foquz_poll_answer_item.foquz_poll_answer_id',
                'foquz_poll_answer_item.rating',
                'foquz_poll_answer_item.skipped',
                'filials.name as filial_name'])
            ->addSelect(new Expression('foquz_poll_answer_item.answer AS answer, foquz_poll_answer_item.self_variant as comment'))
            ->addSelect(new Expression('IF(`orders`.`number` is null or `orders`.`number` = "", `orders`.`id`, `orders`.`number`) as orderId'))
            ->from('foquz_poll_answer')
            ->leftJoin('foquz_poll_answer_item', 'foquz_poll_answer_item.foquz_poll_answer_id = foquz_poll_answer.id')
            ->leftJoin('foquz_contact', 'foquz_contact.id = foquz_poll_answer.contact_id')
            ->leftJoin('orders', 'orders.id = foquz_poll_answer.order_id')
            ->leftJoin('filials', 'filials.id = foquz_poll_answer.answer_filial_id')
            ->where(['foquz_question_id' => $id])
            ->orderBy('foquz_poll_answer_item.created_at desc');
        if (Yii::$app->request->get('q')) {
            $query->andWhere([
                'OR',
                ['like', 'answer', Yii::$app->request->get('q')],
                ['like', 'self_variant', Yii::$app->request->get('q')],
                ['like', 'rating', Yii::$app->request->get('q')],
                ['like', 'foquz_contact.phone', Yii::$app->request->get('q')],
                [
                    'like',
                    "TRIM(CONCAT(IFNULL(last_name, ''), ' ', IFNULL(first_name, ''), ' ', IFNULL(patronymic, '')))",
                    Yii::$app->request->get('q')
                ],
                ['like', 'foquz_contact.email', Yii::$app->request->get('q')],
                ['like', 'orders.number', Yii::$app->request->get('q')],
                ['like', 'orders.id', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(foquz_poll_answer.updated_at,"%d.%m.%Y")', Yii::$app->request->get('q')],
                ['like', 'DATE_FORMAT(orders.created_time,"%d.%m.%Y")', Yii::$app->request->get('q')],
                [
                    'like',
                    new Expression("(CASE WHEN rating >= 0 AND rating <= 6 THEN 'Критик' WHEN rating >= 7 AND rating < 9 THEN 'Нейтрал' ELSE 'Промоутер' END)"),
                    Yii::$app->request->get('q')
                ]
            ]);
        }
        if (Yii::$app->request->get('from') && Yii::$app->request->get('to')) {
            $query->andWhere([
                'between',
                'foquz_poll_answer_item.created_at',
                date('Y-m-d 00:00:00', strtotime(Yii::$app->request->get('from'))),
                date('Y-m-d 23:59:59', strtotime(Yii::$app->request->get('to')))
            ]);
        }

        $query = $this->addAdditionalFiltersToModals($query, Yii::$app->request->get());

        if ($filterSetting = $this->getFilterSettings($foquzQuestion->poll_id)) {
            $query->andWhere([
                'foquz_poll_answer.id' => FoquzPollAnswer::filterByAnswer($filterSetting, $foquzQuestion->poll_id)
            ]);
        }

        $recordsCount = $query->count();
        $answersItemsDb = $query
            ->limit(30)
            ->offset(($page - 1) * 30)
            ->all();

        foreach ($answersItemsDb as $answerItemKey => $answerItem) {
            $chooseAnswers = json_decode($answerItem['answer'], true);
            $selfAnswerValue = $answerItem['comment'];
            if ($foquzQuestion->donor) {
                $donorAnswer = FoquzPollAnswerItem::findOne([
                    'foquz_poll_answer_id' => $answerItem['foquz_poll_answer_id'],
                    'foquz_question_id'    => $foquzQuestion->getMainDonor()->id,
                ]);
                if ($donorAnswer) {
                    $selfAnswerValue = $donorAnswer->self_variant;
                }
            }

            $answers[$answerItemKey] = [
                'name'            => implode(' ',
                        [$answerItem['last_name'], $answerItem['first_name'], $answerItem['patronymic']]) ?? '',
                'phone'           => FoquzContact::formatPhone($answerItem['phone']) ?? '',
                'email'           => $answerItem['email'] ?? '',
                'passedAt'        => date('d.m.Y', strtotime($answerItem['updated_at'])),
                'orderId'         => $answerItem['orderId'] ?? '',
                'orderCreatedAt'  => $answerItem['created_time'] ? date('d.m.Y',
                    strtotime($answerItem['created_time'])) : '',
                'rating'          => $answerItem['rating'],
                'comment'         => !json_decode($answerItem['comment']) ? $answerItem['comment'] : '',
                'selfAnswerValue' => $selfAnswerValue,
                'skipped'         => (int)$answerItem['skipped'],
                'filialName'      => $answerItem['filial_name'] ?? '',
            ];

            if ($chooseAnswers) {
                $array = [];
                foreach ($chooseAnswers as $answerId => $chooseAnswer) {
                    if ($answerId != -1 && (!$foquzQuestion->donor || $foquzQuestion->getMainDonor()->main_question_type !== FoquzQuestion::TYPE_DICTIONARY) && $questionDetail = FoquzQuestionDetail::findOne($answerId)) {
                        $array[] = [
                            'id'         => $questionDetail->id,
                            'name'       => $questionDetail->question,
                            'value'      => $chooseAnswer ?? 'Респондент отказался от оценки',
                            'is_deleted' => $questionDetail->is_deleted,
                        ];
                    } elseif ($answerId != -1 && $foquzQuestion->donor && $foquzQuestion->getMainDonor()->main_question_type === FoquzQuestion::TYPE_DICTIONARY && $questionDetail = DictionaryElement::findOne($answerId)) {
                        $array[] = [
                            'id'         => $questionDetail->id,
                            'name'       => $questionDetail->fullPath,
                            'value'      => $chooseAnswer,
                            'is_deleted' => in_array($questionDetail->id,
                                json_decode($foquzQuestion->getMainDonor()->deleted_detail_question) ?? []),
                        ];
                    } elseif ($answerId == -1) {
                        if ($foquzQuestion->donor) {
                            $array[] = [
                                'id'         => -1,
                                'name'       => $foquzQuestion->getMainDonor()->self_variant_text ?: 'Свой вариант',
                                'value'      => $chooseAnswer,
                                'is_deleted' => !$foquzQuestion->getMainDonor()->is_self_answer,
                            ];
                        } else {
                            $array[] = [
                                'id'         => -1,
                                'name'       => $foquzQuestion->self_variant_text ?: 'Свой вариант',
                                'value'      => $chooseAnswer,
                                'is_deleted' => !$foquzQuestion->is_self_answer,
                            ];
                        }
                    }
                }

                $answers[$answerItemKey]['answer'] = $array;
            }
        }
        Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            'answers'  => $answers,
            'lastPage' => $recordsCount <= ($page * 30)
        ];
    }

    public function actionStatsFilter()
    {
        ini_set('memory_limit', '1000M');
        $post = Yii::$app->request->post();
        if (empty($post)) {
            $post = Yii::$app->request->get();
        }
        $post['from'] = (isset($post['from']) && $post['from'] !== '') ? date('Y-m-d 00:00:00',
            strtotime($post['from'])) : null;
        $post['to'] = (isset($post['to']) && $post['to'] !== '') ? date('Y-m-d 23:59:59',
            strtotime($post['to'])) : null;
        $id = $post['id'];
        $linkKey = Yii::$app->request->get('link');
        if (!empty($linkKey)) {
            $service = AnswerStatService::getInstanceByLink($linkKey, $id);
        } elseif (!Yii::$app->user->isGuest) {
            $service = AnswerStatService::getInstanceByPollId($id, Yii::$app->user->identity->id);
        } else {
            throw new ForbiddenHttpException('Доступ запрещен');
        }
        $service->applyParams($post);
        return $service->stat();
    }


    public function actionWidgetStats($id, $key)
    {
        ini_set('memory_limit', '2000M');
        $question = FoquzQuestion::findOne($id);
        if (!$question) {
            throw new NotFoundHttpException('Вопрос не найден');
        }
        /** @var FoquzPollStatWidgetKey $linkKey */
        $linkKey = FoquzPollStatWidgetKey::find()->where(['key' => $key])->one();
        if (!$linkKey || $linkKey->poll_id !== $question->poll_id) {
            throw new ForbiddenHttpException('Доступ запрещён');
        }
        $service = AnswerStatService::getInstanceByPollId($question->poll_id);
        $service->applyParams([]);
        $data = $service->stat(true, [$question->id]);
        return $this->asJson(['data' => $data[0] ?? []]);
    }

    /**
     * Страница со статистикой по опросу для авторизованного пользователя
     * @param int $id
     * @return string
     * @throws ForbiddenHttpException
     * @throws NotFoundHttpException
     */
    public function actionStats(int $id)
    {
        if (Yii::$app->user->isGuest) {
            throw new ForbiddenHttpException('Доступ запрещен');
        }
        $service = AnswerStatService::getInstanceByPollId($id, Yii::$app->user->identity->id);
        $model = $service->getPoll()->setWithDeletedQuestions(true);

        $answerFilterEnabled = FoquzQuestion::find()
            ->where(['poll_id' => $service->getPoll()->id, 'is_deleted' => 0, 'is_tmp' => 0])
            ->andWhere(['main_question_type' => FoquzQuestion::TYPE_VARIANTS])
            ->exists();
        if ($answerFilterEnabled) {
            $filterSettings = FoquzPollStatFilterSettings::findOne([
                'foquz_poll_id' => $service->getPoll()->id,
                'user_id'       => Yii::$app->user->identity->id
            ]);
        }
        $data = $service->statuses();
        $goal = $data['processed'] >= $model->goals_count && $model->goals_count > 0;

        return $this->render('stats', [
            'poll'                => $model,
            'data'                => [],
            'questions'           => $model->foquzQuestions,
            'sended'              => $data['sended'],
            'opened'              => $data['opened'],
            'processed'           => $data['processed'],
            'done'                => $data['done'],
            'goal'                => $goal,
            'firstAnswer'         => $model->first_answer_at ? date('d.m.Y H:i',
                strtotime($model->first_answer_at)) : null,
            'lastAnswer'          => $model->last_answer_at ? date('d.m.Y H:i',
                strtotime($model->last_answer_at)) : null,
            'avgTime'             => null,
            'filterSettings'      => $filterSettings->filter_settings ?? [],
            'answerFilterEnabled' => $answerFilterEnabled,
        ]);
    }

    public function actionUpdateProcessing($id)
    {
        $model = FoquzPollAnswerProcessing::findOne($id);
        $model->load(Yii::$app->getRequest()->post());
        if (Yii::$app->getRequest()->post()['FoquzPollAnswerProcessing']['process_up']) {
            $model->process_up = date('Y-m-d',
                strtotime(Yii::$app->getRequest()->post()['FoquzPollAnswerProcessing']['process_up']));
        }
        if (Yii::$app->getRequest()->post()['FoquzPollAnswerProcessing']['delayed_up']) {
            $model->delayed_up = date('Y-m-d',
                strtotime(Yii::$app->getRequest()->post()['FoquzPollAnswerProcessing']['delayed_up']));
        }
        $model->updated_at = date('Y-m-d H:i:s');
        $model->save();
        return json_encode([
            'status'     => 200,
            'updated_at' => date('d.m.Y H:i', strtotime($model->updated_at)),
            'executor'   => $model->executor->name ?? $model->executor->username
        ]);
    }

    public function actionGetAnswerData()
    {
        $id = Yii::$app->getRequest()->get('answerId');
        $answer = FoquzPollAnswer::findOne($id);
        Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            'id'                  => $answer->id,
            'status'              => $answer->processing ? $answer->processing->status : null,
            'isAuto'              => $answer->foquzPoll->is_auto,
            'pollName'            => $answer->foquzPoll->name,
            'createdAt'           => date("d.m.Y H:i", $answer->foquzPoll->created_at),
            'updated_at'          => date("d.m.Y H:i", strtotime($answer->updated_at)),
            'updated_at2'         => strtotime($answer->updated_at),
            'passedAt'            => date("d.m.Y H:i",
                strtotime(isset($answer->foquzAnswer[0]) ? $answer->foquzAnswer[count($answer->foquzAnswer) - 1]->created_at : $answer->updated_at)),
            'executor'            => ['name' => ($answer->processing && $answer->processing->executor) ? $answer->processing->executor->name ?? $answer->processing->executor->username : ''],
            'complaint'           => $answer->complaint ? [
                'text'      => $answer->complaint->text,
                'photoUrls' => FoquzComplaintFile::find()->select(['file_path'])->where(['foquz_poll_answer_id' => $answer->id])->column()
            ] : null,
            'lastStatusChangedAt' => $answer->updated_at,
            'client_id'           => $answer->contact ? $answer->contact->client_id : $answer->client_id,
            'contact_id'          => $answer->contact_id,
            'client'              => $answer->contact ? implode(' ',
                [$answer->contact->last_name, $answer->contact->first_name, $answer->contact->patronymic]) : '',
            'phone'               => $answer->contact->formattedPhone ?? '',
            'ordersCount'         => $answer->contact ? $answer->contact->ordersCount : 0,
            'reviewsCount'        => $answer->contact ? $answer->contact->reviewsCount : 0,
            'compensationsCount'  => $answer->contact ? (int)$answer->contact->compensationsCount : 0,
            'filialName'          => $answer->order_id ? $answer->order->filial->name : '',
            'branchName'          => $answer->order && $answer->order->filial && $answer->foquzPoll->trigger != 3 ? $answer->order->filial->name : null,
            'order_sum'           => $answer->order->sum ?? 0,
            'orderCreatedAt'      => $answer->order_id ? date("d.m.Y H:i",
                strtotime($answer->order->created_time)) : '',
            'deliveryType'        => $answer->order->delivery->name ?? '',
            'deliveryAddress'     => $answer->order->address ?? '',
            'email'               => $answer->clientEmail ? $answer->clientEmail->email : '',
            'orderNumber'         => $answer->order ? ($answer->order->number ?? $answer->order->id) : '',
            'questions_count'     => count($answer->foquzPoll->foquzQuestions),
            'notifications'       => $answer->collectNotifications(),
            'processing'          => $answer->processing ?? null,
            'answers'             => $answer->collectAnswers(),
            'questions'           => $answer->collectQuestions(),
        ];
    }

    public function actionOrders()
    {
        $contactId = Yii::$app->getRequest()->get('contactId');
        Yii::$app->response->format = Response::FORMAT_JSON;
        if (!$contactId) {
            return [];
        }
        $contact = FoquzContact::findOne($contactId);
        $client = $contact->client;
        if (!$client) {
            return [];
        }
        $page = Yii::$app->getRequest()->get('page');
        return $client->collectOrders($page);
    }

    public function actionReviews()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $contactId = Yii::$app->getRequest()->get('contactId');
        if (!$contactId) {
            return [];
        }
        $contact = FoquzContact::findOne($contactId);
        $page = Yii::$app->getRequest()->get('page');
        return $contact->collectReviews($page);
    }

    public function actionCompensations()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        $contactId = Yii::$app->getRequest()->get('contactId');
        if (!$contactId) {
            return [];
        }
        $contact = FoquzContact::findOne($contactId);
        $page = Yii::$app->getRequest()->get('page');
        return $contact->collectCompensations($page);

    }

    /**
     * Finds the FoquzPoll model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return FoquzPoll the loaded model
     * @throws ForbiddenHttpException
     * @throws NotFoundHttpException if the model cannot be found
     */
    private function findModel($id)
    {
        if (($model = FoquzPoll::findOne($id)) !== null) {
            $companyId = Yii::$app->user->identity->company->id;
            if ($companyId !== $model->company_id || $model->deleted || $model->status === FoquzPoll::STATUS_ARCHIVE || ($model->is_tmp && $model->updated_at !== $model->created_at)) {
                throw new NotFoundHttpException();
            }

            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    /**
     * @throws Exception
     */
    private function addChannels($id)
    {
        Yii::$app->db->createCommand()->batchInsert(
            'channel',
            ['active', 'position', 'name', 'text', 'delay', 'poll_id', 'delay_days', 'passed_trigger'],
            [
                [0, 1, 'Email', '', '-', $id, 0, null],
                [0, 2, 'SMS', '', '-', $id, 0, null],
                [0, 3, 'Telegram', '', '-', $id, 0, null],
                [0, 4, 'Viber', '', '-', $id, 0, null],
                [0, 5, 'Push', '', '-', $id, 0, null],
                [0, 1, 'Email', '', '-', $id, 0, 0],
                [0, 2, 'SMS', '', '-', $id, 0, 0],
                [0, 3, 'Telegram', '', '-', $id, 0, 0],
                [0, 4, 'Viber', '', '-', $id, 0, 0],
                [0, 5, 'Push', '', '-', $id, 0, 0],
            ]
        )->execute();
    }


    private function addChannelsPassed($id)
    {
        Yii::$app->db->createCommand()->batchInsert(
            'channel',
            ['active', 'position', 'name', 'text', 'delay', 'poll_id', 'delay_days', 'passed_trigger'],
            [
                [0, 1, 'Email', '', '-', $id, 0, 0],
                [0, 2, 'SMS', '', '-', $id, 0, 0],
                [0, 3, 'Telegram', '', '-', $id, 0, 0],
                [0, 4, 'Viber', '', '-', $id, 0, 0],
                [0, 5, 'Push', '', '-', $id, 0, 0],
            ]
        )->execute();
    }

    public function actionCheckGlobalSettings($channel)
    {
        switch (strtolower($channel)) {
            case 'email':
                $query = GlobalEmailSettings::find();
                break;
            case 'sms':
                $query = GlobalSmsSettings::find();
                break;
            case 'telegram':
                $query = GlobalTelegramSettings::find();
                break;
            case 'viber':
                $query = GlobalViberSettings::find();
                break;
            case 'push':
                $query = GlobalPushSettings::find();
                break;
            default:
                throw new \DomainException();
        }
        $model = $query->where(['company_id' => Yii::$app->user->identity->company->id])->asArray()->one();
        return json_encode([
            'success' => $model !== null,
            'model'   => $model
        ]);
    }

    public function actionUpdateQuestionsPositions(int $pollId)
    {
        $poll = $this->findModel($pollId);
        $post = Yii::$app->request->post('ids');
        foreach ($post as $k => $v) {
            $question = $poll->getFoquzQuestions()->where(['id' => $v])->one();
            if ($question) {
                $question->position = $k + 1;
                $question->save();
            }
        }
        Yii::$app->response->format = Response::FORMAT_JSON;
        return $poll->foquzQuestions;
    }

    public function actionMailingConditions($id)
    {
        $poll = $this->findModel($id);
        return $this->render('mailing-conditions', [
            'poll' => $poll
        ]);
    }

    private function generateOrder($client_id, $dishes)
    {
        $orderNumber = ReportHelper::randomGuid();
        $order = new Order([
            'order_number' => $orderNumber,
        ]);

        $order->client_id = $client_id;

        $randFilial = Filial::find()
            ->select(['id'])
            ->where(['company_id' => \Yii::$app->user->identity->company->id])
            ->limit(1)
            ->orderBy((new Expression('RAND()')))
            ->scalar();

        $order->filial_id = $randFilial === false ? null : $randFilial;

        $order->driver_id = Worker::find()
            ->select(['id'])
            ->where(['post_code' => 'driver', 'is_active' => true])
            ->limit(1)
            ->orderBy((new Expression('RAND()')))
            ->scalar();

        $order->operator_id = Worker::find()
            ->select(['id'])
            ->where(['post_code' => 'operator', 'is_active' => true])
            ->limit(1)
            ->orderBy((new Expression('RAND()')))
            ->scalar();
        $order->created_time = date('Y-m-d H:i:s', time());
        $order->delivery_type = FoquzOrderType::find()
            ->select(['id'])
            ->limit(1)
            ->orderBy((new Expression('RAND()')))
            ->scalar();
        $order->source_type = rand(1, 3);
        if (!$order->save()) {
            throw new \Exception('Что-то пошло не так');
        }
        foreach ($dishes as $dish) {
            $res = (new OrderDishes([
                'order_id' => $order->id,
                'dish_id'  => $dish->id,
                'sum'      => rand(250, 1200),
                'quantity' => rand(1, 5)
            ]))->save();

            if (!$res) {
                throw new \Exception('Что-то пошло не так');
            }
        }
        $order->sum = $order->getSum();
        $order->save();
        return $order;
    }

    private function formatPhone($phone)
    {
        $formatted = str_replace([' ', '+', '(', ')'], '', $phone);
        if (strlen($formatted) === 10) {
            $formatted = '7' . $formatted;
        } elseif (strlen($formatted) === 11 && $formatted[0] === '8') {
            $formatted[0] = '7';
        }
        if (strlen($formatted) === 11) {
            $formatted = '+' . $formatted[0] . ' (' . substr($formatted, 1, 3) . ') ' . substr($formatted, 4,
                    3) . ' ' . substr($formatted, 7, 4);
        }
        return $formatted;
    }

    private function makePollDishScoreForAnswer($answer, $dishes)
    {
        foreach ($dishes as $dish) {
            $res = (new FoquzPollDishScore([
                'answer_id' => $answer->id,
                'dish_id'   => $dish->id,
                'sum'       => rand(250, 1200),
                'score'     => 0
            ]))->save();
        }
    }

    public function actionLogic($id)
    {
        $model = $this->findModel($id);

        return $this->render('logic', [
            'questions' => $model->foquzQuestionsWithDetails,
            'poll'      => $model,
        ]);
    }

    public function actionStatsToPdf($id)
    {
        $dompdf = new Dompdf();

        $dompdf->loadHtml(Yii::$app->request->post('html'));
        $dompdf->render();

        $folder = Yii::getAlias("@app/web/uploads/foquz/stats/" . $id);

        if (false === file_exists($folder)) {
            mkdir($folder, 0777, true);
        }

        $file = Yii::getAlias('@webroot/uploads/foquz/stats/' . $id . '/stats.pdf');

        file_put_contents($file, $dompdf->output());

        Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            'url' => Url::to('/uploads/foquz/stats/' . $id . '/stats.pdf')
        ];
    }


    /**
     * Статистика пока внешней ссылке
     * @param string $id
     * @return string
     * @throws BadRequestHttpException
     * @throws ForbiddenHttpException
     * @throws NotFoundHttpException
     */
    public function actionStatsLink(string $id): string
    {
        $getData = Yii::$app->request->get();
        $service = AnswerStatService::getInstanceByLink($id);
        $service->applyParams($getData);
        $data = $service->statuses();
        $model = $service->getPoll()->setWithDeletedQuestions(true);
        $goal = $data['processed'] >= $model->goals_count && $model->goals_count > 0;
        $this->layout = '@app/modules/foquz/views/layouts/external';
        return $this->render('stats.external.php', [
            'poll'         => $model,
            'data'         => $data,
            'questions'    => $model->foquzQuestions,
            'sended'       => $data['sended'],
            'opened'       => $data['opened'],
            'processed'    => $data['processed'],
            'done'         => $data['done'],
            'goal'         => $goal,
            'firstAnswer'  => $model->first_answer_at ? date('d.m.Y H:i', strtotime($model->first_answer_at)) : null,
            'lastAnswer'   => $model->last_answer_at ? date('d.m.Y H:i', strtotime($model->last_answer_at)) : null,
            'avgTime'      => null,
            'show_answers' => (bool)$model->statsLink->show_answers,
        ]);
    }

    public function actionAnswersLink($id)
    {
        if (!is_string($id) || strlen($id) !== 32) {
            throw new HttpException(403, 'Доступ запрещён');
        }

        /** @var FoquzPollStatsLink $linkData */
        $linkData = FoquzPollStatsLink::find()->where(['like', 'link', $id])->one();
        /** @var FoquzPoll $poll */
        $poll = FoquzPoll::find()->with(['statsLink' => function ($query) use ($linkData) {
            $query->where(['link' => $linkData->link]);
        }])->where(['id' => $linkData->foquz_poll_id, 'deleted' => 0])->one();

        if (!$poll || !$linkData || $linkData->right_level === FoquzPollStatsLink::RIGHT_BLOCK || $linkData->show_answers != 1) {
            throw new HttpException(403, 'Доступ запрещён');
        }

        $advantageSuppliersExcelEnabled = false;
        if (
            ((YII_DEBUG && $poll->company_id === 1) || ($poll->company_id === 749)) &&
            FoquzQuestion::find()->where([
                'poll_id'            => $poll->id,
                'main_question_type' => FoquzQuestion::TYPE_3D_MATRIX,
                'is_deleted'         => 0
            ])->exists()
        ) {
            $advantageSuppliersExcelEnabled = true;
        }

        $this->layout = '@app/modules/foquz/views/layouts/external';
        return $this->render('answer-external', [
            'poll'                           => $poll->setWithDeletedQuestions(true),
            'advantageSuppliersExcelEnabled' => $advantageSuppliersExcelEnabled,
        ]);
    }

    public function actionStatWidget($key)
    {
        /** @var FoquzPollStatWidgetKey $linkKey */
        $linkKey = FoquzPollStatWidgetKey::find()->where(['key' => $key])->one();
        if (!$linkKey) {
            throw new NotFoundHttpException();
        }
        /** @var FoquzPoll $poll */
        $poll = FoquzPoll::find()->where(['id' => $linkKey->poll_id])->one();
        if (!$poll) {
            throw new NotFoundHttpException('Опрос не найден');
        }
        $this->layout = '@app/modules/foquz/views/layouts/stat-widget';
        return $this->render('stat-widget', [
            'poll'      => $poll,
            'questions' => $poll->getFoquzQuestions()->andWhere([
                'main_question_type' => [
                    FoquzQuestion::TYPE_VARIANTS,
                    FoquzQuestion::TYPE_NPS_RATING,
                    FoquzQuestion::TYPE_SMILE_RATING,
                    FoquzQuestion::TYPE_STAR_RATING,
                    FoquzQuestion::TYPE_RATING,
                ]
            ])->all(),
            'qr'        => '/foquz/foquz-poll/qr?v=' . Url::to('/p/' . $poll->quotes[0]->key, 'https'),
            'key'       => $key,
        ]);
    }

    private function addAdditionalFiltersToModals($query, $params)
    {
        $tags = $params['tags'] ?? [];
        $tagsOperation = $params['tagsOperation'] ?? null;
        $filials = $params['filials'] ?? [];
        $devices = $params['devices'] ?? [];

        if (isset($params['from']) && isset($params['to'])) {
            $query->andWhere([
                'between',
                'foquz_poll_answer_item.created_at',
                date('Y-m-d 00:00:00', strtotime($params['from'])),
                date('Y-m-d 23:59:59', strtotime($params['to']))
            ]);
        }
        if (isset($params['questionnaires'])) {
            $query->andWhere(['foquz_poll_answer.status' => $params['questionnaires']]);
        }
        if (count($tags) > 0 && $tagsOperation) {
            $query->leftJoin('foquz_contact_tag', 'foquz_contact_tag.contact_id = foquz_poll_answer.contact_id');
            if ($tagsOperation == 1) {
                $query->andWhere(['in', 'foquz_contact_tag.tag_id', $tags]);
            } else {
                $query
                    ->andWhere(['in', 'tag_id', $tags])
                    ->andHaving("(" . (new Query())->select('COUNT(DISTINCT tag_id)')
                            ->from('foquz_contact_tag')
                            ->where('contact_id = foquz_poll_answer.contact_id')
                            ->andWhere(['in', 'foquz_contact_tag.tag_id', $tags])
                            ->createCommand()->rawSql . ") =" . count($tags)
                    );
            }
        }
        if (count($filials) > 0) {
            $query
                ->leftJoin('foquz_poll', 'foquz_poll_answer.foquz_poll_id = foquz_poll.id');
            $index = array_search(0, $filials);
            if ($index !== false) {
                $filials[$index] = null;
            }
            $query->andWhere([
                'in',
                'IF(foquz_poll.is_auto, orders.filial_id, foquz_poll_answer.answer_filial_id)',
                $filials
            ]);
        }
        if (count($devices) > 0) {
            $query->andWhere(['in', 'foquz_poll_answer.device', $devices]);
        }

        return $query;
    }

    public function actionLaunch($id)
    {
        $poll = $this->findModel($id);
        if (!$poll->is_auto) {
            throw new NotFoundHttpException('Страница не найдена');
        }

        $points = FoquzPointItem::getTotalPoints($id);
        $pointsSelected = FoquzPointItem::getTotalPoints($id, true);

        $conditions = FoquzPointItem::getConditions($id);

        return $this->render('launch', [
            'poll'           => $poll,
            'points'         => $points,
            'pointsSelected' => $pointsSelected,
            'conditions'     => $conditions,
        ]);
    }

    public function actionChannels($id)
    {
        if ($mailing_id = Yii::$app->request->get('mailing_id')) {
            $mailingExists = FoquzPollMailingList::find()
                ->joinWith('foquzPoll')
                ->where([
                    'foquz_poll_mailing_list.id' => $mailing_id,
                    'foquz_poll.id'              => $id,
                    'foquz_poll.company_id'      => Yii::$app->user->identity->company->id,
                ])
                ->exists();
            if (!$mailingExists) {
                throw new NotFoundHttpException();
            }
        }
        if (!Channel::findOne(['poll_id' => $id])) {
            $this->addChannels($id);
        }
        if (Channel::find()->where(['poll_id' => $id])->andWhere("passed_trigger is not null")->count() == 0) {
            $this->addChannelsPassed($id);
        }

        $channels = Channel::find()->where(['poll_id' => $id])->orderBy(['position' => SORT_ASC])->all();
        $icons = [];
        foreach ($channels as $index => $channel) {
            $icons[$channel->id] = "settings__" . strtolower($channel->name) . "-icon";
        }

        $senders = Sender::getSenderValues();
        $sender_names = SenderName::getSenderNameValues();

        return $this->render('channels', [
            'poll'         => $this->findModel($id),
            'channels'     => $channels,
            'icons'        => $icons,
            'senders'      => $senders,
            'sender_names' => $sender_names
        ]);
    }

    public function actionDownloadJson($id)
    {
        try {
            $jsonCrypt = new JsonCrypt();
            $model = FoquzPoll::findOne([
                'id' => $id,
                'company_id' => \Yii::$app->user->identity->company->id,
                'deleted' => 0,
            ]);

            if ($model === null) {
                throw new NotFoundHttpException();
            }

            if ($model->is_auto) {
                throw new BadRequestHttpException("Недоступно для автоматических опросов");
            }

            $builder = new Poll2ArrayBuilder($model);
            $json = Json::encode($builder->build());

            $filename = preg_replace("/[^a-zA-ZА-Яа-я0-9\s\-]/u", '', $model->name);

            header("Content-type: application/vnd.ms-excel");
            header("Content-Type: application/force-download");
            header("Content-Type: application/download");
            header("Content-disposition: " . $filename . ".json");
            header("Content-disposition: filename=" . $filename . ".json");

            print $jsonCrypt->encrypt($json);
            exit;
        } catch (\Throwable $e) {
            throw new BadRequestHttpException($e->getMessage());
        }
    }

    public function actionInterscreens($id)
    {
        $poll = $this->findModel($id);

        if ((!$poll->startPage || !$poll->startPage->enabled) && (!$poll->endPage || !$poll->endPage->enabled)) {
            $this->redirect(Url::to(['foquz-poll/custom-design', 'id' => $id]));
        }

        return $this->render('interscreens', [
            'poll' => $poll
        ]);
    }

    public function actionMailingSelectList($id)
    {
        $name = Yii::$app->request->get('name');
        $page = Yii::$app->request->get('page', 1) - 1;
        $query = FoquzPollMailingListSearch::find()
            ->select([
                'foquz_poll_mailing_list.id',
                'foquz_poll_mailing_list.name',
                'customChannelSettings' => 'IF (channel.id IS NULL, 0, 1)'
            ])
            ->leftJoin("channel", "channel.mailing_id = foquz_poll_mailing_list.id")
            ->where([
                'foquz_poll_mailing_list.foquz_poll_id' => $id,
                'foquz_poll_mailing_list.deleted'       => 0
            ])
            ->andFilterWhere(['like', 'foquz_poll_mailing_list.name', $name])
            ->orderBy(['foquz_poll_mailing_list.name' => SORT_ASC])
            ->asArray()
            ->distinct();

        $dataProvider = new ActiveDataProvider([
            'query'      => $query,
            'pagination' => [
                'pageSize' => 30,
                'page'     => $page,
            ],
        ]);

        $this->asJson(['items' => $dataProvider->getModels(), 'total' => $dataProvider->getTotalCount()]);
    }

    /**
     * Авторизует пользователя по кукам или хешу ссылки
     * @param int $question_id
     * @return bool
     * @throws ForbiddenHttpException
     */
    public function auth(int $question_id): bool
    {
        $linkKey = Yii::$app->request->get('link');
        if ($question_id && !Yii::$app->user->isGuest) {
            $haveRights = FoquzQuestion::find()
                ->joinWith('poll')
                ->where([
                    'foquz_question.id'     => $question_id,
                    'foquz_poll.company_id' => Yii::$app->user->identity->company->id
                ])
                ->exists();
            if ($haveRights) {
                return true;
            }
        } elseif ($question_id && is_string($linkKey) && strlen($linkKey) === 32) {
            $linkPollID = FoquzPollStatsLink::find()
                ->select('foquz_poll_id')
                ->where(['right_level' => FoquzPollStatsLink::RIGHT_READ])
                ->andWhere(['like', 'link', '/stats/' . $linkKey])
                ->scalar();
            if ($linkPollID && FoquzQuestion::find()->where([
                    'id'            => $question_id,
                    'foquz_poll_id' => $linkPollID
                ])->exists()) {
                return true;
            }
        }
        throw new ForbiddenHttpException('Доступ запрещен');
    }

    public function getFilterSettings($poll_id)
    {
        $filterSetting = null;
        if (!Yii::$app->user->isGuest && !Yii::$app->request->get('link') && !Yii::$app->request->get('link-key')) {
            $filterSetting = FoquzPollStatFilterSettings::findOne([
                'foquz_poll_id' => $poll_id,
                'user_id'       => Yii::$app->user->id
            ])->filter_settings ?? null;
        }
        return $filterSetting;
    }
}
