// @ts-check
import { defineConfig, devices } from '@playwright/test'

/**
 * Read environment variables from file.
 * https://github.com/motdotla/dotenv
 */
// require('dotenv').config();

const BASE_URL = 'https://devfoquz.ru'
const mainSnapshotsPath
    = 'tests/e2e/snapshots/{projectName}/{testFilePath}/{arg}{ext}'
const localSnapshotsPath
    = 'tests/e2e/.local-snapshots/{projectName}/{testFilePath}/{arg}{ext}'

export default defineConfig({
    testDir: './tests/e2e',
    /* Run tests in files in parallel */
    fullyParallel: true,
    snapshotPathTemplate: process.env.CI ? mainSnapshotsPath : localSnapshotsPath,
    reporter: [
        [
            'html',
            {
                outputFolder: 'tests/e2e/playwright-report',
                open: process.env.CI ? 'never' : 'always',
            },
        ],
    ],
    expect: {
        toHaveScreenshot: {
            maxDiffPixels: 300,
        },
    },
    /* Fail the build on CI if you accidentally left test.only in the source code. */
    forbidOnly: !!process.env.CI,
    /* Retry on CI only */
    retries: process.env.CI ? 3 : 1,
    workers: process.env.CI ? 1 : 4,
    /* Reporter to use. See https://playwright.dev/docs/test-reporters */
    // reporter: "html",
    /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
    use: {
        /* Base URL to use in actions like `await page.goto('/')`. */
        // baseURL: 'http://127.0.0.1:3000',

        /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
        trace: 'on-first-retry',
        baseURL: BASE_URL,
        viewport: { width: 1024, height: 800 },
    },

    /* Configure projects for major browsers */
    projects: [
        // {
        //   name: "firefox",
        //   use: {
        //     ...devices["Desktop Firefox"],
        //     viewport: { width: 600, height: 600 },
        //   },
        // },

        {
            name: 'webkit',
            use: { ...devices['Desktop Safari'] },
        },

        /* Test against mobile viewports. */
        // {
        //   name: 'Mobile Chrome',
        //   use: { ...devices['Pixel 5'] },
        // },
        // {
        //   name: 'Mobile Safari',
        //   use: { ...devices['iPhone 12'] },
        // },

        /* Test against branded browsers. */
        // {
        //   name: 'Microsoft Edge',
        //   use: { ...devices['Desktop Edge'], channel: 'msedge' },
        // },
        // {
        //   name: 'Google Chrome',
        //   use: { ...devices['Desktop Chrome'], channel: 'chrome' },
        // },
    ],

})
