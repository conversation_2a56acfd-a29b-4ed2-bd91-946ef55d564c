/* eslint-disable */
const TerserPlugin = require("terser-webpack-plugin");
const { sentryWebpackPlugin } = require("@sentry/webpack-plugin");
const mode = process.env.NODE_ENV || 'development';
const PRODUCTION = mode === 'production';
const SentryConfig = require("./sentryConfig.json");

const path = require('path');
// trigger dep

const config = {
  mode: mode === 'production' ? 'production' : 'development',
  
  resolve: {
    fallback: {
      path: false,
      crypto: false
    },
    extensions: ['.js', '.ts', '.json', '.less', '.html'],
    alias: {
      '@': path.resolve(__dirname, 'ko'),
      Web: path.resolve(__dirname, 'web'),
      Bindings: path.resolve(__dirname, 'ko/bindings'),
      Components: path.resolve(__dirname, 'ko/components'),
      Data: path.resolve(__dirname, 'ko/data'),
      Modals: path.resolve(__dirname, 'ko/modals'),
      Dialogs: path.resolve(__dirname, 'ko/dialogs'),
      Models: path.resolve(__dirname, 'ko/models'),
      Entities: path.resolve(__dirname, 'ko/entities'),
      Presentation: path.resolve(__dirname, 'ko/presentation'),
      Utils: path.resolve(__dirname, 'ko/utils'),
      Settings: path.resolve(__dirname, 'ko/settings'),
      Layouts: path.resolve(__dirname, 'ko/layouts'),
      Vendor: path.resolve(__dirname, 'vendorjs'),
      Style: path.resolve(__dirname, 'less/vars'),

      Legacy: path.resolve(__dirname, 'ko/legacy'),
      Blocks: path.resolve(__dirname, 'ko/legacy/blocks')
    }
  },

  entry: require('./webpack/entries'),
  output: {
    publicPath: '/js/',
    filename: '[name].js',
    //path: path.resolve(__dirname, 'modules/foquz/public/build')
    path: path.resolve(__dirname, 'web/js'),
    chunkFilename: '[id].[fullhash].js',
  },

  module: {
    rules: [
      require('./webpack/presets/ts-preset')(PRODUCTION),
      require('./webpack/presets/js-preset')(PRODUCTION),
      require('./webpack/presets/less-preset')(PRODUCTION),
      require('./webpack/presets/html-preset')(PRODUCTION),
      require('./webpack/presets/img-preset')(PRODUCTION),
      require('./webpack/presets/fonts-preset')(PRODUCTION),
      require('./webpack/presets/svg-preset')(PRODUCTION)
    ].filter(Boolean)
  },
  devtool: "source-map",
  plugins: [
    require('./webpack/plugins/extract-css-plugin')(PRODUCTION),
    require('./webpack/plugins/image-minimizer-plugin')(PRODUCTION),
  ].filter(Boolean),
  optimization: {
    minimize: PRODUCTION,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: PRODUCTION,
          },
        },
      }),
    ],
  },
};

if (mode !== 'development') {
  config.plugins.push(
      sentryWebpackPlugin(SentryConfig[mode])
  )
  /*config.plugins.push(
    sentryWebpackPlugin({
      url: "https://sentry.doxswf.ru/",
      org: "foquz",
      project: process.env.SENTRY_PROJECT || "js-foquzru",
      authToken: process.env.SENTRY_AUTH_TOKEN || "****************************************************************",
      release: {name: process.env.SENTRY_RELEASE},
    })
  )*/
}

module.exports = config;
