<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%foquz_question_first_click_area}}`.
 */
class m250526_141616_create_foquz_question_first_click_area_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%foquz_question_first_click_area}}', [
            'id' => $this->primaryKey(),
            'question_id' => $this->integer(),
            'name' => $this->string()->comment('Название области клика'),
            'height' => $this->integer()->comment('Высота области клика'),
            'width' => $this->integer()->comment('Ширина области клика'),
            'x_coord' => $this->integer()->comment('Координата X области клика'),
            'y_coord' => $this->integer()->comment('Координата Y области клика'),
        ]);
        $this->addForeignKey(
            'fk-foquz_question_first_click_area-question_id',
            '{{%foquz_question_first_click_area}}',
            'question_id',
            '{{%foquz_question}}',
            'id',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk-foquz_question_first_click_area-question_id', '{{%foquz_question_first_click_area}}');
        $this->dropTable('{{%foquz_question_first_click_area}}');
    }
}
