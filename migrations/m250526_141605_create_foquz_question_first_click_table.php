<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%foquz_question_first_click}}`.
 */
class m250526_141605_create_foquz_question_first_click_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%foquz_question_first_click}}', [
            'id' => $this->primaryKey(),
            'foquz_question_id' => $this->integer(),
            'mobile_view' => $this->tinyInteger(1)->notNull()->defaultValue(0)
                ->comment('Отображение на сматрфоне, 0-по ширине, 1-по высоте'),
            'min_click' => $this->integer()->notNull()->defaultValue(1)
                ->comment('Min кол-во кликов'),
            'max_click' => $this->integer()->null()->defaultValue(null)
                ->comment('<PERSON> кол-во кликов'),
            'show_time' => $this->integer()->null()->defaultValue(null)
                ->comment('Время показа изображения, секунд'),
            'button_text' => $this->string()->null()->defaultValue(null)
            ->comment('Текст кнопки'),
            'allow_cancel_click' => $this->boolean()->notNull()->defaultValue(false)
            ->comment('Возможность отменить клик'),
        ]);
        $this->addForeignKey(
            'fk-foquz_question_first_click-foquz_question_id',
            '{{%foquz_question_first_click}}',
            'foquz_question_id',
            '{{%foquz_question}}',
            'id',
            'CASCADE'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk-foquz_question_first_click-foquz_question_id', '{{%foquz_question_first_click}}');
        $this->dropTable('{{%foquz_question_first_click}}');
    }
}
