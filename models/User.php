<?php

namespace app\models;

use app\components\SendsayService;
use app\helpers\DevHelper;
use app\models\company\Company;
use app\models\company\CompanyFilterAccess;
use app\models\company\CompanyFilterSet;
use app\models\company\CompanyStaff;
use app\modules\foquz\behaviors\CreateAccessTokenBehavior;
use app\modules\foquz\models\BrowserNotification;
use app\modules\foquz\models\BrowserNotificationUserDevice;
use app\modules\foquz\models\EditorFolder;
use app\modules\foquz\models\FilialEmployeeSettings;
use app\modules\foquz\models\FilialEmployeeUserFilial;
use app\modules\foquz\models\FoquzContact;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\log\Log;
use app\modules\foquz\models\notifications\SiteNotification;
use app\modules\foquz\models\RespondentPoll;
use app\modules\foquz\models\Tariff;
use mohorev\file\UploadImageBehavior;
use OAuth2\Storage\UserCredentialsInterface;
use webvimark\modules\UserManagement\models\rbacDB\Role;
use webvimark\modules\UserManagement\models\User as BaseUser;
use Yii;
use yii\base\InvalidConfigException;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveQuery;
use yii\db\Exception;
use yii\helpers\ArrayHelper;
use yii\imagine\Image;
use yii\web\NotFoundHttpException;

/**
 * Class User
 *
 * @property Company $company
 * @property $quick_help_showed boolean
 * @property boolean $allFilials
 * @property $mobile_quick_help_showed boolean
 * @property string $phone
 * @property string $photo
 * @property int $language_id
 * @property string $zapier_access
 * @property string $avatar
 * @property BrowserNotification[] $browserNotifications
 * @property SiteNotification[] $notifications
 * @property FilialEmployeeUserFilial[] $userFilials
 * @property string $name
 * @property string $access_token
 * @property bool $clients_section // Доступ к разделу "Клиенты" для роли Редактор
 * @property bool $hide_personal_data // Скрыть персональные данные клиента для роли Исполнитель
 * @property bool $can_edit_answers Может редактировать анкеты (для роли Исполнитель)
 * @property bool $superadmin
 * @property EditorFolder[] $editorFolders
 * @property RespondentPoll[] $respondentPolls Опросы, доступные для прохождения для роли Респондент
 *
 * @property string $correctName
 *
 * @property CompanyFilterSet[] $companyFilterSettings
 * @property CompanyFilterSet[] $companyFilterSettingsWithSort
 * @property CompanyFilterAccess[] $companyFilterAccess
 * @property-read BrowserNotificationUserDevice[] $notificationDevices
 */
class User extends BaseUser implements UserCredentialsInterface
{
    const DEFAULT_ROLE = 'foquz_admin';
    const AVATARS_FOLDER = 'uploads/avatars';
    const AVATAR_MAXSIZE = 5 * 1024 * 1024;

    public $companies = [];

    public function behaviors()
    {
        return [
            TimestampBehavior::className(),
            CreateAccessTokenBehavior::className(),
            [
                'class' => \app\models\components\UploadImageBehavior::class,
                'attribute' => 'avatar',
                'scenarios' => ['newUser', 'updateUser'],
                'placeholder' => '@app/web/avatars/dummy.png',
                'path' => '@app/web/' . self::AVATARS_FOLDER . '/{id}',
                'url' => '/' . User::AVATARS_FOLDER . '/{id}',
                'thumbs' => [
                    'thumb' => ['width' => 400, 'quality' => 90],
                    'preview' => ['width' => 200, 'height' => 200],
                    'news_thumb' => ['width' => 200, 'height' => 200, 'bg_color' => '000'],
                ],
            ],
        ];
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios['updateUser'] = [
            'password',
            'avatar',
            'name',
            'status',
            'superadmin',
            'username',
            'bind_to_ip',
            'email',
            'email_confirmed',
            'companies',
            'phone',
            'clients_section',
            'hide_personal_data',
            'can_edit_answers'
        ];
        return $scenarios;
    }

    public function rules()
    {
        $rules = parent::rules();
        $rules = array_filter($rules, static function($rule) {
            return $rule !== ['email', 'validateEmailConfirmedUnique'];
        });
        $rules = array_values($rules);

        $rules[] = [['name'], 'string', 'max' => 250];
        $rules[] = [['companies', 'quick_help_showed', 'mobile_quick_help_showed'], 'safe'];
        $rules[] = [['language_id', 'superadmin', 'clients_section', 'hide_personal_data'], 'integer'];
        $rules[] = [['can_edit_answers'], 'boolean'];
        $rules[] = [['phone'], 'string', 'max' => 15];
        $rules[] = ['avatar', 'file',
            'extensions' => 'jpg, jpeg, gif, png, svg',
            'mimeTypes' => ['image/gif', 'image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'],
            'checkExtensionByMimeType' => true,
            'on' => ['newUser', 'updateUser'],
            'tooBig' => \Yii::t('validation', "Размер файла не должен превышать " . (self::AVATAR_MAXSIZE/1024/1024) . " Мб"),
            'maxSize' => self::AVATAR_MAXSIZE,
            'skipOnEmpty' => true
        ];
        $rules[] = ['zapier_access', 'datetime', 'format' => 'php:Y-m-d H:i:s'];
        $rules[] = ['username', 'match', 'pattern' => '/^[\w@\._-]+$/i'];
        $rules[] = ['email', 'unique', 'message' => 'Такой E-mail уже существует'];
        $rules[] = ['password', 'validatePasswordStrength', 'on' => ['newUser', 'updateUser']];

        return $rules;
    }

    public function fields()
    {
        return ArrayHelper::merge(
            parent::fields(),
            [
                'photo' => function () {return $this->getThumbUploadUrl('avatar', 'preview');}
            ]
        );
    }

    public function extraFields()
    {
        return ['editorFolders'];
    }

    /**
     * @throws Exception
     * @throws InvalidConfigException
     * @throws NotFoundHttpException
     */
    public function afterSave($insert, $changedAttributes)
    {
        parent::afterSave($insert, $changedAttributes);
        Log::createRecord($this, $insert ? Log::TYPE_CREATE : Log::TYPE_UPDATE, $changedAttributes);

        $params = Yii::$app->request->getBodyParams();
        if (isset($params['role']) && ($params['role'] === 'editor' || $params['role'] === 'foquz_watcher') && isset($params['folders'])) {
            EditorFolder::deleteAll(['user_id' => $this->id]);
            foreach ($params['folders'] as $folder) {
                if (!FoquzPoll::findOne(['id' => $folder, 'is_folder' => true, 'deleted' => false])) {
                    throw new NotFoundHttpException("Папка с ID$folder не найдена");
                }
                $editorFolder = new EditorFolder();
                $editorFolder->user_id = $this->id;
                $editorFolder->folder_id = $folder;
                if (!$editorFolder->save()) {
                    throw new Exception('Не удалось сохранить папку, доступную пользователю');
                }
            }
        } elseif (isset($params['role'], $params['polls']) && $params['role'] === 'foquz_respondent') {
            RespondentPoll::deleteAll(['user_id' => $this->id]);
            if (!empty($params['polls'])) {
                $pollsCount = FoquzPoll::find()->where(['id' => $params['polls'], 'is_folder' => false]);
                if (!empty($this->company->id)) {
                    $pollsCount->andWhere(['company_id' => $this->company->id]);
                }
                $pollsCount = $pollsCount->count();
                if ((int)$pollsCount !== count($params['polls'])) {
                    throw new NotFoundHttpException('Один или несколько опросов респондента не найдены');
                }
                $insertArray = [];
                foreach ($params['polls'] as $poll) {
                    $insertArray[] = [$this->id, $poll];
                }
                if (!empty($insertArray)) {
                    Yii::$app->db->createCommand()->batchInsert(RespondentPoll::tableName(), ['user_id', 'poll_id'], $insertArray)->execute();
                }
            }
        }
    }

    public function afterDelete()
    {
        parent::afterDelete();
        Log::createRecord($this, Log::TYPE_DELETE);
    }

    public static function findIdentityByAccessToken($token, $type = null)
    {
        return static::findOne(['access_token' => $token, 'status'=>User::STATUS_ACTIVE]);
    }

    /**
     * Implemented for Oauth2 Interface
     */
    public function checkUserCredentials($username, $password)
    {
        $user = static::findByUsername($username);
        if (empty($user)) {
            return false;
        }
        return $user->validatePassword($password);
    }

    /**
     * Implemented for Oauth2 Interface
     */
    public function getUserDetails($username)
    {
        $user = static::findByUsername($username);
        return ['user_id' => $user->getId()];
    }

    public function getCorrectName()
    {
        return $this->name ?: $this->username;
    }

    public function getCompanyStaff()
    {
        return $this->hasOne(CompanyStaff::class, ['user_id' => 'id']);
    }

    public function getCompany()
    {
        return $this->hasOne(Company::class, ['id' => 'company_id'])->via('companyStaff');
    }

    public function collectArrayForUsersPage()
    {
        $companyData = [];
        $companies = CompanyStaff::find()
            ->leftJoin('company', 'company.id = company_staff.company_id')
            ->select('company.id, name, alias')
            ->where(['user_id' => $this->id])
            ->andWhere(['deleted' => 0])
            ->asArray()
            ->all();
        $companyIds = ArrayHelper::getColumn($companies, 'id');
        foreach($companies as $company) {
            $companyData[] = [
                'name' => $company['name'],
                'url' => 'http://'.$company['alias']
            ];
        }
        $companyNames = ArrayHelper::getColumn($companies, 'name');
        $roles = [];
        $rolesIds = [];
        foreach(Role::getUserRoles($this->id) as $role) {
            $roles[] = $role->description;
            $rolesIds[] = $role->name;
        }
        asort($roles);
        $data = [
            'id' => $this->id,
            'photo' => [
                'preview' => $this->getThumbUploadUrl('avatar', 'preview'),
                'detail' => $this->getThumbUploadUrl('avatar', 'thumb')
            ],
            'login' => $this->username,
            'email' => $this->email,
            'phone' => FoquzContact::formatPhone($this->phone),
            'email_confirmed' => (int)$this->email_confirmed,
            'activity' => (int)$this->status,
            'addedAt' => date('d.m.Y', $this->created_at)
        ];
        $data['company'] = $companyData;
        $data['company_id'] = count($companyIds) > 0 ? (int)$companyIds[0] : null;
        $data['role'] = count($roles) > 0 ? $roles[0] : null;
        $data['role_id'] = count($rolesIds) > 0 ? $rolesIds[0] : null;
        $data['respondent_polls'] = ArrayHelper::getColumn($this->respondentPolls, 'poll_id');
        if ($folders = $this->getFolderPaths($this->id)) {
            $data['editor_folders'] = $folders;
        }

        if ($data['role_id']=="filial_employee") {
            $allFilials = Filial::find()->where(["company_id"=>$companyIds])->count();
            $filials = ArrayHelper::getColumn($this->getUserFilials()->all(), "filial_id");
            $data["filials"] = $filials;
            $data["allFilials"] = count($filials)==0 || count($filials)==$allFilials;
            $settings = $this->getFilialEmployeeSettings()->one();
            if ($settings) {
                $data["googleReviewAnswer"] = $settings->can_google_review_answer ? true : false;
                $data["answerProcessing"] = $settings->can_process_answer ? true : false;
            }
        }

        if($this->superadmin) $data['company'] = null;
        if($this->avatar === null) $data['photo'] = false;
        if($this->name !== null) $data['name'] = $this->name;
        $data['clients_section'] = $this->isClientsSectionAccess();
        $data['hide_personal_data'] = $this->isHidePersonalData();
        $data['can_edit_answers'] = $this->can_edit_answers;

        return $data;
    }

    public function isHidePersonalData(): bool
    {

        // Для пользователя с ролью Исполнитель, когда у компании тариф Базовый и отключён доступ к разделу Контакты, не нужно отображать информацию о Респонденте в Анкете
        if ($this->isExecutor() && $this->company->tariff->id === Tariff::TARIFF_BASE) {
            return !$this->company->contacts_enabled;
        }
        // Если у компании тариф Базовый и настройках компании выключена опция Доступ к разделу «Контакты,
        // то при авторизации пользователя под этой ролью, игнорировать состояние опции Скрыть персональные данные клиента.
        if (!Yii::$app->user->isSuperadmin && $this->company->tariff->id === Tariff::TARIFF_BASE) {
            return $this->company->contacts_enabled;
        }

        return $this->hide_personal_data;
    }
    public function isClientsSectionAccess(): bool
    {
        // Если у компании тариф Базовый и настройках компании выключена опция Доступ к разделу «Контакты»,
        // то при авторизации пользователя под этой ролью, игнорировать состояние опции Доступ к разделу «Клиенты» в настройках роли, этот раздел будет не доступен.
        if (!Yii::$app->user->isSuperadmin && $this->company->tariff->id === Tariff::TARIFF_BASE) {
            return $this->company->contacts_enabled;
        }

        return $this->clients_section;
    }

    public function getFolderPaths($id)
    {
        $editorFolders = EditorFolder::findAll(['user_id' => $id]);
        $array = [];
        foreach ($editorFolders as $editorFolder) {
            $foquzPoll = FoquzPoll::findOne($editorFolder->folder_id);
            if (!$foquzPoll) {
                throw new NotFoundHttpException('Папка не найдена');
            }
            $id = $foquzPoll->id;
            $name = $path = $foquzPoll->name;
            while ($foquzPoll->folder_id) {
                $foquzPoll = FoquzPoll::findOne($foquzPoll->folder_id);
                if ($foquzPoll) {
                    $path = $foquzPoll->name . '/' . $path;
                }
            }
            $array[] = [
                'id' => $id,
                'name' => $name,
                'path' => $path
            ];
        }

        return $array;
    }

    public function getAllFilials()
    {
        $isAllFilials = true;

        $company = CompanyStaff::find()
            ->leftJoin('company', 'company.id = company_staff.company_id')
            ->where(['user_id' => $this->id])
            ->andWhere(['deleted' => 0])
            ->one();

        if($this->isFilialEmployee() && $company) {
            $filials = $this->getUserFilials()->count();
            $allFilials = Filial::find()->where(["company_id"=>$company->company_id])->count();
            $isAllFilials = $filials==0 || $filials==$allFilials;
        }

        return $isAllFilials;
    }

    public static function findByUsername($username)
    {
        return static::find()
            ->where(['username' => $username])
            ->orWhere(['email' => $username])
            ->one();
    }

    public function isReportAdmin()
    {
        $roles = Role::getUserRoles($this->id);
        return count($roles) === 1 && array_key_exists('foquz_report_admin', $roles);
    }

    public function isExecutor()
    {
        $roles = Role::getUserRoles($this->id);
        return count($roles) === 1 && array_key_exists('foquz_executor', $roles);
    }

    public function isAdmin()
    {
        $roles = Role::getUserRoles($this->id);
        return count($roles) === 1 && array_key_exists('foquz_admin', $roles);
    }

    public function isWikiEditor()
    {
        $roles = Role::getUserRoles($this->id);
        return count($roles) === 1 && array_key_exists('wiki_editor', $roles);
    }

    public function isEditor()
    {
        $roles = Role::getUserRoles($this->id);
        return count($roles) === 1 && array_key_exists('editor', $roles);
    }

    public function isWatcher()
    {
        $roles = Role::getUserRoles($this->id);
        return count($roles) === 1 && array_key_exists('foquz_watcher', $roles);
    }

    public function isFilialEmployee()
    {
        $roles = Role::getUserRoles($this->id);
        return count($roles) === 1 && array_key_exists('filial_employee', $roles);
    }

    public function isRespondent()
    {
        $roles = Role::getUserRoles($this->id);
        return count($roles) === 1 && array_key_exists('foquz_respondent', $roles);
    }

    public function getNotifications()
    {
        return $this->hasMany(SiteNotification::className(), ['user_id' => 'id']);
    }

    public function getNotificationDevices()
    {
        return $this->hasMany(BrowserNotificationUserDevice::className(), ['user_id' => 'id']);
    }

    public function getUserFilials()
    {
        return $this->hasMany(FilialEmployeeUserFilial::class, ['user_id' => 'id']);
    }

    public function getFilialEmployeeSettings()
    {
        return $this->hasOne(FilialEmployeeSettings::class, ['user_id' => 'id']);
    }

    public function getEditorFolders()
    {
        return $this->hasMany(EditorFolder::class, ['user_id' => 'id']);
    }

    public function getRespondentPolls()
    {
        return $this->hasMany(RespondentPoll::class, ['user_id' => 'id']);
    }

    public static function deleteSessionDevices($session)
    {
        BrowserNotificationUserDevice::deleteAll(['session' => $session]);
    }

    public function canGoogleReviewAnswer($filial_id = null)
    {
        if(!$this->isFilialEmployee())
            return true;
        $can = $this->filialEmployeeSettings->can_google_review_answer ?? false;
        if(!$can)
            return false;
        if($filial_id and $this->userFilials and
            !in_array($filial_id, array_map(function($item){ return $item->id; }, $this->userFilials)))
            return false;
        return true;
    }

    public function canProcessAnswer($filial_id = null)
    {
        if(!$this->isFilialEmployee())
            return true;

        $can = $this->filialEmployeeSettings->can_process_answer ?? false;
        if(!$can)
            return false;

        //print($filial_id);;
        //print_r($this->userFilials);

        if($filial_id and $this->userFilials and
            !in_array($filial_id, array_map(function($item){ return $item->filial_id; }, $this->userFilials)))
            return false;
        return true;
    }

    public function validate($attributeNames = null, $clearErrors = true)
    {
        $isValid = parent::validate($attributeNames, $clearErrors); // TODO: Change the autogenerated stub
        $postData = Yii::$app->request->post();
        if(!Yii::$app->user->isGuest && Yii::$app->user->identity->superadmin && ($postData['User']['role'] ?? $postData['role']) === 'filial_employee') {
            if (!$postData['company'] or !Filial::findOne(['company_id' => $postData['company']])) {
                $this->addError('company', ['Для сотрудника филиала должна быть указана компания']);
                $isValid = false;
            }
        }
        return $isValid;
    }

    public function validatePasswordStrength($attribute, $params)
    {
        if (
            strlen($this->password) < 8 ||
            !preg_match('/[A-Z]/', $this->password) ||
            !preg_match('/[a-z]/', $this->password) ||
            !preg_match('/\d/', $this->password) ||
            !preg_match('/[!"#$%&\'()*+,-.\/:;<=>?@\[\]^_`{|}~]/', $this->password) ||
            preg_match('/\s/', $this->password)
        ) {
            $this->addError($attribute, 'Пароль должен содержать: <li>не менее 8 символов, <li> заглавные и строчные буквы (A-Z, a-z), <li> не менее одной цифры (0-9), <li>не менее одного спецсимвола (!, #, +, % и т.п.). <br> Пробелы не допускаются.');
        }
    }
    
    public function notify($password)
    {
        /*(new SendsayService(\Yii::$app->params['sendsay']['login'], \Yii::$app->params['sendsay']['password']))->sendEmail(
            Yii::$app->params['main_sender_email'],
            "Опросы Foquz.ru",
            $this->email,
            'Добавлена учётная запись в Foquz',
            Yii::$app->controller->renderPartial('@app/mail/new-user.php', [
                'login' => $this->username,
                'password' => $password
            ])
        );*/
    }
    
    
    public function getLanguage()
    {
        return $this->hasOne(Language::class, ['id' => 'language_id']);
    }
    
    private static function mime_header_encode($str, $data_charset, $send_charset) {
        if($data_charset != $send_charset) {
            $str = iconv($data_charset, $send_charset, $str);
        }
        return '=?' . $send_charset . '?B?' . base64_encode($str) . '?=';
    }

    public function isZapierAccessAllowed() : bool
    {
        return $this->zapier_access !== null;
    }

    public function allowZapierAccess() : bool
    {
        if ($this->isZapierAccessAllowed() === true) {
            throw new \Exception('Already accepted');
        }

        $this->zapier_access = (new \DateTime())->format('Y-m-d H:i:s');
        return $this->save();
    }

    /**
     * @return bool
     */
    public function isActive()
    {
        return (bool)$this->status;
    }

    /**
     * @return ActiveQuery | CompanyFilterSet[]
     */
    public function getCompanyFilterSettings()
    {
        return $this->hasMany(CompanyFilterSet::class, ['id' => 'settings_id'])
            ->via('companyFilterAccess')
            ->joinWith('author')
            ->where([self::tableName().'.status' => 1]);
    }

    /**
     * @return ActiveQuery | CompanyFilterAccess[]
     */
    public function getCompanyFilterAccess()
    {
        return $this->hasMany(CompanyFilterAccess::class, ['user_id' => 'id']);
    }

    /**
     * @return ActiveQuery | CompanyFilterSet[]
     */
    public function getCompanyFilterSettingsWithSort()
    {
        return $this->getCompanyFilterSettings()->orderBy([CompanyFilterSet::tableName().'.updated' => SORT_DESC]);
    }

    public function roleList() : array
    {
        foreach(Role::getUserRoles($this->id) as $role) {
            $roles[] = $role->description;
        }

        return $roles;
    }
}
