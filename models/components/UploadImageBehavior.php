<?php

namespace app\models\components;

use yii\base\InvalidArgumentException;
use yii\db\BaseActiveRecord;
use yii\helpers\FileHelper;
use yii\web\UploadedFile;

class UploadImageBehavior extends \mohorev\file\UploadImageBehavior
{
    protected function createThumbs()
    {
        $path = $this->getUploadPath($this->attribute);
        if (!is_file($path)) {
            return;
        }

        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));

        if ($extension === 'svg') {
            $dirtySvg = file_get_contents($path);
            $cleanSvg = preg_replace('/<script\b[^>]*>(.*?)<\/script>/is', '', $dirtySvg);
            file_put_contents($path, $cleanSvg);
        }

        foreach ($this->thumbs as $profile => $config) {
            $thumbPath = $this->getThumbUploadPath($this->attribute, $profile);
            if ($thumbPath !== null) {
                if (!FileHelper::createDirectory(dirname($thumbPath))) {
                    throw new InvalidArgumentException(
                        "Directory specified in 'thumbPath' attribute doesn't exist or cannot be created."
                    );
                }
                if (!is_file($thumbPath)) {
                    if ($extension === 'svg') {
                        copy(
                            $path,
                            $thumbPath
                        );
                        continue;
                    }
                    $this->generateImageThumb($config, $path, $thumbPath);
                }
            }
        }

        if ($this->deleteOriginalFile) {
            parent::delete($this->attribute);
        }
    }


    public function beforeSave()
    {
        /** @var BaseActiveRecord $model */
        $model = $this->owner;
        if (in_array($model->scenario, $this->scenarios)) {
            if ($this->file instanceof UploadedFile) {
                if (!$model->getIsNewRecord() && $model->isAttributeChanged($this->attribute)) {
                    if ($this->unlinkOnSave === true) {
                        $this->delete($this->attribute, true);
                    }
                }
                $model->setAttribute($this->attribute, $this->file->name);
            } else {
                if ($this->unlinkOnSave === true) {
                    $this->delete($this->attribute, true);
                }
                $model->{$this->attribute} = null;
            }
        } else {
            if (!$model->getIsNewRecord() && $model->isAttributeChanged($this->attribute)) {
                if ($this->unlinkOnSave === true) {
                    $this->delete($this->attribute, true);
                }
            }
        }
    }
}