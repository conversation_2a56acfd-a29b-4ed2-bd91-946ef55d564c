stages:
    - prebuild
    - main_devfoquz
    - build-base
    - build
    - deploy
    - mail_devfoquz
    - widget_new_devfoquz
    - test

variables:
    TAG: latest

.rules_build_main_js:
    rules:
        - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
          changes:
              paths:
                  - 'ko/**/*'
                  - 'less/**/*'
                  - 'messages/**/*'
                  - 'webpack/**/*'

.rules_build_mail_js:
    rules:
        - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
          changes:
              paths:
                  - 'emails-markup/**/*'
                  - 'mail/**/*'

.rules_build_widget_js:
    rules:
        - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
          changes:
              paths:
                  - 'ko/widgets/poll_new/**/*'

stage_main_devfoquz:
    stage: main_devfoquz
    resource_group: devfoquz
    rules:
        - if: $PIPELINE_TRIGGERED
          when: never
        - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    tags:
        - devfoquz
    script:
        - composer install
        - cp /var/www/devfoquz.ru/project/config/db.php config/db.php
        - cp /var/www/devfoquz.ru/project/config/db-log.php config/db-log.php
        - cp /var/www/devfoquz.ru/project/config/mailer.php config/mailer.php
        - cp /var/www/devfoquz.ru/project/config/params-local.php config/params-local.php
        - cp /var/www/devfoquz.ru/project/config/queue.php config/queue.php || true
        - cp /var/www/devfoquz.ru/project/config/rabbit.php config/rabbit.php || true
        - cp /var/www/devfoquz.ru/project/config/web-local.php config/web-local.php
        - cp /var/www/devfoquz.ru/project/web/widgets/poll/widget_new.js web/widgets/poll/widget_new.js
        - cp -r /var/www/devfoquz.ru/project/web/widgets/dom/* web/widgets/dom/
        - ./yii migrate --interactive=0
        #- ./yii swagger/go /var/www/swagger.doxswf.ru/manual.yaml
        #- ./yii swagger/go /var/www/redoc.doxswf.ru/manual.yaml v1
        - rsync -azvh --delete --exclude web/js --exclude mail --exclude .git --exclude runtime --exclude web/poll-vue --exclude web/poll-vue-preview --exclude web/uploads --exclude web/assets --exclude web/avatars --exclude web/img/smiles  --exclude upload ./  /var/www/devfoquz.ru/project
        #  - pkill -f "queue/listen" || true
        - pkill -f "rabbit/listen" || true


stage_front_devfoquz:
    stage: build
    resource_group: devfoquz
    needs: ['stage_main_devfoquz']
    rules:
        - if: $PIPELINE_TRIGGERED
          when: never
        - !reference [.rules_build_main_js, rules]
    tags:
        - devfoquz
    artifacts:
        paths:
            - web/js/
    script:
        - export PATH=$PATH:/home/<USER>/.nvm/versions/node/v14.19.3/bin
        - export NODE_OPTIONS="--max-old-space-size=5120"
        - npm install -force
        - npm run build
        - cd web/js
        - rsync -azvh --delete ./ /var/www/devfoquz.ru/project/web/js


stage_mail_devfoquz:
    stage: mail_devfoquz
    resource_group: devfoquz
    needs: ['stage_main_devfoquz']
    rules:
        - if: $PIPELINE_TRIGGERED
          when: never
        - !reference [.rules_build_mail_js, rules]
    tags:
        - devfoquz
    artifacts:
        paths:
            - mail/
    script:
        - export PATH=$PATH:/home/<USER>/.nvm/versions/node/v14.19.3/bin
        - export OPENSSL_CONF=/dev/null
        - cd mail
        - rsync -azvh --delete ./ /var/www/devfoquz.ru/project/mail
        - cd ../emails-markup
        - npm install
        - npm run deploy
        - cd ../mail
        - rsync -azvh --delete ./ /var/www/devfoquz.ru/project/mail


stage_widget_new_devfoquz:
    stage: widget_new_devfoquz
    resource_group: devfoquz
    needs: ['stage_main_devfoquz']
    rules:
        - if: $PIPELINE_TRIGGERED
          when: never
        - !reference [.rules_build_widget_js, rules]
    tags:
        - devfoquz
    artifacts:
        paths:
            - web/widgets/poll/
    script:
        - export PATH=$PATH:/home/<USER>/.nvm/versions/node/v14.19.3/bin
        - cd ko/widgets/poll_new
        - npm install
        - npm run build
        - cd ../../../web/widgets/poll
        - rsync -azvh ./ /var/www/devfoquz.ru/project/web/widgets/poll

task_branch_k8s:
    stage: prebuild
    rules:
        - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
    image: cr.yandex/crpsvv5iscafkhlubkmt/foquz-debian-builder-base:1.0.0
    script:
        - chmod 777 ./images/scripts/versions.sh
        - ./images/scripts/versions.sh
    artifacts:
        reports:
            dotenv: build.env

run_playwright_tests:
    stage: test
    rules:
        - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
          #changes:
          #  paths:
          #    - 'ko/**/*'
          #    - 'less/**/*'
          #    - 'messages/**/*'
          #    - 'webpack/**/*'
          #    - 'tests/e2e/**/*'
          #    - 'playwright.config.js'
    tags:
        - docker-vps-tw
    image: mcr.microsoft.com/playwright:v1.52.0-jammy
    allow_failure: true
    script:
        - npm ci

        - |
            set +e
            npx playwright test
            PLAYWRIGHT_EXIT_CODE=$?
            set -e
            if [ $PLAYWRIGHT_EXIT_CODE -ne 0 ]; then
              echo "Playwright tests failed with exit code $PLAYWRIGHT_EXIT_CODE"
            fi
        - |

include:
    - 'images/cicd/build-images-base.yml'
    - 'images/cicd/build-images-ci.yml'