You task is to add new question type "Test first click". Below is the task description in Russian, but you should write the code in English. In parentheses are my comments.

Описание

Нужно добавить новый тип вопроса Тест первого клика.

«Тест первого клика» – это быстрый и недорогой способ оценки юзабилити-решений. Респонденту показывают экран и дают задачу, с которой регулярно сталкиваются пользователи продукта. Для ответа на вопрос респонденту нужно кликнуть на один или несколько элементов изображения.

Метод также позволяет оценить, какие элементы рекламного макета наиболее привлекательны для потенциальных клиентов.
Есть у опросников:

    Oprosso
    https://oprosso.ru/landing/blog/study/test-pervogo-klika-zachem-on-nuzhen-i-kak-pravilno-ispolzovat/

    Анкетолог
    https://help.anketolog.ru/p/1098671

    PathWay
    https://pthwy.design/guide/4-methods

Ориентируемся на опроссо.
Опрос / Вопросы

опрос
https://devfoquz.ru/foquz/foquz-question/update?id=187303&pollId=56659

Настройка вопроса
https://www.figma.com/design/VfmUpjti8Rj8fAYX2DsfVS/fqz_poll_settings-questions?node-id=12041-1351&t=gT6ypjubmn5ZAD8s-1

(above is the just the information about the task)

1.
Нужно добавить для контрола Тип вопроса - значение Тест первого клика.
https://disk.yandex.ru/i/m7Ryy6XF0F8Fzw

(tell me where the popup with question type selection is located and where to add new icon)

Пример в выбранном состоянии.
https://disk.yandex.ru/i/TvFQW4f8f1FEzw
2.
Параметры, как и для других типов вопоросов:

    Тумблер Обязательный
    Служебное название
    Название вопроса
    Текст вопроса
    Дополнительное описание
3.

(all questions have it. We have all components for those fields)

Блок Загрузка изображения
https://disk.yandex.ru/i/GscINcpDXQJpEw

    Загружать можно файлы форматов: jpg, jpeg, png, gif, svg
    Ограничение 5 МБ
    Обязательный параметр

    Для выбранного изображения отображается (https://disk.yandex.ru/i/qPlYrKoj9WmUNw):
        Превью
        https://disk.yandex.ru/i/ytf0SyE_aHpoPA
        При клике просмотр в полноэкранном режиме
        Кнопка Удалить
        https://disk.yandex.ru/i/YRRfOvH9k3BELw
        Кнопка Добавить области клика
        https://disk.yandex.ru/i/a3kyos7gxtnV0Q
        Информационный текст
        https://disk.yandex.ru/i/fM62DFwMEf4kTQ

4.
При клике на кнопку Добавить области клика показать сайдшит с добавлением областей.

(SEE screenshot with sidesheet. Sidesheets are defined in ko/dialogs folder)

5.
В сайдшите с добавлением областей можно добавить области на загруженное изображение.
https://disk.yandex.ru/i/6BGsQifFAxL0WA
Для каждой области параметры:

    Название
    Размеры
    Координаты XY


(See the screenshot with sidesheet. IMPORTANT: we need to create knockout component where will be image + where we can define areas for clicks. Find appropriate location for this component)

6.
Добавление областей для изображения необязательно.

7.
Отображение на сматрфоне - селект2 с одним выбором, без строки поиска, без кнопки сброса
https://disk.yandex.ru/i/x5NSEP9T8p1cuQ

    Справочник значений:
        По ширине
        По высоте

    По умолчанию выбрано значение По ширине

(we have component for field with select)

8.
Min кол-во кликов - стандартное поле для ввода, без возможности сбросить значение
https://disk.yandex.ru/i/F68HczHsXV1oiw

    По умолчанию проставлено значение 1
    Маска: целые положительный числа
    Ограничение maxlength=3
    Обязательный параметр

9.
(we have component for field with input)

Max кол-во кликов - стандартное поле для ввода, без возможности сбросить значение
https://disk.yandex.ru/i/YgIv8nlsfGGe7w

    По умолчанию поле пустое
    Маска: целые положительный числа
    Ограничение maxlength=3
    Валидация: если значение введено, то оно должно быть >= Min кол-во кликов
    Необязательный параметр

(we have component for field with input)

10.
Время показа изображения, секунд - стандартное поле для ввода, без возможности сбросить значение
https://disk.yandex.ru/i/4QWtyEjB2OKXbA

    По умолчанию поле пустое
    Маска: целые положительный числа
    Ограничение maxlength=5
    Необязательный параметр


(we have component for field with input)

11.
Текст кнопки - стандартное поле для ввода, без возможности сбросить значение, со счётчиком символов, maxlength=30, placeholder="Показать изображение"
https://disk.yandex.ru/i/tdKcoWuE2KX1Mw

    Если значение не введено, то в прохождении нужно использовать дефолтное значение Показать изображение
    Необязательный параметр

12.
Возможность отменить клик - стилизованный чекбокс, по умолчанию выключен
https://disk.yandex.ru/i/ZWxsIrFR5oklPw

(we have component for field with input)

13.
Опция Пропуск ответа
https://disk.yandex.ru/i/R1mAGPlTHxpgbQ

    placeholder="Затрудняюсь ответить"
    Если значения для параметра не введено, то использовать значение Затрудняюсь ответить

14.
Опция Комментарий
https://disk.yandex.ru/i/Y7yQrWbWIW_70w

Пример в типе вопроса Приоритет
https://disk.yandex.ru/i/TG4knzKVUwcBbw

15.
Баллы для этого типа вопроса не нужны.

(ignore this)

16.
Функционал Донор-Реципиент не нужен для этого типа вопроса.

(ignore this)

17.
Для пользователя с ролью Наблюдатель все параметры должны быть заблокированы.
watcher
12332145

(ignore this)