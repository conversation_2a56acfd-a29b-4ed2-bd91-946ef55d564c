{"info": {"_postman_id": "61203d29-933d-4e7e-89a4-1a2e8eee5754", "name": "#5076 АПИ. Тип вопроса Тест первого клика", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json", "_exporter_id": "17930641"}, "item": [{"name": "Настройки", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "FoquzQuestion[poll_id]", "value": "42597", "type": "text"}, {"key": "FoquzQuestion[description]", "value": "<p style=\"text-align:center;\">Тест первого клика</p>", "type": "text"}, {"key": "FoquzQuestion[main_question_type]", "value": "24", "type": "text"}, {"key": "FoquzQuestion[is_required]", "value": "1", "type": "text"}, {"key": "FoquzQuestion[enableGallery]", "value": "1", "type": "text"}, {"key": "FoquzQuestion[gallery][0][id]", "value": "55899", "description": "id загруженного изображения", "type": "text"}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>[skip]", "value": "1", "type": "text"}, {"key": "FoquzQuestion[skip_text]", "value": "Затрудняюсь ответить", "type": "text"}, {"key": "FoquzQuestion[comment_enabled]", "value": "1", "type": "text"}, {"key": "FoquzQuestion[placeholder_text]", "value": "Подсказка для коммента", "type": "text"}, {"key": "FoquzQuestion[comment_minlength]", "value": "0", "type": "text"}, {"key": "FoquzQuestion[comment_maxlength]", "value": "100", "type": "text"}, {"key": "FoquzQuestion[comment_label]\n", "value": "Коммент", "type": "text"}, {"key": "FoquzQuestion[comment_required]", "value": "0", "type": "text"}, {"key": "FoquzQuestion[firstClick][mobile_view]", "value": "0", "description": "Отображение на сматрфоне 0-по ширине, 1-по высоте", "type": "text"}, {"key": "FoquzQuestion[firstClick][min_click]", "value": "1", "description": "<PERSON> кол-во кликов", "type": "text"}, {"key": "FoquzQuestion[firstClick][max_click]", "value": "", "description": "<PERSON> кол-во кликов", "type": "text"}, {"key": "FoquzQuestion[firstClick][show_time]", "value": "", "description": "Время показа изображения, секунд ", "type": "text"}, {"key": "FoquzQuestion[firstClick][button_text]", "value": "", "description": "Текст кнопки", "type": "text"}, {"key": "FoquzQuestion[firstClick][allow_cancel_click]", "value": "0", "description": "Возможность отменить клик ", "type": "text"}]}, "url": {"raw": "{{base_url}}/foquz/api/questions/update?id=135151&access-token=30ZKhybu_U1MLi0", "host": ["{{base_url}}"], "path": ["foquz", "api", "questions", "update"], "query": [{"key": "id", "value": "135151"}, {"key": "access-token", "value": "30ZKhybu_U1MLi0"}]}}, "response": []}, {"name": "Загрузка изображения", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "UploadForm[file]", "type": "file", "src": "/D:/картинки-фотки/SampleJPGImage_2mbmb.jpg"}]}, "url": {"raw": "http://localhost:8081/foquz/api/questions/image-upload?access-token=30ZKhybu_U1MLi0&id=135151&scenario=first_click", "protocol": "http", "host": ["localhost"], "port": "8081", "path": ["foquz", "api", "questions", "image-upload"], "query": [{"key": "access-token", "value": "30ZKhybu_U1MLi0"}, {"key": "id", "value": "135151"}, {"key": "scenario", "value": "first_click"}]}}, "response": []}, {"name": "Добавление/редактирование областей картинки", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "FirstClickArea[0][id]", "value": "1", "description": "Id записи, 0 - новая запись", "type": "text"}, {"key": "FirstClickArea[0][height]", "value": "2", "description": "высота области, px", "type": "text"}, {"key": "FirstClickArea[0][width]", "value": "333", "description": "ширина области, px", "type": "text"}, {"key": "FirstClickArea[0][x_coord]", "value": "", "description": "X-координата области", "type": "text"}, {"key": "FirstClickArea[0][y_coord]", "value": "", "description": "Y-координата области", "type": "text"}, {"key": "FirstClickArea[0][name]", "value": "Кнопка входа 1", "description": "название области", "type": "text"}, {"key": "FirstClickArea[1][id]", "value": "16", "type": "text"}, {"key": "FirstClickArea[1][height]", "value": "", "type": "text"}, {"key": "FirstClickArea[1][width]", "value": "", "type": "text"}, {"key": "FirstClickArea[1][x_coord]", "value": "", "type": "text"}, {"key": "FirstClickArea[1][y_coord]", "value": "", "type": "text"}, {"key": "FirstClickArea[1][name]", "value": "Кнопка входа 2", "type": "text"}, {"key": "FirstClickArea[2][id]", "value": "18", "type": "text"}, {"key": "FirstClickArea[2][height]", "value": "", "type": "text"}, {"key": "FirstClickArea[2][width]", "value": "", "type": "text"}, {"key": "FirstClickArea[2][x_coord]", "value": "", "type": "text"}, {"key": "FirstClickArea[2][y_coord]", "value": "", "type": "text"}, {"key": "FirstClickArea[2][name]", "value": "Area 3", "type": "text"}]}, "url": {"raw": "{{base_url}}/foquz/api/questions/update-first-click-area?id=135151&access-token=30ZKhybu_U1MLi0", "host": ["{{base_url}}"], "path": ["foquz", "api", "questions", "update-first-click-area"], "query": [{"key": "id", "value": "135151"}, {"key": "access-token", "value": "30ZKhybu_U1MLi0"}]}}, "response": []}, {"name": "Удаление области", "request": {"method": "DELETE", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{base_url}}/foquz/api/questions/delete-first-click-area?id=42597&areaId=2&access-token=30ZKhybu_U1MLi0", "host": ["{{base_url}}"], "path": ["foquz", "api", "questions", "delete-first-click-area"], "query": [{"key": "id", "value": "42597"}, {"key": "areaId", "value": "2"}, {"key": "access-token", "value": "30ZKhybu_U1MLi0"}]}}, "response": []}]}