# Template and Styling Implementation Guide

## Main Question Template

### File: `ko/components/question-form/templates/first-click-test.php`

```php
<template id="question-form-template-first-click-test">
  <!-- ko let: { $translator: controller.translator } -->
  <div class="question-form-first-click-test">

    <!-- Image Upload Block -->
    <div class="form-group">
      <label class="form-label required" data-bind="text: $translator.t('Загрузка изображения')"></label>

      <!-- ko if: !question.imagePreview() -->
      <div class="image-upload-area"
           data-bind="dnd: function(files) { question.imageLoader.loadFile(files[0]) },
                      dndDisabled: question.imagePreview">
        <dnd-cover params="type: 'image'"></dnd-cover>
        <div class="upload-content">
          <media-load-button params="loader: question.imageLoader">
            <svg-icon params="name: 'upload', width: 24, height: 24"></svg-icon>
            <span data-bind="text: $translator.t('Загрузить изображение')"></span>
          </media-load-button>
          <div class="upload-formats">
            Форматы: jpg, jpeg, png, gif, svg (до 5 МБ)
          </div>
        </div>
      </div>

      <!-- ko foreach: question.imageLoader.errors -->
      <file-loader-error params="error: $data"></file-loader-error>
      <!-- /ko -->
      <!-- /ko -->

      <!-- ko if: question.imagePreview() -->
      <div class="image-preview-container">
        <div class="image-preview" data-bind="click: question.openFullscreen">
          <img data-bind="attr: { src: question.imagePreview() }" alt="Preview">
          <div class="fullscreen-hint">
            <svg-icon params="name: 'expand', width: 16, height: 16"></svg-icon>
            Нажмите для просмотра в полноэкранном режиме
          </div>
        </div>

        <div class="image-actions">
          <button type="button" class="btn btn-danger btn-sm"
                  data-bind="click: question.removeImage, disable: controller.isBlocked">
            <svg-icon params="name: 'trash', width: 14, height: 14"></svg-icon>
            Удалить
          </button>

          <button type="button" class="btn btn-primary btn-sm"
                  data-bind="click: question.openClickAreasDialog, disable: controller.isBlocked">
            <svg-icon params="name: 'edit', width: 14, height: 14"></svg-icon>
            Добавить области клика
          </button>
        </div>

        <div class="info-text">
          Отметьте области, по которым хотели бы получить статистику, либо собирайте клики и анализируйте по тепловой карте
        </div>
      </div>
      <!-- /ko -->

      <!-- ko if: question.validationErrors().length -->
      <div class="form-error" data-bind="text: question.validationErrors()[0]"></div>
      <!-- /ko -->
    </div>

    <!-- Mobile Display Settings -->
    <div class="form-group">
      <label class="form-label" data-bind="text: $translator.t('Отображение на смартфоне')"></label>
      <div class="form-help-text">
        <svg-icon params="name: 'info-circle', width: 14, height: 14"></svg-icon>
        Выберите способ масштабирования изображения на мобильных устройствах
      </div>
      <select class="form-control"
              data-bind="value: question.mobileDisplay, disable: controller.isBlocked">
        <option value="width">По ширине</option>
        <option value="height">По высоте</option>
      </select>
    </div>

    <!-- Click Count Settings -->
    <div class="row">
      <div class="col-6">
        <div class="form-group">
          <label class="form-label required" data-bind="text: $translator.t('Min кол-во кликов')"></label>
          <div class="form-help-text">
            <svg-icon params="name: 'info-circle', width: 14, height: 14"></svg-icon>
            Минимальное количество кликов для завершения задания
          </div>
          <input type="number" class="form-control"
                 data-bind="value: question.minClicks,
                            disable: controller.isBlocked,
                            attr: { placeholder: '1' }"
                 min="1" max="999" required>
          <!-- ko if: question.minClicks.hasError -->
          <div class="form-error" data-bind="text: question.minClicks.error"></div>
          <!-- /ko -->
        </div>
      </div>
      <div class="col-6">
        <div class="form-group">
          <label class="form-label" data-bind="text: $translator.t('Max кол-во кликов')"></label>
          <div class="form-help-text">
            <svg-icon params="name: 'info-circle', width: 14, height: 14"></svg-icon>
            Максимальное количество кликов (необязательно)
          </div>
          <input type="number" class="form-control"
                 data-bind="value: question.maxClicks,
                            disable: controller.isBlocked,
                            attr: { placeholder: '2' }"
                 min="1" max="999">
          <!-- ko if: question.maxClicks.hasError -->
          <div class="form-error" data-bind="text: question.maxClicks.error"></div>
          <!-- /ko -->
        </div>
      </div>
    </div>

    <!-- Display Time -->
    <div class="form-group">
      <label class="form-label" data-bind="text: $translator.t('Время показа изображения, секунд')"></label>
      <div class="form-help-text">
        <svg-icon params="name: 'info-circle', width: 14, height: 14"></svg-icon>
        Если ограничение по времени не нужно, оставьте поле пустым
      </div>
      <input type="number" class="form-control"
             data-bind="value: question.displayTime,
                        disable: controller.isBlocked,
                        attr: { placeholder: '60' }"
             min="1" max="99999">
    </div>

    <!-- Button Text -->
    <div class="form-group">
      <label class="form-label" data-bind="text: $translator.t('Текст кнопки')"></label>
      <div class="form-help-text">
        <svg-icon params="name: 'info-circle', width: 14, height: 14"></svg-icon>
        Текст кнопки для показа изображения респонденту
      </div>
      <div class="input-with-counter">
        <input type="text" class="form-control"
               data-bind="value: question.buttonText,
                          disable: controller.isBlocked,
                          attr: { placeholder: 'Показать изображение' }"
               maxlength="30">
        <div class="character-counter">
          <span data-bind="text: (question.buttonText() || '').length"></span>/30
        </div>
      </div>
    </div>

    <!-- Options Section -->
    <div class="form-section">
      <h5 class="form-section-title">Дополнительные настройки</h5>

      <!-- Allow Click Cancel -->
      <div class="form-group">
        <div class="form-check">
          <input type="checkbox" class="form-check-input"
                 data-bind="checked: question.allowClickCancel, disable: controller.isBlocked"
                 id="allowClickCancel">
          <label class="form-check-label" for="allowClickCancel"
                 data-bind="text: $translator.t('Возможность отменить клик')"></label>
          <div class="form-help-text">
            <svg-icon params="name: 'info-circle', width: 14, height: 14"></svg-icon>
            Позволяет респонденту отменить сделанный клик
          </div>
        </div>
      </div>

      <!-- Skip Option -->
      <div class="form-group">
        <div class="form-check">
          <input type="checkbox" class="form-check-input"
                 data-bind="checked: question.skipOption, disable: controller.isBlocked"
                 id="skipOption">
          <label class="form-check-label" for="skipOption"
                 data-bind="text: $translator.t('Пропуск ответа')"></label>
          <div class="form-help-text">
            <svg-icon params="name: 'info-circle', width: 14, height: 14"></svg-icon>
            Добавляет кнопку для пропуска вопроса
          </div>
        </div>

        <!-- ko if: question.skipOption() -->
        <div class="mt-2">
          <input type="text" class="form-control"
                 data-bind="value: question.skipText,
                            disable: controller.isBlocked,
                            attr: { placeholder: 'Затрудняюсь ответить' }"
                 maxlength="50">
        </div>
        <!-- /ko -->
      </div>

      <!-- Comment Option -->
      <div class="form-group">
        <div class="form-check">
          <input type="checkbox" class="form-check-input"
                 data-bind="checked: question.commentOption, disable: controller.isBlocked"
                 id="commentOption">
          <label class="form-check-label" for="commentOption"
                 data-bind="text: $translator.t('Комментарий')"></label>
          <div class="form-help-text">
            <svg-icon params="name: 'info-circle', width: 14, height: 14"></svg-icon>
            Добавляет поле для комментария к ответу
          </div>
        </div>
      </div>
    </div>

  </div>
  <!-- /ko -->
</template>
```

## Sidesheet Template

### File: `ko/dialogs/first-click-areas-sidesheet/template.html`

```html
<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="foquz-dialog__header">
    <div class="container">
      <h2 class="foquz-dialog__title">Добавление областей клика</h2>
      <p class="foquz-dialog__subtitle">
        Определите области на изображении для отслеживания кликов
      </p>
    </div>
  </div>

  <div class="foquz-dialog__body">
    <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
      <div class="container">

        <!-- Image Preview with Areas -->
        <div class="image-areas-preview">
          <div class="image-container">
            <img data-bind="attr: { src: imageUrl }" alt="Image for click areas">

            <!-- Click Areas Overlay -->
            <!-- ko foreach: areas -->
            <div class="click-area-overlay"
                 data-bind="style: {
                   left: x + 'px',
                   top: y + 'px',
                   width: width + 'px',
                   height: height + 'px'
                 },
                 css: {
                   'selected': $parent.editingArea() === $data
                 },
                 click: function() { $parent.editArea($data) }">
              <span class="area-label" data-bind="text: name"></span>
              <button class="area-remove"
                      data-bind="click: function(e) {
                        e.stopPropagation();
                        $parent.removeArea($data);
                      }">
                <svg-icon params="name: 'times', width: 10, height: 10"></svg-icon>
              </button>
            </div>
            <!-- /ko -->
          </div>

          <div class="image-instructions">
            <svg-icon params="name: 'info-circle', width: 16, height: 16"></svg-icon>
            Нажмите на область для редактирования или используйте кнопку "Добавить область"
          </div>
        </div>

        <!-- Areas Management -->
        <div class="areas-management">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>Области клика (<span data-bind="text: areas().length"></span>)</h4>
            <button type="button" class="btn btn-primary btn-sm"
                    data-bind="click: addNewArea">
              <svg-icon params="name: 'plus', width: 14, height: 14"></svg-icon>
              Добавить область
            </button>
          </div>

          <!-- ko if: areas().length === 0 -->
          <div class="empty-state">
            <svg-icon params="name: 'image', width: 48, height: 48"></svg-icon>
            <p>Области не добавлены</p>
            <small class="text-muted">Добавьте области для отслеживания кликов на изображении</small>
          </div>
          <!-- /ko -->

          <!-- Areas List -->
          <!-- ko foreach: areas -->
          <div class="area-item" data-bind="css: { 'editing': $parent.editingArea() === $data }">
            <!-- ko if: $parent.editingArea() !== $data -->
            <div class="area-item-view">
              <div class="area-info">
                <strong data-bind="text: name"></strong>
                <small class="text-muted">
                  Позиция: <span data-bind="text: x"></span>, <span data-bind="text: y"></span> |
                  Размер: <span data-bind="text: width"></span>×<span data-bind="text: height"></span>
                </small>
              </div>
              <div class="area-actions">
                <button type="button" class="btn btn-sm btn-outline-primary"
                        data-bind="click: function() { $parent.editArea($data) }">
                  <svg-icon params="name: 'edit', width: 12, height: 12"></svg-icon>
                  Изменить
                </button>
                <button type="button" class="btn btn-sm btn-outline-danger"
                        data-bind="click: function() { $parent.removeArea($data) }">
                  <svg-icon params="name: 'trash', width: 12, height: 12"></svg-icon>
                  Удалить
                </button>
              </div>
            </div>
            <!-- /ko -->

            <!-- ko if: $parent.editingArea() === $data -->
            <div class="area-item-edit">
              <div class="form-group">
                <label>Название области</label>
                <input type="text" class="form-control"
                       data-bind="value: $parent.areaForm.name"
                       placeholder="Введите название области">
              </div>

              <div class="row">
                <div class="col-3">
                  <div class="form-group">
                    <label>X</label>
                    <input type="number" class="form-control"
                           data-bind="value: $parent.areaForm.x"
                           min="0">
                  </div>
                </div>
                <div class="col-3">
                  <div class="form-group">
                    <label>Y</label>
                    <input type="number" class="form-control"
                           data-bind="value: $parent.areaForm.y"
                           min="0">
                  </div>
                </div>
                <div class="col-3">
                  <div class="form-group">
                    <label>Ширина</label>
                    <input type="number" class="form-control"
                           data-bind="value: $parent.areaForm.width"
                           min="1">
                  </div>
                </div>
                <div class="col-3">
                  <div class="form-group">
                    <label>Высота</label>
                    <input type="number" class="form-control"
                           data-bind="value: $parent.areaForm.height"
                           min="1">
                  </div>
                </div>
              </div>

              <div class="area-edit-actions">
                <button type="button" class="btn btn-sm btn-success"
                        data-bind="click: $parent.saveAreaEdit">
                  <svg-icon params="name: 'check', width: 12, height: 12"></svg-icon>
                  Сохранить
                </button>
                <button type="button" class="btn btn-sm btn-secondary"
                        data-bind="click: $parent.cancelAreaEdit">
                  <svg-icon params="name: 'times', width: 12, height: 12"></svg-icon>
                  Отменить
                </button>
              </div>
            </div>
            <!-- /ko -->
          </div>
          <!-- /ko -->
        </div>

      </div>
    </div>
  </div>

  <div class="foquz-dialog__footer">
    <div class="container">
      <div class="d-flex justify-content-end">
        <button type="button" class="btn btn-secondary me-2"
                data-bind="click: cancelChanges">
          Отменить
        </button>
        <button type="button" class="btn btn-primary"
                data-bind="click: saveAreas">
          <svg-icon params="name: 'check', width: 14, height: 14"></svg-icon>
          Применить изменения
        </button>
      </div>
    </div>
  </div>
</sidesheet>
```

## Styling Implementation

### File: `ko/components/question-form/templates/first-click-test.less`

```less
.question-form-first-click-test {
  .image-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.2s ease;
    position: relative;

    &:hover {
      border-color: #007bff;
      background: #f0f8ff;
    }

    &.drag-over {
      border-color: #007bff;
      background: #e3f2fd;
    }

    .upload-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
    }

    .upload-formats {
      font-size: 0.875rem;
      color: #6c757d;
    }
  }

  .image-preview-container {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;

    .image-preview {
      position: relative;
      cursor: pointer;

      img {
        width: 100%;
        height: auto;
        display: block;
      }

      .fullscreen-hint {
        position: absolute;
        bottom: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
        opacity: 0;
        transition: opacity 0.2s ease;
      }

      &:hover .fullscreen-hint {
        opacity: 1;
      }
    }

    .image-actions {
      padding: 1rem;
      background: #f8f9fa;
      display: flex;
      gap: 0.5rem;
    }

    .info-text {
      padding: 0.75rem 1rem;
      background: #e3f2fd;
      color: #1976d2;
      font-size: 0.875rem;
      border-top: 1px solid #bbdefb;
    }
  }

  .form-help-text {
    font-size: 0.75rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .input-with-counter {
    position: relative;

    .character-counter {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 0.75rem;
      color: #6c757d;
      background: white;
      padding: 0 0.25rem;
    }
  }

  .form-section {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #dee2e6;

    .form-section-title {
      margin-bottom: 1rem;
      color: #495057;
      font-size: 1.1rem;
      font-weight: 600;
    }
  }
}
```

### File: `ko/dialogs/first-click-areas-sidesheet/style.less`

```less
.first-click-areas-sidesheet {
  .foquz-dialog__subtitle {
    margin: 0;
    color: #6c757d;
    font-size: 0.875rem;
    font-weight: normal;
  }

  .image-areas-preview {
    margin-bottom: 2rem;

    .image-container {
      position: relative;
      display: inline-block;
      max-width: 100%;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      overflow: hidden;

      img {
        max-width: 100%;
        height: auto;
        display: block;
      }
    }

    .click-area-overlay {
      position: absolute;
      border: 2px solid #007bff;
      background: rgba(0, 123, 255, 0.1);
      cursor: pointer;
      transition: all 0.2s ease;
      min-width: 20px;
      min-height: 20px;

      &:hover {
        background: rgba(0, 123, 255, 0.2);
        border-color: #0056b3;
        transform: scale(1.02);
      }

      &.selected {
        border-color: #dc3545;
        background: rgba(220, 53, 69, 0.15);
        box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.3);
      }

      .area-label {
        position: absolute;
        top: -25px;
        left: 0;
        background: #007bff;
        color: white;
        padding: 2px 6px;
        font-size: 11px;
        border-radius: 3px;
        white-space: nowrap;
        font-weight: 500;
        z-index: 10;
      }

      .area-remove {
        position: absolute;
        top: -8px;
        right: -8px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: #dc3545;
        color: white;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.2s ease;
        cursor: pointer;

        &:hover {
          background: #c82333;
        }
      }

      &:hover .area-remove {
        opacity: 1;
      }
    }

    .image-instructions {
      margin-top: 1rem;
      padding: 0.75rem;
      background: #f8f9fa;
      border-radius: 4px;
      font-size: 0.875rem;
      color: #6c757d;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
  }

  .areas-management {
    h4 {
      color: #495057;
      font-size: 1.1rem;
      margin: 0;
    }

    .empty-state {
      text-align: center;
      padding: 3rem 1rem;
      color: #6c757d;

      svg {
        margin-bottom: 1rem;
        opacity: 0.5;
      }

      p {
        margin-bottom: 0.5rem;
        font-weight: 500;
      }
    }

    .area-item {
      border: 1px solid #dee2e6;
      border-radius: 6px;
      margin-bottom: 1rem;
      padding: 1rem;
      transition: all 0.2s ease;

      &:hover {
        border-color: #007bff;
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
      }

      &.editing {
        border-color: #007bff;
        background: #f8f9fa;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
      }

      .area-item-view {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .area-info {
          flex: 1;

          strong {
            display: block;
            margin-bottom: 0.25rem;
            color: #495057;
          }

          small {
            font-size: 0.75rem;
          }
        }

        .area-actions {
          display: flex;
          gap: 0.5rem;
        }
      }

      .area-item-edit {
        .form-group {
          margin-bottom: 1rem;

          label {
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.25rem;
            color: #495057;
          }
        }

        .area-edit-actions {
          display: flex;
          gap: 0.5rem;
          margin-top: 1.5rem;
          padding-top: 1rem;
          border-top: 1px solid #dee2e6;
        }
      }
    }
  }
}
```

This comprehensive styling implementation provides a modern, user-friendly interface that follows the FOQUZ design system and ensures consistent visual presentation across the application.
