# Refined Implementation Strategy: "Test First Click" Question Type

## Overview

This document provides a detailed implementation strategy for adding the "Test First Click" (Тест первого клика) question type to the FOQUZ system. Based on the analysis of the existing codebase and user requirements, this strategy outlines the exact steps, components, and files needed for implementation.

## Key Requirements Summary

1. **New Question Type**: Add "Test First Click" to question type selector
2. **Image Upload**: Support jpg, jpeg, png, gif, svg files up to 5MB
3. **Click Areas**: Interactive area definition on uploaded images
4. **Mobile Display**: Width/height scaling options
5. **Click Validation**: Min/max click count settings
6. **Timing Control**: Optional image display duration
7. **UI Components**: Button text, skip option, comment option

## Implementation Steps

### Step 1: Question Type Registration

#### 1.1 Add Type Constant
**File**: `ko/data/question-types.js`

Add new constant and register in typesSet:
```javascript
export const FIRST_CLICK_TEST = "24"; // Next available ID

// Add to typesSet array:
{
  id: FIRST_CLICK_TEST,
  label: "Тест первого клика",
  name: "first-click-test",
}
```

#### 1.2 Add Icon Definition
**File**: `ko/components/ui/icon/types/question-type.less`

Add icon dimensions to question-icon-set:
```less
.question-icon-set() {
  // ... existing icons
  first-click-test: 24, 24;
}
```

#### 1.3 Create SVG Icon
**File**: `modules/foquz/views/layouts/_sprite.php`

Add SVG symbol for the new question type:
```html
<symbol id="foquz-icon-question-first-click-test" viewBox="0 0 24 24" fill="none">
  <!-- Icon design for first click test -->
  <path d="..." stroke="currentColor" stroke-width="2"/>
</symbol>
```

### Step 2: Question Model Implementation

#### 2.1 Create Question Model
**File**: `ko/components/question-form/models/types/first-click-test/index.js`

```javascript
import Question from "../../question";
import { FIRST_CLICK_TEST } from "Data/question-types";
import { ImageClickAreasController } from "./image-click-areas-controller";

export default class FirstClickTestQuestion extends Question {
  constructor(controller) {
    super(controller);
    
    this.type = FIRST_CLICK_TEST;
    
    // Image upload properties
    this.imageFile = ko.observable(null);
    this.imagePreview = ko.observable("");
    this.imageLoader = null; // Will be initialized in updateData
    
    // Click areas
    this.clickAreas = ko.observableArray([]);
    this.areasController = new ImageClickAreasController(this);
    
    // Display settings
    this.mobileDisplay = ko.observable("width"); // "width" or "height"
    this.minClicks = ko.observable(1);
    this.maxClicks = ko.observable("");
    this.displayTime = ko.observable("");
    this.buttonText = ko.observable("");
    
    // Options
    this.allowClickCancel = ko.observable(false);
    this.skipOption = ko.observable(false);
    this.skipText = ko.observable("");
    this.commentOption = ko.observable(false);
    
    this.initializeImageLoader();
    this.setupValidation();
  }
  
  initializeImageLoader() {
    // Initialize image loader with specific formats
    // Implementation details in separate section
  }
  
  setupValidation() {
    // Validation rules for required fields
    // Implementation details in separate section
  }
  
  updateData(data) {
    super.updateData(data);
    // Update first-click-test specific data
    // Implementation details in separate section
  }
  
  getData() {
    return {
      ...super.getData(),
      imageFile: this.imageFile(),
      clickAreas: this.clickAreas().map(area => area.getData()),
      mobileDisplay: this.mobileDisplay(),
      minClicks: this.minClicks(),
      maxClicks: this.maxClicks(),
      displayTime: this.displayTime(),
      buttonText: this.buttonText(),
      allowClickCancel: this.allowClickCancel(),
      skipOption: this.skipOption(),
      skipText: this.skipText(),
      commentOption: this.commentOption(),
    };
  }
}
```

#### 2.2 Create Image Click Areas Controller
**File**: `ko/components/question-form/models/types/first-click-test/image-click-areas-controller.js`

This controller will handle the interactive image area definition functionality.

### Step 3: Template Implementation

#### 3.1 Main Template
**File**: `ko/components/question-form/templates/first-click-test.php`

```php
<template id="question-form-template-first-click-test">
  <!-- ko let: { $translator: controller.translator } -->
  <div class="question-form-first-click-test">
    
    <!-- Image Upload Block -->
    <div class="form-group">
      <label class="form-label required" data-bind="text: $translator.t('Загрузка изображения')"></label>
      
      <!-- ko if: !question.imagePreview() -->
      <div class="image-upload-area" data-bind="dnd: question.handleImageDrop">
        <media-load-button params="loader: question.imageLoader">
          <svg-icon params="name: 'upload'"></svg-icon>
          <span data-bind="text: $translator.t('Загрузить изображение')"></span>
          <div class="upload-formats">jpg, jpeg, png, gif, svg (до 5 МБ)</div>
        </media-load-button>
      </div>
      <!-- /ko -->
      
      <!-- ko if: question.imagePreview() -->
      <div class="image-preview-container">
        <div class="image-preview" data-bind="click: question.openFullscreen">
          <img data-bind="attr: { src: question.imagePreview() }" alt="Preview">
        </div>
        
        <div class="image-actions">
          <button type="button" class="btn btn-danger btn-sm" 
                  data-bind="click: question.removeImage">
            <svg-icon params="name: 'trash'"></svg-icon>
            Удалить
          </button>
          
          <button type="button" class="btn btn-primary btn-sm"
                  data-bind="click: question.openClickAreasDialog">
            <svg-icon params="name: 'edit'"></svg-icon>
            Добавить области клика
          </button>
        </div>
        
        <div class="info-text">
          Отметьте области, по которым хотели бы получить статистику, либо собирайте клики и анализируйте по тепловой карте
        </div>
      </div>
      <!-- /ko -->
    </div>
    
    <!-- Mobile Display Settings -->
    <div class="form-group">
      <label class="form-label" data-bind="text: $translator.t('Отображение на смартфоне')"></label>
      <select class="form-control" data-bind="value: question.mobileDisplay, disable: controller.isBlocked">
        <option value="width">По ширине</option>
        <option value="height">По высоте</option>
      </select>
    </div>
    
    <!-- Click Count Settings -->
    <div class="row">
      <div class="col-6">
        <div class="form-group">
          <label class="form-label required" data-bind="text: $translator.t('Min кол-во кликов')"></label>
          <input type="number" class="form-control" 
                 data-bind="value: question.minClicks, disable: controller.isBlocked"
                 min="1" max="999" required>
        </div>
      </div>
      <div class="col-6">
        <div class="form-group">
          <label class="form-label" data-bind="text: $translator.t('Max кол-во кликов')"></label>
          <input type="number" class="form-control"
                 data-bind="value: question.maxClicks, disable: controller.isBlocked"
                 min="1" max="999">
        </div>
      </div>
    </div>
    
    <!-- Display Time -->
    <div class="form-group">
      <label class="form-label" data-bind="text: $translator.t('Время показа изображения, секунд')"></label>
      <input type="number" class="form-control"
             data-bind="value: question.displayTime, disable: controller.isBlocked"
             max="99999">
    </div>
    
    <!-- Button Text -->
    <div class="form-group">
      <label class="form-label" data-bind="text: $translator.t('Текст кнопки')"></label>
      <input type="text" class="form-control"
             data-bind="value: question.buttonText, disable: controller.isBlocked"
             maxlength="30" placeholder="Показать изображение">
      <div class="character-counter">
        <span data-bind="text: (question.buttonText() || '').length"></span>/30
      </div>
    </div>
    
    <!-- Options -->
    <div class="form-group">
      <div class="form-check">
        <input type="checkbox" class="form-check-input"
               data-bind="checked: question.allowClickCancel, disable: controller.isBlocked">
        <label class="form-check-label" data-bind="text: $translator.t('Возможность отменить клик')"></label>
      </div>
    </div>
    
    <!-- Skip Option -->
    <div class="form-group">
      <div class="form-check">
        <input type="checkbox" class="form-check-input"
               data-bind="checked: question.skipOption, disable: controller.isBlocked">
        <label class="form-check-label" data-bind="text: $translator.t('Пропуск ответа')"></label>
      </div>
      
      <!-- ko if: question.skipOption() -->
      <input type="text" class="form-control mt-2"
             data-bind="value: question.skipText, disable: controller.isBlocked"
             placeholder="Затрудняюсь ответить">
      <!-- /ko -->
    </div>
    
    <!-- Comment Option -->
    <div class="form-group">
      <div class="form-check">
        <input type="checkbox" class="form-check-input"
               data-bind="checked: question.commentOption, disable: controller.isBlocked">
        <label class="form-check-label" data-bind="text: $translator.t('Комментарий')"></label>
      </div>
    </div>
    
  </div>
  <!-- /ko -->
</template>
```

### Step 4: Click Areas Sidesheet

#### 4.1 Create Sidesheet Component
**File**: `ko/dialogs/first-click-areas-sidesheet/index.js`

```javascript
import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('first-click-areas-sidesheet', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('first-click-areas-sidesheet');
      return new ViewModel(params, element);
    },
  },
  template: html,
});
```

#### 4.2 Sidesheet Model
**File**: `ko/dialogs/first-click-areas-sidesheet/model.js`

```javascript
import { DialogWrapper } from "Dialogs/wrapper";

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);
    
    this.imageUrl = params.imageUrl;
    this.areas = ko.observableArray(params.areas || []);
    this.onSave = params.onSave;
    
    this.selectedArea = ko.observable(null);
    this.isDrawing = ko.observable(false);
    
    this.initializeImageCanvas();
  }
  
  initializeImageCanvas() {
    // Initialize interactive canvas for area selection
    // Implementation details in separate section
  }
  
  addArea(coordinates) {
    // Add new click area
    // Implementation details in separate section
  }
  
  removeArea(area) {
    // Remove click area
    // Implementation details in separate section
  }
  
  saveAreas() {
    if (typeof this.onSave === 'function') {
      this.onSave(this.areas());
    }
    this.hide();
  }
}
```

## Next Steps

This strategy provides the foundation for implementing the "Test First Click" question type. The next phase should focus on:

1. **Detailed Component Implementation**: Complete the image upload, click areas, and validation logic
2. **Formatters Creation**: Implement client, server, preview, and cpoint formatters
3. **Registration and Integration**: Register all components in the main system
4. **Testing Strategy**: Develop comprehensive testing approach
5. **UI/UX Refinement**: Ensure consistent styling and user experience

Each component should be implemented following the established patterns in the codebase while meeting the specific requirements outlined in the task description.
