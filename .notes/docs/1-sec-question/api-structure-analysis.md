# API Structure Analysis: First Click Test Question Type

## Based on Postman Collection Analysis

### Key Findings from API Structure

The Postman collection reveals the actual server-side data structure for the First Click Test question type. This analysis updates our implementation to match the real API expectations.

## API Data Structure

### Question Type Identification
```
FoquzQuestion[main_question_type] = "24"
```
- Confirms our `FIRST_CLICK_TEST = "24"` constant is correct

### Image Handling
```
FoquzQuestion[enableGallery] = "1"
FoquzQuestion[gallery][0][id] = "55850"
```
- **Important**: Reuses existing gallery system instead of custom image upload
- `enableGallery` flag enables gallery functionality
- `gallery` array contains uploaded image IDs
- This means we should integrate with existing gallery components

### First Click Specific Data
All first-click settings are under `FoquzQuestion[firstClick]` namespace:

```
FoquzQuestion[firstClick][mobile_view] = "0"     // 0=width, 1=height
FoquzQuestion[firstClick][min_click] = "1"       // Required minimum clicks
FoquzQuestion[firstClick][max_click] = ""        // Optional maximum clicks
FoquzQuestion[firstClick][show_time] = ""        // Optional display time in seconds
FoquzQuestion[firstClick][button_text] = ""      // Optional button text
FoquzQuestion[firstClick][allow_cancel_click] = "0"  // 0=false, 1=true
```

### Standard Question Fields
```
FoquzQuestion[skip] = "1"
FoquzQuestion[skip_text] = "Затрудняюсь ответить"
```

### Image fields
```
FoquzQuestion[gallery][0][id] = "55850"
FoquzQuestion[galleryEnabled] = "1" // will always be 1
```

### Comment Functionality
```
FoquzQuestion[comment_enabled] = "1"
FoquzQuestion[placeholder_text] = "Подсказка для коммента"
FoquzQuestion[comment_minlength] = "0"
FoquzQuestion[comment_maxlength] = "100"
FoquzQuestion[comment_label] = "Коммент"
FoquzQuestion[comment_required] = "0"
```

## Implementation Updates Required

### 1. Gallery Integration
Instead of custom image upload, integrate with existing gallery system:
- Use existing gallery components
- Leverage `enableGallery` flag
- Handle `gallery` array for image IDs

### 2. Data Structure Alignment
Update formatters to match API structure:
- Use `firstClick` namespace for specific settings
- Map `mobile_view` to 0/1 instead of string values
- Handle empty strings for optional fields

### 3. Comment System Integration
Integrate with existing comment system:
- Use standard comment fields
- Leverage existing comment validation
- Reuse comment UI components

## Missing from API: Click Areas (WILL BE ADDED LATER)

**Important Note**: The Postman collection doesn't show click areas data structure. This could mean:
1. Click areas are handled in a separate API call
2. Click areas feature is not yet implemented on backend
3. Click areas are stored differently (possibly as JSON in a separate field)

**Recommendation**: Implement click areas as planned but prepare for potential API changes when backend implementation is clarified.

## Updated Implementation Strategy

### Frontend: FoquzFileLoader with API Transformation
```javascript
// Frontend uses FoquzFileLoader for better UX
this.imageLoader = new FoquzImageLoader(null, {
  presets: ["image"],
  formats: ["jpg", "jpeg", "png", "gif", "svg"],
  maxSize: 5 * 1024 * 1024, // 5MB
});

// But formatters transform to gallery API format
```

### Data Mapping
```javascript
// Client to Server mapping (in server formatter)
const serverData = {
  enableGallery: this.imageFile() ? 1 : 0,
  // gallery array will be populated by file upload process
  firstClick: {
    mobile_view: this.mobileDisplay() === "height" ? 1 : 0,
    min_click: parseInt(this.minClicks()) || 1,
    max_click: this.maxClicks() || "",
    show_time: this.displayTime() || "",
    button_text: this.buttonText() || "",
    allow_cancel_click: this.allowClickCancel() ? 1 : 0
  }
};
```

### Server to Client mapping
```javascript
// Server to Client mapping (in client formatter)
const clientData = {
  imageFile: data.imageFile || null,
  imagePreview: data.imagePreview || "",
  mobileDisplay: data.firstClick?.mobile_view === 1 ? "height" : "width",
  minClicks: data.firstClick?.min_click || 1,
  maxClicks: data.firstClick?.max_click || "",
  displayTime: data.firstClick?.show_time || "",
  buttonText: data.firstClick?.button_text || "",
  allowClickCancel: data.firstClick?.allow_cancel_click === 1
};
```

## Action Items

### High Priority
1. **Update formatters** to match API structure
2. **Keep FoquzFileLoader** for frontend UX but transform to gallery format in formatters
3. **Use existing comment components** for comment functionality
4. **Test with actual API** to verify data flow

### Medium Priority
1. **Clarify click areas implementation** with backend team
2. **Handle file upload to gallery transformation** in the submission process
3. **Ensure proper error handling** for file upload failures

### Low Priority
1. **Update documentation** to reflect API structure
2. **Add API validation** for data integrity
3. **Create migration guide** if needed

## Benefits of Hybrid Implementation

1. **User Experience**: FoquzFileLoader provides better frontend UX
2. **API Compatibility**: Formatters ensure backend compatibility
3. **Flexibility**: Can easily switch between file upload methods
4. **Maintainability**: Uses existing, tested components
5. **Future-proof**: Aligns with backend expectations while keeping frontend simple

This analysis ensures our implementation will work seamlessly with the actual API and follow established patterns in the FOQUZ system.
