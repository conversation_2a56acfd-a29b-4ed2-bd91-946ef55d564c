# Component Locations and Implementation Answers

## User Questions and Answers

### Question 1: Where is the popup with question type selection located and where to add new icon?

**Answer:**

The question type selection is handled by multiple components:

#### Main Question Type Selector Component:
- **Location**: `ko/components/question-form/components/type-select/`
- **Template**: `ko/components/question-form/components/type-select/template.html`
- **Model**: `ko/components/question-form/components/type-select/model.js`

#### Question Type Dialog (Popup):
- **Location**: `ko/dialogs/question-type-dialog/`
- **Template**: `ko/dialogs/question-type-dialog/template.html`
- **Model**: `ko/dialogs/question-type-dialog/model.js`

#### Where to Add New Icon:

1. **SVG Sprite Definition**:
   - **File**: `modules/foquz/views/layouts/_sprite.php`
   - **Add**: New `<symbol>` with id `foquz-icon-question-first-click-test`

2. **Icon Dimensions**:
   - **File**: `ko/components/ui/icon/types/question-type.less`
   - **Add**: `first-click-test: 24, 24;` to `.question-icon-set()`

3. **Question Type Registration**:
   - **File**: `ko/data/question-types.js`
   - **Add**: New constant `FIRST_CLICK_TEST = "24"` and entry in `typesSet` array

#### Icon Usage Pattern:
The system uses this pattern for icons:
```html
<svg>
  <use href="#foquz-icon-question-{typeName}"></use>
</svg>
```

Where `{typeName}` comes from `getTypeName()` function in `ko/data/question-types.js`.

### Question 2: Where to create knockout component for image + click areas definition?

**Answer:**

Based on the codebase analysis, the appropriate location for the image click areas component is:

#### Recommended Location:
- **Directory**: `ko/components/image-click-areas/`
- **Files**:
  - `ko/components/image-click-areas/index.js` - Component registration
  - `ko/components/image-click-areas/model.js` - Component logic
  - `ko/components/image-click-areas/template.html` - Component template
  - `ko/components/image-click-areas/style.less` - Component styles

#### Alternative Location (Following Question Form Pattern):
- **Directory**: `ko/components/question-form/components/image-click-areas/`

#### Integration with Sidesheet:
The component should be used within the sidesheet:
- **Sidesheet Location**: `ko/dialogs/first-click-areas-sidesheet/`
- **Usage**: The sidesheet will contain the image-click-areas component

#### Component Structure:
```javascript
// ko/components/image-click-areas/index.js
import { ViewModel } from './model';
import html from './template.html';
import './style.less';

ko.components.register('image-click-areas', {
  viewModel: {
    createViewModel: (params, componentInfo) => {
      let element = componentInfo.element;
      element.classList.add('image-click-areas');
      return new ViewModel(params, element);
    },
  },
  template: html,
});
```

#### Existing Similar Components for Reference:
1. **Media Controller**: `ko/components/question-form/controllers/media-controller/`
2. **File Loader Preview**: `ko/components/input/file-loader/file-loader-preview/`
3. **Media Preview**: `ko/components/ui/media-preview/`

## Implementation Details

### Image Upload Component Integration

**Existing Components to Use:**
1. **File Loader**: `ko/models/file-loader/image-loader.js`
2. **Media Load Button**: `ko/components/input/file-loader/media-load-button/`
3. **File Loader Preview**: `ko/components/input/file-loader/file-loader-preview/`
4. **DND (Drag & Drop)**: `ko/bindings/dnd/`

### Form Field Components

**Existing Components Available:**
1. **Select Field**: Standard HTML select with knockout bindings
2. **Input Fields**: Standard HTML inputs with knockout bindings
3. **Checkbox Fields**: Standard HTML checkboxes with knockout bindings
4. **Character Counter**: Custom implementation needed (pattern exists in codebase)

### Sidesheet Implementation

**Pattern to Follow:**
- **Base Class**: `DialogWrapper` from `Dialogs/wrapper`
- **Registration**: Similar to `ko/dialogs/user-sidesheet/`
- **Template Structure**: Follow `ko/dialogs/foquz-dialog/sidesheet/template.html`

### File Structure Summary

```
ko/
├── components/
│   ├── question-form/
│   │   ├── models/types/first-click-test/
│   │   │   ├── index.js (Question Model)
│   │   │   └── image-click-areas-controller.js
│   │   └── templates/
│   │       └── first-click-test.php
│   └── image-click-areas/
│       ├── index.js
│       ├── model.js
│       ├── template.html
│       └── style.less
├── dialogs/
│   └── first-click-areas-sidesheet/
│       ├── index.js
│       ├── model.js
│       ├── template.html
│       └── style.less
└── data/
    └── question-types.js (Add new type)
```

### Integration Points

1. **Question Form Registration**: `ko/components/question-form/index.js`
2. **Template Include**: `ko/components/question-form/_components.php`
3. **Formatters Registration**: `ko/components/question-form/utils/formatters.js`
4. **Sidesheet Registration**: Import in main dialogs index or where needed

This structure follows the established patterns in the codebase and provides clear separation of concerns while maintaining consistency with existing implementations.
