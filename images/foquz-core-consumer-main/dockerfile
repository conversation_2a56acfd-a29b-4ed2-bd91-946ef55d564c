ARG TAG_CORE_JS_MAIL
FROM cr.yandex/crpsvv5iscafkhlubkmt/foquz-core-js-mail:${TAG_CORE_JS_MAIL} AS mail

ARG TAG_BASE
FROM cr.yandex/crpsvv5iscafkhlubkmt/foquz-core-cli-php-base:${TAG_BASE}

COPY --from=mail  /jsbuild  /app/mail
COPY . /app

RUN  mkdir /app/runtime && \
		chmod -R 777 /app/runtime && \
		addgroup -S -g 1099 foquz  && \
    adduser  -S -u 1099 -G foquz foquz  && \
		chown foquz:foquz -R /app/

USER foquz
WORKDIR /app

ENTRYPOINT ["php", "/app/yii", "rabbit-queue/listen"]

