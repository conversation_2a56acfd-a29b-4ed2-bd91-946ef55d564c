.job_build_imag_base: &job_build_configuration_base
  allow_failure: true
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: [""]
  script:
    - chmod 777 ${CI_PROJECT_DIR}/images/scripts/prebuild.sh
    - ${CI_PROJECT_DIR}/images/scripts/prebuild.sh
    - mkdir -p /kaniko/.docker
    - echo "{\"auths\":{\"${CI_REGISTRY}\":{\"auth\":\"$(echo -n "json_key:${CI_REGISTRY_KEY}" | base64 | tr -d '\n' )\"}}}" > /kaniko/.docker/config.json
    - >-
      /kaniko/executor --cache=false
      --context "${CI_PROJECT_DIR}"  
      --build-arg "MODE=${MODE}"
      --build-arg "TYPE=${TYPE}"
      --dockerfile "${CI_PROJECT_DIR}/images/${IMAGE_NAME}/dockerfile"
      --destination "${CI_REGISTRY}/${IMAGE_NAME}:${TAG}"
    - echo "${NAME_POD}=true" >> build.env
  artifacts:
    reports:
      dotenv: build.env

build-foquz-core-alpine-php-fpm-base:
  stage: build-base
  variables:
    IMAGE_NAME: "foquz-core-alpine-php-fpm-base"
    NAME_POD: "CORE_FPM_BASE"
  rules:
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
      changes:
        paths:
          - 'images/${IMAGE_NAME}/**/*'
          - 'composer.json'
          - 'images/rebuild_all'
  <<: *job_build_configuration_base

build-foquz-core-cli-php-base:
  stage: build-base
  variables:
    IMAGE_NAME: "foquz-core-cli-php-base"
    NAME_POD: "CORE_CLI_BASE"
  rules:
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
      changes:
        paths:
          - 'images/${IMAGE_NAME}/**/*'
          - 'composer.json'
          - 'images/rebuild_all'
  <<: *job_build_configuration_base

build-foquz-core-foquz-core-js-mail-image:
  stage: build-base
  variables:
    IMAGE_NAME: "foquz-core-js-mail"
    NAME_POD: "CORE_JS_MAIL"
  rules:
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/ || $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH == 'bugfixes'
      changes:
        paths:
          - 'emails-markup/**/*'
          - 'images/${IMAGE_NAME}/*'
          - 'images/rebuild_all'
  <<: *job_build_configuration_base



