.job_build_image: &job_build_configuration
  allow_failure: true
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: [""]
  script:
    - chmod 777 ${CI_PROJECT_DIR}/images/scripts/prebuild.sh
    - source ${CI_PROJECT_DIR}/images/scripts/prebuild.sh
    - echo "${BASE_TAG}"
    - mkdir -p /kaniko/.docker
    - echo "{\"auths\":{\"${CI_REGISTRY}\":{\"auth\":\"$(echo -n "json_key:${CI_REGISTRY_KEY}" | base64 | tr -d '\n' )\"}}}" > /kaniko/.docker/config.json
    - >-
      /kaniko/executor --cache=false
      --build-arg "RELEASE=${RELEASE}"
      --build-arg "MODE=${MODE}"  
      --build-arg "TYPE=${TYPE}"
      --build-arg "TAG_BASE=${BASE_TAG}"
      --build-arg "TAG_CORE_JS_MAIL=${CORE_JS_MAIL_TAG}"
      --build-arg "SENTRY_PROJECT=${SENTRY_PROJECT}"
      --build-arg "SENTRY_AUTH_TOKEN=${SENTRY_AUTH_TOKEN}"
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/images/${IMAGE_NAME}/dockerfile"
      --destination "${CI_REGISTRY}/${IMAGE_NAME}:${TAG}"
    - echo "${NAME_POD}=true" >> build.env
  artifacts:
    reports:
      dotenv: build.env

build-foquz-core-js-main:
  stage: build
  rules:
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
      changes:
        paths:
          - 'ko/**/*'
          - 'less/**/*'
          - 'messages/**/*'
          - 'webpack/**/*'
          - 'images/${IMAGE_NAME}/**/*'
          - 'images/rebuild_all'
  variables:
    IMAGE_NAME: "foquz-core-js-main"
    NAME_POD: "CORE_JS_MAIN"
  <<: *job_build_configuration

build-foquz-core-js-widget-iframe:
  stage: build
  rules:
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
      changes:
        paths:
          - 'ko/widgets/poll_new/**/*'
          - 'images/${IMAGE_NAME}/*'
          - 'images/rebuild_all'
  variables:
    IMAGE_NAME: "foquz-core-js-widget-iframe"
    NAME_POD: "CORE_JS_WIDGET_IFRAME"
  <<: *job_build_configuration

#временно
#build-foquz-core-foquz-core-js-mail-image:
#  stage: build
#  rules:
#    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/ || $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
#      changes:
#        paths:
#          - 'emails-markup/**/*'
#          - 'images/${IMAGE_NAME}/*'
#          - 'images/rebuild_all'
#  variables:
#    IMAGE_NAME: "foquz-core-js-mail"
#    NAME_POD: "CORE_JS_MAIL"
#  <<: *job_build_configuration


build-foquz-core-nginx:
  stage: build
  rules:
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
      changes:
        paths:
          - 'web/**/*'
          - 'images/foquz-core-nginx/**/*'
          - 'images/rebuild_all'
  variables:
    IMAGE_NAME: "foquz-core-nginx"
    NAME_POD: "CORE_NGINX"
  <<: *job_build_configuration

build-foquz-core-main-app:
  stage: build
  variables:
    IMAGE_NAME: "foquz-core-main-app"
    NAME_POD: "CORE_FPM"
  rules:
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
  <<: *job_build_configuration

build-foquz-core-consumer-main:
  stage: build
  variables:
    IMAGE_NAME: "foquz-core-consumer-main"
    NAME_POD: "CORE_CONSUMER_MAIN"
  rules:
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
  <<: *job_build_configuration

build-foquz-core-consumer-mailings:
  stage: build
  variables:
    IMAGE_NAME: "foquz-core-consumer-mailings"
    NAME_POD: "CORE_CONSUMER_MAILING"
  rules:
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
  <<: *job_build_configuration


#build-core-debug:
#  stage: build
#  variables:
#    IMAGE_NAME: "foquz-core-debug"
#    NAME_POD: "CORE_DEBUG"
#  rules:
#    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH =~ /TASK-.*/  || $CI_COMMIT_BRANCH =~ /Ver-.*/
#  <<: *job_build_configuration



build-foquz-core-consumer-widget:
  stage: build
  variables:
    IMAGE_NAME: "foquz-core-consumer-widget"
    NAME_POD: "CORE_CONSUMER_WIDGET"
  rules:
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
  <<: *job_build_configuration


build-foquz-core-consumer-quiz:
  stage: build
  variables:
    IMAGE_NAME: "foquz-core-consumer-quiz"
    NAME_POD: "CORE_CONSUMER_QUIZ"
  rules:
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
  <<: *job_build_configuration

build-foquz-core-migrations:
  stage: build
  variables:
    IMAGE_NAME: "foquz-core-migrations"
    NAME_POD: "CORE_MIGRATIONS"
  rules:
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
      changes:
        paths:
          - 'migrations/**/*'
          - 'images/foquz-core-migrations/**/*'
          - 'composer.json'
          - 'images/rebuild_all'
  <<: *job_build_configuration

build-foquz-core-job-init:
   stage: build
   variables:
      IMAGE_NAME: "foquz-core-job-init"
      NAME_POD: "CORE_INIT"
   rules:
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
      changes:
        paths:
          - 'commands/**/*'
          - 'images/foquz-core-job-init/**/*'
          - 'images/rebuild_all'
   <<: *job_build_configuration

#build-foquz-core-doc-builder:
#   stage: build
#   variables:
#     IMAGE_NAME: "foquz-core-doc-builder"
#     NAME_POD: "CORE_DOC_BUILDER"
#   rules:
#     - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
#       changes:
#         paths:
#           - 'commands/SwaggerController.php'
#           - 'modules/foquz/controllers/**/*'
#           - 'images/foquz-core-doc-builder/**/*'
#           - 'images/rebuild_all'
#   <<: *job_build_configuration

deploy-docs:
  stage: deploy
  resource_group: devfoquz
  variables:
    RELEASE: "$RELEASE"
  tags:
    - devfoquz
  rules:
    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/  ||  $CI_COMMIT_BRANCH == "release" || $CI_COMMIT_BRANCH == 'bugfixes'
      changes:
        paths:
          - 'commands/SwaggerController.php'
          - 'modules/foquz/controllers/**/*'
          - 'images/foquz-core-doc-builder/**/*'
          - 'images/rebuild_all'
  image:
    name: $CI_REGISTRY/foquz-core-doc-builder:$TAG
    entrypoint: [""]
  script:
    - composer install
    - cp /var/www/devfoquz.ru/project/config/db.php config/db.php
    - cp /var/www/devfoquz.ru/project/config/db-log.php config/db-log.php
    - cp /var/www/devfoquz.ru/project/config/mailer.php config/mailer.php
    - cp /var/www/devfoquz.ru/project/config/params-local.php config/params-local.php
    - mkdir -p docs
    - ./yii swagger/go docs/swagger.yml
    - ./yii swagger/go docs/redoc.yml v1
    - >-
      curl -X POST "https://docs.foquzdev.ru/api/docs/upload" \
        -H "Host: ${RELEASE}.docs.foquzdev.ru" \
        -H "Authorization: Bearer ${DOCS_AUTH_TOKEN_DEV}" \
        -F "doc_type=swagger" \
        -F "file=@./"docs/swagger.yml
    - >-
      curl -X POST "https://docs.foquzdev.ru/api/docs/upload" \
        -H "Host: ${RELEASE}.docs.foquzdev.ru" \
        -H "Authorization: Bearer ${DOCS_AUTH_TOKEN_DEV}" \
        -F "doc_type=redoc" \
        -F "file=@./"docs/redoc.yml

set-version-task:
  stage: deploy
  variables:
    SET_VERSION: true
    TAG: "$TAG"
    RELEASE: "$RELEASE"
    MODE: "$MODE"
    TYPE: "$TYPE"
    BRANCH: "$CI_COMMIT_BRANCH"
    SOURCE_CI_PROJECT_TITLE: "$CI_PROJECT_TITLE"
    SOURCE_CI_PROJECT_PATH: "$CI_PROJECT_PATH"
    SOURCE_CI_COMMIT_SHA: "$CI_COMMIT_SHA"
    SOURCE_CI_COMMIT_MESSAGE: "$CI_COMMIT_TITLE"
    SOURCE_CI_PIPELINE_CREATED_AT: "$CI_PIPELINE_CREATED_AT"
    CONTAINER_foquz_core_cli_php_base: "$CORE_CLI_BASE"
    CONTAINER_foquz_core_alpine_php_fpm_base: "$CORE_FPM_BASE"
    CONTAINER_foquz_core_migrations: "$CORE_MIGRATIONS"
    CONTAINER_foquz_core_consumer_widget: "$CORE_CONSUMER_WIDGET"
    CONTAINER_foquz_core_consumer_mailings: "$CORE_CONSUMER_MAILING"
    CONTAINER_foquz_core_consumer_main: "$CORE_CONSUMER_MAIN"
    CONTAINER_foquz_core_consumer_quiz: "$CORE_CONSUMER_QUIZ"
    CONTAINER_foquz_core_main_app: "$CORE_FPM"
    CONTAINER_foquz_core_nginx: "$CORE_NGINX"
    CONTAINER_foquz_core_js_main: "$CORE_JS_MAIN"
    CONTAINER_foquz_core_js_widget_iframe: "$CORE_JS_WIDGET_IFRAME"
    CONTAINER_foquz_core_js_mail: "$CORE_JS_MAIL"
    CONTAINER_foquz_core_job_init: "$CORE_INIT"
#  rules:
#    - if: $CI_COMMIT_BRANCH =~ /TASK-.*/
  trigger:
    project: 'doxsw/foquz-app-deploy'
    branch: 'stage'



