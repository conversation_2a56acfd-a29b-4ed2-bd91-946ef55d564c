ARG TAG
ARG SENTRY_PROJECT
ARG SENTRY_AUTH_TOKEN
FROM cr.yandex/crpsvv5iscafkhlubkmt/node:14.19.3-alpine  AS node

COPY . /app

ENV SENTRY_PROJECT=$SENTRY_PROJECT \
    SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN \
    SENTRY_RELEASE=$TAG

RUN cd /app \
	&& chmod -R 777 /app/web \
	&& export NODE_OPTIONS="--max-old-space-size=5120" \
	&& npm install -force \
	&& npm run build

FROM cr.yandex/crpsvv5iscafkhlubkmt/alpine:3.21

COPY --from=node  /app/web/js /jsbuild



