<?php

use app\components\GetHostComponent;
use app\components\JsonStdoutLogTarget;
use app\components\RabbitExternalQueue;
use app\models\company\Company;
use app\models\User;
use app\models\UserVisitLog;
use app\modules\foquz\models\BrowserNotificationUserDevice;
//use app\modules\foquz\services\authorization\AuthorizationService;
use notamedia\sentry\SentryTarget;
use yii\base\Event;
use yii\helpers\Html;
use yii\helpers\Url;
use yii\queue\amqp_interop\Queue;
use yii\queue\LogBehavior;
use yii\queue\serializers\JsonSerializer;
use yii\redis\Cache;
use yii\web\View;

$protocol = empty(getenv('HTTP_PROTOCOL')) ? "https" : getenv('HTTP_PROTOCOL');
$mailerFromName = empty(getenv('MAILER_FROM_NAME')) ? "Foquz" : getenv('MAILER_FROM_NAME');
$mailerFromEmail = empty(getenv('MAILER_FROM')) ? "<EMAIL>" : getenv('MAILER_FROM');

$params = [
    'json_crypt'                     => [
        'cipher_algo_name' => 'openssl-aes-256',
        'secret_key'       => 'uYd3fNmbfW@5*',
    ],
    'protocol'                       => $protocol,
    'main_sender'                    => [$mailerFromEmail => $mailerFromName],
    'main_sender_email'              => $mailerFromEmail,
    'default_logo_link'              => empty(getenv('BASE_URL')) ? 'https://foquz.ru' : getenv('BASE_URL'),
    'ffmpeg_binaries'                => '/usr/bin/ffmpeg',
    'ffprobe_binaries'               => '/usr/bin/ffprobe',
    'bsDependencyEnabled'            => false,
    'system_sender'                  => $mailerFromEmail,
    'queueNamePrefix'                => 'core',
    'allowQueryParamAuthInternalApi' => false,
    'host'                           => getenv('HOST'),
    'sendsay'                        => ["login" => getenv('SENDSAY_LOGIN'), "password" => getenv('SENDSAY_PASSWORD')],
    'short_link'                     => getenv('SHORT_LINK'),
    'hr_manager_company_id'          => 0,
    'adminEmail'                     => empty(getenv('ADMIN_EMAIL')) ? "<EMAIL>" : getenv('ADMIN_EMAIL'),
    'info_panel'                     => '',
    'dadata_token'                   => '',
    'show_requests'                  => empty(getenv('SHOW_REQUESTS')) ? false : getenv('SHOW_REQUESTS'),
    'dev_on'                         => false,
    'authkey2'                       => "",
    'sentry_dsn'                     => getenv('SENTRY_DSN'),
    'sentry_js_url'                  => getenv('SENTRY_JS'),
    'queueDiagnosticFile'          => '@runtime/last_start_time.txt',
    'queueMaxMessageExecutionTime' => 2,
];

if (!empty(getenv('URL_SHORTENER_HOSTPORT'))) {
    $params['urlShortenerHostPort'] = getenv('URL_SHORTENER_HOSTPORT');
    $params['urlShortenerAuthKey'] = getenv('URL_SHORTENER_AUTHKEY');
}

$config = [
    'on beforeRequest' => function () {
      //AuthorizationService::init();
    },

    'on afterRequest' => function (Event $event) {
      //  AuthorizationService::userBaseAuthorization($event);
    },

    'id'              => 'foquz-app',
    'basePath'        => dirname(__DIR__),
    'bootstrap'       => [
        'devicedetect',
        'log',
        GetHostComponent::class,
        'queue_mailings_in',
        'rabbit_queue',
    ],
    'language'        => 'ru-RU',
    'sourceLanguage'  => 'ru-RU',
    'defaultRoute'    => 'site/index',
    'homeUrl'         => '/foquz',
    'timeZone'        => 'Europe/Moscow',
    'aliases'         => [
        '@bower'   => '@vendor/bower-asset',
        '@npm'     => '@vendor/npm-asset',
        '@cases'   => '@app/views/site/cases',
        '@web'     => '@app/web',
        '@uploads' => '@app/web/uploads',
    ],
    'components'      => [
        'i18n'     => [
            'translations' => [
                '*' => [
                    'class'          => 'app\components\JsonMessageSource',
                    'sourceLanguage' => 'ru-RU',
                ],
            ],
        ],
        'html2pdf' => [
            'class'     => 'yii2tech\html2pdf\Manager',
            'viewPath'  => '@app/views/pdf',
            'converter' => 'wkhtmltopdf',
        ],

        'redis'         => [
            'class'    => yii\redis\Connection::class,
            'hostname' => getenv('REDIS_HOSTNAME'),
            'password' => getenv('REDIS_PASSWORD'),
            'database' => getenv('REDIS_DATABASE'),
            'port'     => getenv('REDIS_PORT'),
        ],
        'session'       => [
            'class'        => yii\redis\Session::class,
            'cookieParams' => [
                'httpOnly' => true,
                'path'     => '/',
            ],
            'redis'        => [
                'hostname' => getenv('REDIS_HOSTNAME'),
                'password' => getenv('REDIS_PASSWORD'),
                'database' => getenv('REDIS_SESSION_DATABASE'),
                'port'     => getenv('REDIS_PORT'),
            ]
        ],
        'request'       => [
            'cookieValidationKey' => empty(getenv("COOKIE_VALIDATION_KEY")) ? 'as4h5sakjFhaDSgasdZghsajh' : getenv("COOKIE_VALIDATION_KEY"),
            'csrfCookie'          => [
                'name' => '_csrf',
                'path' => '/',
            ],
            'parsers'             => [
                'multipart/form-data' => 'yii\web\MultipartFormDataParser',
                'application/json'    => 'yii\web\JsonParser'
            ]
        ],
        'response'      => 'app\response\Response',
        'cache'         => [
            'class' => Cache::class,
            'redis' => [
                'hostname' => getenv('REDIS_HOSTNAME'),
                'password' => getenv('REDIS_PASSWORD'),
                'database' => getenv('REDIS_CACHE_DATABASE'),
                'port'     => getenv('REDIS_PORT'),
            ],
        ],
        'authManager'   => [
            'class' => 'yii\rbac\DbManager',
        ],
        'assetManager'  => [
            'linkAssets'      => false,
            'appendTimestamp' => true,
        ],
        'user'          => [
            'identityCookie'  => [
                'path' => '/',
                'name' => empty(getenv("identityCookieName")) ? "_identity_foquz" : getenv("identityCookieName"),
            ],
            'class'           => 'app\models\UserConfig',
            'identityClass'   => User::class,
            'enableAutoLogin' => true,
            'on afterLogin'   => function ($event) {
                UserVisitLog::newVisitor($event->identity->id);
            },
            'on beforeLogout' => function () {
                Yii::$app->response->cookies->remove('SENT_FOR_SESSION_COOKIE');
                User::deleteSessionDevices(Yii::$app->session->getId());
            }
        ],
        'errorHandler'  => [
            'errorAction' => 'foquz/default/error',
        ],
        'mailer'        => [
            'class'            => 'yii\swiftmailer\Mailer',
            'useFileTransport' => empty(getenv("MAILER_HOST")),
            'transport'        => [
                'class'      => 'Swift_SmtpTransport',
                'host'       => getenv("MAILER_HOST"),
                'username'   => getenv("MAILER_USERNAME"),
                'password'   => getenv("MAILER_PASSWORD"),
                'port'       => getenv("MAILER_PORT") ?? 465,
                'encryption' => getenv("MAILER_ENCRYPTION") ?? 'SSL',
            ]
        ],
        'log'           => [
            'flushInterval' => 1,
            'targets'       => [
                'errors' => [
                    'class'   => JsonStdoutLogTarget::class,
                    'url'     => 'php://stderr',
                    'levels'  => ['error', 'warning'],
                    'except'  => [
                        'yii\web\HttpException:404',
                    ],
                    'logVars' => ['_FILES'],
                    'exportInterval' => 1,
                ],
                'info'   => [
                    'class'          => JsonStdoutLogTarget::class,
                    'url'            => 'php://stdout',
                    'levels'         => ['info'],
                    'categories'     => [
                        'app\*',
                        'object_changes',
                        'auth',
                    //'User',
                        'debug',
                        'yii\queue\*',
                        'app\modules\foquz\queue\*'                         ],
                    'logVars'        => [],
                    'exportInterval' => 1
                ],
            ],
        ],
        'db'            => [
            'class'    => 'yii\db\Connection',
            'dsn'      => 'mysql:host=' . getenv('DB_HOST') . ':' . getenv('DB_PORT') . ';dbname=' . getenv('DB_DBNAME'),
            'username' => getenv('DB_USER'),
            'password' => getenv('DB_PASSWORD'),
            'charset'  => 'utf8mb3',
        ],
        'urlManager'    => [
            'enablePrettyUrl' => true,
            'showScriptName'  => false,
            'rules'           => [
                'GET /requests-projects/<key:\w+>'                  => 'foquz/requests-projects/view',
                'GET /requests-projects-list/<key:\w+>'             => 'foquz/requests-projects/external-list',
                'GET /q<id:\w+>'                                    => 'site/shortlink',
                'GET /stats/<id:\w+>'                               => 'foquz/foquz-poll/stats-link',
                'GET /answers/<id:\w+>'                             => 'foquz/foquz-poll/answers-link',
                'GET /stat-widget/<key:\w+>'                        => 'foquz/foquz-poll/stat-widget',
                '/foquz/api/i/p/<action:[\w\-\d]+>'                 => 'foquz/api/p/<action>',
                'GET /lang'                                         => 'foquz/api/language/language/index',
                'GET /lang/company/<id:\d+>'                        => 'foquz/api/language/company/index',
                'PUT /lang/company/<lang:\d+>/<token:\w+>'          => 'foquz/api/language/company/update',
                'GET /lang/user/<id:\d+>'                           => 'foquz/api/language/user/index',
                'PUT /lang/user/<lang:\d+>/<token:\w+>'             => 'foquz/api/language/user/update',
                'GET /setting-table/<table-code:[\-\d\w]+>'         => 'foquz/api/setting-tables/index',
                'PUT /setting-table'                                => 'foquz/api/setting-tables/update',
                'GET /setting-table-filters/<table-code:[\-\d\w]+>' => 'foquz/api/setting-table-filters/index',
                'PUT /setting-table-filters'                        => 'foquz/api/setting-table-filters/update',
            ],
        ],
        'view'          => [
            'class'     => View::class,
            'renderers' => [
                'twig' => [
                    'class'     => 'yii\twig\ViewRenderer',
                    'cachePath' => '@runtime/Twig/cache',
                    'options'   => [
                        'auto_reload' => true,
                    ],
                    'globals'   => [
                        'html' => ['class' => Html::class],
                        'Url'  => ['class' => Url::class],
                    ],
                    'uses'      => ['yii\bootstrap'],
                ],
            ],
            'theme'     => [
                'pathMap' => [
                    '@vendor/webvimark/module-user-management/views' => '@app/views/user',
                ],
            ],
        ],
        'qr'            => [
            'class' => '\Da\QrCode\Component\QrCodeComponent',
        ],
        'consoleRunner' => [
            'class' => 'vova07\console\ConsoleRunner',
            'file'  => '@app/yii' // or an absolute path to console file
        ],
        'devicedetect'  => [
            'class' => 'alexandernst\devicedetect\DeviceDetect'
        ],
        'rabbit_queue'  => [
            'host'         => getenv('RABBITMQ_HOSTNAME'),
            'port'         => getenv('RABBITMQ_PORT'),
            'user'         => getenv('RABBITMQ_USER'),
            'password'     => getenv('RABBITMQ_PASSWORD'),
            'vhost'        => getenv('RABBITMQ_VHOST'),
            'class'        => Queue::class,
            'queueName'    => 'foquz',
            'driver'       => yii\queue\amqp_interop\Queue::ENQUEUE_AMQP_LIB,
            'serializer'   => JsonSerializer::class,
            'exchangeName' => 'foquz',
            'as log'       => LogBehavior::class,
        ],

        'queue_mailings_in' => [
            'host'                   => getenv('RABBITMQ_HOSTNAME'),
            'port'                   => getenv('RABBITMQ_PORT'),
            'user'                   => getenv('RABBITMQ_USER'),
            'password'               => getenv('RABBITMQ_PASSWORD'),
            'vhost'                  => getenv('RABBITMQ_VHOST'),
            'class'                  => RabbitExternalQueue::class,
            'exchangeName'           => 'foquz_mailing',
            'queueName'              => 'foquz_mailing',
            'driver'                 => yii\queue\amqp_interop\Queue::ENQUEUE_AMQP_LIB,
            'strictJobType'          => false,
            'serializer'             => JsonSerializer::class,
            'as log'                 => LogBehavior::class,
            'queueOptionalArguments' => ['x-max-priority' => 10],
        ],

        'queue_mailings_out' => [
            'host'                   => getenv('RABBITMQ_HOSTNAME'),
            'port'                   => getenv('RABBITMQ_PORT'),
            'user'                   => getenv('RABBITMQ_USER'),
            'password'               => getenv('RABBITMQ_PASSWORD'),
            'vhost'                  => getenv('RABBITMQ_VHOST'),
            'class'                  => yii\queue\amqp_interop\Queue::class,
            'strictJobType'          => false,
            'serializer'             => JsonSerializer::class,
            'queueName'              => 'mailings_external',
            'exchangeName'           => 'mailings_external',
            'driver'                 => yii\queue\amqp_interop\Queue::ENQUEUE_AMQP_LIB,
            'as log'                 => LogBehavior::class,
            'queueOptionalArguments' => ['x-max-priority' => 10],
        ],
    ],
    'modules'         => [
        'foquz'           => [
            'class'           => 'app\modules\foquz\Module',
            'layout'          => 'foquz',
            'on beforeAction' => function () {
                if (!Yii::$app->user->isGuest) {
                    $company = Company::findOne(Yii::$app->user->identity->company->id);
                    if (Yii::$app->user->identity->language) {
                        Yii::$app->language = Yii::$app->user->identity->language->sign;
                    }
                    $host = strtolower($_SERVER['HTTP_HOST']);
                    if ($host === strtolower($company->alias)) {
                        return;
                    }
                    if (
                        !str_starts_with(Yii::$app->request->url, "/foquz/api/p//")
                    ) {
                        header('Location: ' . 'https://' . strtolower($company->alias) . Yii::$app->request->url);
                    }
                }
            }
        ],
        'user-management' => [
            'class'               => 'webvimark\modules\UserManagement\UserManagementModule',
            'controllerNamespace' => 'app\controllers\user',
            'layout'              => '@app/views/layouts/report',
            'on beforeAction'     => function (yii\base\ActionEvent $event) {
                if ($event->action->uniqueId == 'user-management/auth/login') {
                    $event->action->controller->enableCsrfValidation = false;
                    $event->action->controller->layout = 'loginLayout.php';
                }
            },
        ],
    ],
    'params'          => $params,
    'on beforeAction' => function () {
        if (($lang = Yii::$app->request->get('lang')) && !preg_match('/^[a-z0-9_-]+$/i', $lang)) {
            header('Location: /');
            exit;
        }
        BrowserNotificationUserDevice::updateActivity(Yii::$app->session->getId());
    }
];

$config = array_merge_recursive(
    $config,
    require(__DIR__ . '/web-db-log.php')
);

if (!empty(getenv("QUIZ_AUTHKEY")) && file_exists(__DIR__ . '/web-quiz.php')) {
    $config = array_merge_recursive(
        $config,
        require(__DIR__ . '/web-quiz.php')
    );
}


if (!empty(getenv("WIDGETS_URL")) && file_exists(__DIR__ . '/web-widget.php')) {
    $config = array_merge_recursive(
        $config,
        require(__DIR__ . '/web-widget.php')
    );
}


if (!empty(getenv("SAML_ENTITY_ID")) && file_exists(__DIR__ . '/web-saml.php')) {
    $config = array_merge_recursive(
        $config,
        require(__DIR__ . '/web-saml.php')
    );
}

if (file_exists(__DIR__ . '/web.type.php')) {
    $config = array_merge_recursive(
        $config,
        require(__DIR__ . '/web.type.php')
    );
}


if (file_exists(__DIR__ . '/web.mode.php')) {
    $config = array_merge_recursive(
        $config,
        require(__DIR__ . '/web.mode.php')
    );
}

if (file_exists(__DIR__ . '/web.type.mode.php')) {
    $config = array_merge_recursive(
        $config,
        require(__DIR__ . '/web.type.mode.php')
    );
}

if (!empty($params['sentry_dsn'])) {
    $config['components']['log']['targets']['sentry'] = [
        'class'   => SentryTarget::class,
        'dsn'     => $params['sentry_dsn'],
        'levels'  => ['error', 'warning'],
        'context' => true,
    ];
}


return $config;
