ARG TAG_CORE_JS_MAIL
FROM cr.yandex/crpsvv5iscafkhlubkmt/foquz-core-js-mail:${TAG_CORE_JS_MAIL} AS mail

ARG TAG_BASE
FROM cr.yandex/crpsvv5iscafkhlubkmt/foquz-core-alpine-php-fpm-base:${TAG_BASE}

ARG RELEASE
ARG MODE= dev
ARG TYPE

COPY --from=mail  /jsbuild  /var/www/foquz/mail
COPY . /var/www/foquz

COPY images/foquz-core-main-app/php-fpm-healthcheck /usr/local/bin/php-fpm-healthcheck
RUN chmod +x /usr/local/bin/php-fpm-healthcheck
RUN set -xe && echo "pm.status_path = /status" >> /usr/local/etc/php-fpm.d/zz-docker.conf

RUN rm -R /var/www/foquz/images && \
    mkdir /var/www/foquz/runtime && \
		chmod -R 777 /var/www/foquz/runtime && \
		addgroup -S -g 1099 foquz  && \
    adduser  -S -u 1099 -G foquz foquz  && \
		chown foquz:foquz -R /var/www/foquz/ &&  \
		ln -s  /var/www/foquz/web/ /app

ENV CLUSTER  1
USER foquz
WORKDIR /var/www



