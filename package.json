{"name": "hatimaki-site", "version": "1.0.0", "description": "hatimaki project", "main": "index.js", "scripts": {"prod": "cross-env NODE_ENV=production webpack", "build": "cross-env NODE_ENV=rc webpack", "build-local": "cross-env NODE_ENV=development webpack", "watch": "cross-env NODE_ENV=development webpack --watch", "start": "npm run watch", "styleguide": "cross-env NODE_ENV=development webpack --config webpack.styleguide.config.js --watch", "build:styleguide": "cross-env NODE_ENV=production webpack --config webpack.styleguide.config.js", "test": "jest", "lint": "eslint --fix ./ko"}, "author": "Furry Cat", "license": "ISC", "devDependencies": {"@babel/core": "^7.12.3", "@babel/eslint-parser": "^7.21.3", "@babel/plugin-transform-runtime": "^7.12.1", "@babel/plugin-transform-typescript": "^7.21.3", "@babel/preset-env": "^7.12.1", "@playwright/test": "^1.52.0", "@sentry/webpack-plugin": "^2.18.0", "@types/knockout": "^3.4.72", "@typescript-eslint/parser": "^5.59.1", "autoprefixer": "^10.0.1", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-loader": "^8.1.0", "babel-preset-env": "^1.7.0", "babel-preset-es2015": "^6.24.1", "babelify": "^10.0.0", "browser-sync": "^2.14.0", "browser-sync-reuse-tab": "^1.0.3", "browserify": "^16.5.1", "css-loader": "^5.0.0", "eslint": "^8.5.0", "eslint-webpack-plugin": "^4.0.1", "file-loader": "^6.1.1", "gulp": "^4.0.2", "gulp-autoprefixer": "^3.1.1", "gulp-babel": "^7.0.1", "gulp-buffer": "0.0.2", "gulp-clean-css": "^2.0.12", "gulp-concat": "^2.6.0", "gulp-cssimport": "^6.0.0", "gulp-file-include": "^2.2.2", "gulp-group-css-media-queries": "^1.2.0", "gulp-if": "^2.0.1", "gulp-less": "^4.0.1", "gulp-notify": "^2.2.0", "gulp-plumber": "^1.1.0", "gulp-pretty-html": "^2.0.9", "gulp-rename": "^1.4.0", "gulp-sourcemaps": "^2.6.4", "gulp-tap": "^2.0.0", "gulp-uglify": "^2.0.0", "html-loader": "^1.3.2", "image-minimizer-webpack-plugin": "^1.0.0", "imagemin-svgo": "^8.0.0", "less": "^3.12.2", "less-loader": "^7.0.2", "mini-css-extract-plugin": "^1.1.2", "postcss": "^8.1.3", "postcss-loader": "^4.0.4", "postcss-preset-env": "^6.7.0", "raw-loader": "^4.0.2", "rimraf": "^2.5.4", "sass": "^1.32.7", "sass-loader": "^11.0.1", "style-loader": "^2.0.0", "svg-inline-loader": "^0.8.2", "svg-sprite-loader": "^5.2.1", "terser-webpack-plugin": "^5.3.10", "ts-loader": "^9.4.1", "typescript": "^4.8.4", "watchify": "^3.11.1", "webpack": "^5.74.0", "webpack-cli": "^4.1.0", "yargs": "^12.0.5"}, "dependencies": {"@babel/plugin-transform-template-literals": "^7.12.1", "@popperjs/core": "^2.5.4", "@shopify/draggable": "^1.0.0-beta.8", "@types/knockout.validation": "^0.0.38", "@types/sortablejs": "^1.15.0", "autosize-input": "^1.0.2", "axios": "^0.27.2", "core-js": "^3.6.5", "cross-env": "^7.0.3", "css-vars-ponyfill": "^2.3.2", "daterangepicker": "^3.1.0", "deparam": "^1.0.5", "deparam.js": "^3.0.5", "fetch-jsonp": "^1.1.3", "highcharts": "^9.3.2", "html2canvas": "^1.0.0-rc.7", "html2pdf.js": "^0.9.2", "htmltidy2": "^0.3.0", "inputmask": "^5.0.6-beta.25", "json-form-data": "^1.7.2", "jsonp": "^0.2.1", "knockout-sortable": "^1.2.0", "lodash": "^4.17.21", "moment": "^2.29.1", "nanoid": "^4.0.0", "nanostores": "^0.9.3", "nouislider": "^15.5.0", "overlayscrollbars": "^1.13.0", "perfect-scrollbar": "^1.5.0", "query-string": "^9.1.1", "run-parallel": "^1.1.10", "smooth-scrollbar": "^8.5.3", "sortablejs": "^1.15.0", "sticky-events": "^3.4.10", "sticky-sidebar": "^3.3.1", "svg2base64-loader": "^1.0.4", "tippy.js": "^6.2.7", "util": "^0.12.3"}, "directories": {"test": "tests"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/vitplotnikov/opros.hatimaki.ru.git"}, "homepage": "https://bitbucket.org/vitplotnikov/opros.hatimaki.ru#readme"}