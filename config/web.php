<?php


use notamedia\sentry\SentryTarget;
use yii\helpers\Url;
use yii\helpers\Html;
use yii\web\View;
use app\components\GetHostComponent;
use app\models\company\Company;
use app\models\User;
use app\modules\foquz\models\BrowserNotificationUserDevice;
use webvimark\modules\UserManagement\models\UserVisitLog;

$params = array_merge(
    require __DIR__ . '/params.php',
    require __DIR__ . '/params-local.php'
);
$db = require __DIR__ . '/db.php';

$config = [
    'as hostControl' => [
        'class' => 'yii\filters\HostControl',
        'allowedHosts' => [
            'foquz.ru',
            '*.foquz.ru',
            '*.r-ulybka.ru',
            'opros.eae-consult.ru',
            '*.4qz.ru',
            '4qz.ru',
            '3qz.ru',
            '*.mai3.ru',
            '*.eae-consult.ru',
            'poll.rskh.ru',
            'foquzdev.ru',
            '*.foquzdev.ru',
            'doxswf.ru',
            '*.doxswf.ru',
            'devfoquz.ru',
            '*.devfoquz.ru',
            '*.web',
            'web',
            'localhost',
            '*.localhost',
            'foquz-nginx',
        ],
    ],
    'id' => 'basic',
    'basePath' => dirname(__DIR__),
    'bootstrap' => [
  //      'app\modules\zapier\api\v1\PathBootstrap',
        'devicedetect',
        'log',
        GetHostComponent::class,
    ],

    //'language' => 'en-US',
    'language' => 'ru-RU',

    'sourceLanguage' => 'ru-RU',
    'defaultRoute' => 'site/index',
    'homeUrl' => '/foquz',
    'timeZone' => 'Europe/Moscow',
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm' => '@vendor/npm-asset',
        '@cases' => '@app/views/site/cases',
        '@web' => '@app/web',
        '@uploads' => '@app/web/uploads',
        '@runtime' => '@app/runtime',
    ],
    'components' => [
        'saml' => [
            'class' => 'asasmoyo\yii2saml\Saml',
            'configFileName' => '@app/config/saml.php', // OneLogin_Saml config file (Optional)
        ],
        'sphinx' => [
            'class' => 'yii\sphinx\Connection',
            'dsn' => 'mysql:host=127.0.0.1;port=9306;',
            //  'username' => '',
            //  'password' => '',
        ],

        'i18n' => [
            'translations' => [
                '*' => [
                    'class' => 'app\components\JsonMessageSource',
                    //'basePath' => '@app/messages',
                    'sourceLanguage' => 'ru-RU',
                ],
            ],
        ],

        'html2pdf' => [
            'class' => 'yii2tech\html2pdf\Manager',
            'viewPath' => '@app/views/pdf',
            'converter' => 'wkhtmltopdf',
        ],
        'session' => [
            'cookieParams' => [
                'domain' => $params['company_domen'],
                'httpOnly' => true,
                'path' => '/',
            ],
        ],
        'request' => [
            // !!! insert aide secret key in the following (if it is empty) - this is required by cookie validation
            'cookieValidationKey' => 'askfjhsakjghasgasdkghsajh',
            'csrfCookie' => [
                'name' => '_csrf',
                'path' => '/',
                'domain' => $params['company_domen'],
            ],
            'parsers' => [
                'multipart/form-data' => 'yii\web\MultipartFormDataParser',
                'application/json' => 'yii\web\JsonParser'
            ]
        ],
        'response' => 'app\response\Response',
        'cache' => [
            'class' => 'yii\caching\FileCache',
        ],
        'authManager' => [
            'class' => 'yii\rbac\DbManager',
        ],
        'assetManager' => [
            'linkAssets' => false,
            'appendTimestamp' => true,
        ],
        'user' => [
            'identityCookie' => [
                'path' => '/',
                'name' => '_identity',
                'domain' => $params['company_domen'],
                'domain' => $params['company_domen'],
            ],
            'class' => 'app\models\UserConfig',
            'identityClass' => User::class,
            'enableAutoLogin' => true,
            // Comment this if you don't want to record user logins
            'on afterLogin' => function ($event) {
                 \app\models\UserVisitLog::newVisitor($event->identity->id);
            },
            'on beforeLogout' => function ($event) {
                Yii::$app->response->cookies->remove('SENT_FOR_SESSION_COOKIE');
                User::deleteSessionDevices(Yii::$app->session->getId());
            }
        ],
        'errorHandler' => [
            'errorAction' => 'foquz/default/error',
        ],
        'mailer' => require __DIR__ . '/mailer.php',
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                'errors'=>[
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
                'profile'=>[
                    'class' => 'yii\log\FileTarget',
                    'categories' => ['answer_search'],
                    'logFile' => '@app/runtime/logs/profile.log',
                    'logVars' => ['_SERVER.HTTP_HOST', '_SERVER.HTTP_REFERER', '_GET'],
                ],
                'queue' => [
                    'class'          => 'yii\log\FileTarget',
                    'levels'         => ['error', 'warning', 'info'],
                    'categories'     => [
                        'yii\queue\*',
                        'app\modules\foquz\queue\*'
                    ],
                    'logFile'        => '@app/runtime/logs/queue.log',
                    'logVars'        => [],
                    'exportInterval' => 1
                ],
                'debug' => [
                    'class'          => 'yii\log\FileTarget',
                    'levels'         => ['info'],
                    'logFile'        => '@app/runtime/logs/debug.log',
                    'logVars'        => [],
                    'exportInterval' => 1,
                    'categories' =>['debug'],
                ],
                'object_changes' => [
                    'class'          => \app\components\JsonFileTarget::class,
                    'levels'         => ['info'],
                    'logFile'        => '@app/runtime/logs/object_changes.log',
                    'logVars'        => [],
                    'exportInterval' => 1,
                    'categories' =>['object_changes'],
                ],
                'user' => [
                    'class'          => \app\components\JsonFileTarget::class,
                    'levels'         => ['info'],
                    'logFile'        => '@app/runtime/logs/user.log',
                    'logVars'        => [],
                    'exportInterval' => 1,
                    'categories' =>['User'],
                ],

                'auth' => [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['warning', 'info'],
                    'categories' => ['auth'],
                    'logFile' => '@app/runtime/logs/auth.log',
                    'logVars' => [],
                ]
            ],
        ],
        'db' => $db,
        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
            'rules' => [
                'GET /google-sheets/create/<table:\w+>/<user_id:\w+>' => 'google-sheets/create',
                'GET /p/<id:\w+>' => 'foquz/default/anonymous',
                'GET /pkn/<id:\w+>' => 'foquz/default/anonymous',
                'GET /p/<tablet:t>/<id:\w+>' => 'foquz/default/anonymous',
                'GET /requests-projects/<key:\w+>' => 'foquz/requests-projects/view',
                'GET /requests-projects-list/<key:\w+>' => 'foquz/requests-projects/external-list',
                'GET /r/<r:\w+>' => 'site/index',
                'GET /q<id:\w+>' => 'site/shortlink',
                'GET /cases/<id:\w+>' => 'site/case',
                'GET /site/cases/<id:[\w\-]+>' => 'site/case',
                'GET /stats/<id:\w+>' => 'foquz/foquz-poll/stats-link',
                'GET /answers/<id:\w+>' => 'foquz/foquz-poll/answers-link',
                'GET /stat-widget/<key:\w+>' => 'foquz/foquz-poll/stat-widget',
                'GET /foquz/user-wiki/<name:[\w|-]+>' => 'foquz/user-wiki/index',
                'GET /activate/<token:\w+>' => 'user-management/auth/activate-email',

                'GET /lang' => 'foquz/api/language/language/index',
                'GET /lang/company/<id:\d+>' => 'foquz/api/language/company/index',
                'PUT /lang/company/<lang:\d+>/<token:\w+>'  => 'foquz/api/language/company/update',
                'GET /lang/user/<id:\d+>' => 'foquz/api/language/user/index',
                'PUT /lang/user/<lang:\d+>/<token:\w+>'  => 'foquz/api/language/user/update',

                'GET /setting-table/<table-code:[\-\d\w]+>' => 'foquz/api/setting-tables/index',
                'PUT /setting-table' => 'foquz/api/setting-tables/update',
                'GET /setting-table-filters/<table-code:[\-\d\w]+>' => 'foquz/api/setting-table-filters/index',
                'PUT /setting-table-filters' => 'foquz/api/setting-table-filters/update',

                'GET /obrazcy-anket' => 'site/solutions',
                'GET /obrazcy-anket/<code>' => 'site/solutions',

                'GET /author/<name:[\-\w]+>' => 'site/author',

                '/<name:(^(?!foquz\/|foquz$|iiko\/|iiko$|zapier\/|zapier$|adfs\/|adfs$|api20\/|api20$|api\/|api$|base\/|base$|companyaccess\/|companyaccess$|company\/|company$|googleoauth\/|googleoauth$|googlesheets\/|googlesheets$|interview\/|interview$|oauth\/|oauth$|polls\/|polls$|report\/|report$|site\/|site$|telegram\/|telegram$|auth\/|auth$|authitemgroup\/|authitemgroup$|permission\/|permission$|role\/|role$|user\/|user$|userpermission\/|userpermission$|uservisitlog\/|uservisitlog$)([\w-]+)$)+>' => 'site/article',

                /*'GET /kak-sostavit-anketu-dlya-oprosa' => 'site/questionnaires-poll',
                'GET /vidy-voprosov-v-ankete' => 'site/questions-types',
                'GET /nps-index-potrebitelskoy-loyalnosti' => 'site/loyalty-index',
                'GET /obratnaya-svyaz' => 'site/feedback',
                'GET /oprosy-potrebitelej' => 'site/consumer-polls',
                'GET /marketingovye-issledovaniya' => 'site/marketing',
                'GET /otsenka-uznavaemosti-brenda' => 'site/brand',
                'GET /upravlenie-klientskim-opytom' => 'site/customer-experience',
                'GET /pyat-lifehakov-dlya-raboty-s-foquz' => 'site/life-hack',
                'GET /obrazcy-anket' => 'site/solutions',

                'GET /tochki-kontakta-o-knige-igorya-manna' => 'site/tochki-kontakta',
                'GET /kak-pravilno-sobirat-obratnuyu-svyaz' => 'site/kak-pravilno-sobirat-obratnuyu-svyaz',
                'GET /stranica-blagodarnosti-chto-eto-takoe-i-zachem-ona-nuzhna' => 'site/stranica-blagodarnosti',
                'GET /kolichestvennye-issledovaniya-chto-eto-takoe-i-kakie-vidy-sushchestvuyut' => 'site/kolichestvennye-issledovaniya',
                'GET /sostavlenie-ankety-dlya-marketingovogo-issledovaniya-osnovnye-oshibki-i-kak-ih-izbezhat' => 'site/sostavlenie-ankety-dlya-marketingovogo-issledovaniya',
                'GET /onlajn-oprosy-kak-sozdat-anketu-besplatno' => 'site/onlajn-oprosy',
                'GET /foquz-pro-platforma-sbora-obratnoj-svyazi-i-upravleniya-klientskim-opytom-na-vashem-servere' => 'site/foquz-pro',
                'GET /zakrytye-voprosy-v-ankete-osnovnye-vidy-i-osobennosti-ispolzovaniya' => 'site/zakrytye-voprosy-v-ankete',

                'GET /onlajn-oprosy-dostoinstva-i-nedostatki' => 'site/onlajn-oprosy-dostoinstva-i-nedostatki',
                'GET /opros-udovletvorennosti-sotrudnikov' => 'site/opros-udovletvorennosti-sotrudnikov',

                'GET /anketa-psihologa-dlya-klienta' => 'site/anketa-psihologa-dlya-klienta',
                'GET /anketa-kosmetologa-dlya-klienta' => 'site/anketa-kosmetologa-dlya-klienta',
                'GET /anketa-klienta-massazhnogo-kabineta-obrazec' => 'site/anketa-klienta-massazhnogo-kabineta-obrazec',
                'GET /anketa-anonimnaya-primer' => 'site/anketa-anonimnaya-primer',
                'GET /chto-takoe-anketa' => 'site/chto-takoe-anketa',

                'GET /anketa-postoyannogo-pokupatelya-obrazec' => 'site/anketa-postoyannogo-pokupatelya-obrazec',
                'GET /anketa-kachestva-obsluzhivaniya-klientov' => 'site/anketa-kachestva-obsluzhivaniya-klientov',
                'GET /anketa-dlya-oprosa-potrebitelej-tovarov-i-uslug' => 'site/anketa-dlya-oprosa-potrebitelej-tovarov-i-uslug',
                'GET /chek-list-tajnogo-pokupatelya-obrazec' => 'site/chek-list-tajnogo-pokupatelya-obrazec',

                'GET /anketa-novogo-klienta' => 'site/anketa-novogo-klienta',
                'GET /anketa-kompanii' => 'site/anketa-kompanii',
                'GET /anketa-turagentstva-dlya-klienta' => 'site/anketa-turagentstva-dlya-klienta',
                'GET /anketa-posetitelya-vystavki-ili-stenda' => 'site/anketa-posetitelya-vystavki-ili-stenda',
                'GET /anketa-dlya-restorana-oprosnik' => 'site/anketa-dlya-restorana-oprosnik',

                'GET /anketa-dlya-diskontnyh-kart' => 'site/anketa-dlya-diskontnyh-kart',
                'GET /anketa-dlya-izucheniya-sprosa' => 'site/anketa-dlya-izucheniya-sprosa',
                'GET /anketa-klienta-fizicheskogo-lica' => 'site/anketa-klienta-fizicheskogo-lica',
                'GET /opros-otkuda-vy-o-nas-uznali' => 'site/opros-otkuda-vy-o-nas-uznali',
                'GET /kak-otvechat-na-voprosy-v-ankete-pri-prieme-na-rabotu' => 'site/kak-otvechat-na-voprosy-v-ankete-pri-prieme-na-rabotu',
                'GET /obyasnenie-modeli-kano-analiz-i-primery' => 'site/obyasnenie-modeli-kano-analiz-i-primery',
                'GET /total-experience-odno-uluchshenie-4-rezultata' => 'site/total-experience-odno-uluchshenie-4-rezultata',
                'GET /obnovlenie-foquz-klyuchevye-uluchsheniya-za-pervoe-polugodie-2023-goda' => 'site/obnovlenie-foquz-klyuchevye-uluchsheniya-za-pervoe-polugodie-2023-goda',
                'GET /obnovlenie-foquz-itogi-leta-2023-goda' => 'site/obnovlenie-foquz-itogi-leta-2023-goda',
                'GET /golos-klienta-v-kolichestvennyh-oprosah-kak-rabotat-s-otkrytymi-kommentariyami' => 'site/golos-klienta-v-kolichestvennyh-oprosah-kak-rabotat-s-otkrytymi-kommentariyami',
                'GET /semantic-differential-guide' => 'site/semantic-differential-guide',
                'GET /cjm-instrument-dlya-sozdaniya-klientskogo-opyta-i-upravleniya-im' => 'site/cjm-instrument-dlya-sozdaniya-klientskogo-opyta-i-upravleniya-im',
                'GET /produktovye-gipotezy-101-chto-eto-kak-najti-i-kak-stat-luchshe' => 'site/produktovye-gipotezy-101-chto-eto-kak-najti-i-kak-stat-luchshe',
                'GET /chto-takoe-tone-of-voice-tonalnost-obshcheniya-v-klientskom-opyte' => 'site/chto-takoe-tone-of-voice-tonalnost-obshcheniya-v-klientskom-opyte',
                'GET /sistema-edinogo-vhoda-i-razgranicheniya-prav-dostupa-na-baze-tekhnologii-saml-sso' => 'site/sistema-edinogo-vhoda-i-razgranicheniya-prav-dostupa-na-baze-tekhnologii-saml-sso',
                'GET /interaktivnyj-opros-obedinite-raznye-vidy-opyta' => 'site/interaktivnyj-opros-obedinite-raznye-vidy-opyta',*/
            ],
        ],
        'view' => [
            'class' => View::class,
            'renderers' => [
                'twig' => [
                    'class' => 'yii\twig\ViewRenderer',
                    'cachePath' => '@runtime/Twig/cache',
                    // Array of twig options:
                    'options' => [
                        'auto_reload' => true,
                    ],
                    'globals' => [
                        'html' => ['class' => Html::class],
                        'Url' => ['class' => Url::class],
                    ],
                    'uses' => ['yii\bootstrap'],
                ],
            ],
            'theme' => [
                'pathMap' => [
                    '@vendor/webvimark/module-user-management/views' => '@app/views/user',
                ],
            ],
        ],
        'qr' => [
            'class' => '\Da\QrCode\Component\QrCodeComponent',
        ],
        'consoleRunner' => [
            'class' => 'vova07\console\ConsoleRunner',
            'file' => '@app/yii' // or an absolute path to console file
        ],
        'devicedetect' => [
            'class' => 'alexandernst\devicedetect\DeviceDetect'
        ]
    ],
    'modules' => [
    /*    'zapier' => [
            'class' => 'app\modules\zapier\Module'
        ],*/
        'foquz' => [
            'class' => 'app\modules\foquz\Module',
            'layout' => 'foquz',
            'on beforeAction' => function (yii\base\ActionEvent $event) {
                $host = strtolower($_SERVER['HTTP_HOST']);
                if (!Yii::$app->user->isGuest) {
                    $company = Company::findOne(Yii::$app->user->identity->company->id);

                    if (Yii::$app->user->identity->language) {
                        \Yii::$app->language = Yii::$app->user->identity->language->sign;
                    }

                    if (Yii::$app->user->identity->company->id == 1 && $company->alias == "foquz.ru") {
                        if (Yii::$app->user->id != 1 && Yii::$app->user->id != 812) {
                            header('Location: ' . 'https://hatimaki.foquz.ru' . Yii::$app->request->url);
                            exit;
                        }
                    }

                    if ($host !== strtolower($company->alias)) {
                        if (@$_GET["authKey"] != "dummyDesign" && Yii::$app->request->url != "/foquz/ajax/quick-help" && Yii::$app->request->pathInfo != "foquz/ajax/mark-done") {
                            if (
                                !preg_match("@^/p/@", Yii::$app->request->url) &&
                                strpos(Yii::$app->request->pathInfo, 'foquz/api/p') === false &&
                                !in_array(Yii::$app->request->pathInfo, [
                                    'foquz/default/save-answer',
                                    'foquz/default/answer-variables',
                                    'foquz/api/poll/widget-poll-info',
                                    'foquz/ajax/answer-upload-files',
                                    'foquz/ajax/delete-answer-item-file',
                                    'foquz/ajax/simple-upload-complaint',
                                    'foquz/ajax/delete-complaint-file',
                                    'foquz/ajax/add-complaint',
                                    'foquz/ajax/login',
                                ], true)
                            ) {
                                header('Location: ' . 'https://' . strtolower($company->alias) . Yii::$app->request->url);
                            }
                        }
                    }
                }
            }
        ],
        'user-management' => [
            'class' => 'webvimark\modules\UserManagement\UserManagementModule',
            'controllerNamespace' => 'app\controllers\user',
            'layout' => '@app/views/layouts/report',

            // 'enableRegistration' => true,

            // Add regexp validation to passwords. Default pattern does not restrict user and can enter any set of characters.
            // The example below allows user to enter :
            // any set of characters
            // (?=\S{8,}): of at least length 8
            // (?=\S*[a-z]): containing at least one lowercase letter
            // (?=\S*[A-Z]): and at least one uppercase letter
            // (?=\S*[\d]): and at least one number
            // $: anchored to the end of the string

            //'passwordRegexp' => '^\S*(?=\S{8,})(?=\S*[a-z])(?=\S*[A-Z])(?=\S*[\d])\S*$',


            // Here you can set your handler to change layout for any controller or action
            // Tip: you can use this event in any module
            'on beforeAction' => function (yii\base\ActionEvent $event) {
                if ($event->action->uniqueId == 'user-management/auth/login') {
                    if ($_SERVER['HTTP_HOST']=="sibur.foquz.ru") {
                        header('Location: https://sibur.foquz.ru/adfs/saml');
                        exit;
                    }
                    $event->action->controller->enableCsrfValidation = false;
                    $event->action->controller->layout = 'loginLayout.php';
                };
            },
        ],

        'oauth2' => [
            'class' => 'filsh\yii2\oauth2server\Module',
            'tokenParamName' => 'accessToken',
            'tokenAccessLifetime' => 3600 * 24,
            'storageMap' => [
                'user_credentials' => 'app\models\User',
            ],
            'grantTypes' => [
                'user_credentials' => [
                    'class' => 'OAuth2\GrantType\UserCredentials',
                ],
                'authorization_code' => [
                    'class' => 'OAuth2\GrantType\AuthorizationCode',
                ],
                'refresh_token' => [
                    'class' => 'OAuth2\GrantType\RefreshToken',
                    'always_issue_new_refresh_token' => true
                ]
            ],
        ],
        'markdown' => [
            'class' => 'kartik\markdown\Module'
        ]
    ],
    'params' => array_merge($params, require '../components/messenger/config/params.php'),
    'on beforeAction' => function ($event) {
        if (($lang = Yii::$app->request->get('lang')) && !preg_match('/^[a-z0-9_-]+$/i', $lang)) {
            header('Location: https://foquz.ru');
            exit;
        }
        BrowserNotificationUserDevice::updateActivity(Yii::$app->session->getId());
    }
];
if (file_exists(__DIR__ . '/queue.php')) {
    $config = \yii\helpers\ArrayHelper::merge($config, require __DIR__ . '/queue.php');
}
if (file_exists(__DIR__ . '/rabbit.php')) {
    $config = \yii\helpers\ArrayHelper::merge($config, require __DIR__ . '/rabbit.php');
}
if (file_exists(__DIR__ . '/db-log.php')) {
    $config = \yii\helpers\ArrayHelper::merge($config, require __DIR__ . '/db-log.php');
}
if (file_exists(__DIR__ . '/debug.php')) {
    $config = \yii\helpers\ArrayHelper::merge($config, require __DIR__ . '/debug.php');
}



if (!empty($params['sentry_dsn'])) {
    $config['components']['log']['targets'][] = [
        'class' => SentryTarget::class,
        'dsn' => $params['sentry_dsn'],
        'levels' => ['error', 'warning'],
        'context' => true,
    ];
}

/*$config['bootstrap'][] = 'debug';

if (true) {
    // configuration adjustments for 'dev' environment
    $config['bootstrap'][] = 'debug';
    $config['modules']['debug'] = [
        'class' => 'yii\debug\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
        'allowedIPs' => ['127.0.0.1', '::1'],
        //'allowedIPs' => ['************']
    ];

    $config['bootstrap'][] = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
        'allowedIPs' => ['127.0.0.1', '::1'],
        //'allowedIPs' => ['************']
    ];
}*/



/*
if (YII_ENV_DEV) {
    $config['bootstrap'][] = 'debug';

    // configuration adjustments for 'dev' environment
    //$config['bootstrap'][] = 'debug';
    $config['modules']['debug'] = [
        'class' => 'yii\debug\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
        //'allowedIPs' => ['127.0.0.1', '::1'],
    'allowedIPs' => ['************', '172.*.0.1']
    ];

    $config['bootstrap'][] = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
    'allowedIPs' => ['************', '172.*.0.1']
    ];
}
*/

if (file_exists(__DIR__ . '/web-local.php')) {
    $config = \yii\helpers\ArrayHelper::merge($config, require __DIR__ . '/web-local.php');
}

return $config;
