// @ts-check
import { expect, test } from '@playwright/test';


// Successful key entry 
test('auth1', async ({ page }) => {
    await page.goto('/');
    await page.getByRole('link', { name: 'Переход в личный кабинет' }).click();
    await page.locator('input[name="username"]').click();
    await page.locator('input[name="username"]').fill('autotest');
    await page.locator('input[name="password"]').click();
    await page.locator('input[name="password"]').fill('12332145Qq!');
    await page.getByRole('button', { name: 'Войти' }).click();
});


// Error for empty fields
test('auth2', async ({ page }) => {
    await page.goto('/user-management/auth/login');
    await page.getByRole('button', { name: 'Войти' }).click();
    await expect(page.locator('#login-form div').filter({ hasText: 'Логин / Email' }).locator('div')).toBeVisible();
    await expect(page.locator('#login-form div').filter({ hasText: 'grxehcfvgbhjn' }).locator('div')).toBeVisible();
});


// Error for invalid password
test('auth3', async ({ page }) => {
    await page.goto('/user-management/auth/login');
    await page.locator('input[name="username"]').click();
    await page.locator('input[name="username"]').fill('asda');
    await page.locator('input[name="password"]').click();
    await page.locator('input[name="password"]').fill('asd');
    await page.getByRole('button', { name: 'Войти' }).click();
    await expect(page.getByText('Должно быть введено хотя бы 6')).toBeVisible();
});


// Incorrect keys error
test('auth4', async ({ page }) => {
    await page.goto('/user-management/auth/login');
    await page.locator('input[name="username"]').click();
    await page.locator('input[name="username"]').fill('autotest');
    await page.locator('input[name="password"]').click();
    await page.locator('input[name="password"]').fill('12332131');
    await page.getByRole('button', { name: 'Войти' }).click();
    await expect(page.getByText('Неверный логин или пароль')).toBeVisible();
});